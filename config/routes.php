<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
use Hyperf\HttpServer\Router\Router;
use App\Middleware\Auth\AuthMiddleware;
// use App\Middleware\Log\LoggingMiddleware;

// Router::addRoute(['GET', 'POST', 'HEAD'], '/', 'App\Controller\IndexController@index');

Router::addServer('chat', function () {
    Router::get('/ws', 'App\Controller\ChatController');
});

Router::get('/favicon.ico', function () {
    return '';
});

Router::addRoute(['GET'], '/ja4/check', 'App\Controller\Ja4Controller@check');

Router::addGroup('/api', function () {
    //验证码
    Router::get('/adminUser/captcha', 'App\Controller\AdminUserController@captcha');
    //登录
    Router::post('/adminUser/login', 'App\Controller\AdminUserController@login');
    //刷新signingKey
    // Router::post('/adminUser/refresh', 'App\Controller\AdminUserController@refresh');
    //登出
    Router::post('/adminUser/loginOut', 'App\Controller\AdminUserController@loginOut');
    //权限列表
    Router::get('/adminUser/getRoleList', 'App\Controller\AdminUserController@getRoleList');
    //添加账号
    Router::post('/adminUser/addUser', 'App\Controller\AdminUserController@addUser');
    //更新账号
    Router::post('/adminUser/editUser', 'App\Controller\AdminUserController@editUser');
    //账号列表
    Router::get('/adminUser/userList', 'App\Controller\AdminUserController@userList');
    //账号详情
    Router::get('/adminUser/userDetail', 'App\Controller\AdminUserController@userDetail');
    //删除账号
    Router::get('/adminUser/delUser', 'App\Controller\AdminUserController@delUser');
    //更新当前账号密码
    Router::post('/adminUser/updatePass', 'App\Controller\AdminUserController@updatePass');

    //下载wordpress插件
    Router::get('/adminUser/downWordpressPlugin', 'App\Controller\AdminUserController@downWordpressPlugin');
    //下载wordpress pp插件
    Router::get('/adminUser/downWordpressPaypalPlugin', 'App\Controller\AdminUserController@downWordpressPaypalPlugin');

    //数据中心
    Router::post('/user/listData', 'App\Controller\UserController@list');
    //操作状态 支持批量
    Router::post('/user/charge', 'App\Controller\UserController@charge');
    Router::post('/user/addRemarks', 'App\Controller\UserController@addRemarks');
    
    //清空数据
    Router::get('/user/doEmpty', 'App\Controller\UserController@doEmpty');
    //域名列表
    Router::get('/user/referer', 'App\Controller\UserController@referer');
    //域名统计
    Router::post('/user/statistics', 'App\Controller\UserController@statistics');
    //域名统计
    Router::get('/user/clearLogs', 'App\Controller\UserController@clearLogs');
    //导出
    Router::get('/user/orderExport', 'App\Controller\UserController@orderExport');
    //删除文件
    Router::post('/user/unlink', 'App\Controller\UserController@unlink');
    //黑名单列表
    Router::get('/user/blacklistList', 'App\Controller\UserController@blacklistList');

    //操作黑名单用户
    Router::post('/user/blacklisting', 'App\Controller\UserController@blacklisting');
    


    //提交数据
    Router::post('/client/postData', 'App\Controller\UserController@postData');
    Router::post('/client/postLog', 'App\Controller\UserController@postLog');
    Router::post('/client/visit', 'App\Controller\UserController@visit');
    
    //操作日志记录
    Router::get('/operationLog/index', 'App\Controller\OperationLogController@index');


    //清空日志
    Router::post('/operationLog/doEmpty', 'App\Controller\OperationLogController@doEmpty');

    //访问记录
    Router::get('/operationLog/viewIndex', 'App\Controller\OperationLogController@index');
    //清空访问记录
    Router::post('/operationLog/doViewEmpty', 'App\Controller\OperationLogController@doEmpty');

    //添加角色
    Router::post('/role/addRole', 'App\Controller\RoleController@addRole');
    //更新角色
    Router::post('/role/editRole', 'App\Controller\RoleController@editRole');
    //角色列表
    Router::get('/role/roleList', 'App\Controller\RoleController@roleList');
    //角色详情
    Router::get('/role/roleDetail', 'App\Controller\RoleController@roleDetail');
    //角色删除
    Router::get('/role/delRole', 'App\Controller\RoleController@delRole');

    //获取配置信息
    Router::get('/adminConfig/infoIndex', 'App\Controller\AdminConfigController@index');
    //配置信息更新
    Router::post('/adminConfig/updateConfig', 'App\Controller\AdminConfigController@updateConfig');

    //获取卡头备注列表
    Router::get('/cardRemark/remarkIndex', 'App\Controller\CardRemarkListController@index');
    //添加卡头备注
    Router::post('/cardRemark/addRemark', 'App\Controller\CardRemarkListController@addRemark');
    //编辑卡头备注
    Router::post('/cardRemark/editRemark', 'App\Controller\CardRemarkListController@editRemark');
    //删除卡头备注
    Router::get('/cardRemark/delRemark', 'App\Controller\CardRemarkListController@delRemark');
    //清空卡头备注
    Router::get('/cardRemark/clearRemark', 'App\Controller\CardRemarkListController@clearRemark');

    //批量导入卡头备注
    Router::post('/cardRemark/batchBin', 'App\Controller\CardRemarkListController@batchBin');

    //首页统计数据
    Router::post('/home/<USER>', 'App\Controller\IndexController@index');

    //首页系统信息
    Router::get('/home/<USER>', 'App\Controller\IndexController@system');

    //获取站点列表
    Router::get('/siteManager/siteIndex', 'App\Controller\SiteManagerController@index');
    //添加站点
    // Router::post('/siteManager/addSite', 'App\Controller\SiteManagerController@addSite');
    //编辑站点
    Router::post('/siteManager/editSite', 'App\Controller\SiteManagerController@editSite');
    //删除站点
    Router::get('/siteManager/delSite', 'App\Controller\SiteManagerController@delSite');
    //删除域名nginx配置
    Router::get('/siteManager/removeDomainConfig', 'App\Controller\SiteManagerController@removeDomainConfig');
    //更新站点代码
    Router::get('/siteManager/updateSite', 'App\Controller\SiteManagerController@updateSite');

    //更新后台系统代码
    Router::get('/siteManager/updateAdminSite', 'App\Controller\SiteManagerController@updateAdminSite');

    //下载ssl证书
    // Router::post('/siteManager/applySsl', 'App\Controller\SiteManagerController@applySsl');

    //下载后台ssl证书
    // Router::get('/siteManager/applyBackstageSsl', 'App\Controller\SiteManagerController@applyBackstageSsl');

    //新下载后台ssl证书
    Router::get('/sse/applyBackstageSsl', 'App\Controller\SseSslController@applyBackstageSsl');

    //新申请ssl证书
    Router::post('/sse/applySsl', 'App\Controller\SseSslController@applySsl');

    //站点详情
    Router::get('/siteManager/siteDetail', 'App\Controller\SiteManagerController@detail');

    //获取自定义验证列表
    Router::get('/siteManager/customIndex', 'App\Controller\SiteManagerController@customIndex');
    //添加自定义验证
    Router::post('/siteManager/addCustom', 'App\Controller\SiteManagerController@addCustom');
    //编辑自定义验证
    Router::post('/siteManager/editCustom', 'App\Controller\SiteManagerController@editCustom');
    //删除自定义验证
    Router::get('/siteManager/delCustom', 'App\Controller\SiteManagerController@delCustom');
    //自定义验证详情
    Router::get('/siteManager/customDetail', 'App\Controller\SiteManagerController@customDetail');

    //获取配置信息
    Router::get('/adminConfig/getConfig', 'App\Controller\AdminConfigController@getConfig');

    //获取激活信息
    Router::get('/active/userInfo', 'App\Controller\ActiveSiteController@userInfo');
    Router::post('/active/doStatus', 'App\Controller\ActiveSiteController@doStatus');
    Router::get('/active/downCode', 'App\Controller\ActiveSiteController@downCode');
    Router::get('/active/sourceList', 'App\Controller\ActiveSiteController@sourceList');
    Router::get('/active/searchDownCode', 'App\Controller\ActiveSiteController@searchDownCode');

    //用户限制卡头信息
    Router::post('/adminUserCard/addUserCard', 'App\Controller\AdminUserCardController@addUserCard');
    Router::post('/adminUserCard/editUserCard', 'App\Controller\AdminUserCardController@editUserCard');
    Router::post('/adminUserCard/delUserCard', 'App\Controller\AdminUserCardController@delUserCard');

    //插入redis
    Router::get('/adminConfig/insertRedis', 'App\Controller\AdminConfigController@insertRedis');



    Router::post('/yy/pay', 'App\Controller\UserController@yyPay');
    Router::post('/yy/notify', 'App\Controller\UserController@yyNotify');
    Router::post('/yy/return', 'App\Controller\UserController@yyReturn');

}, ['middleware' => [AuthMiddleware::class]]);
// LoggingMiddleware::class, 
