# gateway/Dockerfile
FROM alpine:3.21

RUN apk add --no-cache \
    curl \
    nginx \
    nginx-mod-http-geoip2 \
    nginx-mod-http-lua \
    lua5.1-cjson



RUN apk add --no-cache \
    --repository=https://dl-cdn.alpinelinux.org/alpine/edge/testing \
    lua-resty-http \
    lua-resty-redis \
    lua-resty-lock \
    lua-resty-openssl \
    lua-resty-string \
    lua-resty-lrucache

STOPSIGNAL SIGTERM
CMD ["nginx", "-g", "daemon off;"]
