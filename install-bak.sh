#!/bin/bash
# 脚本开头，设置-e选项，表示任何命令执行失败则立即退出
set -e

# 设置DEBIAN_FRONTEND为noninteractive，以避免在apt-get等命令执行时出现交互式提示
export DEBIAN_FRONTEND=noninteractive

# 获取操作系统信息
OS_INFO=$(cat /etc/os-release)
# 检查操作系统是否为 Ubuntu 22.04
if [[ "$OS_INFO" =~ 'Ubuntu 22.04' ]]; then
    echo "Operating system is Ubuntu 22.04."
else
    # 如果系统版本不符，则输出错误信息并退出脚本
    echo "服务器操作系统必须使用 Ubuntu 22.04."
    exit 1
fi

# 检查当前用户是否为root用户
if [ "$EUID" -ne 0 ]; then
    # 如果不是root用户，则设置SUDO变量为'sudo'，以便后续命令能以root权限执行
    SUDO='sudo'
else
    # 如果是root用户，则SUDO变量为空字符串
    SUDO=''
fi


# 检查/etc/sysctl.conf文件中是否已存在vm.overcommit_memory配置
if grep -q "^vm.overcommit_memory" /etc/sysctl.conf; then
    # 如果存在，则使用sed命令修改其值为1。这个参数控制内核对内存分配的策略。
    $SUDO sed -i 's/^vm.overcommit_memory.*/vm.overcommit_memory = 1/' /etc/sysctl.conf
else
    # 如果不存在，则将'vm.overcommit_memory = 1'追加到文件末尾
    echo "vm.overcommit_memory = 1" | $SUDO tee -a /etc/sysctl.conf
fi
# 使用sysctl命令立即应用该内核参数，使其生效
$SUDO sysctl -w vm.overcommit_memory=1


echo 'Applying Linux kernel optimizations...'

# 定义一个函数，用于添加或更新/etc/sysctl.conf中的内核参数
add_or_update_sysctl_conf() {
    local key="$1"
    local value="$2"
    local file="/etc/sysctl.conf"

    # 检查文件中是否已存在该配置项
    if grep -q "^$key" $file; then
        # 如果存在，则使用sed命令更新其值
        $SUDO sed -i "s|^$key.*|$key = $value|g" $file
    else
        # 如果不存在，则将配置项追加到文件末尾
        echo "$key = $value" >>$file
    fi
}


# 应用一系列网络和文件系统相关的内核优化配置，以提高服务器性能
add_or_update_sysctl_conf "net.ipv4.tcp_max_tw_buckets" "20000"
add_or_update_sysctl_conf "net.core.somaxconn" "65535"
add_or_update_sysctl_conf "net.ipv4.tcp_max_syn_backlog" "262144"
add_or_update_sysctl_conf "net.core.netdev_max_backlog" "30000"
add_or_update_sysctl_conf "net.ipv4.tcp_tw_recycle" "0"
add_or_update_sysctl_conf "fs.file-max" "6815744"
add_or_update_sysctl_conf "net.netfilter.nf_conntrack_max" "2621440"
add_or_update_sysctl_conf "net.ipv4.ip_local_port_range" "10240 65000"

echo 'Linux kernel optimizations applied successfully.'

# 定义颜色变量，用于在终端输出中高亮显示信息
GREEN="\033[0;32m"
RED="\033[0;31m"
NC="\033[0m"
BLUE="\033[0;34m"
# 定义勾和叉的符号，用于美化输出
CHECK_MARK="${GREEN}\xE2\x9C\x94${NC}"
CROSS_MARK="${RED}\xE2\x9D\x8C${NC}"


# Ubuntu官方镜像源地址
URL="http://archive.ubuntu.com/ubuntu"


# 使用curl检查官方镜像源是否可用，设置5秒连接超时
if ! curl --connect-timeout 5 -Is $URL | grep "200 OK" >/dev/null; then
    # 如果不可用，则替换/etc/apt/sources.list文件中的镜像源地址为官方主镜像
    # 这可以处理位于德国(de)、中国(cn)、美国(us)等地区的服务器无法访问本地镜像的问题
    $SUDO sed -i -e 's/de\.archive\.ubuntu\.com/archive.ubuntu.com/' /etc/apt/sources.list
    $SUDO sed -i -e 's/cn\.archive\.ubuntu\.com/archive.ubuntu.com/' /etc/apt/sources.list
    $SUDO sed -i -e 's/us\.archive\.ubuntu\.com/archive.ubuntu.com/' /etc/apt/sources.list
    
    # 替换后更新apt包列表
    $SUDO apt-get update
    
    echo "镜像源已替换为主镜像 archive.ubuntu.com"
else
    echo "镜像源正常，继续执行..."
fi

# 确保/etc/apt/sources.list.d目录存在，用于存放额外的apt源配置文件
$SUDO mkdir -p /etc/apt/sources.list.d 
# 应用所有在/etc/sysctl.conf中的配置，忽略可能出现的错误
$SUDO sysctl -p 2>/dev/null || true

# 检查docker命令是否存在，判断docker是否已安装
if ! command -v docker >/dev/null 2>&1; then
    # 如果未安装，则从官方源下载并执行docker安装脚本
    curl -fsSL https://get.docker.com -o get-docker.sh
    $SUDO sh get-docker.sh
    # 删除下载的安装脚本
    rm -f get-docker.sh
else
    # 如果已安装，则提示用户准备清理旧的Docker环境
    echo "Docker 已安装，准备清理旧环境..."

    echo -e "\033[33m即将清理所有 Docker 容器、镜像、网络、卷、构建缓存。\033[0m"
    echo -e "\033[31m注意：这将删除你当前所有的 Docker 数据！\033[0m"
    # 等待用户输入'y'确认，否则脚本将退出
    read -p "如果你需要重装后台，请输入 y 确认清理（其他输入将停止运行）: " confirm

    if [ "$confirm" = "y" ]; then
        echo "正在清理 Docker 环境..."
        
        # 停止所有正在运行的容器
        docker stop $(docker ps -aq) 2>/dev/null || true
        # 强制删除所有容器
        docker rm -f $(docker ps -aq) 2>/dev/null || true

        # 强制删除所有镜像
        docker rmi -f $(docker images -q) 2>/dev/null || true

        # 清理所有未使用的网络
        docker network prune -f

        # 删除所有未使用的Docker卷
        docker volume rm $(docker volume ls -q) 2>/dev/null || true

        # 清理所有构建缓存
        docker builder prune -f
        
        
        # 删除当前目录下的docker文件夹（如果存在）
        if [ -d "./docker" ]; then
            rm -rf ./docker
            echo "已删除当前目录下的 docker 文件夹。"
        else
            echo "未找到 docker 文件夹，跳过删除。"
        fi
        
        # 清空nginx错误日志文件（如果存在）
        if [ -f /var/log/nginx/error.log ]; then
            cat /dev/null >/var/log/nginx/error.log
        fi
        
        # 删除letsencrypt证书文件夹（如果存在），用于SSL证书
        if [ -d "/etc/letsencrypt" ]; then
            rm -rf /etc/letsencrypt
            echo "已删除证书文件夹。"
        else
            echo "未找到证书文件夹，跳过删除。"
        fi
        
        
        echo "Docker 清理完成。"
    else
        # 如果用户未确认，则输出提示并退出脚本
        echo -e "\033[31m用户未确认，已退出脚本。\033[0m"
        exit 1
    fi
fi


# 检查ufw防火墙命令是否存在
if command -v ufw >/dev/null 2>&1; then
    # 允许80 (HTTP) 和 443 (HTTPS) 端口的TCP流量
    $SUDO ufw allow 80/tcp
    $SUDO ufw allow 443/tcp

    # 从安全角度考虑，拒绝外部访问3306 (MySQL) 和 6379 (Redis) 端口
    $SUDO ufw deny from any to any port 3306 proto tcp
    $SUDO ufw deny from any to any port 6379 proto tcp

else
    echo "ufw not installed, skipping firewall disable step."
fi


# 更新apt包列表
$SUDO apt-get update
# 安装expect，它是一个用于自动化交互式应用程序的工具
$SUDO apt-get install -y expect

# 定义需要安装的软件包列表
install_command="apt-get install -y curl unzip python3 python3-pip python3-venv iptables-persistent atop jq"

# 使用expect来自动处理apt-get安装过程中可能出现的交互式提问
expect -c "
set timeout 600

spawn $install_command

expect {
    \"Which services should be restarted?\" {
        send \"\r\"
        exp_continue
    }
    eof {
         # 捕获 EOF (End of File) 来完成安装过程
    }
}
"

# 检查dpkg锁文件，等待其他软件包管理器（如apt）完成操作
while $SUDO fuser /var/lib/dpkg/lock-frontend >/dev/null 2>&1; do
    echo "Waiting for other software managers to finish..."
    sleep 5
done

# 重新配置所有未配置的软件包，解决可能的安装中断问题
$SUDO dpkg --configure -a


# 定义一个关键软件包列表，以确保它们都已成功安装
packages=("unzip" "curl" "python3" "python3-pip" "python3-venv" "iptables-persistent" "atop")

# 循环检查每个软件包是否已安装
for package in "${packages[@]}"; do
    # 使用dpkg -l检查软件包状态，'ii'表示已安装
    if dpkg -l | grep -q "^ii  $package "; then
        echo "$package is installed."
    else
        echo "$package is not installed. Attempting to reinstall..."
        # 如果未安装，则尝试重新安装
        $SUDO apt-get install -y $package
        # 再次检查是否安装成功，如果失败则退出脚本
        if ! dpkg -l | grep -q "^ii  $package "; then
            echo "Failed to install $package. Exiting."
            exit 1
        fi
    fi
done

# 获取当前UNIX时间戳
TIMESTAMP=$(date +%s)

# 使用ipinfo.io服务获取服务器的公网IP地址
SERVER_IP=$(curl -s http://ipinfo.io/ip)
# 生成一个10位的随机字母数字字符串，用作后台安全入口
RANDOM_ENTRY=$(cat /dev/urandom | tr -dc 'a-z0-9' | head -c 10)

# 定义安装包的URL
ZIP_URL="https://install.lighthousea.top/v4/install.zip"
# 在URL后附加时间戳作为查询参数，以防止CDN缓存导致无法获取最新版本
ZIP_URL_WITH_TIMESTAMP="${ZIP_URL}?t=${TIMESTAMP}"


# 下载安装包并命名为docker.zip
$SUDO curl -L "$ZIP_URL_WITH_TIMESTAMP" -o "docker.zip"
# 解压docker.zip到docker目录，-o表示覆盖已存在的文件，-q表示静默模式
$SUDO unzip -o -q "docker.zip" -d "docker"
# 删除已解压的zip文件
$SUDO rm -f "docker.zip"

# 进入解压后的docker目录
cd "docker" || {
    echo "无法进入项目目录，请检查解压情况。"
    exit 1
}

# 下载GeoIPLite2国家数据库，用于根据IP地址识别国家
curl -L -o "./geo/Country.mmdb" \
  https://install.lighthousea.top/v2/Country.mmdb


# 检查jq（一个命令行JSON处理工具）是否安装
jq_installed=$(command -v jq || echo "")
if [ -z "$jq_installed" ]; then
    echo "Installing jq..."
    $SUDO apt-get install -y jq
fi

# Cloudflare API地址，用于获取其IP地址范围
CF_API=https://api.cloudflare.com/client/v4/ips
# Cloudflare真实IP配置文件路径
CF_FILE=./geo/cf_realip.conf

# 生成Cloudflare真实IP配置文件，用于Nginx获取客户端真实IP
echo "# Generated by setup script" > "$CF_FILE"
# 获取并格式化IPv4地址段，追加到配置文件
curl -s $CF_API | jq -r '.result.ipv4_cidrs[]' | sed 's/^/set_real_ip_from /;s/$/;/' >> "$CF_FILE"
# 获取并格式化IPv6地址段，追加到配置文件
curl -s $CF_API | jq -r '.result.ipv6_cidrs[]' | sed 's/^/set_real_ip_from /;s/$/;/' >> "$CF_FILE"




# 如果存在名为local.nginx.conf的目录（异常情况），则删除它
if [ -d "./local.nginx.conf" ]; then
    echo "./local.nginx.conf 是一个目录，正在删除它..."
    rm -rf ./local.nginx.conf
fi

# 使用cat和here document语法创建Nginx主配置文件nginx.conf（但在脚本里命名为local.nginx.conf）
cat <<'EOF' >./local.nginx.conf
# 加载Nginx模块：NDK、Lua和GeoIP2
load_module /usr/lib/nginx/modules/ndk_http_module.so;
load_module /usr/lib/nginx/modules/ngx_http_lua_module.so;
load_module /usr/lib/nginx/modules/ngx_http_geoip2_module.so;

# Nginx运行用户和工作进程数（auto表示自动检测CPU核心数）
user  nginx;
worker_processes  auto;

# 错误日志和PID文件路径
error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

# events块配置，定义了Nginx的工作模式和连接数上限
events {
    worker_connections  51200;
}

# http块配置，是Nginx配置的主要部分
http {
    # Lua模块搜索路径
    lua_package_path "/etc/nginx/conf.d/ngconf/lua/?.lua;/usr/share/lua/5.1/?.lua;/usr/share/lua/5.1/?/init.lua;;";
    lua_package_cpath  "/usr/lib/lua/5.1/?.so;;";

    # Docker内建DNS解析器
    resolver 127.0.0.11 valid=30s;

    # 包含Cloudflare真实IP配置，用于正确获取经过CDN的客户端IP
    include /etc/nginx/geo/cf_realip.conf;

    real_ip_header    CF-Connecting-IP; 
    real_ip_recursive on;

    # GeoIP2模块配置，用于IP地址地理位置查询
    geoip2 /etc/nginx/geo/Country.mmdb {
        auto_reload 5m;                        
        $tag  source=$remote_addr  country iso_code;
    }
    
    # 服务器名称哈希表大小和Lua共享内存字典
    server_names_hash_bucket_size 256;
    lua_shared_dict gate_cache 20m;
    lua_shared_dict site_config_cache 10m;

    # 包含MIME类型定义文件
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # 定义主日志格式
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    # 开启高效文件传输模式
    sendfile        on;
    #tcp_nopush     on;

    # 长连接超时时间
    keepalive_timeout  65;

    #gzip  on;

    # 包含conf.d目录下的所有.conf文件
    include /etc/nginx/conf.d/*.conf;
}


EOF


# 如果存在名为nginx.conf的目录（异常情况），则删除它
if [ -d "./nginx.conf" ]; then
    echo "./nginx.conf 是一个目录，正在删除它..."
    rm -rf ./nginx.conf
fi

# 创建Nginx的server块配置文件nginx.conf
cat <<EOF >./nginx.conf
# 客户端请求体最大大小
client_max_body_size 88m;
# 代理超时设置
proxy_send_timeout 300;
proxy_read_timeout 300;
proxy_connect_timeout 300;

# 包含更具体的Nginx规则
include /etc/nginx/conf.d/ngconf/*.conf;

# 定义后端Hyperf应用的上游服务
upstream hyperf {
    server hyperf:9501;
}

# 定义后端Hyperf WebSocket应用的上游服务
upstream hyperf_websocket {
    ip_hash; # 使用ip_hash进行负载均衡，确保同一客户端的请求被转发到同一台服务器
    server hyperf:9502;
}

# 监听80端口的HTTP服务
server {
    listen 80;
    server_name $SERVER_IP; # 使用服务器公网IP作为server_name
    index index.html;
    root /var/www/html;

    access_log off; # 关闭访问日志

    # Gzip压缩配置，用于减少传输数据大小
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 9;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_types text/plain text/css text/javascript application/javascript application/x-javascript application/json application/xml application/xml+rss image/svg+xml application/x-httpd-php font/woff2;
    gzip_min_length 1024;
    gzip_disable "MSIE [1-6]\.";

    # 屏蔽常见的爬虫和扫描器的User-Agent
    if (\$http_user_agent ~* "Scrapy|Baiduspider|Curl|HttpClient|Bytespider|FeedDemon|JikeSpider|Indy Library|Alexa Toolbar|AskTbFXTV|AhrefsBot|CrawlDaddy|CoolpadWebkit|Java|Feedly|UniversalFeedParser
    |ApacheBench|Microsoft URL Control|Swiftbot|ZmEu|oBot|jaunty|Python-urllib|lightDeckReports Bot|YYSpider|DigExt|YisouSpider|HttpClient|MJ12bot|heritrix|EasouSp
    ider|Ezooms|^$"){
        return 404;
    }

    # 自定义404页面
    error_page 404 /404.html;
	location = /404.html {
	    root /var/www/html/shopify;
	}

    # 拒绝访问.php文件
    location ~ \.php$ {
        return 404;
    }

    # 用于Let's Encrypt SSL证书验证的路径
    location ^~ /.well-known/acme-challenge/ {
        alias /var/www/html/vue/dist/.well-known/acme-challenge/;
        try_files \$uri =404;
    }

    # 后台管理界面的访问路径，使用之前生成的随机字符串作为安全入口
    location /$RANDOM_ENTRY {
        alias /var/www/html/vue/dist/;  
        try_files \$uri \$uri/ /index.html;  
    }

    # 上传文件的访问路径
    location /uploads/ {
        alias /var/www/uploads/;
        try_files \$uri \$uri/ =404;
    }
 
    # API请求代理到Hyperf后端服务
    location ^~ /api {
        proxy_set_header Host \$http_host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_cookie_path / "/; HttpOnly; SameSite=strict";
        proxy_pass http://hyperf;
    }

    # WebSocket请求代理到Hyperf WebSocket后端服务
    location /ws {
        proxy_http_version 1.1;
        proxy_set_header Upgrade websocket;
        proxy_set_header Connection "Upgrade";
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header Host \$http_host;
        proxy_read_timeout 300s ;
        proxy_pass http://hyperf_websocket;
    }

    # 根路径返回404，增强安全性
    location = / {
        return 404;
    }

}
EOF


# 检查.env文件是否存在
if [ -f ".env" ]; then
    # 如果存在，则加载.env文件中的环境变量到当前shell会话
    set -a      
    source .env 
    set +a     
else
    # 如果不存在，则退出脚本，因为后续步骤需要这些变量
    exit 1
fi


# 打印数据库连接信息用于调试
echo "DB_HOST=$DB_HOST"
echo "DB_USERNAME=$DB_USERNAME"
echo "DB_PASSWORD=$DB_PASSWORD"
echo "DB_DATABASE=$DB_DATABASE"


# 使用docker compose构建镜像，如果失败则退出
docker compose build || {
    echo "Docker 镜像构建失败，请检查 Dockerfile。"
    exit 1
}
echo "The Docker image is built.。"





# 使用docker compose在后台启动所有服务，如果失败则退出
docker compose up -d || {
    echo "Docker 容器启动失败，请重置系统，然后再次运行此命令。"
    exit 1
}
echo "The Docker container is started."


# 检查数据库初始化SQL文件是否存在
if [ -f "scripts/setup.sql" ]; then
    # 获取数据库服务的名称（通常是mysql或mariadb）
    DB_SERVICE=$(docker compose config --services | grep -Ei 'mysql|mariadb' | head -n1)
    echo "Waiting for database service ($DB_SERVICE) to start..."
    DB_READY=false
    # 循环120次（约4分钟），等待数据库服务就绪
    for i in {1..120}; do
        # 尝试连接数据库，成功则设置DB_READY为true并跳出循环
        docker compose exec -T "$DB_SERVICE" mysqladmin --protocol=TCP ping -h localhost ${DB_PASSWORD:+-p"$DB_PASSWORD"} &>/dev/null && {
            DB_READY=true
            break
        }
        echo "  Trying to connect to the database...($i)"
        sleep 2
    done
    
    # 如果数据库在超时后仍未就绪，则退出脚本
    if [ "$DB_READY" = false ]; then
        echo -e "\e[31m数据库服务启动失败，已终止脚本。\e[0m"
        exit 1
    else
        # 数据库就绪后，导入初始化数据
        if [ -n "$DB_USERNAME" ] && [ -n "$DB_PASSWORD" ]; then
            # 使用.env中定义的用户名和密码
            docker compose exec -T "$DB_SERVICE" mysql --protocol=TCP -u"$DB_USERNAME" -p"$DB_PASSWORD" "$DB_DATABASE" <scripts/setup.sql
        else
            # 使用默认的用户名和密码
            docker compose exec -T "$DB_SERVICE" mysql --protocol=TCP -uroot -p"lighthouse" "lighthouse" <scripts/setup.sql
        fi
        echo "✅ The database import is complete."
    fi
else
    echo -e "\e[31m未找到数据库文件，已终止脚本。\e[0m"
    exit 1
fi


# 生成随机的管理员用户名和密码
ADMIN_USERNAME=$(cat /dev/urandom | tr -dc 'A-Za-z0-9' | fold -w 12 | head -n 1)
ADMIN_PASSWORD=$(cat /dev/urandom | tr -dc 'A-Za-z0-9' | fold -w 16 | head -n 1)
# 对密码进行特定规则的拼接和两次MD5加密
ADMIN_PASSWORD1="$ADMIN_PASSWORD&*09"
ADMIN_PASSWORD_MD5=$(echo -n $ADMIN_PASSWORD1 | md5sum | cut -d ' ' -f 1)
ADMIN_PASSWORD_MD5=$(echo -n $ADMIN_PASSWORD_MD5 | md5sum | cut -d ' ' -f 1)


# 更新数据库中的管理员信息
if [ -n "$DB_PASSWORD" ] && [ -n "$DB_USERNAME" ]; then
    # 使用.env中定义的用户名和密码连接数据库
    docker compose exec "$DB_SERVICE" mysql --protocol=TCP -u"$DB_USERNAME" -p"$DB_PASSWORD" "$DB_DATABASE" -e \
        "UPDATE admin_users SET username='$ADMIN_USERNAME', showword='$ADMIN_PASSWORD', password='$ADMIN_PASSWORD_MD5' WHERE id=1"
else
    # 使用默认的用户名和密码连接数据库
    docker compose exec "$DB_SERVICE" mysql --protocol=TCP -uroot -p"lighthouse" "lighthouse" -e \
        "UPDATE admin_users SET username='$ADMIN_USERNAME', showword='$ADMIN_PASSWORD', password='$ADMIN_PASSWORD_MD5' WHERE id=1"
fi

# 将后台地址、管理员用户名和密码保存到项目根目录的lighthouse.txt文件中
echo "Website URL: http://$SERVER_IP/$RANDOM_ENTRY" >../lighthouse.txt
echo "Admin username: $ADMIN_USERNAME" >>../lighthouse.txt
echo "Admin password: $ADMIN_PASSWORD" >>../lighthouse.txt
chmod 644 ../lighthouse.txt # 设置文件权限为644


# 检查iptables命令是否存在
if ! command -v iptables >/dev/null 2>&1; then
    # 如果不存在，则安装iptables
    $SUDO apt-get update
    echo "iptables not found, installing..."
    # 使用expect自动处理安装过程中的交互
    expect -c "
    set timeout 600
    spawn $SUDO apt-get install -y iptables

    expect {
        \"Which services should be restarted?\" {
            send \"1-18\r\"
            exp_continue
        }
        eof
    }
    "
fi


# 创建Python虚拟环境
python3 -m venv /opt/venv
# 激活虚拟环境
source /opt/venv/bin/activate
# 在虚拟环境中安装必要的Python库
pip install requests urllib3 redis

# 创建一个Python脚本，用于从URL拉取蜘蛛IP列表并存入Redis
cat <<EOF >/usr/local/bin/spider_ip.py
# -*- coding: utf-8 -*-
import requests
import redis
import json

url = "https://install.lighthousea.top/v2/spider_ipss.json"
out_path = "/usr/local/bin/spider_ip.json"

redis_host = "127.0.0.1"
redis_port = 6379
r = redis.StrictRedis(host=redis_host, port=redis_port, decode_responses=True)

def main():
    resp = requests.get(url, timeout=10)
    resp.raise_for_status()
    with open(out_path, "w", encoding="utf-8") as fp:
        fp.write(resp.text)
    data = resp.json()
    for key, ips in data.items():
        for ip in ips:
            r.sadd("spider:cidr", ip)

if __name__ == "__main__":
    main()
EOF

# 赋予该Python脚本执行权限
$SUDO chmod +x /usr/local/bin/spider_ip.py

# 执行该Python脚本
if python3 /usr/local/bin/spider_ip.py; then
    echo "[OK] Pull and write successfully"
else
    echo "Failed to fetch, exit"
    exit 1
fi

# 创建一个shell脚本，用于定时更新蜘蛛IP列表
cat <<'EOF' | $SUDO tee /usr/local/bin/update_spider_ip.sh >/dev/null
#!/bin/bash
set -e

source /opt/venv/bin/activate
LOGFILE="/var/log/update_spider_ip.log"
echo "[$(date '+%Y-%m-%d %H:%M:%S')] update_spider_ip.sh started" >> "$LOGFILE"

python3 /usr/local/bin/spider_ip.py >> "$LOGFILE" 2>&1
EOF


# 赋予该更新脚本执行权限
$SUDO chmod +x /usr/local/bin/update_spider_ip.sh

# 创建cron定时任务，每30分钟执行一次蜘蛛IP更新脚本
cat <<EOF >/etc/cron.d/update_spider_ip
SHELL=/bin/bash
PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin
*/30 * * * * root /usr/local/bin/update_spider_ip.sh >> /var/log/update_spider_ip.log 2>&1
EOF
chmod 644 /etc/cron.d/update_spider_ip


# 获取当前项目目录的绝对路径
PROJECT_DIR="$(pwd)"

# 创建一个shell脚本，用于定时更新Cloudflare的IP地址列表
cat <<EOF | $SUDO tee /usr/local/bin/update_cf_ip.sh >/dev/null
#!/usr/bin/env bash
set -e

GEO_DIR="$PROJECT_DIR/geo"

CF_API=https://api.cloudflare.com/client/v4/ips

echo "# Auto-generated by update_cf_ip.sh" > "\$GEO_DIR/cf_realip.conf.tmp"
curl -s \$CF_API | jq -r '.result.ipv4_cidrs[]' | sed 's/^/set_real_ip_from /;s/\$/;/' >> "\$GEO_DIR/cf_realip.conf.tmp"
curl -s \$CF_API | jq -r '.result.ipv6_cidrs[]' | sed 's/^/set_real_ip_from /;s/\$/;/' >> "\$GEO_DIR/cf_realip.conf.tmp"

if [ ! -s "\$GEO_DIR/cf_realip.conf.tmp" ]; then
    echo "❌ Cloudflare IP 下载失败，更新中止。"
    exit 1
fi

mv "\$GEO_DIR/cf_realip.conf.tmp" "\$GEO_DIR/cf_realip.conf"
docker exec hyperf-nginx nginx -s reload # 重新加载Nginx配置
EOF

# 赋予该更新脚本执行权限
$SUDO chmod +x /usr/local/bin/update_cf_ip.sh

# 创建一个shell脚本，用于定时更新GeoIP数据库
cat <<EOF | $SUDO tee /usr/local/bin/update_mmdb.sh >/dev/null
#!/usr/bin/env bash
set -e

GEO_DIR="$PROJECT_DIR/geo"


URL="https://install.lighthousea.top/v2/Country.mmdb"
DEST="\$GEO_DIR/Country.mmdb"

# -z 选项可以实现增量下载，如果本地文件已是最新则不会下载
curl -z "\$DEST" -fLo "\${DEST}.tmp" "\$URL" && mv "\${DEST}.tmp" "\$DEST"
docker exec hyperf-nginx nginx -s reload # 重新加载Nginx配置
EOF
$SUDO chmod +x /usr/local/bin/update_mmdb.sh



# 创建cron定时任务，每天凌晨4点执行CF IP更新
cat <<EOF | $SUDO tee /etc/cron.d/update_cf_ip
0 4 * * * root /usr/local/bin/update_cf_ip.sh >> /var/log/update_cfip.log 2>&1
EOF

# 创建cron定时任务，每周四凌晨0点10分执行GeoIP数据库更新
cat <<EOF | $SUDO tee /etc/cron.d/update_mmdb
 10 0 * * 4 root /usr/local/bin/update_mmdb.sh >> /var/log/update_mmdb.log 2>&1
EOF


# 设置cron配置文件的权限
chmod 644 /etc/cron.d/update_mmdb /etc/cron.d/update_cf_ip


# 重启cron服务以应用新的定时任务
$SUDO systemctl restart cron || $SUDO systemctl restart crond


# 定义一个包含恶意扫描器特征字符串的数组
strings=("acunetix" "photon" "zgrab2" "benchmark" "inspect" "shodan" "censys" "nessus" "nmap" "shadow" "measurement" "census" "scanner" "gptbot" "bingbot" "uptime" "pingdom" "sucuri" "qualys" "photon" "masscan" "zmap" "zgrab" "expanse")
# 遍历数组，为每个特征字符串添加iptables规则，丢弃匹配的数据包
for string in "${strings[@]}"; do
    $SUDO iptables -A INPUT -m string --string "$string" --algo kmp -j DROP
done

# 丢弃ICMP echo请求（ping请求），防止被扫描
$SUDO iptables -A INPUT -p icmp --icmp-type echo-request -j DROP

# 丢弃无效的TCP标志组合的数据包，防御部分网络扫描
$SUDO iptables -A INPUT -p tcp --tcp-flags ALL NONE -j DROP
$SUDO iptables -A INPUT -p tcp --tcp-flags ALL FIN -j DROP
$SUDO iptables -A INPUT -p tcp --tcp-flags ALL URG,PSH,FIN -j DROP

# 将当前的iptables规则保存到/etc/iptables/rules.v4，以便系统重启后能自动加载
$SUDO iptables-save >/etc/iptables/rules.v4

# 使用netfilter-persistent工具保存和重载iptables规则
$SUDO netfilter-persistent save
$SUDO netfilter-persistent reload


# 启动并设置atop服务开机自启。atop是用于监控系统资源的工具
$SUDO systemctl start atop
$SUDO systemctl enable atop



sleep 1

# 在hyperf-app容器中执行Python脚本，将数据导入Redis
docker exec -it hyperf-app python3 /work/scripts/import_to_redis.py
# 在hyperf-redis容器中执行BGSAVE命令，将Redis数据异步保存到磁盘
docker exec -it hyperf-redis redis-cli BGSAVE


# 输出安装成功的提示信息和访问凭据
echo -e "${CHECK_MARK}${BLUE} The lighthouse is ready, let's begin the adventure!${NC}"
echo -e "${CHECK_MARK}${GREEN} 后台地址: http://$SERVER_IP/$RANDOM_ENTRY${NC}"
echo -e "${CHECK_MARK}${GREEN} 用户名: $ADMIN_USERNAME${NC}"
echo -e "${CHECK_MARK}${GREEN} 密码: $ADMIN_PASSWORD${NC}"

# 退出Python虚拟环境
deactivate


# 清理安装过程中产生的临时文件和目录
find . -mindepth 1 -maxdepth 1 \
    ! -name 'docker-compose.yml' \
    ! -name 'Dockerfile' \
    ! -name 'local.nginx.conf' \
    ! -name 'nginx.conf' \
    ! -name 'geo' \
    ! -name 'gateway' \
    ! -name 'ngconf' \
    ! -name 'uploads' \
    ! -name 'html' \
    ! -name '.db_inited' \
    -exec rm -rf {} + 