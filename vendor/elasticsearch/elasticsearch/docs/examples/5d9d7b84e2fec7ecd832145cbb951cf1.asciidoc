// aggregations/bucket/terms-aggregation.asciidoc:683

[source, php]
----
$params = [
    'body' => [
        'size' => 0,
        'aggs' => [
            'expired_sessions' => [
                'terms' => [
                    'field' => 'account_id',
                    'include' => [
                        'partition' => 0,
                        'num_partitions' => 20,
                    ],
                    'size' => 10000,
                    'order' => [
                        'last_access' => 'asc',
                    ],
                ],
                'aggs' => [
                    'last_access' => [
                        'max' => [
                            'field' => 'access_date',
                        ],
                    ],
                ],
            ],
        ],
    ],
];
$response = $client->search($params);
----
