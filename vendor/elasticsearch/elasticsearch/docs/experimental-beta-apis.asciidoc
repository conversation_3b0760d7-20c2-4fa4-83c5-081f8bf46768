[[experimental_and_beta_apis]]
=== Experimental and beta APIs

The PHP client offers also `experimental` and `beta` APIs for {es}.

The {es} APIs are marked using the following convention:

- **Stable** APIs should be safe to use extensively in production. Any breaking 
  changes to these APIs should only occur in major versions and will be 
  documented in the breaking changes documentation for that release.
- **Beta** APIs are on track to become stable and permanent features. Use them 
  with caution because it is possible that breaking changes are made to these 
  APIs in a minor version.
- **Experimental** APIs are just that - an experiment. An experimental API might
  have breaking changes in any future version, or it might even be removed
  entirely.

All the `experimental` and `beta` APIs are marked with a `@note` tag in the
phpdoc section of the code.

[discrete]
=== Experimental

The experimental APIs included in the current version of `elasticsearch-php` 
are:

- https://www.elastic.co/guide/en/elasticsearch/reference/7.4/search-rank-eval.html[Ranking Evaluation]

[source,php]
----
$client = ClientBuilder::create()->build();
$params = [
    // ...
];
$result = $client->rankEval($params);
----

- https://www.elastic.co/guide/en/elasticsearch/painless/7.4/painless-execute-api.html[Painless Execute]

[source,php]
----
$client = ClientBuilder::create()->build();
$params = [
    // ...
];
$result = $client->scriptsPainlessExecute($params);
----

- Get Script Context

Returns all script contexts.

[source,php]
----
$client = ClientBuilder::create()->build();

$result = $client->getScriptContext();
----

- Get Script Languages

Returns available script types, languages and contexts.

[source,php]
----
$client = ClientBuilder::create()->build();

$result = $client->getScriptLanguages();
----

[discrete]
=== Beta

There are no beta APIs in the current version of `elasticsearch-php`.