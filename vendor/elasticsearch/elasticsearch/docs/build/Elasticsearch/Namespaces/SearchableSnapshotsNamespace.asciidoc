

[[Elasticsearch_Namespaces_SearchableSnapshotsNamespace]]
=== Elasticsearch\Namespaces\SearchableSnapshotsNamespace



Class SearchableSnapshotsNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_SearchableSnapshotsNamespaceclearCache_clearCache,`clearCache()`>>
* <<Elasticsearch_Namespaces_SearchableSnapshotsNamespacemount_mount,`mount()`>>
* <<Elasticsearch_Namespaces_SearchableSnapshotsNamespacerepositoryStats_repositoryStats,`repositoryStats()`>>
* <<Elasticsearch_Namespaces_SearchableSnapshotsNamespacestats_stats,`stats()`>>



[[Elasticsearch_Namespaces_SearchableSnapshotsNamespaceclearCache_clearCache]]
.`clearCache(array $params = [])`
*NOTE:* This API is EXPERIMENTAL and may be changed or removed completely in a future release
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names
$params['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']   = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,none,all) (Default = open)
*/
----
****



[[Elasticsearch_Namespaces_SearchableSnapshotsNamespacemount_mount]]
.`mount(array $params = [])`
*NOTE:* This API is EXPERIMENTAL and may be changed or removed completely in a future release
****
[source,php]
----
/*
$params['repository']          = (string) The name of the repository containing the snapshot of the index to mount
$params['snapshot']            = (string) The name of the snapshot of the index to mount
$params['master_timeout']      = (time) Explicit operation timeout for connection to master node
$params['wait_for_completion'] = (boolean) Should this request wait until the operation has completed before returning (Default = false)
$params['storage']             = (string) Selects the kind of local storage used to accelerate searches. Experimental, and defaults to `full_copy` (Default = )
$params['body']                = (array) The restore configuration for mounting the snapshot as searchable (Required)
*/
----
****



[[Elasticsearch_Namespaces_SearchableSnapshotsNamespacerepositoryStats_repositoryStats]]
.`repositoryStats(array $params = [])`
*NOTE:* This API is EXPERIMENTAL and may be changed or removed completely in a future release
****
[source,php]
----
/*
$params['repository'] = (string) The repository for which to get the stats for
*/
----
****



[[Elasticsearch_Namespaces_SearchableSnapshotsNamespacestats_stats]]
.`stats(array $params = [])`
*NOTE:* This API is EXPERIMENTAL and may be changed or removed completely in a future release
****
[source,php]
----
/*
$params['index'] = (list) A comma-separated list of index names
$params['level'] = (enum) Return stats aggregated at cluster, index or shard level (Options = cluster,indices,shards) (Default = indices)
*/
----
****


