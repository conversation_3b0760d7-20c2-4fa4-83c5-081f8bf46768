

[[Elasticsearch_Namespaces_AutoscalingNamespace]]
=== Elasticsearch\Namespaces\AutoscalingNamespace



Class AutoscalingNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_AutoscalingNamespacedeleteAutoscalingPolicy_deleteAutoscalingPolicy,`deleteAutoscalingPolicy()`>>
* <<Elasticsearch_Namespaces_AutoscalingNamespacegetAutoscalingCapacity_getAutoscalingCapacity,`getAutoscalingCapacity()`>>
* <<Elasticsearch_Namespaces_AutoscalingNamespacegetAutoscalingPolicy_getAutoscalingPolicy,`getAutoscalingPolicy()`>>
* <<Elasticsearch_Namespaces_AutoscalingNamespaceputAutoscalingPolicy_putAutoscalingPolicy,`putAutoscalingPolicy()`>>



[[Elasticsearch_Namespaces_AutoscalingNamespacedeleteAutoscalingPolicy_deleteAutoscalingPolicy]]
.`deleteAutoscalingPolicy(array $params = [])`
****
[source,php]
----
/*
$params['name'] = (string) the name of the autoscaling policy
*/
----
****



[[Elasticsearch_Namespaces_AutoscalingNamespacegetAutoscalingCapacity_getAutoscalingCapacity]]
.`getAutoscalingCapacity(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_Namespaces_AutoscalingNamespacegetAutoscalingPolicy_getAutoscalingPolicy]]
.`getAutoscalingPolicy(array $params = [])`
****
[source,php]
----
/*
$params['name'] = (string) the name of the autoscaling policy
*/
----
****



[[Elasticsearch_Namespaces_AutoscalingNamespaceputAutoscalingPolicy_putAutoscalingPolicy]]
.`putAutoscalingPolicy(array $params = [])`
****
[source,php]
----
/*
$params['name'] = (string) the name of the autoscaling policy
$params['body'] = (array) the specification of the autoscaling policy (Required)
*/
----
****


