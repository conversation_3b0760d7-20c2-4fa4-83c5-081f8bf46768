

[[Elasticsearch_Namespaces_RollupNamespace]]
=== Elasticsearch\Namespaces\RollupNamespace



Class RollupNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_RollupNamespacedeleteJob_deleteJob,`deleteJob()`>>
* <<Elasticsearch_Namespaces_RollupNamespacegetJobs_getJobs,`getJobs()`>>
* <<Elasticsearch_Namespaces_RollupNamespacegetRollupCaps_getRollupCaps,`getRollupCaps()`>>
* <<Elasticsearch_Namespaces_RollupNamespacegetRollupIndexCaps_getRollupIndexCaps,`getRollupIndexCaps()`>>
* <<Elasticsearch_Namespaces_RollupNamespaceputJob_putJob,`putJob()`>>
* <<Elasticsearch_Namespaces_RollupNamespacerollup_rollup,`rollup()`>>
* <<Elasticsearch_Namespaces_RollupNamespacerollupSearch_rollupSearch,`rollupSearch()`>>
* <<Elasticsearch_Namespaces_RollupNamespacestartJob_startJob,`startJob()`>>
* <<Elasticsearch_Namespaces_RollupNamespacestopJob_stopJob,`stopJob()`>>



[[Elasticsearch_Namespaces_RollupNamespacedeleteJob_deleteJob]]
.`deleteJob(array $params = [])`
*NOTE:* This API is EXPERIMENTAL and may be changed or removed completely in a future release
****
[source,php]
----
/*
$params['id'] = (string) The ID of the job to delete
*/
----
****



[[Elasticsearch_Namespaces_RollupNamespacegetJobs_getJobs]]
.`getJobs(array $params = [])`
*NOTE:* This API is EXPERIMENTAL and may be changed or removed completely in a future release
****
[source,php]
----
/*
$params['id'] = (string) The ID of the job(s) to fetch. Accepts glob patterns, or left blank for all jobs
*/
----
****



[[Elasticsearch_Namespaces_RollupNamespacegetRollupCaps_getRollupCaps]]
.`getRollupCaps(array $params = [])`
*NOTE:* This API is EXPERIMENTAL and may be changed or removed completely in a future release
****
[source,php]
----
/*
$params['id'] = (string) The ID of the index to check rollup capabilities on, or left blank for all jobs
*/
----
****



[[Elasticsearch_Namespaces_RollupNamespacegetRollupIndexCaps_getRollupIndexCaps]]
.`getRollupIndexCaps(array $params = [])`
*NOTE:* This API is EXPERIMENTAL and may be changed or removed completely in a future release
****
[source,php]
----
/*
$params['index'] = (string) The rollup index or index pattern to obtain rollup capabilities from.
*/
----
****



[[Elasticsearch_Namespaces_RollupNamespaceputJob_putJob]]
.`putJob(array $params = [])`
*NOTE:* This API is EXPERIMENTAL and may be changed or removed completely in a future release
****
[source,php]
----
/*
$params['id']   = (string) The ID of the job to create
$params['body'] = (array) The job configuration (Required)
*/
----
****



[[Elasticsearch_Namespaces_RollupNamespacerollup_rollup]]
.`rollup(array $params = [])`
****
[source,php]
----
/*
$params['index']        = (string) The index to roll up
$params['rollup_index'] = (string) The name of the rollup index to create
$params['body']         = (array) The rollup configuration (Required)
*/
----
****



[[Elasticsearch_Namespaces_RollupNamespacerollupSearch_rollupSearch]]
.`rollupSearch(array $params = [])`
*NOTE:* This API is EXPERIMENTAL and may be changed or removed completely in a future release
****
[source,php]
----
/*
$params['index']                  = (list) The indices or index-pattern(s) (containing rollup or regular data) that should be searched (Required)
$params['type']                   = DEPRECATED (string) The doc type inside the index
$params['typed_keys']             = (boolean) Specify whether aggregation and suggester names should be prefixed by their respective types in the response
$params['rest_total_hits_as_int'] = (boolean) Indicates whether hits.total should be rendered as an integer or an object in the rest search response (Default = false)
$params['body']                   = (array) The search request body (Required)
*/
----
****



[[Elasticsearch_Namespaces_RollupNamespacestartJob_startJob]]
.`startJob(array $params = [])`
*NOTE:* This API is EXPERIMENTAL and may be changed or removed completely in a future release
****
[source,php]
----
/*
$params['id'] = (string) The ID of the job to start
*/
----
****



[[Elasticsearch_Namespaces_RollupNamespacestopJob_stopJob]]
.`stopJob(array $params = [])`
*NOTE:* This API is EXPERIMENTAL and may be changed or removed completely in a future release
****
[source,php]
----
/*
$params['id']                  = (string) The ID of the job to stop
$params['wait_for_completion'] = (boolean) True if the API should block until the job has fully stopped, false if should be executed async. Defaults to false.
*/
----
****


