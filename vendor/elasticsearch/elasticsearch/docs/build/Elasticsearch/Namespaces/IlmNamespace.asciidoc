

[[Elasticsearch_Namespaces_IlmNamespace]]
=== Elasticsearch\Namespaces\IlmNamespace



Class IlmNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_IlmNamespacedeleteLifecycle_deleteLifecycle,`deleteLifecycle()`>>
* <<Elasticsearch_Namespaces_IlmNamespaceexplainLifecycle_explainLifecycle,`explainLifecycle()`>>
* <<Elasticsearch_Namespaces_IlmNamespacegetLifecycle_getLifecycle,`getLifecycle()`>>
* <<Elasticsearch_Namespaces_IlmNamespacegetStatus_getStatus,`getStatus()`>>
* <<Elasticsearch_Namespaces_IlmNamespacemoveToStep_moveToStep,`moveToStep()`>>
* <<Elasticsearch_Namespaces_IlmNamespaceputLifecycle_putLifecycle,`putLifecycle()`>>
* <<Elasticsearch_Namespaces_IlmNamespaceremovePolicy_removePolicy,`removePolicy()`>>
* <<Elasticsearch_Namespaces_IlmNamespaceretry_retry,`retry()`>>
* <<Elasticsearch_Namespaces_IlmNamespacestart_start,`start()`>>
* <<Elasticsearch_Namespaces_IlmNamespacestop_stop,`stop()`>>



[[Elasticsearch_Namespaces_IlmNamespacedeleteLifecycle_deleteLifecycle]]
.`deleteLifecycle(array $params = [])`
****
[source,php]
----
/*
$params['policy'] = (string) The name of the index lifecycle policy
*/
----
****



[[Elasticsearch_Namespaces_IlmNamespaceexplainLifecycle_explainLifecycle]]
.`explainLifecycle(array $params = [])`
****
[source,php]
----
/*
$params['index']        = (string) The name of the index to explain
$params['only_managed'] = (boolean) filters the indices included in the response to ones managed by ILM
$params['only_errors']  = (boolean) filters the indices included in the response to ones in an ILM error state, implies only_managed
*/
----
****



[[Elasticsearch_Namespaces_IlmNamespacegetLifecycle_getLifecycle]]
.`getLifecycle(array $params = [])`
****
[source,php]
----
/*
$params['policy'] = (string) The name of the index lifecycle policy
*/
----
****



[[Elasticsearch_Namespaces_IlmNamespacegetStatus_getStatus]]
.`getStatus(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_Namespaces_IlmNamespacemoveToStep_moveToStep]]
.`moveToStep(array $params = [])`
****
[source,php]
----
/*
$params['index'] = (string) The name of the index whose lifecycle step is to change
$params['body']  = (array) The new lifecycle step to move to
*/
----
****



[[Elasticsearch_Namespaces_IlmNamespaceputLifecycle_putLifecycle]]
.`putLifecycle(array $params = [])`
****
[source,php]
----
/*
$params['policy'] = (string) The name of the index lifecycle policy
$params['body']   = (array) The lifecycle policy definition to register
*/
----
****



[[Elasticsearch_Namespaces_IlmNamespaceremovePolicy_removePolicy]]
.`removePolicy(array $params = [])`
****
[source,php]
----
/*
$params['index'] = (string) The name of the index to remove policy on
*/
----
****



[[Elasticsearch_Namespaces_IlmNamespaceretry_retry]]
.`retry(array $params = [])`
****
[source,php]
----
/*
$params['index'] = (string) The name of the indices (comma-separated) whose failed lifecycle step is to be retry
*/
----
****



[[Elasticsearch_Namespaces_IlmNamespacestart_start]]
.`start(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_Namespaces_IlmNamespacestop_stop]]
.`stop(array $params = [])`
****
[source,php]
----
/*
*/
----
****


