

[[Elasticsearch_Namespaces_EqlNamespace]]
=== Elasticsearch\Namespaces\EqlNamespace



Class EqlNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_EqlNamespacedelete_delete,`delete()`>>
* <<Elasticsearch_Namespaces_EqlNamespaceget_get,`get()`>>
* <<Elasticsearch_Namespaces_EqlNamespacegetStatus_getStatus,`getStatus()`>>
* <<Elasticsearch_Namespaces_EqlNamespacesearch_search,`search()`>>



[[Elasticsearch_Namespaces_EqlNamespacedelete_delete]]
.`delete(array $params = [])`
****
[source,php]
----
/*
$params['id'] = (string) The async search ID
*/
----
****



[[Elasticsearch_Namespaces_EqlNamespaceget_get]]
.`get(array $params = [])`
****
[source,php]
----
/*
$params['id']                          = (string) The async search ID
$params['wait_for_completion_timeout'] = (time) Specify the time that the request should block waiting for the final response
$params['keep_alive']                  = (time) Update the time interval in which the results (partial or final) for this search will be available (Default = 5d)
*/
----
****



[[Elasticsearch_Namespaces_EqlNamespacegetStatus_getStatus]]
.`getStatus(array $params = [])`
****
[source,php]
----
/*
$params['id'] = (string) The async search ID
*/
----
****



[[Elasticsearch_Namespaces_EqlNamespacesearch_search]]
.`search(array $params = [])`
****
[source,php]
----
/*
$params['index']                       = (string) The name of the index to scope the operation
$params['wait_for_completion_timeout'] = (time) Specify the time that the request should block waiting for the final response
$params['keep_on_completion']          = (boolean) Control whether the response should be stored in the cluster if it completed within the provided [wait_for_completion] time (default: false) (Default = false)
$params['keep_alive']                  = (time) Update the time interval in which the results (partial or final) for this search will be available (Default = 5d)
$params['body']                        = (array) Eql request body. Use the `query` to limit the query scope. (Required)
*/
----
****


