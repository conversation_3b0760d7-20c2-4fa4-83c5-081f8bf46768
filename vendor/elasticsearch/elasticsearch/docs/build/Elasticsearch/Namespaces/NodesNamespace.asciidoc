

[[Elasticsearch_Namespaces_NodesNamespace]]
=== Elasticsearch\Namespaces\NodesNamespace



Class NodesNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_NodesNamespacehotThreads_hotThreads,`hotThreads()`>>
* <<Elasticsearch_Namespaces_NodesNamespaceinfo_info,`info()`>>
* <<Elasticsearch_Namespaces_NodesNamespacereloadSecureSettings_reloadSecureSettings,`reloadSecureSettings()`>>
* <<Elasticsearch_Namespaces_NodesNamespacestats_stats,`stats()`>>
* <<Elasticsearch_Namespaces_NodesNamespaceusage_usage,`usage()`>>



[[Elasticsearch_Namespaces_NodesNamespacehotThreads_hotThreads]]
.`hotThreads(array $params = [])`
****
[source,php]
----
/*
$params['node_id']             = (list) A comma-separated list of node IDs or names to limit the returned information; use `_local` to return information from the node you're connecting to, leave empty to get information from all nodes
$params['interval']            = (time) The interval for the second sampling of threads
$params['snapshots']           = (number) Number of samples of thread stacktrace (default: 10)
$params['threads']             = (number) Specify the number of threads to provide information for (default: 3)
$params['ignore_idle_threads'] = (boolean) Don't show threads that are in known-idle places, such as waiting on a socket select or pulling from an empty task queue (default: true)
$params['type']                = (enum) The type to sample (default: cpu) (Options = cpu,wait,block)
$params['timeout']             = (time) Explicit operation timeout
*/
----
****



[[Elasticsearch_Namespaces_NodesNamespaceinfo_info]]
.`info(array $params = [])`
****
[source,php]
----
/*
$params['node_id']       = (list) A comma-separated list of node IDs or names to limit the returned information; use `_local` to return information from the node you're connecting to, leave empty to get information from all nodes
$params['metric']        = (list) A comma-separated list of metrics you wish returned. Leave empty to return all.
*/
----
****



[[Elasticsearch_Namespaces_NodesNamespacereloadSecureSettings_reloadSecureSettings]]
.`reloadSecureSettings(array $params = [])`
****
[source,php]
----
/*
$params['node_id'] = (list) A comma-separated list of node IDs to span the reload/reinit call. Should stay empty because reloading usually involves all cluster nodes.
*/
----
****



[[Elasticsearch_Namespaces_NodesNamespacestats_stats]]
.`stats(array $params = [])`
****
[source,php]
----
/*
$params['node_id']                    = (list) A comma-separated list of node IDs or names to limit the returned information; use `_local` to return information from the node you're connecting to, leave empty to get information from all nodes
$params['metric']                     = (list) Limit the information returned to the specified metrics
$params['index_metric']               = (list) Limit the information returned for `indices` metric to the specific index metrics. Isn't used if `indices` (or `all`) metric isn't specified.
*/
----
****



[[Elasticsearch_Namespaces_NodesNamespaceusage_usage]]
.`usage(array $params = [])`
****
[source,php]
----
/*
$params['node_id'] = (list) A comma-separated list of node IDs or names to limit the returned information; use `_local` to return information from the node you're connecting to, leave empty to get information from all nodes
$params['metric']  = (list) Limit the information returned to the specified metrics
$params['timeout'] = (time) Explicit operation timeout
*/
----
****


