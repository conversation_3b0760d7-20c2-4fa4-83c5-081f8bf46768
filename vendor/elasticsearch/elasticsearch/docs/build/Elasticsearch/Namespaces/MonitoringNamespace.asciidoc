

[[Elasticsearch_Namespaces_MonitoringNamespace]]
=== Elasticsearch\Namespaces\MonitoringNamespace



Class MonitoringNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_MonitoringNamespacebulk_bulk,`bulk()`>>



[[Elasticsearch_Namespaces_MonitoringNamespacebulk_bulk]]
.`bulk(array $params = [])`
*NOTE:* This API is EXPERIMENTAL and may be changed or removed completely in a future release
****
[source,php]
----
/*
$params['type']               = DEPRECATED (string) Default document type for items which don't provide one
$params['system_id']          = (string) Identifier of the monitored system
$params['system_api_version'] = (string) API Version of the monitored system
$params['interval']           = (string) Collection interval (e.g., '10s' or '10000ms') of the payload
$params['body']               = (array) The operation definition and data (action-data pairs), separated by newlines (Required)
*/
----
****


