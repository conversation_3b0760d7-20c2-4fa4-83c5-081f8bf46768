

[[Elasticsearch_Namespaces_LicenseNamespace]]
=== Elasticsearch\Namespaces\LicenseNamespace



Class LicenseNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_LicenseNamespacedelete_delete,`delete()`>>
* <<Elasticsearch_Namespaces_LicenseNamespaceget_get,`get()`>>
* <<Elasticsearch_Namespaces_LicenseNamespacegetBasicStatus_getBasicStatus,`getBasicStatus()`>>
* <<Elasticsearch_Namespaces_LicenseNamespacegetTrialStatus_getTrialStatus,`getTrialStatus()`>>
* <<Elasticsearch_Namespaces_LicenseNamespacepost_post,`post()`>>
* <<Elasticsearch_Namespaces_LicenseNamespacepostStartBasic_postStartBasic,`postStartBasic()`>>
* <<Elasticsearch_Namespaces_LicenseNamespacepostStartTrial_postStartTrial,`postStartTrial()`>>



[[Elasticsearch_Namespaces_LicenseNamespacedelete_delete]]
.`delete(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_Namespaces_LicenseNamespaceget_get]]
.`get(array $params = [])`
****
[source,php]
----
/*
$params['local']             = (boolean) Return local information, do not retrieve the state from master node (default: false)
$params['accept_enterprise'] = (boolean) If the active license is an enterprise license, return type as 'enterprise' (default: false)
*/
----
****



[[Elasticsearch_Namespaces_LicenseNamespacegetBasicStatus_getBasicStatus]]
.`getBasicStatus(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_Namespaces_LicenseNamespacegetTrialStatus_getTrialStatus]]
.`getTrialStatus(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_Namespaces_LicenseNamespacepost_post]]
.`post(array $params = [])`
****
[source,php]
----
/*
$params['acknowledge'] = (boolean) whether the user has acknowledged acknowledge messages (default: false)
$params['body']        = (array) licenses to be installed
*/
----
****



[[Elasticsearch_Namespaces_LicenseNamespacepostStartBasic_postStartBasic]]
.`postStartBasic(array $params = [])`
****
[source,php]
----
/*
$params['acknowledge'] = (boolean) whether the user has acknowledged acknowledge messages (default: false)
*/
----
****



[[Elasticsearch_Namespaces_LicenseNamespacepostStartTrial_postStartTrial]]
.`postStartTrial(array $params = [])`
****
[source,php]
----
/*
$params['type']        = (string) The type of trial license to generate (default: "trial")
$params['acknowledge'] = (boolean) whether the user has acknowledged acknowledge messages (default: false)
*/
----
****


