

[[Elasticsearch_Namespaces_CatNamespace]]
=== Elasticsearch\Namespaces\CatNamespace



Class CatNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_CatNamespacealiases_aliases,`aliases()`>>
* <<Elasticsearch_Namespaces_CatNamespaceallocation_allocation,`allocation()`>>
* <<Elasticsearch_Namespaces_CatNamespacecount_count,`count()`>>
* <<Elasticsearch_Namespaces_CatNamespacefielddata_fielddata,`fielddata()`>>
* <<Elasticsearch_Namespaces_CatNamespacehealth_health,`health()`>>
* <<Elasticsearch_Namespaces_CatNamespacehelp_help,`help()`>>
* <<Elasticsearch_Namespaces_CatNamespaceindices_indices,`indices()`>>
* <<Elasticsearch_Namespaces_CatNamespacemaster_master,`master()`>>
* <<Elasticsearch_Namespaces_CatNamespacemlDataFrameAnalytics_mlDataFrameAnalytics,`mlDataFrameAnalytics()`>>
* <<Elasticsearch_Namespaces_CatNamespacemlDatafeeds_mlDatafeeds,`mlDatafeeds()`>>
* <<Elasticsearch_Namespaces_CatNamespacemlJobs_mlJobs,`mlJobs()`>>
* <<Elasticsearch_Namespaces_CatNamespacemlTrainedModels_mlTrainedModels,`mlTrainedModels()`>>
* <<Elasticsearch_Namespaces_CatNamespacenodeattrs_nodeattrs,`nodeattrs()`>>
* <<Elasticsearch_Namespaces_CatNamespacenodes_nodes,`nodes()`>>
* <<Elasticsearch_Namespaces_CatNamespacependingTasks_pendingTasks,`pendingTasks()`>>
* <<Elasticsearch_Namespaces_CatNamespaceplugins_plugins,`plugins()`>>
* <<Elasticsearch_Namespaces_CatNamespacerecovery_recovery,`recovery()`>>
* <<Elasticsearch_Namespaces_CatNamespacerepositories_repositories,`repositories()`>>
* <<Elasticsearch_Namespaces_CatNamespacesegments_segments,`segments()`>>
* <<Elasticsearch_Namespaces_CatNamespaceshards_shards,`shards()`>>
* <<Elasticsearch_Namespaces_CatNamespacesnapshots_snapshots,`snapshots()`>>
* <<Elasticsearch_Namespaces_CatNamespacetasks_tasks,`tasks()`>>
* <<Elasticsearch_Namespaces_CatNamespacetemplates_templates,`templates()`>>
* <<Elasticsearch_Namespaces_CatNamespacethreadPool_threadPool,`threadPool()`>>
* <<Elasticsearch_Namespaces_CatNamespacetransforms_transforms,`transforms()`>>



[[Elasticsearch_Namespaces_CatNamespacealiases_aliases]]
.`aliases(array $params = [])`
****
[source,php]
----
/*
$params['name']             = (list) A comma-separated list of alias names to return
$params['format']           = (string) a short version of the Accept header, e.g. json, yaml
$params['local']            = (boolean) Return local information, do not retrieve the state from master node (default: false)
$params['h']                = (list) Comma-separated list of column names to display
$params['help']             = (boolean) Return help information (Default = false)
$params['s']                = (list) Comma-separated list of column names or column aliases to sort by
$params['v']                = (boolean) Verbose mode. Display column headers (Default = false)
$params['expand_wildcards'] = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = all)
*/
----
****



[[Elasticsearch_Namespaces_CatNamespaceallocation_allocation]]
.`allocation(array $params = [])`
****
[source,php]
----
/*
$params['node_id']        = (list) A comma-separated list of node IDs or names to limit the returned information
$params['format']         = (string) a short version of the Accept header, e.g. json, yaml
$params['bytes']          = (enum) The unit in which to display byte values (Options = b,k,kb,m,mb,g,gb,t,tb,p,pb)
$params['local']          = (boolean) Return local information, do not retrieve the state from master node (default: false)
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
$params['h']              = (list) Comma-separated list of column names to display
$params['help']           = (boolean) Return help information (Default = false)
$params['s']              = (list) Comma-separated list of column names or column aliases to sort by
$params['v']              = (boolean) Verbose mode. Display column headers (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_CatNamespacecount_count]]
.`count(array $params = [])`
****
[source,php]
----
/*
$params['index']  = (list) A comma-separated list of index names to limit the returned information
$params['format'] = (string) a short version of the Accept header, e.g. json, yaml
$params['h']      = (list) Comma-separated list of column names to display
$params['help']   = (boolean) Return help information (Default = false)
$params['s']      = (list) Comma-separated list of column names or column aliases to sort by
$params['v']      = (boolean) Verbose mode. Display column headers (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_CatNamespacefielddata_fielddata]]
.`fielddata(array $params = [])`
****
[source,php]
----
/*
$params['fields'] = (list) A comma-separated list of fields to return the fielddata size
$params['format'] = (string) a short version of the Accept header, e.g. json, yaml
$params['bytes']  = (enum) The unit in which to display byte values (Options = b,k,kb,m,mb,g,gb,t,tb,p,pb)
$params['h']      = (list) Comma-separated list of column names to display
$params['help']   = (boolean) Return help information (Default = false)
$params['s']      = (list) Comma-separated list of column names or column aliases to sort by
$params['v']      = (boolean) Verbose mode. Display column headers (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_CatNamespacehealth_health]]
.`health(array $params = [])`
****
[source,php]
----
/*
$params['format'] = (string) a short version of the Accept header, e.g. json, yaml
$params['h']      = (list) Comma-separated list of column names to display
$params['help']   = (boolean) Return help information (Default = false)
$params['s']      = (list) Comma-separated list of column names or column aliases to sort by
$params['time']   = (enum) The unit in which to display time values (Options = d,h,m,s,ms,micros,nanos)
$params['ts']     = (boolean) Set to false to disable timestamping (Default = true)
$params['v']      = (boolean) Verbose mode. Display column headers (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_CatNamespacehelp_help]]
.`help(array $params = [])`
****
[source,php]
----
/*
$params['help'] = (boolean) Return help information (Default = false)
$params['s']    = (list) Comma-separated list of column names or column aliases to sort by
*/
----
****



[[Elasticsearch_Namespaces_CatNamespaceindices_indices]]
.`indices(array $params = [])`
****
[source,php]
----
/*
$params['index']                     = (list) A comma-separated list of index names to limit the returned information
$params['format']                    = (string) a short version of the Accept header, e.g. json, yaml
$params['bytes']                     = (enum) The unit in which to display byte values (Options = b,k,kb,m,mb,g,gb,t,tb,p,pb)
$params['local']                     = (boolean) Return local information, do not retrieve the state from master node (default: false)
$params['master_timeout']            = (time) Explicit operation timeout for connection to master node
$params['h']                         = (list) Comma-separated list of column names to display
$params['health']                    = (enum) A health status ("green", "yellow", or "red" to filter only indices matching the specified health status (Options = green,yellow,red)
$params['help']                      = (boolean) Return help information (Default = false)
$params['pri']                       = (boolean) Set to true to return stats only for primary shards (Default = false)
$params['s']                         = (list) Comma-separated list of column names or column aliases to sort by
$params['time']                      = (enum) The unit in which to display time values (Options = d,h,m,s,ms,micros,nanos)
$params['v']                         = (boolean) Verbose mode. Display column headers (Default = false)
$params['include_unloaded_segments'] = (boolean) If set to true segment stats will include stats for segments that are not currently loaded into memory (Default = false)
$params['expand_wildcards']          = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = all)
*/
----
****



[[Elasticsearch_Namespaces_CatNamespacemaster_master]]
.`master(array $params = [])`
****
[source,php]
----
/*
$params['format']         = (string) a short version of the Accept header, e.g. json, yaml
$params['local']          = (boolean) Return local information, do not retrieve the state from master node (default: false)
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
$params['h']              = (list) Comma-separated list of column names to display
$params['help']           = (boolean) Return help information (Default = false)
$params['s']              = (list) Comma-separated list of column names or column aliases to sort by
$params['v']              = (boolean) Verbose mode. Display column headers (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_CatNamespacemlDataFrameAnalytics_mlDataFrameAnalytics]]
.`mlDataFrameAnalytics(array $params = [])`
****
[source,php]
----
/*
$params['id']             = (string) The ID of the data frame analytics to fetch
$params['allow_no_match'] = (boolean) Whether to ignore if a wildcard expression matches no configs. (This includes `_all` string or when no configs have been specified)
$params['bytes']          = (enum) The unit in which to display byte values (Options = b,k,kb,m,mb,g,gb,t,tb,p,pb)
$params['format']         = (string) a short version of the Accept header, e.g. json, yaml
$params['h']              = (list) Comma-separated list of column names to display
$params['help']           = (boolean) Return help information (Default = false)
$params['s']              = (list) Comma-separated list of column names or column aliases to sort by
$params['time']           = (enum) The unit in which to display time values (Options = d,h,m,s,ms,micros,nanos)
$params['v']              = (boolean) Verbose mode. Display column headers (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_CatNamespacemlDatafeeds_mlDatafeeds]]
.`mlDatafeeds(array $params = [])`
****
[source,php]
----
/*
$params['datafeed_id']        = (string) The ID of the datafeeds stats to fetch
$params['allow_no_match']     = (boolean) Whether to ignore if a wildcard expression matches no datafeeds. (This includes `_all` string or when no datafeeds have been specified)
$params['allow_no_datafeeds'] = (boolean) Whether to ignore if a wildcard expression matches no datafeeds. (This includes `_all` string or when no datafeeds have been specified)
$params['format']             = (string) a short version of the Accept header, e.g. json, yaml
$params['h']                  = (list) Comma-separated list of column names to display
$params['help']               = (boolean) Return help information (Default = false)
$params['s']                  = (list) Comma-separated list of column names or column aliases to sort by
$params['time']               = (enum) The unit in which to display time values (Options = d,h,m,s,ms,micros,nanos)
$params['v']                  = (boolean) Verbose mode. Display column headers (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_CatNamespacemlJobs_mlJobs]]
.`mlJobs(array $params = [])`
****
[source,php]
----
/*
$params['job_id']         = (string) The ID of the jobs stats to fetch
$params['allow_no_match'] = (boolean) Whether to ignore if a wildcard expression matches no jobs. (This includes `_all` string or when no jobs have been specified)
$params['allow_no_jobs']  = (boolean) Whether to ignore if a wildcard expression matches no jobs. (This includes `_all` string or when no jobs have been specified)
$params['bytes']          = (enum) The unit in which to display byte values (Options = b,k,kb,m,mb,g,gb,t,tb,p,pb)
$params['format']         = (string) a short version of the Accept header, e.g. json, yaml
$params['h']              = (list) Comma-separated list of column names to display
$params['help']           = (boolean) Return help information (Default = false)
$params['s']              = (list) Comma-separated list of column names or column aliases to sort by
$params['time']           = (enum) The unit in which to display time values (Options = d,h,m,s,ms,micros,nanos)
$params['v']              = (boolean) Verbose mode. Display column headers (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_CatNamespacemlTrainedModels_mlTrainedModels]]
.`mlTrainedModels(array $params = [])`
****
[source,php]
----
/*
$params['model_id']       = (string) The ID of the trained models stats to fetch
$params['allow_no_match'] = (boolean) Whether to ignore if a wildcard expression matches no trained models. (This includes `_all` string or when no trained models have been specified) (Default = true)
$params['from']           = (int) skips a number of trained models (Default = 0)
$params['size']           = (int) specifies a max number of trained models to get (Default = 100)
$params['bytes']          = (enum) The unit in which to display byte values (Options = b,k,kb,m,mb,g,gb,t,tb,p,pb)
$params['format']         = (string) a short version of the Accept header, e.g. json, yaml
$params['h']              = (list) Comma-separated list of column names to display
$params['help']           = (boolean) Return help information (Default = false)
$params['s']              = (list) Comma-separated list of column names or column aliases to sort by
$params['time']           = (enum) The unit in which to display time values (Options = d,h,m,s,ms,micros,nanos)
$params['v']              = (boolean) Verbose mode. Display column headers (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_CatNamespacenodeattrs_nodeattrs]]
.`nodeattrs(array $params = [])`
****
[source,php]
----
/*
$params['format']         = (string) a short version of the Accept header, e.g. json, yaml
$params['local']          = (boolean) Return local information, do not retrieve the state from master node (default: false)
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
$params['h']              = (list) Comma-separated list of column names to display
$params['help']           = (boolean) Return help information (Default = false)
$params['s']              = (list) Comma-separated list of column names or column aliases to sort by
$params['v']              = (boolean) Verbose mode. Display column headers (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_CatNamespacenodes_nodes]]
.`nodes(array $params = [])`
****
[source,php]
----
/*
$params['bytes']          = (enum) The unit in which to display byte values (Options = b,k,kb,m,mb,g,gb,t,tb,p,pb)
$params['format']         = (string) a short version of the Accept header, e.g. json, yaml
$params['full_id']        = (boolean) Return the full node ID instead of the shortened version (default: false)
$params['local']          = (boolean) Calculate the selected nodes using the local cluster state rather than the state from master node (default: false)
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
$params['h']              = (list) Comma-separated list of column names to display
$params['help']           = (boolean) Return help information (Default = false)
$params['s']              = (list) Comma-separated list of column names or column aliases to sort by
$params['time']           = (enum) The unit in which to display time values (Options = d,h,m,s,ms,micros,nanos)
$params['v']              = (boolean) Verbose mode. Display column headers (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_CatNamespacependingTasks_pendingTasks]]
.`pendingTasks(array $params = [])`
****
[source,php]
----
/*
$params['format']         = (string) a short version of the Accept header, e.g. json, yaml
$params['local']          = (boolean) Return local information, do not retrieve the state from master node (default: false)
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
$params['h']              = (list) Comma-separated list of column names to display
$params['help']           = (boolean) Return help information (Default = false)
$params['s']              = (list) Comma-separated list of column names or column aliases to sort by
$params['time']           = (enum) The unit in which to display time values (Options = d,h,m,s,ms,micros,nanos)
$params['v']              = (boolean) Verbose mode. Display column headers (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_CatNamespaceplugins_plugins]]
.`plugins(array $params = [])`
****
[source,php]
----
/*
$params['format']            = (string) a short version of the Accept header, e.g. json, yaml
$params['local']             = (boolean) Return local information, do not retrieve the state from master node (default: false)
$params['master_timeout']    = (time) Explicit operation timeout for connection to master node
$params['h']                 = (list) Comma-separated list of column names to display
$params['help']              = (boolean) Return help information (Default = false)
$params['include_bootstrap'] = (boolean) Include bootstrap plugins in the response (Default = false)
$params['s']                 = (list) Comma-separated list of column names or column aliases to sort by
$params['v']                 = (boolean) Verbose mode. Display column headers (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_CatNamespacerecovery_recovery]]
.`recovery(array $params = [])`
****
[source,php]
----
/*
$params['index']       = (list) Comma-separated list or wildcard expression of index names to limit the returned information
$params['format']      = (string) a short version of the Accept header, e.g. json, yaml
$params['active_only'] = (boolean) If `true`, the response only includes ongoing shard recoveries (Default = false)
$params['bytes']       = (enum) The unit in which to display byte values (Options = b,k,kb,m,mb,g,gb,t,tb,p,pb)
$params['detailed']    = (boolean) If `true`, the response includes detailed information about shard recoveries (Default = false)
$params['h']           = (list) Comma-separated list of column names to display
$params['help']        = (boolean) Return help information (Default = false)
$params['s']           = (list) Comma-separated list of column names or column aliases to sort by
$params['time']        = (enum) The unit in which to display time values (Options = d,h,m,s,ms,micros,nanos)
$params['v']           = (boolean) Verbose mode. Display column headers (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_CatNamespacerepositories_repositories]]
.`repositories(array $params = [])`
****
[source,php]
----
/*
$params['format']         = (string) a short version of the Accept header, e.g. json, yaml
$params['local']          = (boolean) Return local information, do not retrieve the state from master node (Default = false)
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
$params['h']              = (list) Comma-separated list of column names to display
$params['help']           = (boolean) Return help information (Default = false)
$params['s']              = (list) Comma-separated list of column names or column aliases to sort by
$params['v']              = (boolean) Verbose mode. Display column headers (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_CatNamespacesegments_segments]]
.`segments(array $params = [])`
****
[source,php]
----
/*
$params['index']  = (list) A comma-separated list of index names to limit the returned information
$params['format'] = (string) a short version of the Accept header, e.g. json, yaml
$params['bytes']  = (enum) The unit in which to display byte values (Options = b,k,kb,m,mb,g,gb,t,tb,p,pb)
$params['h']      = (list) Comma-separated list of column names to display
$params['help']   = (boolean) Return help information (Default = false)
$params['s']      = (list) Comma-separated list of column names or column aliases to sort by
$params['v']      = (boolean) Verbose mode. Display column headers (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_CatNamespaceshards_shards]]
.`shards(array $params = [])`
****
[source,php]
----
/*
$params['index']          = (list) A comma-separated list of index names to limit the returned information
$params['format']         = (string) a short version of the Accept header, e.g. json, yaml
$params['bytes']          = (enum) The unit in which to display byte values (Options = b,k,kb,m,mb,g,gb,t,tb,p,pb)
$params['local']          = (boolean) Return local information, do not retrieve the state from master node (default: false)
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
$params['h']              = (list) Comma-separated list of column names to display
$params['help']           = (boolean) Return help information (Default = false)
$params['s']              = (list) Comma-separated list of column names or column aliases to sort by
$params['time']           = (enum) The unit in which to display time values (Options = d,h,m,s,ms,micros,nanos)
$params['v']              = (boolean) Verbose mode. Display column headers (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_CatNamespacesnapshots_snapshots]]
.`snapshots(array $params = [])`
****
[source,php]
----
/*
$params['repository']         = (list) Name of repository from which to fetch the snapshot information
$params['format']             = (string) a short version of the Accept header, e.g. json, yaml
$params['ignore_unavailable'] = (boolean) Set to true to ignore unavailable snapshots (Default = false)
$params['master_timeout']     = (time) Explicit operation timeout for connection to master node
$params['h']                  = (list) Comma-separated list of column names to display
$params['help']               = (boolean) Return help information (Default = false)
$params['s']                  = (list) Comma-separated list of column names or column aliases to sort by
$params['time']               = (enum) The unit in which to display time values (Options = d,h,m,s,ms,micros,nanos)
$params['v']                  = (boolean) Verbose mode. Display column headers (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_CatNamespacetasks_tasks]]
.`tasks(array $params = [])`
****
[source,php]
----
/*
$params['format']         = (string) a short version of the Accept header, e.g. json, yaml
$params['nodes']          = (list) A comma-separated list of node IDs or names to limit the returned information; use `_local` to return information from the node you're connecting to, leave empty to get information from all nodes
$params['actions']        = (list) A comma-separated list of actions that should be returned. Leave empty to return all.
*/
----
****



[[Elasticsearch_Namespaces_CatNamespacetemplates_templates]]
.`templates(array $params = [])`
****
[source,php]
----
/*
$params['name']           = (string) A pattern that returned template names must match
$params['format']         = (string) a short version of the Accept header, e.g. json, yaml
$params['local']          = (boolean) Return local information, do not retrieve the state from master node (default: false)
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
$params['h']              = (list) Comma-separated list of column names to display
$params['help']           = (boolean) Return help information (Default = false)
$params['s']              = (list) Comma-separated list of column names or column aliases to sort by
$params['v']              = (boolean) Verbose mode. Display column headers (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_CatNamespacethreadPool_threadPool]]
.`threadPool(array $params = [])`
****
[source,php]
----
/*
$params['thread_pool_patterns'] = (list) A comma-separated list of regular-expressions to filter the thread pools in the output
$params['format']               = (string) a short version of the Accept header, e.g. json, yaml
$params['size']                 = (enum) The multiplier in which to display values (Options = ,k,m,g,t,p)
$params['local']                = (boolean) Return local information, do not retrieve the state from master node (default: false)
$params['master_timeout']       = (time) Explicit operation timeout for connection to master node
$params['h']                    = (list) Comma-separated list of column names to display
$params['help']                 = (boolean) Return help information (Default = false)
$params['s']                    = (list) Comma-separated list of column names or column aliases to sort by
$params['v']                    = (boolean) Verbose mode. Display column headers (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_CatNamespacetransforms_transforms]]
.`transforms(array $params = [])`
****
[source,php]
----
/*
$params['transform_id']   = (string) The id of the transform for which to get stats. '_all' or '*' implies all transforms
$params['from']           = (int) skips a number of transform configs, defaults to 0
$params['size']           = (int) specifies a max number of transforms to get, defaults to 100
$params['allow_no_match'] = (boolean) Whether to ignore if a wildcard expression matches no transforms. (This includes `_all` string or when no transforms have been specified)
$params['format']         = (string) a short version of the Accept header, e.g. json, yaml
$params['h']              = (list) Comma-separated list of column names to display
$params['help']           = (boolean) Return help information (Default = false)
$params['s']              = (list) Comma-separated list of column names or column aliases to sort by
$params['time']           = (enum) The unit in which to display time values (Options = d,h,m,s,ms,micros,nanos)
$params['v']              = (boolean) Verbose mode. Display column headers (Default = false)
*/
----
****


