

[[Elasticsearch_Namespaces_SlmNamespace]]
=== Elasticsearch\Namespaces\SlmNamespace



Class SlmNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_SlmNamespacedeleteLifecycle_deleteLifecycle,`deleteLifecycle()`>>
* <<Elasticsearch_Namespaces_SlmNamespaceexecuteLifecycle_executeLifecycle,`executeLifecycle()`>>
* <<Elasticsearch_Namespaces_SlmNamespaceexecuteRetention_executeRetention,`executeRetention()`>>
* <<Elasticsearch_Namespaces_SlmNamespacegetLifecycle_getLifecycle,`getLifecycle()`>>
* <<Elasticsearch_Namespaces_SlmNamespacegetStats_getStats,`getStats()`>>
* <<Elasticsearch_Namespaces_SlmNamespacegetStatus_getStatus,`getStatus()`>>
* <<Elasticsearch_Namespaces_SlmNamespaceputLifecycle_putLifecycle,`putLifecycle()`>>
* <<Elasticsearch_Namespaces_SlmNamespacestart_start,`start()`>>
* <<Elasticsearch_Namespaces_SlmNamespacestop_stop,`stop()`>>



[[Elasticsearch_Namespaces_SlmNamespacedeleteLifecycle_deleteLifecycle]]
.`deleteLifecycle(array $params = [])`
****
[source,php]
----
/*
$params['policy_id'] = (string) The id of the snapshot lifecycle policy to remove
*/
----
****



[[Elasticsearch_Namespaces_SlmNamespaceexecuteLifecycle_executeLifecycle]]
.`executeLifecycle(array $params = [])`
****
[source,php]
----
/*
$params['policy_id'] = (string) The id of the snapshot lifecycle policy to be executed
*/
----
****



[[Elasticsearch_Namespaces_SlmNamespaceexecuteRetention_executeRetention]]
.`executeRetention(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_Namespaces_SlmNamespacegetLifecycle_getLifecycle]]
.`getLifecycle(array $params = [])`
****
[source,php]
----
/*
$params['policy_id'] = (list) Comma-separated list of snapshot lifecycle policies to retrieve
*/
----
****



[[Elasticsearch_Namespaces_SlmNamespacegetStats_getStats]]
.`getStats(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_Namespaces_SlmNamespacegetStatus_getStatus]]
.`getStatus(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_Namespaces_SlmNamespaceputLifecycle_putLifecycle]]
.`putLifecycle(array $params = [])`
****
[source,php]
----
/*
$params['policy_id'] = (string) The id of the snapshot lifecycle policy
$params['body']      = (array) The snapshot lifecycle policy definition to register
*/
----
****



[[Elasticsearch_Namespaces_SlmNamespacestart_start]]
.`start(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_Namespaces_SlmNamespacestop_stop]]
.`stop(array $params = [])`
****
[source,php]
----
/*
*/
----
****


