

[[Elasticsearch_Namespaces_FeaturesNamespace]]
=== Elasticsearch\Namespaces\FeaturesNamespace



Class FeaturesNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_FeaturesNamespacegetFeatures_getFeatures,`getFeatures()`>>



[[Elasticsearch_Namespaces_FeaturesNamespacegetFeatures_getFeatures]]
.`getFeatures(array $params = [])`
****
[source,php]
----
/*
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
*/
----
****


