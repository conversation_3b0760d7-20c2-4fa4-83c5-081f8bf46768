

[[Elasticsearch_Namespaces_AsyncSearchNamespace]]
=== Elasticsearch\Namespaces\AsyncSearchNamespace



Class AsyncSearchNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_AsyncSearchNamespacedelete_delete,`delete()`>>
* <<Elasticsearch_Namespaces_AsyncSearchNamespaceget_get,`get()`>>
* <<Elasticsearch_Namespaces_AsyncSearchNamespacestatus_status,`status()`>>
* <<Elasticsearch_Namespaces_AsyncSearchNamespacesubmit_submit,`submit()`>>



[[Elasticsearch_Namespaces_AsyncSearchNamespacedelete_delete]]
.`delete(array $params = [])`
****
[source,php]
----
/*
$params['id'] = (string) The async search ID
*/
----
****



[[Elasticsearch_Namespaces_AsyncSearchNamespaceget_get]]
.`get(array $params = [])`
****
[source,php]
----
/*
$params['id']                          = (string) The async search ID
$params['wait_for_completion_timeout'] = (time) Specify the time that the request should block waiting for the final response
$params['keep_alive']                  = (time) Specify the time interval in which the results (partial or final) for this search will be available
$params['typed_keys']                  = (boolean) Specify whether aggregation and suggester names should be prefixed by their respective types in the response
*/
----
****



[[Elasticsearch_Namespaces_AsyncSearchNamespacestatus_status]]
.`status(array $params = [])`
****
[source,php]
----
/*
$params['id'] = (string) The async search ID
*/
----
****



[[Elasticsearch_Namespaces_AsyncSearchNamespacesubmit_submit]]
.`submit(array $params = [])`
****
[source,php]
----
/*
$params['index']                         = (list) A comma-separated list of index names to search; use `_all` or empty string to perform the operation on all indices
$params['wait_for_completion_timeout']   = (time) Specify the time that the request should block waiting for the final response (Default = 1s)
$params['keep_on_completion']            = (boolean) Control whether the response should be stored in the cluster if it completed within the provided [wait_for_completion] time (default: false) (Default = false)
$params['keep_alive']                    = (time) Update the time interval in which the results (partial or final) for this search will be available (Default = 5d)
$params['batched_reduce_size']           = (number) The number of shard results that should be reduced at once on the coordinating node. This value should be used as the granularity at which progress results will be made available. (Default = 5)
$params['request_cache']                 = (boolean) Specify if request cache should be used for this request or not, defaults to true
$params['analyzer']                      = (string) The analyzer to use for the query string
$params['analyze_wildcard']              = (boolean) Specify whether wildcard and prefix queries should be analyzed (default: false)
$params['default_operator']              = (enum) The default operator for query string query (AND or OR) (Options = AND,OR) (Default = OR)
$params['df']                            = (string) The field to use as default where no field prefix is given in the query string
$params['explain']                       = (boolean) Specify whether to return detailed information about score computation as part of a hit
$params['stored_fields']                 = (list) A comma-separated list of stored fields to return as part of a hit
$params['docvalue_fields']               = (list) A comma-separated list of fields to return as the docvalue representation of a field for each hit
$params['from']                          = (number) Starting offset (default: 0)
$params['ignore_unavailable']            = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['ignore_throttled']              = (boolean) Whether specified concrete, expanded or aliased indices should be ignored when throttled
$params['allow_no_indices']              = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']              = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
$params['lenient']                       = (boolean) Specify whether format-based query failures (such as providing text to a numeric field) should be ignored
$params['preference']                    = (string) Specify the node or shard the operation should be performed on (default: random)
$params['q']                             = (string) Query in the Lucene query string syntax
$params['routing']                       = (list) A comma-separated list of specific routing values
$params['search_type']                   = (enum) Search operation type (Options = query_then_fetch,dfs_query_then_fetch)
$params['size']                          = (number) Number of hits to return (default: 10)
$params['sort']                          = (list) A comma-separated list of <field>:<direction> pairs
$params['_source']                       = (list) True or false to return the _source field or not, or a list of fields to return
$params['_source_excludes']              = (list) A list of fields to exclude from the returned _source field
$params['_source_includes']              = (list) A list of fields to extract and return from the _source field
$params['terminate_after']               = (number) The maximum number of documents to collect for each shard, upon reaching which the query execution will terminate early.
*/
----
****


