

[[Elasticsearch_Namespaces_WatcherNamespace]]
=== Elasticsearch\Namespaces\WatcherNamespace



Class WatcherNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_WatcherNamespaceackWatch_ackWatch,`ackWatch()`>>
* <<Elasticsearch_Namespaces_WatcherNamespaceactivateWatch_activateWatch,`activateWatch()`>>
* <<Elasticsearch_Namespaces_WatcherNamespacedeactivateWatch_deactivateWatch,`deactivateWatch()`>>
* <<Elasticsearch_Namespaces_WatcherNamespacedeleteWatch_deleteWatch,`deleteWatch()`>>
* <<Elasticsearch_Namespaces_WatcherNamespaceexecuteWatch_executeWatch,`executeWatch()`>>
* <<Elasticsearch_Namespaces_WatcherNamespacegetWatch_getWatch,`getWatch()`>>
* <<Elasticsearch_Namespaces_WatcherNamespaceputWatch_putWatch,`putWatch()`>>
* <<Elasticsearch_Namespaces_WatcherNamespacequeryWatches_queryWatches,`queryWatches()`>>
* <<Elasticsearch_Namespaces_WatcherNamespacestart_start,`start()`>>
* <<Elasticsearch_Namespaces_WatcherNamespacestats_stats,`stats()`>>
* <<Elasticsearch_Namespaces_WatcherNamespacestop_stop,`stop()`>>



[[Elasticsearch_Namespaces_WatcherNamespaceackWatch_ackWatch]]
.`ackWatch(array $params = [])`
****
[source,php]
----
/*
$params['watch_id']  = (string) Watch ID (Required)
$params['action_id'] = (list) A comma-separated list of the action ids to be acked
*/
----
****



[[Elasticsearch_Namespaces_WatcherNamespaceactivateWatch_activateWatch]]
.`activateWatch(array $params = [])`
****
[source,php]
----
/*
$params['watch_id'] = (string) Watch ID
*/
----
****



[[Elasticsearch_Namespaces_WatcherNamespacedeactivateWatch_deactivateWatch]]
.`deactivateWatch(array $params = [])`
****
[source,php]
----
/*
$params['watch_id'] = (string) Watch ID
*/
----
****



[[Elasticsearch_Namespaces_WatcherNamespacedeleteWatch_deleteWatch]]
.`deleteWatch(array $params = [])`
****
[source,php]
----
/*
$params['id'] = (string) Watch ID
*/
----
****



[[Elasticsearch_Namespaces_WatcherNamespaceexecuteWatch_executeWatch]]
.`executeWatch(array $params = [])`
****
[source,php]
----
/*
$params['id']    = (string) Watch ID
$params['debug'] = (boolean) indicates whether the watch should execute in debug mode
$params['body']  = (array) Execution control
*/
----
****



[[Elasticsearch_Namespaces_WatcherNamespacegetWatch_getWatch]]
.`getWatch(array $params = [])`
****
[source,php]
----
/*
$params['id'] = (string) Watch ID
*/
----
****



[[Elasticsearch_Namespaces_WatcherNamespaceputWatch_putWatch]]
.`putWatch(array $params = [])`
****
[source,php]
----
/*
$params['id']              = (string) Watch ID
$params['active']          = (boolean) Specify whether the watch is in/active by default
$params['version']         = (number) Explicit version number for concurrency control
$params['if_seq_no']       = (number) only update the watch if the last operation that has changed the watch has the specified sequence number
$params['if_primary_term'] = (number) only update the watch if the last operation that has changed the watch has the specified primary term
$params['body']            = (array) The watch
*/
----
****



[[Elasticsearch_Namespaces_WatcherNamespacequeryWatches_queryWatches]]
.`queryWatches(array $params = [])`
****
[source,php]
----
/*
$params['body'] = (array) From, size, query, sort and search_after
*/
----
****



[[Elasticsearch_Namespaces_WatcherNamespacestart_start]]
.`start(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_Namespaces_WatcherNamespacestats_stats]]
.`stats(array $params = [])`
****
[source,php]
----
/*
$params['metric']           = (list) Controls what additional stat metrics should be include in the response
$params['emit_stacktraces'] = (boolean) Emits stack traces of currently running watches
*/
----
****



[[Elasticsearch_Namespaces_WatcherNamespacestop_stop]]
.`stop(array $params = [])`
****
[source,php]
----
/*
*/
----
****


