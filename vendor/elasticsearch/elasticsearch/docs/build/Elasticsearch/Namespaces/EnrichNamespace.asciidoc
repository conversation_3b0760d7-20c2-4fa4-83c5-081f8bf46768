

[[Elasticsearch_Namespaces_EnrichNamespace]]
=== Elasticsearch\Namespaces\EnrichNamespace



Class EnrichNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_EnrichNamespacedeletePolicy_deletePolicy,`deletePolicy()`>>
* <<Elasticsearch_Namespaces_EnrichNamespaceexecutePolicy_executePolicy,`executePolicy()`>>
* <<Elasticsearch_Namespaces_EnrichNamespacegetPolicy_getPolicy,`getPolicy()`>>
* <<Elasticsearch_Namespaces_EnrichNamespaceputPolicy_putPolicy,`putPolicy()`>>
* <<Elasticsearch_Namespaces_EnrichNamespacestats_stats,`stats()`>>



[[Elasticsearch_Namespaces_EnrichNamespacedeletePolicy_deletePolicy]]
.`deletePolicy(array $params = [])`
****
[source,php]
----
/*
$params['name'] = (string) The name of the enrich policy
*/
----
****



[[Elasticsearch_Namespaces_EnrichNamespaceexecutePolicy_executePolicy]]
.`executePolicy(array $params = [])`
****
[source,php]
----
/*
$params['name']                = (string) The name of the enrich policy
$params['wait_for_completion'] = (boolean) Should the request should block until the execution is complete. (Default = true)
*/
----
****



[[Elasticsearch_Namespaces_EnrichNamespacegetPolicy_getPolicy]]
.`getPolicy(array $params = [])`
****
[source,php]
----
/*
$params['name'] = (list) A comma-separated list of enrich policy names
*/
----
****



[[Elasticsearch_Namespaces_EnrichNamespaceputPolicy_putPolicy]]
.`putPolicy(array $params = [])`
****
[source,php]
----
/*
$params['name'] = (string) The name of the enrich policy
$params['body'] = (array) The enrich policy to register (Required)
*/
----
****



[[Elasticsearch_Namespaces_EnrichNamespacestats_stats]]
.`stats(array $params = [])`
****
[source,php]
----
/*
*/
----
****


