

[[Elasticsearch_Namespaces_GraphNamespace]]
=== Elasticsearch\Namespaces\GraphNamespace



Class GraphNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_GraphNamespaceexplore_explore,`explore()`>>



[[Elasticsearch_Namespaces_GraphNamespaceexplore_explore]]
.`explore(array $params = [])`
****
[source,php]
----
/*
$params['index']   = (list) A comma-separated list of index names to search; use `_all` or empty string to perform the operation on all indices (Required)
$params['type']    = DEPRECATED (list) A comma-separated list of document types to search; leave empty to perform the operation on all types
$params['routing'] = (string) Specific routing value
$params['timeout'] = (time) Explicit operation timeout
$params['body']    = (array) Graph Query DSL
*/
----
****


