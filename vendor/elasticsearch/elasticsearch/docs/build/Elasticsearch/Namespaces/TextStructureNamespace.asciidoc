

[[Elasticsearch_Namespaces_TextStructureNamespace]]
=== Elasticsearch\Namespaces\TextStructureNamespace



Class TextStructureNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_TextStructureNamespacefindStructure_findStructure,`findStructure()`>>



[[Elasticsearch_Namespaces_TextStructureNamespacefindStructure_findStructure]]
.`findStructure(array $params = [])`
****
[source,php]
----
/*
$params['lines_to_sample']       = (int) How many lines of the file should be included in the analysis (Default = 1000)
$params['line_merge_size_limit'] = (int) Maximum number of characters permitted in a single message when lines are merged to create messages. (Default = 10000)
$params['timeout']               = (time) Timeout after which the analysis will be aborted (Default = 25s)
$params['charset']               = (string) Optional parameter to specify the character set of the file
$params['format']                = (enum) Optional parameter to specify the high level file format (Options = ndjson,xml,delimited,semi_structured_text)
$params['has_header_row']        = (boolean) Optional parameter to specify whether a delimited file includes the column names in its first row
$params['column_names']          = (list) Optional parameter containing a comma separated list of the column names for a delimited file
$params['delimiter']             = (string) Optional parameter to specify the delimiter character for a delimited file - must be a single character
$params['quote']                 = (string) Optional parameter to specify the quote character for a delimited file - must be a single character
$params['should_trim_fields']    = (boolean) Optional parameter to specify whether the values between delimiters in a delimited file should have whitespace trimmed from them
$params['grok_pattern']          = (string) Optional parameter to specify the Grok pattern that should be used to extract fields from messages in a semi-structured text file
$params['timestamp_field']       = (string) Optional parameter to specify the timestamp field in the file
$params['timestamp_format']      = (string) Optional parameter to specify the timestamp format in the file - may be either a Joda or Java time format
$params['explain']               = (boolean) Whether to include a commentary on how the structure was derived (Default = false)
$params['body']                  = (array) The contents of the file to be analyzed (Required)
*/
----
****


