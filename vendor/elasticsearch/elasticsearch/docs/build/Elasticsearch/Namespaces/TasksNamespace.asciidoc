

[[Elasticsearch_Namespaces_TasksNamespace]]
=== Elasticsearch\Namespaces\TasksNamespace



Class TasksNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_TasksNamespacecancel_cancel,`cancel()`>>
* <<Elasticsearch_Namespaces_TasksNamespaceget_get,`get()`>>
* <<Elasticsearch_Namespaces_TasksNamespacelist_list,`list()`>>
* <<Elasticsearch_Namespaces_TasksNamespacetasksList_tasksList,`tasksList()`>>



[[Elasticsearch_Namespaces_TasksNamespacecancel_cancel]]
.`cancel(array $params = [])`
*NOTE:* This API is EXPERIMENTAL and may be changed or removed completely in a future release
****
[source,php]
----
/*
$params['task_id']             = (string) Cancel the task with specified task id (node_id:task_number)
$params['nodes']               = (list) A comma-separated list of node IDs or names to limit the returned information; use `_local` to return information from the node you're connecting to, leave empty to get information from all nodes
$params['actions']             = (list) A comma-separated list of actions that should be cancelled. Leave empty to cancel all.
*/
----
****



[[Elasticsearch_Namespaces_TasksNamespaceget_get]]
.`get(array $params = [])`
*NOTE:* This API is EXPERIMENTAL and may be changed or removed completely in a future release
****
[source,php]
----
/*
$params['task_id']             = (string) Return the task with specified id (node_id:task_number)
$params['wait_for_completion'] = (boolean) Wait for the matching tasks to complete (default: false)
$params['timeout']             = (time) Explicit operation timeout
*/
----
****



[[Elasticsearch_Namespaces_TasksNamespacelist_list]]
.`list(array $params = [])`
*NOTE:* This API is EXPERIMENTAL and may be changed or removed completely in a future release
****
[source,php]
----
/*
$params['nodes']               = (list) A comma-separated list of node IDs or names to limit the returned information; use `_local` to return information from the node you're connecting to, leave empty to get information from all nodes
$params['actions']             = (list) A comma-separated list of actions that should be returned. Leave empty to return all.
*/
----
****



[[Elasticsearch_Namespaces_TasksNamespacetasksList_tasksList]]
.`tasksList(array $params = [])`
****
[source,php]
----
/*
Proxy function to list() to prevent BC break since 7.4.0
*/
----
****


