

[[Elasticsearch_Namespaces_SnapshotNamespace]]
=== Elasticsearch\Namespaces\SnapshotNamespace



Class SnapshotNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_SnapshotNamespacecleanupRepository_cleanupRepository,`cleanupRepository()`>>
* <<Elasticsearch_Namespaces_SnapshotNamespaceclone_clone,`clone()`>>
* <<Elasticsearch_Namespaces_SnapshotNamespacecreate_create,`create()`>>
* <<Elasticsearch_Namespaces_SnapshotNamespacecreateRepository_createRepository,`createRepository()`>>
* <<Elasticsearch_Namespaces_SnapshotNamespacedelete_delete,`delete()`>>
* <<Elasticsearch_Namespaces_SnapshotNamespacedeleteRepository_deleteRepository,`deleteRepository()`>>
* <<Elasticsearch_Namespaces_SnapshotNamespaceget_get,`get()`>>
* <<Elasticsearch_Namespaces_SnapshotNamespacegetRepository_getRepository,`getRepository()`>>
* <<Elasticsearch_Namespaces_SnapshotNamespacerestore_restore,`restore()`>>
* <<Elasticsearch_Namespaces_SnapshotNamespacestatus_status,`status()`>>
* <<Elasticsearch_Namespaces_SnapshotNamespaceverifyRepository_verifyRepository,`verifyRepository()`>>



[[Elasticsearch_Namespaces_SnapshotNamespacecleanupRepository_cleanupRepository]]
.`cleanupRepository(array $params = [])`
****
[source,php]
----
/*
$params['repository']     = (string) A repository name
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
$params['timeout']        = (time) Explicit operation timeout
*/
----
****



[[Elasticsearch_Namespaces_SnapshotNamespaceclone_clone]]
.`clone(array $params = [])`
****
[source,php]
----
/*
$params['repository']      = (string) A repository name
$params['snapshot']        = (string) The name of the snapshot to clone from
$params['target_snapshot'] = (string) The name of the cloned snapshot to create
$params['master_timeout']  = (time) Explicit operation timeout for connection to master node
$params['body']            = (array) The snapshot clone definition (Required)
*/
----
****



[[Elasticsearch_Namespaces_SnapshotNamespacecreate_create]]
.`create(array $params = [])`
****
[source,php]
----
/*
$params['repository']          = (string) A repository name
$params['snapshot']            = (string) A snapshot name
$params['master_timeout']      = (time) Explicit operation timeout for connection to master node
$params['wait_for_completion'] = (boolean) Should this request wait until the operation has completed before returning (Default = false)
$params['body']                = (array) The snapshot definition
*/
----
****



[[Elasticsearch_Namespaces_SnapshotNamespacecreateRepository_createRepository]]
.`createRepository(array $params = [])`
****
[source,php]
----
/*
$params['repository']     = (string) A repository name
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
$params['timeout']        = (time) Explicit operation timeout
$params['verify']         = (boolean) Whether to verify the repository after creation
$params['body']           = (array) The repository definition (Required)
*/
----
****



[[Elasticsearch_Namespaces_SnapshotNamespacedelete_delete]]
.`delete(array $params = [])`
****
[source,php]
----
/*
$params['repository']     = (string) A repository name
$params['snapshot']       = (string) A snapshot name
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
*/
----
****



[[Elasticsearch_Namespaces_SnapshotNamespacedeleteRepository_deleteRepository]]
.`deleteRepository(array $params = [])`
****
[source,php]
----
/*
$params['repository']     = (list) Name of the snapshot repository to unregister. Wildcard (`*`) patterns are supported.
*/
----
****



[[Elasticsearch_Namespaces_SnapshotNamespaceget_get]]
.`get(array $params = [])`
****
[source,php]
----
/*
$params['repository']         = (string) A repository name
$params['snapshot']           = (list) A comma-separated list of snapshot names
$params['master_timeout']     = (time) Explicit operation timeout for connection to master node
$params['ignore_unavailable'] = (boolean) Whether to ignore unavailable snapshots, defaults to false which means a SnapshotMissingException is thrown
$params['verbose']            = (boolean) Whether to show verbose snapshot info or only show the basic info found in the repository index blob
*/
----
****



[[Elasticsearch_Namespaces_SnapshotNamespacegetRepository_getRepository]]
.`getRepository(array $params = [])`
****
[source,php]
----
/*
$params['repository']     = (list) A comma-separated list of repository names
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
$params['local']          = (boolean) Return local information, do not retrieve the state from master node (default: false)
*/
----
****



[[Elasticsearch_Namespaces_SnapshotNamespacerestore_restore]]
.`restore(array $params = [])`
****
[source,php]
----
/*
$params['repository']          = (string) A repository name
$params['snapshot']            = (string) A snapshot name
$params['master_timeout']      = (time) Explicit operation timeout for connection to master node
$params['wait_for_completion'] = (boolean) Should this request wait until the operation has completed before returning (Default = false)
$params['body']                = (array) Details of what to restore
*/
----
****



[[Elasticsearch_Namespaces_SnapshotNamespacestatus_status]]
.`status(array $params = [])`
****
[source,php]
----
/*
$params['repository']         = (string) A repository name
$params['snapshot']           = (list) A comma-separated list of snapshot names
$params['master_timeout']     = (time) Explicit operation timeout for connection to master node
$params['ignore_unavailable'] = (boolean) Whether to ignore unavailable snapshots, defaults to false which means a SnapshotMissingException is thrown
*/
----
****



[[Elasticsearch_Namespaces_SnapshotNamespaceverifyRepository_verifyRepository]]
.`verifyRepository(array $params = [])`
****
[source,php]
----
/*
$params['repository']     = (string) A repository name
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
$params['timeout']        = (time) Explicit operation timeout
*/
----
****


