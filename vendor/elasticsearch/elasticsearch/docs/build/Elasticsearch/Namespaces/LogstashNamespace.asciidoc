

[[Elasticsearch_Namespaces_LogstashNamespace]]
=== Elasticsearch\Namespaces\LogstashNamespace



Class LogstashNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_LogstashNamespacedeletePipeline_deletePipeline,`deletePipeline()`>>
* <<Elasticsearch_Namespaces_LogstashNamespacegetPipeline_getPipeline,`getPipeline()`>>
* <<Elasticsearch_Namespaces_LogstashNamespaceputPipeline_putPipeline,`putPipeline()`>>



[[Elasticsearch_Namespaces_LogstashNamespacedeletePipeline_deletePipeline]]
.`deletePipeline(array $params = [])`
****
[source,php]
----
/*
$params['id'] = (string) The ID of the Pipeline
*/
----
****



[[Elasticsearch_Namespaces_LogstashNamespacegetPipeline_getPipeline]]
.`getPipeline(array $params = [])`
****
[source,php]
----
/*
$params['id'] = (string) A comma-separated list of Pipeline IDs
*/
----
****



[[Elasticsearch_Namespaces_LogstashNamespaceputPipeline_putPipeline]]
.`putPipeline(array $params = [])`
****
[source,php]
----
/*
$params['id']   = (string) The ID of the Pipeline
$params['body'] = (array) The Pipeline to add or update (Required)
*/
----
****


