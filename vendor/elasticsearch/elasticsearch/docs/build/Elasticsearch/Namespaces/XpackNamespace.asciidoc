

[[Elasticsearch_Namespaces_XpackNamespace]]
=== Elasticsearch\Namespaces\XpackNamespace



Class XpackNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_XpackNamespaceinfo_info,`info()`>>
* <<Elasticsearch_Namespaces_XpackNamespaceusage_usage,`usage()`>>



[[Elasticsearch_Namespaces_XpackNamespaceinfo_info]]
.`info(array $params = [])`
****
[source,php]
----
/*
$params['categories']        = (list) Comma-separated list of info categories. Can be any of: build, license, features
$params['accept_enterprise'] = (boolean) If an enterprise license is installed, return the type and mode as 'enterprise' (default: false)
*/
----
****



[[Elasticsearch_Namespaces_XpackNamespaceusage_usage]]
.`usage(array $params = [])`
****
[source,php]
----
/*
$params['master_timeout'] = (time) Specify timeout for watch write operation
*/
----
****


