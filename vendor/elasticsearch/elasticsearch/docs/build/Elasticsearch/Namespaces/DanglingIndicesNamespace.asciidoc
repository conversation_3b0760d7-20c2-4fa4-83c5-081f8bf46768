

[[Elasticsearch_Namespaces_DanglingIndicesNamespace]]
=== Elasticsearch\Namespaces\DanglingIndicesNamespace



Class DanglingIndicesNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_DanglingIndicesNamespacedeleteDanglingIndex_deleteDanglingIndex,`deleteDanglingIndex()`>>
* <<Elasticsearch_Namespaces_DanglingIndicesNamespaceimportDanglingIndex_importDanglingIndex,`importDanglingIndex()`>>
* <<Elasticsearch_Namespaces_DanglingIndicesNamespacelistDanglingIndices_listDanglingIndices,`listDanglingIndices()`>>



[[Elasticsearch_Namespaces_DanglingIndicesNamespacedeleteDanglingIndex_deleteDanglingIndex]]
.`deleteDanglingIndex(array $params = [])`
****
[source,php]
----
/*
$params['index_uuid']       = (string) The UUID of the dangling index
$params['accept_data_loss'] = (boolean) Must be set to true in order to delete the dangling index
$params['timeout']          = (time) Explicit operation timeout
$params['master_timeout']   = (time) Specify timeout for connection to master
*/
----
****



[[Elasticsearch_Namespaces_DanglingIndicesNamespaceimportDanglingIndex_importDanglingIndex]]
.`importDanglingIndex(array $params = [])`
****
[source,php]
----
/*
$params['index_uuid']       = (string) The UUID of the dangling index
$params['accept_data_loss'] = (boolean) Must be set to true in order to import the dangling index
$params['timeout']          = (time) Explicit operation timeout
$params['master_timeout']   = (time) Specify timeout for connection to master
*/
----
****



[[Elasticsearch_Namespaces_DanglingIndicesNamespacelistDanglingIndices_listDanglingIndices]]
.`listDanglingIndices(array $params = [])`
****
[source,php]
----
/*
*/
----
****


