

[[Elasticsearch_Namespaces_DataFrameTransformDeprecatedNamespace]]
=== Elasticsearch\Namespaces\DataFrameTransformDeprecatedNamespace



Class DataFrameTransformDeprecatedNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_DataFrameTransformDeprecatedNamespacedeleteTransform_deleteTransform,`deleteTransform()`>>
* <<Elasticsearch_Namespaces_DataFrameTransformDeprecatedNamespacegetTransform_getTransform,`getTransform()`>>
* <<Elasticsearch_Namespaces_DataFrameTransformDeprecatedNamespacegetTransformStats_getTransformStats,`getTransformStats()`>>
* <<Elasticsearch_Namespaces_DataFrameTransformDeprecatedNamespacepreviewTransform_previewTransform,`previewTransform()`>>
* <<Elasticsearch_Namespaces_DataFrameTransformDeprecatedNamespaceputTransform_putTransform,`putTransform()`>>
* <<Elasticsearch_Namespaces_DataFrameTransformDeprecatedNamespacestartTransform_startTransform,`startTransform()`>>
* <<Elasticsearch_Namespaces_DataFrameTransformDeprecatedNamespacestopTransform_stopTransform,`stopTransform()`>>
* <<Elasticsearch_Namespaces_DataFrameTransformDeprecatedNamespaceupdateTransform_updateTransform,`updateTransform()`>>



[[Elasticsearch_Namespaces_DataFrameTransformDeprecatedNamespacedeleteTransform_deleteTransform]]
.`deleteTransform(array $params = [])`
*NOTE:* This API is BETA and may change in ways that are not backwards compatible
****
[source,php]
----
/*
$params['transform_id'] = (string) The id of the transform to delete
$params['force']        = (boolean) When `true`, the transform is deleted regardless of its current state. The default value is `false`, meaning that the transform must be `stopped` before it can be deleted.
*/
----
****



[[Elasticsearch_Namespaces_DataFrameTransformDeprecatedNamespacegetTransform_getTransform]]
.`getTransform(array $params = [])`
*NOTE:* This API is BETA and may change in ways that are not backwards compatible
****
[source,php]
----
/*
$params['transform_id']      = (string) The id or comma delimited list of id expressions of the transforms to get, '_all' or '*' implies get all transforms
$params['from']              = (int) skips a number of transform configs, defaults to 0
$params['size']              = (int) specifies a max number of transforms to get, defaults to 100
$params['allow_no_match']    = (boolean) Whether to ignore if a wildcard expression matches no transforms. (This includes `_all` string or when no transforms have been specified)
$params['exclude_generated'] = (boolean) Omits generated fields. Allows transform configurations to be easily copied between clusters and within the same cluster (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_DataFrameTransformDeprecatedNamespacegetTransformStats_getTransformStats]]
.`getTransformStats(array $params = [])`
*NOTE:* This API is BETA and may change in ways that are not backwards compatible
****
[source,php]
----
/*
$params['transform_id']   = (string) The id of the transform for which to get stats. '_all' or '*' implies all transforms
$params['from']           = (number) skips a number of transform stats, defaults to 0
$params['size']           = (number) specifies a max number of transform stats to get, defaults to 100
$params['allow_no_match'] = (boolean) Whether to ignore if a wildcard expression matches no transforms. (This includes `_all` string or when no transforms have been specified)
*/
----
****



[[Elasticsearch_Namespaces_DataFrameTransformDeprecatedNamespacepreviewTransform_previewTransform]]
.`previewTransform(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_Namespaces_DataFrameTransformDeprecatedNamespaceputTransform_putTransform]]
.`putTransform(array $params = [])`
*NOTE:* This API is BETA and may change in ways that are not backwards compatible
****
[source,php]
----
/*
$params['transform_id']     = (string) The id of the new transform.
*/
----
****



[[Elasticsearch_Namespaces_DataFrameTransformDeprecatedNamespacestartTransform_startTransform]]
.`startTransform(array $params = [])`
*NOTE:* This API is BETA and may change in ways that are not backwards compatible
****
[source,php]
----
/*
$params['transform_id'] = (string) The id of the transform to start
$params['timeout']      = (time) Controls the time to wait for the transform to start
*/
----
****



[[Elasticsearch_Namespaces_DataFrameTransformDeprecatedNamespacestopTransform_stopTransform]]
.`stopTransform(array $params = [])`
*NOTE:* This API is BETA and may change in ways that are not backwards compatible
****
[source,php]
----
/*
$params['transform_id']        = (string) The id of the transform to stop
$params['wait_for_completion'] = (boolean) Whether to wait for the transform to fully stop before returning or not. Default to false
$params['timeout']             = (time) Controls the time to wait until the transform has stopped. Default to 30 seconds
$params['allow_no_match']      = (boolean) Whether to ignore if a wildcard expression matches no transforms. (This includes `_all` string or when no transforms have been specified)
*/
----
****



[[Elasticsearch_Namespaces_DataFrameTransformDeprecatedNamespaceupdateTransform_updateTransform]]
.`updateTransform(array $params = [])`
*NOTE:* This API is BETA and may change in ways that are not backwards compatible
****
[source,php]
----
/*
$params['transform_id']     = (string) The id of the transform.
*/
----
****


