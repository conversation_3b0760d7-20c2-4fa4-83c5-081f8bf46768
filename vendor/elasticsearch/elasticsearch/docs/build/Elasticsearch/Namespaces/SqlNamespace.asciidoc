

[[Elasticsearch_Namespaces_SqlNamespace]]
=== Elasticsearch\Namespaces\SqlNamespace



Class SqlNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_SqlNamespaceclearCursor_clearCursor,`clearCursor()`>>
* <<Elasticsearch_Namespaces_SqlNamespacequery_query,`query()`>>
* <<Elasticsearch_Namespaces_SqlNamespacetranslate_translate,`translate()`>>



[[Elasticsearch_Namespaces_SqlNamespaceclearCursor_clearCursor]]
.`clearCursor(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_Namespaces_SqlNamespacequery_query]]
.`query(array $params = [])`
****
[source,php]
----
/*
$params['format'] = (string) a short version of the Accept header, e.g. json, yaml
$params['body']   = (array) Use the `query` element to start a query. Use the `cursor` element to continue a query. (Required)
*/
----
****



[[Elasticsearch_Namespaces_SqlNamespacetranslate_translate]]
.`translate(array $params = [])`
****
[source,php]
----
/*
$params['body'] = (array) Specify the query in the `query` element. (Required)
*/
----
****


