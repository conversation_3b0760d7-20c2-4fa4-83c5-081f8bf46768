

[[Elasticsearch_Namespaces_MlNamespace]]
=== Elasticsearch\Namespaces\MlNamespace



Class MlNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_MlNamespacecloseJob_closeJob,`closeJob()`>>
* <<Elasticsearch_Namespaces_MlNamespacedeleteCalendar_deleteCalendar,`deleteCalendar()`>>
* <<Elasticsearch_Namespaces_MlNamespacedeleteCalendarEvent_deleteCalendarEvent,`deleteCalendarEvent()`>>
* <<Elasticsearch_Namespaces_MlNamespacedeleteCalendarJob_deleteCalendarJob,`deleteCalendarJob()`>>
* <<Elasticsearch_Namespaces_MlNamespacedeleteDataFrameAnalytics_deleteDataFrameAnalytics,`deleteDataFrameAnalytics()`>>
* <<Elasticsearch_Namespaces_MlNamespacedeleteDatafeed_deleteDatafeed,`deleteDatafeed()`>>
* <<Elasticsearch_Namespaces_MlNamespacedeleteExpiredData_deleteExpiredData,`deleteExpiredData()`>>
* <<Elasticsearch_Namespaces_MlNamespacedeleteFilter_deleteFilter,`deleteFilter()`>>
* <<Elasticsearch_Namespaces_MlNamespacedeleteForecast_deleteForecast,`deleteForecast()`>>
* <<Elasticsearch_Namespaces_MlNamespacedeleteJob_deleteJob,`deleteJob()`>>
* <<Elasticsearch_Namespaces_MlNamespacedeleteModelSnapshot_deleteModelSnapshot,`deleteModelSnapshot()`>>
* <<Elasticsearch_Namespaces_MlNamespacedeleteTrainedModel_deleteTrainedModel,`deleteTrainedModel()`>>
* <<Elasticsearch_Namespaces_MlNamespaceestimateModelMemory_estimateModelMemory,`estimateModelMemory()`>>
* <<Elasticsearch_Namespaces_MlNamespaceevaluateDataFrame_evaluateDataFrame,`evaluateDataFrame()`>>
* <<Elasticsearch_Namespaces_MlNamespaceexplainDataFrameAnalytics_explainDataFrameAnalytics,`explainDataFrameAnalytics()`>>
* <<Elasticsearch_Namespaces_MlNamespacefindFileStructure_findFileStructure,`findFileStructure()`>>
* <<Elasticsearch_Namespaces_MlNamespaceflushJob_flushJob,`flushJob()`>>
* <<Elasticsearch_Namespaces_MlNamespaceforecast_forecast,`forecast()`>>
* <<Elasticsearch_Namespaces_MlNamespacegetBuckets_getBuckets,`getBuckets()`>>
* <<Elasticsearch_Namespaces_MlNamespacegetCalendarEvents_getCalendarEvents,`getCalendarEvents()`>>
* <<Elasticsearch_Namespaces_MlNamespacegetCalendars_getCalendars,`getCalendars()`>>
* <<Elasticsearch_Namespaces_MlNamespacegetCategories_getCategories,`getCategories()`>>
* <<Elasticsearch_Namespaces_MlNamespacegetDataFrameAnalytics_getDataFrameAnalytics,`getDataFrameAnalytics()`>>
* <<Elasticsearch_Namespaces_MlNamespacegetDataFrameAnalyticsStats_getDataFrameAnalyticsStats,`getDataFrameAnalyticsStats()`>>
* <<Elasticsearch_Namespaces_MlNamespacegetDatafeedStats_getDatafeedStats,`getDatafeedStats()`>>
* <<Elasticsearch_Namespaces_MlNamespacegetDatafeeds_getDatafeeds,`getDatafeeds()`>>
* <<Elasticsearch_Namespaces_MlNamespacegetFilters_getFilters,`getFilters()`>>
* <<Elasticsearch_Namespaces_MlNamespacegetInfluencers_getInfluencers,`getInfluencers()`>>
* <<Elasticsearch_Namespaces_MlNamespacegetJobStats_getJobStats,`getJobStats()`>>
* <<Elasticsearch_Namespaces_MlNamespacegetJobs_getJobs,`getJobs()`>>
* <<Elasticsearch_Namespaces_MlNamespacegetModelSnapshots_getModelSnapshots,`getModelSnapshots()`>>
* <<Elasticsearch_Namespaces_MlNamespacegetOverallBuckets_getOverallBuckets,`getOverallBuckets()`>>
* <<Elasticsearch_Namespaces_MlNamespacegetRecords_getRecords,`getRecords()`>>
* <<Elasticsearch_Namespaces_MlNamespacegetTrainedModels_getTrainedModels,`getTrainedModels()`>>
* <<Elasticsearch_Namespaces_MlNamespacegetTrainedModelsStats_getTrainedModelsStats,`getTrainedModelsStats()`>>
* <<Elasticsearch_Namespaces_MlNamespaceinfo_info,`info()`>>
* <<Elasticsearch_Namespaces_MlNamespaceopenJob_openJob,`openJob()`>>
* <<Elasticsearch_Namespaces_MlNamespacepostCalendarEvents_postCalendarEvents,`postCalendarEvents()`>>
* <<Elasticsearch_Namespaces_MlNamespacepostData_postData,`postData()`>>
* <<Elasticsearch_Namespaces_MlNamespacepreviewDatafeed_previewDatafeed,`previewDatafeed()`>>
* <<Elasticsearch_Namespaces_MlNamespaceputCalendar_putCalendar,`putCalendar()`>>
* <<Elasticsearch_Namespaces_MlNamespaceputCalendarJob_putCalendarJob,`putCalendarJob()`>>
* <<Elasticsearch_Namespaces_MlNamespaceputDataFrameAnalytics_putDataFrameAnalytics,`putDataFrameAnalytics()`>>
* <<Elasticsearch_Namespaces_MlNamespaceputDatafeed_putDatafeed,`putDatafeed()`>>
* <<Elasticsearch_Namespaces_MlNamespaceputFilter_putFilter,`putFilter()`>>
* <<Elasticsearch_Namespaces_MlNamespaceputJob_putJob,`putJob()`>>
* <<Elasticsearch_Namespaces_MlNamespaceputTrainedModel_putTrainedModel,`putTrainedModel()`>>
* <<Elasticsearch_Namespaces_MlNamespacerevertModelSnapshot_revertModelSnapshot,`revertModelSnapshot()`>>
* <<Elasticsearch_Namespaces_MlNamespacesetUpgradeMode_setUpgradeMode,`setUpgradeMode()`>>
* <<Elasticsearch_Namespaces_MlNamespacestartDataFrameAnalytics_startDataFrameAnalytics,`startDataFrameAnalytics()`>>
* <<Elasticsearch_Namespaces_MlNamespacestartDatafeed_startDatafeed,`startDatafeed()`>>
* <<Elasticsearch_Namespaces_MlNamespacestopDataFrameAnalytics_stopDataFrameAnalytics,`stopDataFrameAnalytics()`>>
* <<Elasticsearch_Namespaces_MlNamespacestopDatafeed_stopDatafeed,`stopDatafeed()`>>
* <<Elasticsearch_Namespaces_MlNamespaceupdateDataFrameAnalytics_updateDataFrameAnalytics,`updateDataFrameAnalytics()`>>
* <<Elasticsearch_Namespaces_MlNamespaceupdateDatafeed_updateDatafeed,`updateDatafeed()`>>
* <<Elasticsearch_Namespaces_MlNamespaceupdateFilter_updateFilter,`updateFilter()`>>
* <<Elasticsearch_Namespaces_MlNamespaceupdateJob_updateJob,`updateJob()`>>
* <<Elasticsearch_Namespaces_MlNamespaceupdateModelSnapshot_updateModelSnapshot,`updateModelSnapshot()`>>
* <<Elasticsearch_Namespaces_MlNamespaceupgradeJobSnapshot_upgradeJobSnapshot,`upgradeJobSnapshot()`>>
* <<Elasticsearch_Namespaces_MlNamespacevalidate_validate,`validate()`>>
* <<Elasticsearch_Namespaces_MlNamespacevalidateDetector_validateDetector,`validateDetector()`>>



[[Elasticsearch_Namespaces_MlNamespacecloseJob_closeJob]]
.`closeJob(array $params = [])`
****
[source,php]
----
/*
$params['job_id']         = (string) The name of the job to close
$params['allow_no_match'] = (boolean) Whether to ignore if a wildcard expression matches no jobs. (This includes `_all` string or when no jobs have been specified)
$params['allow_no_jobs']  = (boolean) Whether to ignore if a wildcard expression matches no jobs. (This includes `_all` string or when no jobs have been specified)
$params['force']          = (boolean) True if the job should be forcefully closed
$params['timeout']        = (time) Controls the time to wait until a job has closed. Default to 30 minutes
$params['body']           = (array) The URL params optionally sent in the body
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacedeleteCalendar_deleteCalendar]]
.`deleteCalendar(array $params = [])`
****
[source,php]
----
/*
$params['calendar_id'] = (string) The ID of the calendar to delete
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacedeleteCalendarEvent_deleteCalendarEvent]]
.`deleteCalendarEvent(array $params = [])`
****
[source,php]
----
/*
$params['calendar_id'] = (string) The ID of the calendar to modify
$params['event_id']    = (string) The ID of the event to remove from the calendar
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacedeleteCalendarJob_deleteCalendarJob]]
.`deleteCalendarJob(array $params = [])`
****
[source,php]
----
/*
$params['calendar_id'] = (string) The ID of the calendar to modify
$params['job_id']      = (string) The ID of the job to remove from the calendar
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacedeleteDataFrameAnalytics_deleteDataFrameAnalytics]]
.`deleteDataFrameAnalytics(array $params = [])`
*NOTE:* This API is BETA and may change in ways that are not backwards compatible
****
[source,php]
----
/*
$params['id']      = (string) The ID of the data frame analytics to delete
$params['force']   = (boolean) True if the job should be forcefully deleted (Default = false)
$params['timeout'] = (time) Controls the time to wait until a job is deleted. Defaults to 1 minute
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacedeleteDatafeed_deleteDatafeed]]
.`deleteDatafeed(array $params = [])`
****
[source,php]
----
/*
$params['datafeed_id'] = (string) The ID of the datafeed to delete
$params['force']       = (boolean) True if the datafeed should be forcefully deleted
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacedeleteExpiredData_deleteExpiredData]]
.`deleteExpiredData(array $params = [])`
****
[source,php]
----
/*
$params['job_id']              = (string) The ID of the job(s) to perform expired data hygiene for
$params['requests_per_second'] = (number) The desired requests per second for the deletion processes.
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacedeleteFilter_deleteFilter]]
.`deleteFilter(array $params = [])`
****
[source,php]
----
/*
$params['filter_id'] = (string) The ID of the filter to delete
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacedeleteForecast_deleteForecast]]
.`deleteForecast(array $params = [])`
****
[source,php]
----
/*
$params['job_id']             = (string) The ID of the job from which to delete forecasts (Required)
$params['forecast_id']        = (string) The ID of the forecast to delete, can be comma delimited list. Leaving blank implies `_all`
$params['allow_no_forecasts'] = (boolean) Whether to ignore if `_all` matches no forecasts
$params['timeout']            = (time) Controls the time to wait until the forecast(s) are deleted. Default to 30 seconds
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacedeleteJob_deleteJob]]
.`deleteJob(array $params = [])`
****
[source,php]
----
/*
$params['job_id']              = (string) The ID of the job to delete
$params['force']               = (boolean) True if the job should be forcefully deleted (Default = false)
$params['wait_for_completion'] = (boolean) Should this request wait until the operation has completed before returning (Default = true)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacedeleteModelSnapshot_deleteModelSnapshot]]
.`deleteModelSnapshot(array $params = [])`
****
[source,php]
----
/*
$params['job_id']      = (string) The ID of the job to fetch
$params['snapshot_id'] = (string) The ID of the snapshot to delete
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacedeleteTrainedModel_deleteTrainedModel]]
.`deleteTrainedModel(array $params = [])`
*NOTE:* This API is BETA and may change in ways that are not backwards compatible
****
[source,php]
----
/*
$params['model_id'] = (string) The ID of the trained model to delete
*/
----
****



[[Elasticsearch_Namespaces_MlNamespaceestimateModelMemory_estimateModelMemory]]
.`estimateModelMemory(array $params = [])`
****
[source,php]
----
/*
$params['body'] = (array) The analysis config, plus cardinality estimates for fields it references (Required)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespaceevaluateDataFrame_evaluateDataFrame]]
.`evaluateDataFrame(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_Namespaces_MlNamespaceexplainDataFrameAnalytics_explainDataFrameAnalytics]]
.`explainDataFrameAnalytics(array $params = [])`
*NOTE:* This API is BETA and may change in ways that are not backwards compatible
****
[source,php]
----
/*
$params['id']   = (string) The ID of the data frame analytics to explain
$params['body'] = (array) The data frame analytics config to explain
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacefindFileStructure_findFileStructure]]
.`findFileStructure(array $params = [])`
*NOTE:* This API is EXPERIMENTAL and may be changed or removed completely in a future release
****
[source,php]
----
/*
$params['lines_to_sample']       = (int) How many lines of the file should be included in the analysis (Default = 1000)
$params['line_merge_size_limit'] = (int) Maximum number of characters permitted in a single message when lines are merged to create messages. (Default = 10000)
$params['timeout']               = (time) Timeout after which the analysis will be aborted (Default = 25s)
$params['charset']               = (string) Optional parameter to specify the character set of the file
$params['format']                = (enum) Optional parameter to specify the high level file format (Options = ndjson,xml,delimited,semi_structured_text)
$params['has_header_row']        = (boolean) Optional parameter to specify whether a delimited file includes the column names in its first row
$params['column_names']          = (list) Optional parameter containing a comma separated list of the column names for a delimited file
$params['delimiter']             = (string) Optional parameter to specify the delimiter character for a delimited file - must be a single character
$params['quote']                 = (string) Optional parameter to specify the quote character for a delimited file - must be a single character
$params['should_trim_fields']    = (boolean) Optional parameter to specify whether the values between delimiters in a delimited file should have whitespace trimmed from them
$params['grok_pattern']          = (string) Optional parameter to specify the Grok pattern that should be used to extract fields from messages in a semi-structured text file
$params['timestamp_field']       = (string) Optional parameter to specify the timestamp field in the file
$params['timestamp_format']      = (string) Optional parameter to specify the timestamp format in the file - may be either a Joda or Java time format
$params['explain']               = (boolean) Whether to include a commentary on how the structure was derived (Default = false)
$params['body']                  = (array) The contents of the file to be analyzed (Required)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespaceflushJob_flushJob]]
.`flushJob(array $params = [])`
****
[source,php]
----
/*
$params['job_id']       = (string) The name of the job to flush
$params['calc_interim'] = (boolean) Calculates interim results for the most recent bucket or all buckets within the latency period
$params['start']        = (string) When used in conjunction with calc_interim, specifies the range of buckets on which to calculate interim results
$params['end']          = (string) When used in conjunction with calc_interim, specifies the range of buckets on which to calculate interim results
$params['advance_time'] = (string) Advances time to the given value generating results and updating the model for the advanced interval
$params['skip_time']    = (string) Skips time to the given value without generating results or updating the model for the skipped interval
$params['body']         = (array) Flush parameters
*/
----
****



[[Elasticsearch_Namespaces_MlNamespaceforecast_forecast]]
.`forecast(array $params = [])`
****
[source,php]
----
/*
$params['job_id']           = (string) The ID of the job to forecast for
$params['duration']         = (time) The duration of the forecast
$params['expires_in']       = (time) The time interval after which the forecast expires. Expired forecasts will be deleted at the first opportunity.
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacegetBuckets_getBuckets]]
.`getBuckets(array $params = [])`
****
[source,php]
----
/*
$params['job_id']          = (string) ID of the job to get bucket results from (Required)
$params['timestamp']       = (string) The timestamp of the desired single bucket result
$params['expand']          = (boolean) Include anomaly records
$params['exclude_interim'] = (boolean) Exclude interim results
$params['from']            = (int) skips a number of buckets
$params['size']            = (int) specifies a max number of buckets to get
$params['start']           = (string) Start time filter for buckets
$params['end']             = (string) End time filter for buckets
$params['anomaly_score']   = (double) Filter for the most anomalous buckets
$params['sort']            = (string) Sort buckets by a particular field
$params['desc']            = (boolean) Set the sort direction
$params['body']            = (array) Bucket selection details if not provided in URI
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacegetCalendarEvents_getCalendarEvents]]
.`getCalendarEvents(array $params = [])`
****
[source,php]
----
/*
$params['calendar_id'] = (string) The ID of the calendar containing the events
$params['job_id']      = (string) Get events for the job. When this option is used calendar_id must be '_all'
$params['start']       = (string) Get events after this time
$params['end']         = (date) Get events before this time
$params['from']        = (int) Skips a number of events
$params['size']        = (int) Specifies a max number of events to get
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacegetCalendars_getCalendars]]
.`getCalendars(array $params = [])`
****
[source,php]
----
/*
$params['calendar_id'] = (string) The ID of the calendar to fetch
$params['from']        = (int) skips a number of calendars
$params['size']        = (int) specifies a max number of calendars to get
$params['body']        = (array) The from and size parameters optionally sent in the body
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacegetCategories_getCategories]]
.`getCategories(array $params = [])`
****
[source,php]
----
/*
$params['job_id']                = (string) The name of the job (Required)
$params['category_id']           = (long) The identifier of the category definition of interest
$params['from']                  = (int) skips a number of categories
$params['size']                  = (int) specifies a max number of categories to get
$params['partition_field_value'] = (string) Specifies the partition to retrieve categories for. This is optional, and should never be used for jobs where per-partition categorization is disabled.
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacegetDataFrameAnalytics_getDataFrameAnalytics]]
.`getDataFrameAnalytics(array $params = [])`
*NOTE:* This API is BETA and may change in ways that are not backwards compatible
****
[source,php]
----
/*
$params['id']                = (string) The ID of the data frame analytics to fetch
$params['allow_no_match']    = (boolean) Whether to ignore if a wildcard expression matches no data frame analytics. (This includes `_all` string or when no data frame analytics have been specified) (Default = true)
$params['from']              = (int) skips a number of analytics (Default = 0)
$params['size']              = (int) specifies a max number of analytics to get (Default = 100)
$params['exclude_generated'] = (boolean) Omits fields that are illegal to set on data frame analytics PUT (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacegetDataFrameAnalyticsStats_getDataFrameAnalyticsStats]]
.`getDataFrameAnalyticsStats(array $params = [])`
*NOTE:* This API is BETA and may change in ways that are not backwards compatible
****
[source,php]
----
/*
$params['id']             = (string) The ID of the data frame analytics stats to fetch
$params['allow_no_match'] = (boolean) Whether to ignore if a wildcard expression matches no data frame analytics. (This includes `_all` string or when no data frame analytics have been specified) (Default = true)
$params['from']           = (int) skips a number of analytics (Default = 0)
$params['size']           = (int) specifies a max number of analytics to get (Default = 100)
$params['verbose']        = (boolean) whether the stats response should be verbose (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacegetDatafeedStats_getDatafeedStats]]
.`getDatafeedStats(array $params = [])`
****
[source,php]
----
/*
$params['datafeed_id']        = (string) The ID of the datafeeds stats to fetch
$params['allow_no_match']     = (boolean) Whether to ignore if a wildcard expression matches no datafeeds. (This includes `_all` string or when no datafeeds have been specified)
$params['allow_no_datafeeds'] = (boolean) Whether to ignore if a wildcard expression matches no datafeeds. (This includes `_all` string or when no datafeeds have been specified)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacegetDatafeeds_getDatafeeds]]
.`getDatafeeds(array $params = [])`
****
[source,php]
----
/*
$params['datafeed_id']        = (string) The ID of the datafeeds to fetch
$params['allow_no_match']     = (boolean) Whether to ignore if a wildcard expression matches no datafeeds. (This includes `_all` string or when no datafeeds have been specified)
$params['allow_no_datafeeds'] = (boolean) Whether to ignore if a wildcard expression matches no datafeeds. (This includes `_all` string or when no datafeeds have been specified)
$params['exclude_generated']  = (boolean) Omits fields that are illegal to set on datafeed PUT (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacegetFilters_getFilters]]
.`getFilters(array $params = [])`
****
[source,php]
----
/*
$params['filter_id'] = (string) The ID of the filter to fetch
$params['from']      = (int) skips a number of filters
$params['size']      = (int) specifies a max number of filters to get
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacegetInfluencers_getInfluencers]]
.`getInfluencers(array $params = [])`
****
[source,php]
----
/*
$params['job_id']           = (string) Identifier for the anomaly detection job
$params['exclude_interim']  = (boolean) Exclude interim results
$params['from']             = (int) skips a number of influencers
$params['size']             = (int) specifies a max number of influencers to get
$params['start']            = (string) start timestamp for the requested influencers
$params['end']              = (string) end timestamp for the requested influencers
$params['influencer_score'] = (double) influencer score threshold for the requested influencers
$params['sort']             = (string) sort field for the requested influencers
$params['desc']             = (boolean) whether the results should be sorted in decending order
$params['body']             = (array) Influencer selection criteria
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacegetJobStats_getJobStats]]
.`getJobStats(array $params = [])`
****
[source,php]
----
/*
$params['job_id']         = (string) The ID of the jobs stats to fetch
$params['allow_no_match'] = (boolean) Whether to ignore if a wildcard expression matches no jobs. (This includes `_all` string or when no jobs have been specified)
$params['allow_no_jobs']  = (boolean) Whether to ignore if a wildcard expression matches no jobs. (This includes `_all` string or when no jobs have been specified)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacegetJobs_getJobs]]
.`getJobs(array $params = [])`
****
[source,php]
----
/*
$params['job_id']            = (string) The ID of the jobs to fetch
$params['allow_no_match']    = (boolean) Whether to ignore if a wildcard expression matches no jobs. (This includes `_all` string or when no jobs have been specified)
$params['allow_no_jobs']     = (boolean) Whether to ignore if a wildcard expression matches no jobs. (This includes `_all` string or when no jobs have been specified)
$params['exclude_generated'] = (boolean) Omits fields that are illegal to set on job PUT (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacegetModelSnapshots_getModelSnapshots]]
.`getModelSnapshots(array $params = [])`
****
[source,php]
----
/*
$params['job_id']      = (string) The ID of the job to fetch (Required)
$params['snapshot_id'] = (string) The ID of the snapshot to fetch
$params['from']        = (int) Skips a number of documents
$params['size']        = (int) The default number of documents returned in queries as a string.
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacegetOverallBuckets_getOverallBuckets]]
.`getOverallBuckets(array $params = [])`
****
[source,php]
----
/*
$params['job_id']          = (string) The job IDs for which to calculate overall bucket results
$params['top_n']           = (int) The number of top job bucket scores to be used in the overall_score calculation
$params['bucket_span']     = (string) The span of the overall buckets. Defaults to the longest job bucket_span
$params['overall_score']   = (double) Returns overall buckets with overall scores higher than this value
$params['exclude_interim'] = (boolean) If true overall buckets that include interim buckets will be excluded
$params['start']           = (string) Returns overall buckets with timestamps after this time
$params['end']             = (string) Returns overall buckets with timestamps earlier than this time
$params['allow_no_match']  = (boolean) Whether to ignore if a wildcard expression matches no jobs. (This includes `_all` string or when no jobs have been specified)
$params['allow_no_jobs']   = (boolean) Whether to ignore if a wildcard expression matches no jobs. (This includes `_all` string or when no jobs have been specified)
$params['body']            = (array) Overall bucket selection details if not provided in URI
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacegetRecords_getRecords]]
.`getRecords(array $params = [])`
****
[source,php]
----
/*
$params['job_id']          = (string) The ID of the job
$params['exclude_interim'] = (boolean) Exclude interim results
$params['from']            = (int) skips a number of records
$params['size']            = (int) specifies a max number of records to get
$params['start']           = (string) Start time filter for records
$params['end']             = (string) End time filter for records
$params['record_score']    = (double) Returns records with anomaly scores greater or equal than this value
$params['sort']            = (string) Sort records by a particular field
$params['desc']            = (boolean) Set the sort direction
$params['body']            = (array) Record selection criteria
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacegetTrainedModels_getTrainedModels]]
.`getTrainedModels(array $params = [])`
*NOTE:* This API is BETA and may change in ways that are not backwards compatible
****
[source,php]
----
/*
$params['model_id']                 = (string) The ID of the trained models to fetch
$params['allow_no_match']           = (boolean) Whether to ignore if a wildcard expression matches no trained models. (This includes `_all` string or when no trained models have been specified) (Default = true)
$params['include']                  = (string) A comma-separate list of fields to optionally include. Valid options are 'definition' and 'total_feature_importance'. Default is none.
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacegetTrainedModelsStats_getTrainedModelsStats]]
.`getTrainedModelsStats(array $params = [])`
*NOTE:* This API is BETA and may change in ways that are not backwards compatible
****
[source,php]
----
/*
$params['model_id']       = (string) The ID of the trained models stats to fetch
$params['allow_no_match'] = (boolean) Whether to ignore if a wildcard expression matches no trained models. (This includes `_all` string or when no trained models have been specified) (Default = true)
$params['from']           = (int) skips a number of trained models (Default = 0)
$params['size']           = (int) specifies a max number of trained models to get (Default = 100)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespaceinfo_info]]
.`info(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_Namespaces_MlNamespaceopenJob_openJob]]
.`openJob(array $params = [])`
****
[source,php]
----
/*
$params['job_id'] = (string) The ID of the job to open
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacepostCalendarEvents_postCalendarEvents]]
.`postCalendarEvents(array $params = [])`
****
[source,php]
----
/*
$params['calendar_id'] = (string) The ID of the calendar to modify
$params['body']        = (array) A list of events (Required)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacepostData_postData]]
.`postData(array $params = [])`
****
[source,php]
----
/*
$params['job_id']      = (string) The name of the job receiving the data
$params['reset_start'] = (string) Optional parameter to specify the start of the bucket resetting range
$params['reset_end']   = (string) Optional parameter to specify the end of the bucket resetting range
$params['body']        = (array) The data to process (Required)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacepreviewDatafeed_previewDatafeed]]
.`previewDatafeed(array $params = [])`
****
[source,php]
----
/*
$params['datafeed_id'] = (string) The ID of the datafeed to preview
*/
----
****



[[Elasticsearch_Namespaces_MlNamespaceputCalendar_putCalendar]]
.`putCalendar(array $params = [])`
****
[source,php]
----
/*
$params['calendar_id'] = (string) The ID of the calendar to create
$params['body']        = (array) The calendar details
*/
----
****



[[Elasticsearch_Namespaces_MlNamespaceputCalendarJob_putCalendarJob]]
.`putCalendarJob(array $params = [])`
****
[source,php]
----
/*
$params['calendar_id'] = (string) The ID of the calendar to modify
$params['job_id']      = (string) The ID of the job to add to the calendar
*/
----
****



[[Elasticsearch_Namespaces_MlNamespaceputDataFrameAnalytics_putDataFrameAnalytics]]
.`putDataFrameAnalytics(array $params = [])`
*NOTE:* This API is BETA and may change in ways that are not backwards compatible
****
[source,php]
----
/*
$params['id']   = (string) The ID of the data frame analytics to create
$params['body'] = (array) The data frame analytics configuration (Required)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespaceputDatafeed_putDatafeed]]
.`putDatafeed(array $params = [])`
****
[source,php]
----
/*
$params['datafeed_id']        = (string) The ID of the datafeed to create
$params['ignore_unavailable'] = (boolean) Ignore unavailable indexes (default: false)
$params['allow_no_indices']   = (boolean) Ignore if the source indices expressions resolves to no concrete indices (default: true)
$params['ignore_throttled']   = (boolean) Ignore indices that are marked as throttled (default: true)
$params['expand_wildcards']   = (enum) Whether source index expressions should get expanded to open or closed indices (default: open) (Options = open,closed,hidden,none,all)
$params['body']               = (array) The datafeed config (Required)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespaceputFilter_putFilter]]
.`putFilter(array $params = [])`
****
[source,php]
----
/*
$params['filter_id'] = (string) The ID of the filter to create
$params['body']      = (array) The filter details (Required)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespaceputJob_putJob]]
.`putJob(array $params = [])`
****
[source,php]
----
/*
$params['job_id'] = (string) The ID of the job to create
$params['body']   = (array) The job (Required)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespaceputTrainedModel_putTrainedModel]]
.`putTrainedModel(array $params = [])`
*NOTE:* This API is BETA and may change in ways that are not backwards compatible
****
[source,php]
----
/*
$params['model_id'] = (string) The ID of the trained models to store
$params['body']     = (array) The trained model configuration (Required)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacerevertModelSnapshot_revertModelSnapshot]]
.`revertModelSnapshot(array $params = [])`
****
[source,php]
----
/*
$params['job_id']                     = (string) The ID of the job to fetch
$params['snapshot_id']                = (string) The ID of the snapshot to revert to
$params['delete_intervening_results'] = (boolean) Should we reset the results back to the time of the snapshot?
$params['body']                       = (array) Reversion options
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacesetUpgradeMode_setUpgradeMode]]
.`setUpgradeMode(array $params = [])`
****
[source,php]
----
/*
$params['enabled'] = (boolean) Whether to enable upgrade_mode ML setting or not. Defaults to false.
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacestartDataFrameAnalytics_startDataFrameAnalytics]]
.`startDataFrameAnalytics(array $params = [])`
*NOTE:* This API is BETA and may change in ways that are not backwards compatible
****
[source,php]
----
/*
$params['id']      = (string) The ID of the data frame analytics to start
$params['timeout'] = (time) Controls the time to wait until the task has started. Defaults to 20 seconds
$params['body']    = (array) The start data frame analytics parameters
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacestartDatafeed_startDatafeed]]
.`startDatafeed(array $params = [])`
****
[source,php]
----
/*
$params['datafeed_id'] = (string) The ID of the datafeed to start
$params['start']       = (string) The start time from where the datafeed should begin
$params['end']         = (string) The end time when the datafeed should stop. When not set, the datafeed continues in real time
$params['timeout']     = (time) Controls the time to wait until a datafeed has started. Default to 20 seconds
$params['body']        = (array) The start datafeed parameters
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacestopDataFrameAnalytics_stopDataFrameAnalytics]]
.`stopDataFrameAnalytics(array $params = [])`
*NOTE:* This API is BETA and may change in ways that are not backwards compatible
****
[source,php]
----
/*
$params['id']             = (string) The ID of the data frame analytics to stop
$params['allow_no_match'] = (boolean) Whether to ignore if a wildcard expression matches no data frame analytics. (This includes `_all` string or when no data frame analytics have been specified)
$params['force']          = (boolean) True if the data frame analytics should be forcefully stopped
$params['timeout']        = (time) Controls the time to wait until the task has stopped. Defaults to 20 seconds
$params['body']           = (array) The stop data frame analytics parameters
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacestopDatafeed_stopDatafeed]]
.`stopDatafeed(array $params = [])`
****
[source,php]
----
/*
$params['datafeed_id']        = (string) The ID of the datafeed to stop
$params['allow_no_match']     = (boolean) Whether to ignore if a wildcard expression matches no datafeeds. (This includes `_all` string or when no datafeeds have been specified)
$params['allow_no_datafeeds'] = (boolean) Whether to ignore if a wildcard expression matches no datafeeds. (This includes `_all` string or when no datafeeds have been specified)
$params['force']              = (boolean) True if the datafeed should be forcefully stopped.
*/
----
****



[[Elasticsearch_Namespaces_MlNamespaceupdateDataFrameAnalytics_updateDataFrameAnalytics]]
.`updateDataFrameAnalytics(array $params = [])`
*NOTE:* This API is BETA and may change in ways that are not backwards compatible
****
[source,php]
----
/*
$params['id']   = (string) The ID of the data frame analytics to update
$params['body'] = (array) The data frame analytics settings to update (Required)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespaceupdateDatafeed_updateDatafeed]]
.`updateDatafeed(array $params = [])`
****
[source,php]
----
/*
$params['datafeed_id']        = (string) The ID of the datafeed to update
$params['ignore_unavailable'] = (boolean) Ignore unavailable indexes (default: false)
$params['allow_no_indices']   = (boolean) Ignore if the source indices expressions resolves to no concrete indices (default: true)
$params['ignore_throttled']   = (boolean) Ignore indices that are marked as throttled (default: true)
$params['expand_wildcards']   = (enum) Whether source index expressions should get expanded to open or closed indices (default: open) (Options = open,closed,hidden,none,all)
$params['body']               = (array) The datafeed update settings (Required)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespaceupdateFilter_updateFilter]]
.`updateFilter(array $params = [])`
****
[source,php]
----
/*
$params['filter_id'] = (string) The ID of the filter to update
$params['body']      = (array) The filter update (Required)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespaceupdateJob_updateJob]]
.`updateJob(array $params = [])`
****
[source,php]
----
/*
$params['job_id'] = (string) The ID of the job to create
$params['body']   = (array) The job update settings (Required)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespaceupdateModelSnapshot_updateModelSnapshot]]
.`updateModelSnapshot(array $params = [])`
****
[source,php]
----
/*
$params['job_id']      = (string) The ID of the job to fetch
$params['snapshot_id'] = (string) The ID of the snapshot to update
$params['body']        = (array) The model snapshot properties to update (Required)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespaceupgradeJobSnapshot_upgradeJobSnapshot]]
.`upgradeJobSnapshot(array $params = [])`
****
[source,php]
----
/*
$params['job_id']              = (string) The ID of the job
$params['snapshot_id']         = (string) The ID of the snapshot
$params['timeout']             = (time) How long should the API wait for the job to be opened and the old snapshot to be loaded.
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacevalidate_validate]]
.`validate(array $params = [])`
****
[source,php]
----
/*
$params['body'] = (array) The job config (Required)
*/
----
****



[[Elasticsearch_Namespaces_MlNamespacevalidateDetector_validateDetector]]
.`validateDetector(array $params = [])`
****
[source,php]
----
/*
$params['body'] = (array) The detector (Required)
*/
----
****


