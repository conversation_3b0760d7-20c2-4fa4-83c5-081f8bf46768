

[[Elasticsearch_Namespaces_ClusterNamespace]]
=== Elasticsearch\Namespaces\ClusterNamespace



Class ClusterNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_ClusterNamespaceallocationExplain_allocationExplain,`allocationExplain()`>>
* <<Elasticsearch_Namespaces_ClusterNamespacedeleteComponentTemplate_deleteComponentTemplate,`deleteComponentTemplate()`>>
* <<Elasticsearch_Namespaces_ClusterNamespacedeleteVotingConfigExclusions_deleteVotingConfigExclusions,`deleteVotingConfigExclusions()`>>
* <<Elasticsearch_Namespaces_ClusterNamespaceexistsComponentTemplate_existsComponentTemplate,`existsComponentTemplate()`>>
* <<Elasticsearch_Namespaces_ClusterNamespacegetComponentTemplate_getComponentTemplate,`getComponentTemplate()`>>
* <<Elasticsearch_Namespaces_ClusterNamespacegetSettings_getSettings,`getSettings()`>>
* <<Elasticsearch_Namespaces_ClusterNamespacehealth_health,`health()`>>
* <<Elasticsearch_Namespaces_ClusterNamespacependingTasks_pendingTasks,`pendingTasks()`>>
* <<Elasticsearch_Namespaces_ClusterNamespacepostVotingConfigExclusions_postVotingConfigExclusions,`postVotingConfigExclusions()`>>
* <<Elasticsearch_Namespaces_ClusterNamespaceputComponentTemplate_putComponentTemplate,`putComponentTemplate()`>>
* <<Elasticsearch_Namespaces_ClusterNamespaceputSettings_putSettings,`putSettings()`>>
* <<Elasticsearch_Namespaces_ClusterNamespaceremoteInfo_remoteInfo,`remoteInfo()`>>
* <<Elasticsearch_Namespaces_ClusterNamespacereroute_reroute,`reroute()`>>
* <<Elasticsearch_Namespaces_ClusterNamespacestate_state,`state()`>>
* <<Elasticsearch_Namespaces_ClusterNamespacestats_stats,`stats()`>>



[[Elasticsearch_Namespaces_ClusterNamespaceallocationExplain_allocationExplain]]
.`allocationExplain(array $params = [])`
****
[source,php]
----
/*
$params['include_yes_decisions'] = (boolean) Return 'YES' decisions in explanation (default: false)
$params['include_disk_info']     = (boolean) Return information about disk usage and shard sizes (default: false)
$params['body']                  = (array) The index, shard, and primary flag to explain. Empty means 'explain the first unassigned shard'
*/
----
****



[[Elasticsearch_Namespaces_ClusterNamespacedeleteComponentTemplate_deleteComponentTemplate]]
.`deleteComponentTemplate(array $params = [])`
****
[source,php]
----
/*
$params['name']           = (string) The name of the template
$params['timeout']        = (time) Explicit operation timeout
$params['master_timeout'] = (time) Specify timeout for connection to master
*/
----
****



[[Elasticsearch_Namespaces_ClusterNamespacedeleteVotingConfigExclusions_deleteVotingConfigExclusions]]
.`deleteVotingConfigExclusions(array $params = [])`
****
[source,php]
----
/*
$params['wait_for_removal'] = (boolean) Specifies whether to wait for all excluded nodes to be removed from the cluster before clearing the voting configuration exclusions list. (Default = true)
*/
----
****



[[Elasticsearch_Namespaces_ClusterNamespaceexistsComponentTemplate_existsComponentTemplate]]
.`existsComponentTemplate(array $params = [])`
****
[source,php]
----
/*
$params['name']           = (string) The name of the template
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
$params['local']          = (boolean) Return local information, do not retrieve the state from master node (default: false)
*/
----
****



[[Elasticsearch_Namespaces_ClusterNamespacegetComponentTemplate_getComponentTemplate]]
.`getComponentTemplate(array $params = [])`
****
[source,php]
----
/*
$params['name']           = (list) The comma separated names of the component templates
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
$params['local']          = (boolean) Return local information, do not retrieve the state from master node (default: false)
*/
----
****



[[Elasticsearch_Namespaces_ClusterNamespacegetSettings_getSettings]]
.`getSettings(array $params = [])`
****
[source,php]
----
/*
$params['flat_settings']    = (boolean) Return settings in flat format (default: false)
$params['master_timeout']   = (time) Explicit operation timeout for connection to master node
$params['timeout']          = (time) Explicit operation timeout
$params['include_defaults'] = (boolean) Whether to return all default clusters setting. (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_ClusterNamespacehealth_health]]
.`health(array $params = [])`
****
[source,php]
----
/*
$params['index']                           = (list) Limit the information returned to a specific index
$params['expand_wildcards']                = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = all)
$params['level']                           = (enum) Specify the level of detail for returned information (Options = cluster,indices,shards) (Default = cluster)
$params['local']                           = (boolean) Return local information, do not retrieve the state from master node (default: false)
$params['master_timeout']                  = (time) Explicit operation timeout for connection to master node
$params['timeout']                         = (time) Explicit operation timeout
$params['wait_for_active_shards']          = (string) Wait until the specified number of shards is active
$params['wait_for_nodes']                  = (string) Wait until the specified number of nodes is available
$params['wait_for_events']                 = (enum) Wait until all currently queued events with the given priority are processed (Options = immediate,urgent,high,normal,low,languid)
$params['wait_for_no_relocating_shards']   = (boolean) Whether to wait until there are no relocating shards in the cluster
$params['wait_for_no_initializing_shards'] = (boolean) Whether to wait until there are no initializing shards in the cluster
$params['wait_for_status']                 = (enum) Wait until cluster is in a specific state (Options = green,yellow,red)
*/
----
****



[[Elasticsearch_Namespaces_ClusterNamespacependingTasks_pendingTasks]]
.`pendingTasks(array $params = [])`
****
[source,php]
----
/*
$params['local']          = (boolean) Return local information, do not retrieve the state from master node (default: false)
$params['master_timeout'] = (time) Specify timeout for connection to master
*/
----
****



[[Elasticsearch_Namespaces_ClusterNamespacepostVotingConfigExclusions_postVotingConfigExclusions]]
.`postVotingConfigExclusions(array $params = [])`
****
[source,php]
----
/*
$params['node_ids']   = (string) A comma-separated list of the persistent ids of the nodes to exclude from the voting configuration. If specified, you may not also specify ?node_names.
*/
----
****



[[Elasticsearch_Namespaces_ClusterNamespaceputComponentTemplate_putComponentTemplate]]
.`putComponentTemplate(array $params = [])`
****
[source,php]
----
/*
$params['name']           = (string) The name of the template
$params['create']         = (boolean) Whether the index template should only be added if new or can also replace an existing one (Default = false)
$params['timeout']        = (time) Explicit operation timeout
$params['master_timeout'] = (time) Specify timeout for connection to master
$params['body']           = (array) The template definition (Required)
*/
----
****



[[Elasticsearch_Namespaces_ClusterNamespaceputSettings_putSettings]]
.`putSettings(array $params = [])`
****
[source,php]
----
/*
$params['flat_settings']  = (boolean) Return settings in flat format (default: false)
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
$params['timeout']        = (time) Explicit operation timeout
$params['body']           = (array) The settings to be updated. Can be either `transient` or `persistent` (survives cluster restart). (Required)
*/
----
****



[[Elasticsearch_Namespaces_ClusterNamespaceremoteInfo_remoteInfo]]
.`remoteInfo(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_Namespaces_ClusterNamespacereroute_reroute]]
.`reroute(array $params = [])`
****
[source,php]
----
/*
$params['dry_run']        = (boolean) Simulate the operation only and return the resulting state
$params['explain']        = (boolean) Return an explanation of why the commands can or cannot be executed
$params['retry_failed']   = (boolean) Retries allocation of shards that are blocked due to too many subsequent allocation failures
$params['metric']         = (list) Limit the information returned to the specified metrics. Defaults to all but metadata (Options = _all,blocks,metadata,nodes,routing_table,master_node,version)
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
$params['timeout']        = (time) Explicit operation timeout
$params['body']           = (array) The definition of `commands` to perform (`move`, `cancel`, `allocate`)
*/
----
****



[[Elasticsearch_Namespaces_ClusterNamespacestate_state]]
.`state(array $params = [])`
****
[source,php]
----
/*
$params['metric']                    = (list) Limit the information returned to the specified metrics
$params['index']                     = (list) A comma-separated list of index names; use `_all` or empty string to perform the operation on all indices
$params['local']                     = (boolean) Return local information, do not retrieve the state from master node (default: false)
$params['master_timeout']            = (time) Specify timeout for connection to master
$params['flat_settings']             = (boolean) Return settings in flat format (default: false)
$params['wait_for_metadata_version'] = (number) Wait for the metadata version to be equal or greater than the specified metadata version
$params['wait_for_timeout']          = (time) The maximum time to wait for wait_for_metadata_version before timing out
$params['ignore_unavailable']        = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']          = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']          = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
*/
----
****



[[Elasticsearch_Namespaces_ClusterNamespacestats_stats]]
.`stats(array $params = [])`
****
[source,php]
----
/*
$params['node_id']       = (list) A comma-separated list of node IDs or names to limit the returned information; use `_local` to return information from the node you're connecting to, leave empty to get information from all nodes
$params['flat_settings'] = (boolean) Return settings in flat format (default: false)
$params['timeout']       = (time) Explicit operation timeout
*/
----
****


