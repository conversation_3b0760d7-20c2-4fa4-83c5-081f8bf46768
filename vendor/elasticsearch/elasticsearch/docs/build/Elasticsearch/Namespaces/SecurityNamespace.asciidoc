

[[Elasticsearch_Namespaces_SecurityNamespace]]
=== Elasticsearch\Namespaces\SecurityNamespace



Class SecurityNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_SecurityNamespaceauthenticate_authenticate,`authenticate()`>>
* <<Elasticsearch_Namespaces_SecurityNamespacechangePassword_changePassword,`changePassword()`>>
* <<Elasticsearch_Namespaces_SecurityNamespaceclearApiKeyCache_clearApiKeyCache,`clearApiKeyCache()`>>
* <<Elasticsearch_Namespaces_SecurityNamespaceclearCachedPrivileges_clearCachedPrivileges,`clearCachedPrivileges()`>>
* <<Elasticsearch_Namespaces_SecurityNamespaceclearCachedRealms_clearCachedRealms,`clearCachedRealms()`>>
* <<Elasticsearch_Namespaces_SecurityNamespaceclearCachedRoles_clearCachedRoles,`clearCachedRoles()`>>
* <<Elasticsearch_Namespaces_SecurityNamespacecreateApiKey_createApiKey,`createApiKey()`>>
* <<Elasticsearch_Namespaces_SecurityNamespacedeletePrivileges_deletePrivileges,`deletePrivileges()`>>
* <<Elasticsearch_Namespaces_SecurityNamespacedeleteRole_deleteRole,`deleteRole()`>>
* <<Elasticsearch_Namespaces_SecurityNamespacedeleteRoleMapping_deleteRoleMapping,`deleteRoleMapping()`>>
* <<Elasticsearch_Namespaces_SecurityNamespacedeleteUser_deleteUser,`deleteUser()`>>
* <<Elasticsearch_Namespaces_SecurityNamespacedisableUser_disableUser,`disableUser()`>>
* <<Elasticsearch_Namespaces_SecurityNamespaceenableUser_enableUser,`enableUser()`>>
* <<Elasticsearch_Namespaces_SecurityNamespacegetApiKey_getApiKey,`getApiKey()`>>
* <<Elasticsearch_Namespaces_SecurityNamespacegetBuiltinPrivileges_getBuiltinPrivileges,`getBuiltinPrivileges()`>>
* <<Elasticsearch_Namespaces_SecurityNamespacegetPrivileges_getPrivileges,`getPrivileges()`>>
* <<Elasticsearch_Namespaces_SecurityNamespacegetRole_getRole,`getRole()`>>
* <<Elasticsearch_Namespaces_SecurityNamespacegetRoleMapping_getRoleMapping,`getRoleMapping()`>>
* <<Elasticsearch_Namespaces_SecurityNamespacegetToken_getToken,`getToken()`>>
* <<Elasticsearch_Namespaces_SecurityNamespacegetUser_getUser,`getUser()`>>
* <<Elasticsearch_Namespaces_SecurityNamespacegetUserPrivileges_getUserPrivileges,`getUserPrivileges()`>>
* <<Elasticsearch_Namespaces_SecurityNamespacegrantApiKey_grantApiKey,`grantApiKey()`>>
* <<Elasticsearch_Namespaces_SecurityNamespacehasPrivileges_hasPrivileges,`hasPrivileges()`>>
* <<Elasticsearch_Namespaces_SecurityNamespaceinvalidateApiKey_invalidateApiKey,`invalidateApiKey()`>>
* <<Elasticsearch_Namespaces_SecurityNamespaceinvalidateToken_invalidateToken,`invalidateToken()`>>
* <<Elasticsearch_Namespaces_SecurityNamespaceputPrivileges_putPrivileges,`putPrivileges()`>>
* <<Elasticsearch_Namespaces_SecurityNamespaceputRole_putRole,`putRole()`>>
* <<Elasticsearch_Namespaces_SecurityNamespaceputRoleMapping_putRoleMapping,`putRoleMapping()`>>
* <<Elasticsearch_Namespaces_SecurityNamespaceputUser_putUser,`putUser()`>>



[[Elasticsearch_Namespaces_SecurityNamespaceauthenticate_authenticate]]
.`authenticate(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespacechangePassword_changePassword]]
.`changePassword(array $params = [])`
****
[source,php]
----
/*
$params['username'] = (string) The username of the user to change the password for
$params['refresh']  = (enum) If `true` (the default) then refresh the affected shards to make this operation visible to search, if `wait_for` then wait for a refresh to make this operation visible to search, if `false` then do nothing with refreshes. (Options = true,false,wait_for)
$params['body']     = (array) the new password for the user (Required)
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespaceclearApiKeyCache_clearApiKeyCache]]
.`clearApiKeyCache(array $params = [])`
****
[source,php]
----
/*
$params['ids'] = (list) A comma-separated list of IDs of API keys to clear from the cache
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespaceclearCachedPrivileges_clearCachedPrivileges]]
.`clearCachedPrivileges(array $params = [])`
****
[source,php]
----
/*
$params['application'] = (list) A comma-separated list of application names
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespaceclearCachedRealms_clearCachedRealms]]
.`clearCachedRealms(array $params = [])`
****
[source,php]
----
/*
$params['realms']    = (list) Comma-separated list of realms to clear
$params['usernames'] = (list) Comma-separated list of usernames to clear from the cache
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespaceclearCachedRoles_clearCachedRoles]]
.`clearCachedRoles(array $params = [])`
****
[source,php]
----
/*
$params['name'] = (list) Role name
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespacecreateApiKey_createApiKey]]
.`createApiKey(array $params = [])`
****
[source,php]
----
/*
$params['refresh'] = (enum) If `true` (the default) then refresh the affected shards to make this operation visible to search, if `wait_for` then wait for a refresh to make this operation visible to search, if `false` then do nothing with refreshes. (Options = true,false,wait_for)
$params['body']    = (array) The api key request to create an API key (Required)
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespacedeletePrivileges_deletePrivileges]]
.`deletePrivileges(array $params = [])`
****
[source,php]
----
/*
$params['application'] = (string) Application name
$params['name']        = (string) Privilege name
$params['refresh']     = (enum) If `true` (the default) then refresh the affected shards to make this operation visible to search, if `wait_for` then wait for a refresh to make this operation visible to search, if `false` then do nothing with refreshes. (Options = true,false,wait_for)
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespacedeleteRole_deleteRole]]
.`deleteRole(array $params = [])`
****
[source,php]
----
/*
$params['name']    = (string) Role name
$params['refresh'] = (enum) If `true` (the default) then refresh the affected shards to make this operation visible to search, if `wait_for` then wait for a refresh to make this operation visible to search, if `false` then do nothing with refreshes. (Options = true,false,wait_for)
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespacedeleteRoleMapping_deleteRoleMapping]]
.`deleteRoleMapping(array $params = [])`
****
[source,php]
----
/*
$params['name']    = (string) Role-mapping name
$params['refresh'] = (enum) If `true` (the default) then refresh the affected shards to make this operation visible to search, if `wait_for` then wait for a refresh to make this operation visible to search, if `false` then do nothing with refreshes. (Options = true,false,wait_for)
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespacedeleteUser_deleteUser]]
.`deleteUser(array $params = [])`
****
[source,php]
----
/*
$params['username'] = (string) username
$params['refresh']  = (enum) If `true` (the default) then refresh the affected shards to make this operation visible to search, if `wait_for` then wait for a refresh to make this operation visible to search, if `false` then do nothing with refreshes. (Options = true,false,wait_for)
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespacedisableUser_disableUser]]
.`disableUser(array $params = [])`
****
[source,php]
----
/*
$params['username'] = (string) The username of the user to disable
$params['refresh']  = (enum) If `true` (the default) then refresh the affected shards to make this operation visible to search, if `wait_for` then wait for a refresh to make this operation visible to search, if `false` then do nothing with refreshes. (Options = true,false,wait_for)
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespaceenableUser_enableUser]]
.`enableUser(array $params = [])`
****
[source,php]
----
/*
$params['username'] = (string) The username of the user to enable
$params['refresh']  = (enum) If `true` (the default) then refresh the affected shards to make this operation visible to search, if `wait_for` then wait for a refresh to make this operation visible to search, if `false` then do nothing with refreshes. (Options = true,false,wait_for)
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespacegetApiKey_getApiKey]]
.`getApiKey(array $params = [])`
****
[source,php]
----
/*
$params['id']         = (string) API key id of the API key to be retrieved
$params['name']       = (string) API key name of the API key to be retrieved
$params['username']   = (string) user name of the user who created this API key to be retrieved
$params['realm_name'] = (string) realm name of the user who created this API key to be retrieved
$params['owner']      = (boolean) flag to query API keys owned by the currently authenticated user (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespacegetBuiltinPrivileges_getBuiltinPrivileges]]
.`getBuiltinPrivileges(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespacegetPrivileges_getPrivileges]]
.`getPrivileges(array $params = [])`
****
[source,php]
----
/*
$params['application'] = (string) Application name
$params['name']        = (string) Privilege name
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespacegetRole_getRole]]
.`getRole(array $params = [])`
****
[source,php]
----
/*
$params['name'] = (list) A comma-separated list of role names
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespacegetRoleMapping_getRoleMapping]]
.`getRoleMapping(array $params = [])`
****
[source,php]
----
/*
$params['name'] = (list) A comma-separated list of role-mapping names
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespacegetToken_getToken]]
.`getToken(array $params = [])`
****
[source,php]
----
/*
$params['body'] = (array) The token request to get (Required)
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespacegetUser_getUser]]
.`getUser(array $params = [])`
****
[source,php]
----
/*
$params['username'] = (list) A comma-separated list of usernames
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespacegetUserPrivileges_getUserPrivileges]]
.`getUserPrivileges(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespacegrantApiKey_grantApiKey]]
.`grantApiKey(array $params = [])`
****
[source,php]
----
/*
$params['refresh'] = (enum) If `true` (the default) then refresh the affected shards to make this operation visible to search, if `wait_for` then wait for a refresh to make this operation visible to search, if `false` then do nothing with refreshes. (Options = true,false,wait_for)
$params['body']    = (array) The api key request to create an API key (Required)
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespacehasPrivileges_hasPrivileges]]
.`hasPrivileges(array $params = [])`
****
[source,php]
----
/*
$params['user'] = (string) Username
$params['body'] = (array) The privileges to test (Required)
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespaceinvalidateApiKey_invalidateApiKey]]
.`invalidateApiKey(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespaceinvalidateToken_invalidateToken]]
.`invalidateToken(array $params = [])`
****
[source,php]
----
/*
$params['body'] = (array) The token to invalidate (Required)
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespaceputPrivileges_putPrivileges]]
.`putPrivileges(array $params = [])`
****
[source,php]
----
/*
$params['refresh'] = (enum) If `true` (the default) then refresh the affected shards to make this operation visible to search, if `wait_for` then wait for a refresh to make this operation visible to search, if `false` then do nothing with refreshes. (Options = true,false,wait_for)
$params['body']    = (array) The privilege(s) to add (Required)
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespaceputRole_putRole]]
.`putRole(array $params = [])`
****
[source,php]
----
/*
$params['name']    = (string) Role name
$params['refresh'] = (enum) If `true` (the default) then refresh the affected shards to make this operation visible to search, if `wait_for` then wait for a refresh to make this operation visible to search, if `false` then do nothing with refreshes. (Options = true,false,wait_for)
$params['body']    = (array) The role to add (Required)
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespaceputRoleMapping_putRoleMapping]]
.`putRoleMapping(array $params = [])`
****
[source,php]
----
/*
$params['name']    = (string) Role-mapping name
$params['refresh'] = (enum) If `true` (the default) then refresh the affected shards to make this operation visible to search, if `wait_for` then wait for a refresh to make this operation visible to search, if `false` then do nothing with refreshes. (Options = true,false,wait_for)
$params['body']    = (array) The role mapping to add (Required)
*/
----
****



[[Elasticsearch_Namespaces_SecurityNamespaceputUser_putUser]]
.`putUser(array $params = [])`
****
[source,php]
----
/*
$params['username'] = (string) The username of the User
$params['refresh']  = (enum) If `true` (the default) then refresh the affected shards to make this operation visible to search, if `wait_for` then wait for a refresh to make this operation visible to search, if `false` then do nothing with refreshes. (Options = true,false,wait_for)
$params['body']     = (array) The user to add (Required)
*/
----
****


