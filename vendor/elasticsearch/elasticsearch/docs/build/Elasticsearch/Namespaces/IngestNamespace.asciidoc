

[[Elasticsearch_Namespaces_IngestNamespace]]
=== Elasticsearch\Namespaces\IngestNamespace



Class IngestNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_IngestNamespacedeletePipeline_deletePipeline,`deletePipeline()`>>
* <<Elasticsearch_Namespaces_IngestNamespacegetPipeline_getPipeline,`getPipeline()`>>
* <<Elasticsearch_Namespaces_IngestNamespaceprocessorGrok_processorGrok,`processorGrok()`>>
* <<Elasticsearch_Namespaces_IngestNamespaceputPipeline_putPipeline,`putPipeline()`>>
* <<Elasticsearch_Namespaces_IngestNamespacesimulate_simulate,`simulate()`>>



[[Elasticsearch_Namespaces_IngestNamespacedeletePipeline_deletePipeline]]
.`deletePipeline(array $params = [])`
****
[source,php]
----
/*
$params['id']             = (string) Pipeline ID
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
$params['timeout']        = (time) Explicit operation timeout
*/
----
****



[[Elasticsearch_Namespaces_IngestNamespacegetPipeline_getPipeline]]
.`getPipeline(array $params = [])`
****
[source,php]
----
/*
$params['id']             = (string) Comma separated list of pipeline ids. Wildcards supported
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
*/
----
****



[[Elasticsearch_Namespaces_IngestNamespaceprocessorGrok_processorGrok]]
.`processorGrok(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_Namespaces_IngestNamespaceputPipeline_putPipeline]]
.`putPipeline(array $params = [])`
****
[source,php]
----
/*
$params['id']             = (string) Pipeline ID
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
$params['timeout']        = (time) Explicit operation timeout
$params['body']           = (array) The ingest definition (Required)
*/
----
****



[[Elasticsearch_Namespaces_IngestNamespacesimulate_simulate]]
.`simulate(array $params = [])`
****
[source,php]
----
/*
$params['id']      = (string) Pipeline ID
$params['verbose'] = (boolean) Verbose mode. Display data output for each processor in executed pipeline (Default = false)
$params['body']    = (array) The simulate definition (Required)
*/
----
****


