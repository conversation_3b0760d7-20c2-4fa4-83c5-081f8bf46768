

[[Elasticsearch_Client]]
=== Elasticsearch\Client



Class Client

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Clientbulk_bulk,`bulk()`>>
* <<Elasticsearch_ClientclearScroll_clearScroll,`clearScroll()`>>
* <<Elasticsearch_ClientclosePointInTime_closePointInTime,`closePointInTime()`>>
* <<Elasticsearch_Clientcount_count,`count()`>>
* <<Elasticsearch_Clientcreate_create,`create()`>>
* <<Elasticsearch_Clientdelete_delete,`delete()`>>
* <<Elasticsearch_ClientdeleteByQuery_deleteByQuery,`deleteByQuery()`>>
* <<Elasticsearch_ClientdeleteByQueryRethrottle_deleteByQueryRethrottle,`deleteByQueryRethrottle()`>>
* <<Elasticsearch_ClientdeleteScript_deleteScript,`deleteScript()`>>
* <<Elasticsearch_Clientexists_exists,`exists()`>>
* <<Elasticsearch_ClientexistsSource_existsSource,`existsSource()`>>
* <<Elasticsearch_Clientexplain_explain,`explain()`>>
* <<Elasticsearch_ClientfieldCaps_fieldCaps,`fieldCaps()`>>
* <<Elasticsearch_Clientget_get,`get()`>>
* <<Elasticsearch_ClientgetScript_getScript,`getScript()`>>
* <<Elasticsearch_ClientgetScriptContext_getScriptContext,`getScriptContext()`>>
* <<Elasticsearch_ClientgetScriptLanguages_getScriptLanguages,`getScriptLanguages()`>>
* <<Elasticsearch_ClientgetSource_getSource,`getSource()`>>
* <<Elasticsearch_Clientindex_index,`index()`>>
* <<Elasticsearch_Clientinfo_info,`info()`>>
* <<Elasticsearch_Clientmget_mget,`mget()`>>
* <<Elasticsearch_Clientmsearch_msearch,`msearch()`>>
* <<Elasticsearch_ClientmsearchTemplate_msearchTemplate,`msearchTemplate()`>>
* <<Elasticsearch_Clientmtermvectors_mtermvectors,`mtermvectors()`>>
* <<Elasticsearch_ClientopenPointInTime_openPointInTime,`openPointInTime()`>>
* <<Elasticsearch_Clientping_ping,`ping()`>>
* <<Elasticsearch_ClientputScript_putScript,`putScript()`>>
* <<Elasticsearch_ClientrankEval_rankEval,`rankEval()`>>
* <<Elasticsearch_Clientreindex_reindex,`reindex()`>>
* <<Elasticsearch_ClientreindexRethrottle_reindexRethrottle,`reindexRethrottle()`>>
* <<Elasticsearch_ClientrenderSearchTemplate_renderSearchTemplate,`renderSearchTemplate()`>>
* <<Elasticsearch_ClientscriptsPainlessExecute_scriptsPainlessExecute,`scriptsPainlessExecute()`>>
* <<Elasticsearch_Clientscroll_scroll,`scroll()`>>
* <<Elasticsearch_Clientsearch_search,`search()`>>
* <<Elasticsearch_ClientsearchShards_searchShards,`searchShards()`>>
* <<Elasticsearch_ClientsearchTemplate_searchTemplate,`searchTemplate()`>>
* <<Elasticsearch_Clienttermvectors_termvectors,`termvectors()`>>
* <<Elasticsearch_Clientupdate_update,`update()`>>
* <<Elasticsearch_ClientupdateByQuery_updateByQuery,`updateByQuery()`>>
* <<Elasticsearch_ClientupdateByQueryRethrottle_updateByQueryRethrottle,`updateByQueryRethrottle()`>>
* <<Elasticsearch_ClientasyncSearch_asyncSearch,`asyncSearch()`>>
* <<Elasticsearch_Clientautoscaling_autoscaling,`autoscaling()`>>
* <<Elasticsearch_Clientcat_cat,`cat()`>>
* <<Elasticsearch_Clientccr_ccr,`ccr()`>>
* <<Elasticsearch_Clientcluster_cluster,`cluster()`>>
* <<Elasticsearch_ClientdanglingIndices_danglingIndices,`danglingIndices()`>>
* <<Elasticsearch_ClientdataFrameTransformDeprecated_dataFrameTransformDeprecated,`dataFrameTransformDeprecated()`>>
* <<Elasticsearch_Clientenrich_enrich,`enrich()`>>
* <<Elasticsearch_Clienteql_eql,`eql()`>>
* <<Elasticsearch_Clientfeatures_features,`features()`>>
* <<Elasticsearch_Clientgraph_graph,`graph()`>>
* <<Elasticsearch_Clientilm_ilm,`ilm()`>>
* <<Elasticsearch_Clientindices_indices,`indices()`>>
* <<Elasticsearch_Clientingest_ingest,`ingest()`>>
* <<Elasticsearch_Clientlicense_license,`license()`>>
* <<Elasticsearch_Clientlogstash_logstash,`logstash()`>>
* <<Elasticsearch_Clientmigration_migration,`migration()`>>
* <<Elasticsearch_Clientml_ml,`ml()`>>
* <<Elasticsearch_Clientmonitoring_monitoring,`monitoring()`>>
* <<Elasticsearch_Clientnodes_nodes,`nodes()`>>
* <<Elasticsearch_Clientrollup_rollup,`rollup()`>>
* <<Elasticsearch_ClientsearchableSnapshots_searchableSnapshots,`searchableSnapshots()`>>
* <<Elasticsearch_Clientsecurity_security,`security()`>>
* <<Elasticsearch_Clientslm_slm,`slm()`>>
* <<Elasticsearch_Clientsnapshot_snapshot,`snapshot()`>>
* <<Elasticsearch_Clientsql_sql,`sql()`>>
* <<Elasticsearch_Clientssl_ssl,`ssl()`>>
* <<Elasticsearch_Clienttasks_tasks,`tasks()`>>
* <<Elasticsearch_ClienttextStructure_textStructure,`textStructure()`>>
* <<Elasticsearch_Clienttransform_transform,`transform()`>>
* <<Elasticsearch_Clientwatcher_watcher,`watcher()`>>
* <<Elasticsearch_Clientxpack_xpack,`xpack()`>>
* <<Elasticsearch_Client-call-_call,`__call()`>>
* <<Elasticsearch_ClientextractArgument_extractArgument,`extractArgument()`>>




[[Elasticsearch_Clientbulk_bulk]]
.`bulk(array $params = [])`
****
[source,php]
----
/*
$params['index']                  = (string) Default index for items which don't provide one
$params['type']                   = DEPRECATED (string) Default document type for items which don't provide one
$params['wait_for_active_shards'] = (string) Sets the number of shard copies that must be active before proceeding with the bulk operation. Defaults to 1, meaning the primary shard only. Set to `all` for all shard copies, otherwise set to any non-negative value less than or equal to the total number of copies for the shard (number of replicas + 1)
$params['refresh']                = (enum) If `true` then refresh the affected shards to make this operation visible to search, if `wait_for` then wait for a refresh to make this operation visible to search, if `false` (the default) then do nothing with refreshes. (Options = true,false,wait_for)
$params['routing']                = (string) Specific routing value
$params['timeout']                = (time) Explicit operation timeout
$params['_source']                = (list) True or false to return the _source field or not, or default list of fields to return, can be overridden on each sub-request
$params['_source_excludes']       = (list) Default list of fields to exclude from the returned _source field, can be overridden on each sub-request
$params['_source_includes']       = (list) Default list of fields to extract and return from the _source field, can be overridden on each sub-request
$params['pipeline']               = (string) The pipeline id to preprocess incoming documents with
$params['require_alias']          = (boolean) Sets require_alias for all incoming documents. Defaults to unset (false)
$params['body']                   = (array) The operation definition and data (action-data pairs), separated by newlines (Required)
*/
----
****



[[Elasticsearch_ClientclearScroll_clearScroll]]
.`clearScroll(array $params = [])`
****
[source,php]
----
/*
$params['scroll_id'] = DEPRECATED (list) A comma-separated list of scroll IDs to clear
$params['body']      = (array) A comma-separated list of scroll IDs to clear if none was specified via the scroll_id parameter
*/
----
****



[[Elasticsearch_ClientclosePointInTime_closePointInTime]]
.`closePointInTime(array $params = [])`
****
[source,php]
----
/*
$params['body'] = (array) a point-in-time id to close
*/
----
****



[[Elasticsearch_Clientcount_count]]
.`count(array $params = [])`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of indices to restrict the results
$params['type']               = DEPRECATED (list) A comma-separated list of types to restrict the results
$params['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['ignore_throttled']   = (boolean) Whether specified concrete, expanded or aliased indices should be ignored when throttled
$params['allow_no_indices']   = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
$params['min_score']          = (number) Include only documents with a specific `_score` value in the result
$params['preference']         = (string) Specify the node or shard the operation should be performed on (default: random)
$params['routing']            = (list) A comma-separated list of specific routing values
$params['q']                  = (string) Query in the Lucene query string syntax
$params['analyzer']           = (string) The analyzer to use for the query string
$params['analyze_wildcard']   = (boolean) Specify whether wildcard and prefix queries should be analyzed (default: false)
$params['default_operator']   = (enum) The default operator for query string query (AND or OR) (Options = AND,OR) (Default = OR)
$params['df']                 = (string) The field to use as default where no field prefix is given in the query string
$params['lenient']            = (boolean) Specify whether format-based query failures (such as providing text to a numeric field) should be ignored
$params['terminate_after']    = (number) The maximum count for each shard, upon reaching which the query execution will terminate early
$params['body']               = (array) A query to restrict the results specified with the Query DSL (optional)
*/
----
****



[[Elasticsearch_Clientcreate_create]]
.`create(array $params = [])`
****
[source,php]
----
/*
$params['id']                     = (string) Document ID (Required)
$params['index']                  = (string) The name of the index (Required)
$params['type']                   = DEPRECATED (string) The type of the document
$params['wait_for_active_shards'] = (string) Sets the number of shard copies that must be active before proceeding with the index operation. Defaults to 1, meaning the primary shard only. Set to `all` for all shard copies, otherwise set to any non-negative value less than or equal to the total number of copies for the shard (number of replicas + 1)
$params['refresh']                = (enum) If `true` then refresh the affected shards to make this operation visible to search, if `wait_for` then wait for a refresh to make this operation visible to search, if `false` (the default) then do nothing with refreshes. (Options = true,false,wait_for)
$params['routing']                = (string) Specific routing value
$params['timeout']                = (time) Explicit operation timeout
$params['version']                = (number) Explicit version number for concurrency control
$params['version_type']           = (enum) Specific version type (Options = internal,external,external_gte)
$params['pipeline']               = (string) The pipeline id to preprocess incoming documents with
$params['body']                   = (array) The document (Required)
*/
----
****



[[Elasticsearch_Clientdelete_delete]]
.`delete(array $params = [])`
****
[source,php]
----
/*
$params['id']                     = (string) The document ID (Required)
$params['index']                  = (string) The name of the index (Required)
$params['type']                   = DEPRECATED (string) The type of the document
$params['wait_for_active_shards'] = (string) Sets the number of shard copies that must be active before proceeding with the delete operation. Defaults to 1, meaning the primary shard only. Set to `all` for all shard copies, otherwise set to any non-negative value less than or equal to the total number of copies for the shard (number of replicas + 1)
$params['refresh']                = (enum) If `true` then refresh the affected shards to make this operation visible to search, if `wait_for` then wait for a refresh to make this operation visible to search, if `false` (the default) then do nothing with refreshes. (Options = true,false,wait_for)
$params['routing']                = (string) Specific routing value
$params['timeout']                = (time) Explicit operation timeout
$params['if_seq_no']              = (number) only perform the delete operation if the last operation that has changed the document has the specified sequence number
$params['if_primary_term']        = (number) only perform the delete operation if the last operation that has changed the document has the specified primary term
$params['version']                = (number) Explicit version number for concurrency control
$params['version_type']           = (enum) Specific version type (Options = internal,external,external_gte,force)
*/
----
****



[[Elasticsearch_ClientdeleteByQuery_deleteByQuery]]
.`deleteByQuery(array $params = [])`
****
[source,php]
----
/*
$params['index']                  = (list) A comma-separated list of index names to search; use `_all` or empty string to perform the operation on all indices (Required)
$params['type']                   = DEPRECATED (list) A comma-separated list of document types to search; leave empty to perform the operation on all types
$params['analyzer']               = (string) The analyzer to use for the query string
$params['analyze_wildcard']       = (boolean) Specify whether wildcard and prefix queries should be analyzed (default: false)
$params['default_operator']       = (enum) The default operator for query string query (AND or OR) (Options = AND,OR) (Default = OR)
$params['df']                     = (string) The field to use as default where no field prefix is given in the query string
$params['from']                   = (number) Starting offset (default: 0)
$params['ignore_unavailable']     = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']       = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['conflicts']              = (enum) What to do when the delete by query hits version conflicts? (Options = abort,proceed) (Default = abort)
$params['expand_wildcards']       = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
$params['lenient']                = (boolean) Specify whether format-based query failures (such as providing text to a numeric field) should be ignored
$params['preference']             = (string) Specify the node or shard the operation should be performed on (default: random)
$params['q']                      = (string) Query in the Lucene query string syntax
$params['routing']                = (list) A comma-separated list of specific routing values
$params['scroll']                 = (time) Specify how long a consistent view of the index should be maintained for scrolled search
$params['search_type']            = (enum) Search operation type (Options = query_then_fetch,dfs_query_then_fetch)
$params['search_timeout']         = (time) Explicit timeout for each search request. Defaults to no timeout.
*/
----
****



[[Elasticsearch_ClientdeleteByQueryRethrottle_deleteByQueryRethrottle]]
.`deleteByQueryRethrottle(array $params = [])`
****
[source,php]
----
/*
$params['task_id']             = (string) The task id to rethrottle
$params['requests_per_second'] = (number) The throttle to set on this request in floating sub-requests per second. -1 means set no throttle. (Required)
*/
----
****



[[Elasticsearch_ClientdeleteScript_deleteScript]]
.`deleteScript(array $params = [])`
****
[source,php]
----
/*
$params['id']             = (string) Script ID
$params['timeout']        = (time) Explicit operation timeout
$params['master_timeout'] = (time) Specify timeout for connection to master
*/
----
****



[[Elasticsearch_Clientexists_exists]]
.`exists(array $params = [])`
****
[source,php]
----
/*
$params['id']               = (string) The document ID (Required)
$params['index']            = (string) The name of the index (Required)
$params['type']             = DEPRECATED (string) The type of the document (use `_all` to fetch the first document matching the ID across all types)
$params['stored_fields']    = (list) A comma-separated list of stored fields to return in the response
$params['preference']       = (string) Specify the node or shard the operation should be performed on (default: random)
$params['realtime']         = (boolean) Specify whether to perform the operation in realtime or search mode
$params['refresh']          = (boolean) Refresh the shard containing the document before performing the operation
$params['routing']          = (string) Specific routing value
$params['_source']          = (list) True or false to return the _source field or not, or a list of fields to return
$params['_source_excludes'] = (list) A list of fields to exclude from the returned _source field
$params['_source_includes'] = (list) A list of fields to extract and return from the _source field
$params['version']          = (number) Explicit version number for concurrency control
$params['version_type']     = (enum) Specific version type (Options = internal,external,external_gte,force)
*/
----
****



[[Elasticsearch_ClientexistsSource_existsSource]]
.`existsSource(array $params = [])`
****
[source,php]
----
/*
$params['id']               = (string) The document ID (Required)
$params['index']            = (string) The name of the index (Required)
$params['type']             = DEPRECATED (string) The type of the document; deprecated and optional starting with 7.0
$params['preference']       = (string) Specify the node or shard the operation should be performed on (default: random)
$params['realtime']         = (boolean) Specify whether to perform the operation in realtime or search mode
$params['refresh']          = (boolean) Refresh the shard containing the document before performing the operation
$params['routing']          = (string) Specific routing value
$params['_source']          = (list) True or false to return the _source field or not, or a list of fields to return
$params['_source_excludes'] = (list) A list of fields to exclude from the returned _source field
$params['_source_includes'] = (list) A list of fields to extract and return from the _source field
$params['version']          = (number) Explicit version number for concurrency control
$params['version_type']     = (enum) Specific version type (Options = internal,external,external_gte,force)
*/
----
****



[[Elasticsearch_Clientexplain_explain]]
.`explain(array $params = [])`
****
[source,php]
----
/*
$params['id']               = (string) The document ID (Required)
$params['index']            = (string) The name of the index (Required)
$params['type']             = DEPRECATED (string) The type of the document
$params['analyze_wildcard'] = (boolean) Specify whether wildcards and prefix queries in the query string query should be analyzed (default: false)
$params['analyzer']         = (string) The analyzer for the query string query
$params['default_operator'] = (enum) The default operator for query string query (AND or OR) (Options = AND,OR) (Default = OR)
$params['df']               = (string) The default field for query string query (default: _all)
$params['stored_fields']    = (list) A comma-separated list of stored fields to return in the response
$params['lenient']          = (boolean) Specify whether format-based query failures (such as providing text to a numeric field) should be ignored
$params['preference']       = (string) Specify the node or shard the operation should be performed on (default: random)
$params['q']                = (string) Query in the Lucene query string syntax
$params['routing']          = (string) Specific routing value
$params['_source']          = (list) True or false to return the _source field or not, or a list of fields to return
$params['_source_excludes'] = (list) A list of fields to exclude from the returned _source field
$params['_source_includes'] = (list) A list of fields to extract and return from the _source field
$params['body']             = (array) The query definition using the Query DSL
*/
----
****



[[Elasticsearch_ClientfieldCaps_fieldCaps]]
.`fieldCaps(array $params = [])`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names; use `_all` or empty string to perform the operation on all indices
$params['fields']             = (list) A comma-separated list of field names
$params['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']   = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
$params['include_unmapped']   = (boolean) Indicates whether unmapped fields should be included in the response. (Default = false)
$params['body']               = (array) An index filter specified with the Query DSL
*/
----
****



[[Elasticsearch_Clientget_get]]
.`get(array $params = [])`
****
[source,php]
----
/*
$params['id']               = (string) The document ID (Required)
$params['index']            = (string) The name of the index (Required)
$params['type']             = DEPRECATED (string) The type of the document (use `_all` to fetch the first document matching the ID across all types)
$params['stored_fields']    = (list) A comma-separated list of stored fields to return in the response
$params['preference']       = (string) Specify the node or shard the operation should be performed on (default: random)
$params['realtime']         = (boolean) Specify whether to perform the operation in realtime or search mode
$params['refresh']          = (boolean) Refresh the shard containing the document before performing the operation
$params['routing']          = (string) Specific routing value
$params['_source']          = (list) True or false to return the _source field or not, or a list of fields to return
$params['_source_excludes'] = (list) A list of fields to exclude from the returned _source field
$params['_source_includes'] = (list) A list of fields to extract and return from the _source field
$params['version']          = (number) Explicit version number for concurrency control
$params['version_type']     = (enum) Specific version type (Options = internal,external,external_gte,force)
*/
----
****



[[Elasticsearch_ClientgetScript_getScript]]
.`getScript(array $params = [])`
****
[source,php]
----
/*
$params['id']             = (string) Script ID
$params['master_timeout'] = (time) Specify timeout for connection to master
*/
----
****



[[Elasticsearch_ClientgetScriptContext_getScriptContext]]
.`getScriptContext(array $params = [])`
*NOTE:* This API is EXPERIMENTAL and may be changed or removed completely in a future release
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_ClientgetScriptLanguages_getScriptLanguages]]
.`getScriptLanguages(array $params = [])`
*NOTE:* This API is EXPERIMENTAL and may be changed or removed completely in a future release
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_ClientgetSource_getSource]]
.`getSource(array $params = [])`
****
[source,php]
----
/*
$params['id']               = (string) The document ID (Required)
$params['index']            = (string) The name of the index (Required)
$params['type']             = DEPRECATED (string) The type of the document; deprecated and optional starting with 7.0
$params['preference']       = (string) Specify the node or shard the operation should be performed on (default: random)
$params['realtime']         = (boolean) Specify whether to perform the operation in realtime or search mode
$params['refresh']          = (boolean) Refresh the shard containing the document before performing the operation
$params['routing']          = (string) Specific routing value
$params['_source']          = (list) True or false to return the _source field or not, or a list of fields to return
$params['_source_excludes'] = (list) A list of fields to exclude from the returned _source field
$params['_source_includes'] = (list) A list of fields to extract and return from the _source field
$params['version']          = (number) Explicit version number for concurrency control
$params['version_type']     = (enum) Specific version type (Options = internal,external,external_gte,force)
*/
----
****



[[Elasticsearch_Clientindex_index]]
.`index(array $params = [])`
****
[source,php]
----
/*
$params['id']                     = (string) Document ID
$params['index']                  = (string) The name of the index (Required)
$params['type']                   = DEPRECATED (string) The type of the document
$params['wait_for_active_shards'] = (string) Sets the number of shard copies that must be active before proceeding with the index operation. Defaults to 1, meaning the primary shard only. Set to `all` for all shard copies, otherwise set to any non-negative value less than or equal to the total number of copies for the shard (number of replicas + 1)
$params['op_type']                = (enum) Explicit operation type. Defaults to `index` for requests with an explicit document ID, and to `create`for requests without an explicit document ID (Options = index,create)
$params['refresh']                = (enum) If `true` then refresh the affected shards to make this operation visible to search, if `wait_for` then wait for a refresh to make this operation visible to search, if `false` (the default) then do nothing with refreshes. (Options = true,false,wait_for)
$params['routing']                = (string) Specific routing value
$params['timeout']                = (time) Explicit operation timeout
$params['version']                = (number) Explicit version number for concurrency control
$params['version_type']           = (enum) Specific version type (Options = internal,external,external_gte)
$params['if_seq_no']              = (number) only perform the index operation if the last operation that has changed the document has the specified sequence number
$params['if_primary_term']        = (number) only perform the index operation if the last operation that has changed the document has the specified primary term
$params['pipeline']               = (string) The pipeline id to preprocess incoming documents with
$params['require_alias']          = (boolean) When true, requires destination to be an alias. Default is false
$params['body']                   = (array) The document (Required)
*/
----
****



[[Elasticsearch_Clientinfo_info]]
.`info(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_Clientmget_mget]]
.`mget(array $params = [])`
****
[source,php]
----
/*
$params['index']            = (string) The name of the index
$params['type']             = DEPRECATED (string) The type of the document
$params['stored_fields']    = (list) A comma-separated list of stored fields to return in the response
$params['preference']       = (string) Specify the node or shard the operation should be performed on (default: random)
$params['realtime']         = (boolean) Specify whether to perform the operation in realtime or search mode
$params['refresh']          = (boolean) Refresh the shard containing the document before performing the operation
$params['routing']          = (string) Specific routing value
$params['_source']          = (list) True or false to return the _source field or not, or a list of fields to return
$params['_source_excludes'] = (list) A list of fields to exclude from the returned _source field
$params['_source_includes'] = (list) A list of fields to extract and return from the _source field
$params['body']             = (array) Document identifiers; can be either `docs` (containing full document information) or `ids` (when index and type is provided in the URL. (Required)
*/
----
****



[[Elasticsearch_Clientmsearch_msearch]]
.`msearch(array $params = [])`
****
[source,php]
----
/*
$params['index']                         = (list) A comma-separated list of index names to use as default
$params['type']                          = DEPRECATED (list) A comma-separated list of document types to use as default
$params['search_type']                   = (enum) Search operation type (Options = query_then_fetch,query_and_fetch,dfs_query_then_fetch,dfs_query_and_fetch)
$params['max_concurrent_searches']       = (number) Controls the maximum number of concurrent searches the multi search api will execute
$params['typed_keys']                    = (boolean) Specify whether aggregation and suggester names should be prefixed by their respective types in the response
$params['pre_filter_shard_size']         = (number) A threshold that enforces a pre-filter roundtrip to prefilter search shards based on query rewriting if the number of shards the search request expands to exceeds the threshold. This filter roundtrip can limit the number of shards significantly if for instance a shard can not match any documents based on its rewrite method ie. if date filters are mandatory to match but the shard bounds and the query are disjoint.
*/
----
****



[[Elasticsearch_ClientmsearchTemplate_msearchTemplate]]
.`msearchTemplate(array $params = [])`
****
[source,php]
----
/*
$params['index']                   = (list) A comma-separated list of index names to use as default
$params['type']                    = DEPRECATED (list) A comma-separated list of document types to use as default
$params['search_type']             = (enum) Search operation type (Options = query_then_fetch,query_and_fetch,dfs_query_then_fetch,dfs_query_and_fetch)
$params['typed_keys']              = (boolean) Specify whether aggregation and suggester names should be prefixed by their respective types in the response
$params['max_concurrent_searches'] = (number) Controls the maximum number of concurrent searches the multi search api will execute
$params['rest_total_hits_as_int']  = (boolean) Indicates whether hits.total should be rendered as an integer or an object in the rest search response (Default = false)
$params['ccs_minimize_roundtrips'] = (boolean) Indicates whether network round-trips should be minimized as part of cross-cluster search requests execution (Default = true)
$params['body']                    = (array) The request definitions (metadata-search request definition pairs), separated by newlines (Required)
*/
----
****



[[Elasticsearch_Clientmtermvectors_mtermvectors]]
.`mtermvectors(array $params = [])`
****
[source,php]
----
/*
$params['index']            = (string) The index in which the document resides.
*/
----
****



[[Elasticsearch_ClientopenPointInTime_openPointInTime]]
.`openPointInTime(array $params = [])`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names to open point in time; use `_all` or empty string to perform the operation on all indices
$params['preference']         = (string) Specify the node or shard the operation should be performed on (default: random)
$params['routing']            = (string) Specific routing value
$params['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
$params['keep_alive']         = (string) Specific the time to live for the point in time
*/
----
****



[[Elasticsearch_Clientping_ping]]
.`ping(array $params = [])`
****
[source,php]
----
/*
*/
----
****



[[Elasticsearch_ClientputScript_putScript]]
.`putScript(array $params = [])`
****
[source,php]
----
/*
$params['id']             = (string) Script ID (Required)
$params['context']        = (string) Script context
$params['timeout']        = (time) Explicit operation timeout
$params['master_timeout'] = (time) Specify timeout for connection to master
$params['body']           = (array) The document (Required)
*/
----
****



[[Elasticsearch_ClientrankEval_rankEval]]
.`rankEval(array $params = [])`
*NOTE:* This API is EXPERIMENTAL and may be changed or removed completely in a future release
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names to search; use `_all` or empty string to perform the operation on all indices
$params['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']   = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
$params['search_type']        = (enum) Search operation type (Options = query_then_fetch,dfs_query_then_fetch)
$params['body']               = (array) The ranking evaluation search definition, including search requests, document ratings and ranking metric definition. (Required)
*/
----
****



[[Elasticsearch_Clientreindex_reindex]]
.`reindex(array $params = [])`
****
[source,php]
----
/*
$params['refresh']                = (boolean) Should the affected indexes be refreshed?
$params['timeout']                = (time) Time each individual bulk request should wait for shards that are unavailable. (Default = 1m)
$params['wait_for_active_shards'] = (string) Sets the number of shard copies that must be active before proceeding with the reindex operation. Defaults to 1, meaning the primary shard only. Set to `all` for all shard copies, otherwise set to any non-negative value less than or equal to the total number of copies for the shard (number of replicas + 1)
$params['wait_for_completion']    = (boolean) Should the request should block until the reindex is complete. (Default = true)
$params['requests_per_second']    = (number) The throttle to set on this request in sub-requests per second. -1 means no throttle. (Default = 0)
$params['scroll']                 = (time) Control how long to keep the search context alive (Default = 5m)
$params['slices']                 = (number|string) The number of slices this task should be divided into. Defaults to 1, meaning the task isn't sliced into subtasks. Can be set to `auto`. (Default = 1)
$params['max_docs']               = (number) Maximum number of documents to process (default: all documents)
$params['body']                   = (array) The search definition using the Query DSL and the prototype for the index request. (Required)
*/
----
****



[[Elasticsearch_ClientreindexRethrottle_reindexRethrottle]]
.`reindexRethrottle(array $params = [])`
****
[source,php]
----
/*
$params['task_id']             = (string) The task id to rethrottle
$params['requests_per_second'] = (number) The throttle to set on this request in floating sub-requests per second. -1 means set no throttle. (Required)
*/
----
****



[[Elasticsearch_ClientrenderSearchTemplate_renderSearchTemplate]]
.`renderSearchTemplate(array $params = [])`
****
[source,php]
----
/*
$params['id']   = (string) The id of the stored search template
$params['body'] = (array) The search definition template and its params
*/
----
****



[[Elasticsearch_ClientscriptsPainlessExecute_scriptsPainlessExecute]]
.`scriptsPainlessExecute(array $params = [])`
*NOTE:* This API is EXPERIMENTAL and may be changed or removed completely in a future release
****
[source,php]
----
/*
$params['body'] = (array) The script to execute
*/
----
****



[[Elasticsearch_Clientscroll_scroll]]
.`scroll(array $params = [])`
****
[source,php]
----
/*
$params['scroll_id']              = DEPRECATED (string) The scroll ID
$params['scroll']                 = (time) Specify how long a consistent view of the index should be maintained for scrolled search
$params['rest_total_hits_as_int'] = (boolean) Indicates whether hits.total should be rendered as an integer or an object in the rest search response (Default = false)
$params['body']                   = (array) The scroll ID if not passed by URL or query parameter.
*/
----
****



[[Elasticsearch_Clientsearch_search]]
.`search(array $params = [])`
****
[source,php]
----
/*
$params['index']                         = (list) A comma-separated list of index names to search; use `_all` or empty string to perform the operation on all indices
$params['type']                          = DEPRECATED (list) A comma-separated list of document types to search; leave empty to perform the operation on all types
$params['analyzer']                      = (string) The analyzer to use for the query string
$params['analyze_wildcard']              = (boolean) Specify whether wildcard and prefix queries should be analyzed (default: false)
$params['ccs_minimize_roundtrips']       = (boolean) Indicates whether network round-trips should be minimized as part of cross-cluster search requests execution (Default = true)
$params['default_operator']              = (enum) The default operator for query string query (AND or OR) (Options = AND,OR) (Default = OR)
$params['df']                            = (string) The field to use as default where no field prefix is given in the query string
$params['explain']                       = (boolean) Specify whether to return detailed information about score computation as part of a hit
$params['stored_fields']                 = (list) A comma-separated list of stored fields to return as part of a hit
$params['docvalue_fields']               = (list) A comma-separated list of fields to return as the docvalue representation of a field for each hit
$params['from']                          = (number) Starting offset (default: 0)
$params['ignore_unavailable']            = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['ignore_throttled']              = (boolean) Whether specified concrete, expanded or aliased indices should be ignored when throttled
$params['allow_no_indices']              = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']              = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
$params['lenient']                       = (boolean) Specify whether format-based query failures (such as providing text to a numeric field) should be ignored
$params['preference']                    = (string) Specify the node or shard the operation should be performed on (default: random)
$params['q']                             = (string) Query in the Lucene query string syntax
$params['routing']                       = (list) A comma-separated list of specific routing values
$params['scroll']                        = (time) Specify how long a consistent view of the index should be maintained for scrolled search
$params['search_type']                   = (enum) Search operation type (Options = query_then_fetch,dfs_query_then_fetch)
$params['size']                          = (number) Number of hits to return (default: 10)
$params['sort']                          = (list) A comma-separated list of <field>:<direction> pairs
$params['_source']                       = (list) True or false to return the _source field or not, or a list of fields to return
$params['_source_excludes']              = (list) A list of fields to exclude from the returned _source field
$params['_source_includes']              = (list) A list of fields to extract and return from the _source field
$params['terminate_after']               = (number) The maximum number of documents to collect for each shard, upon reaching which the query execution will terminate early.
*/
----
****



[[Elasticsearch_ClientsearchShards_searchShards]]
.`searchShards(array $params = [])`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names to search; use `_all` or empty string to perform the operation on all indices
$params['preference']         = (string) Specify the node or shard the operation should be performed on (default: random)
$params['routing']            = (string) Specific routing value
$params['local']              = (boolean) Return local information, do not retrieve the state from master node (default: false)
$params['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']   = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
*/
----
****



[[Elasticsearch_ClientsearchTemplate_searchTemplate]]
.`searchTemplate(array $params = [])`
****
[source,php]
----
/*
$params['index']                   = (list) A comma-separated list of index names to search; use `_all` or empty string to perform the operation on all indices
$params['type']                    = DEPRECATED (list) A comma-separated list of document types to search; leave empty to perform the operation on all types
$params['ignore_unavailable']      = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['ignore_throttled']        = (boolean) Whether specified concrete, expanded or aliased indices should be ignored when throttled
$params['allow_no_indices']        = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']        = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
$params['preference']              = (string) Specify the node or shard the operation should be performed on (default: random)
$params['routing']                 = (list) A comma-separated list of specific routing values
$params['scroll']                  = (time) Specify how long a consistent view of the index should be maintained for scrolled search
$params['search_type']             = (enum) Search operation type (Options = query_then_fetch,query_and_fetch,dfs_query_then_fetch,dfs_query_and_fetch)
$params['explain']                 = (boolean) Specify whether to return detailed information about score computation as part of a hit
$params['profile']                 = (boolean) Specify whether to profile the query execution
$params['typed_keys']              = (boolean) Specify whether aggregation and suggester names should be prefixed by their respective types in the response
$params['rest_total_hits_as_int']  = (boolean) Indicates whether hits.total should be rendered as an integer or an object in the rest search response (Default = false)
$params['ccs_minimize_roundtrips'] = (boolean) Indicates whether network round-trips should be minimized as part of cross-cluster search requests execution (Default = true)
$params['body']                    = (array) The search definition template and its params (Required)
*/
----
****



[[Elasticsearch_Clienttermvectors_termvectors]]
.`termvectors(array $params = [])`
****
[source,php]
----
/*
$params['index']            = (string) The index in which the document resides. (Required)
$params['id']               = (string) The id of the document, when not specified a doc param should be supplied.
*/
----
****



[[Elasticsearch_Clientupdate_update]]
.`update(array $params = [])`
****
[source,php]
----
/*
$params['id']                     = (string) Document ID (Required)
$params['index']                  = (string) The name of the index (Required)
$params['type']                   = DEPRECATED (string) The type of the document
$params['wait_for_active_shards'] = (string) Sets the number of shard copies that must be active before proceeding with the update operation. Defaults to 1, meaning the primary shard only. Set to `all` for all shard copies, otherwise set to any non-negative value less than or equal to the total number of copies for the shard (number of replicas + 1)
$params['_source']                = (list) True or false to return the _source field or not, or a list of fields to return
$params['_source_excludes']       = (list) A list of fields to exclude from the returned _source field
$params['_source_includes']       = (list) A list of fields to extract and return from the _source field
$params['lang']                   = (string) The script language (default: painless)
$params['refresh']                = (enum) If `true` then refresh the affected shards to make this operation visible to search, if `wait_for` then wait for a refresh to make this operation visible to search, if `false` (the default) then do nothing with refreshes. (Options = true,false,wait_for)
$params['retry_on_conflict']      = (number) Specify how many times should the operation be retried when a conflict occurs (default: 0)
$params['routing']                = (string) Specific routing value
$params['timeout']                = (time) Explicit operation timeout
$params['if_seq_no']              = (number) only perform the update operation if the last operation that has changed the document has the specified sequence number
$params['if_primary_term']        = (number) only perform the update operation if the last operation that has changed the document has the specified primary term
$params['require_alias']          = (boolean) When true, requires destination is an alias. Default is false
$params['body']                   = (array) The request definition requires either `script` or partial `doc` (Required)
*/
----
****



[[Elasticsearch_ClientupdateByQuery_updateByQuery]]
.`updateByQuery(array $params = [])`
****
[source,php]
----
/*
$params['index']                  = (list) A comma-separated list of index names to search; use `_all` or empty string to perform the operation on all indices (Required)
$params['type']                   = DEPRECATED (list) A comma-separated list of document types to search; leave empty to perform the operation on all types
$params['analyzer']               = (string) The analyzer to use for the query string
$params['analyze_wildcard']       = (boolean) Specify whether wildcard and prefix queries should be analyzed (default: false)
$params['default_operator']       = (enum) The default operator for query string query (AND or OR) (Options = AND,OR) (Default = OR)
$params['df']                     = (string) The field to use as default where no field prefix is given in the query string
$params['from']                   = (number) Starting offset (default: 0)
$params['ignore_unavailable']     = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']       = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['conflicts']              = (enum) What to do when the update by query hits version conflicts? (Options = abort,proceed) (Default = abort)
$params['expand_wildcards']       = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
$params['lenient']                = (boolean) Specify whether format-based query failures (such as providing text to a numeric field) should be ignored
$params['pipeline']               = (string) Ingest pipeline to set on index requests made by this action. (default: none)
$params['preference']             = (string) Specify the node or shard the operation should be performed on (default: random)
$params['q']                      = (string) Query in the Lucene query string syntax
$params['routing']                = (list) A comma-separated list of specific routing values
$params['scroll']                 = (time) Specify how long a consistent view of the index should be maintained for scrolled search
$params['search_type']            = (enum) Search operation type (Options = query_then_fetch,dfs_query_then_fetch)
$params['search_timeout']         = (time) Explicit timeout for each search request. Defaults to no timeout.
*/
----
****



[[Elasticsearch_ClientupdateByQueryRethrottle_updateByQueryRethrottle]]
.`updateByQueryRethrottle(array $params = [])`
****
[source,php]
----
/*
$params['task_id']             = (string) The task id to rethrottle
$params['requests_per_second'] = (number) The throttle to set on this request in floating sub-requests per second. -1 means set no throttle. (Required)
*/
----
****



[[Elasticsearch_ClientasyncSearch_asyncSearch]]
.`asyncSearch()`
****
[source,php]
----
/*
Returns the asyncSearch namespace
*/
----
****



[[Elasticsearch_Clientautoscaling_autoscaling]]
.`autoscaling()`
****
[source,php]
----
/*
Returns the autoscaling namespace
*/
----
****



[[Elasticsearch_Clientcat_cat]]
.`cat()`
****
[source,php]
----
/*
Returns the cat namespace
*/
----
****



[[Elasticsearch_Clientccr_ccr]]
.`ccr()`
****
[source,php]
----
/*
Returns the ccr namespace
*/
----
****



[[Elasticsearch_Clientcluster_cluster]]
.`cluster()`
****
[source,php]
----
/*
Returns the cluster namespace
*/
----
****



[[Elasticsearch_ClientdanglingIndices_danglingIndices]]
.`danglingIndices()`
****
[source,php]
----
/*
Returns the danglingIndices namespace
*/
----
****



[[Elasticsearch_ClientdataFrameTransformDeprecated_dataFrameTransformDeprecated]]
.`dataFrameTransformDeprecated()`
****
[source,php]
----
/*
Returns the dataFrameTransformDeprecated namespace
*/
----
****



[[Elasticsearch_Clientenrich_enrich]]
.`enrich()`
****
[source,php]
----
/*
Returns the enrich namespace
*/
----
****



[[Elasticsearch_Clienteql_eql]]
.`eql()`
****
[source,php]
----
/*
Returns the eql namespace
*/
----
****



[[Elasticsearch_Clientfeatures_features]]
.`features()`
****
[source,php]
----
/*
Returns the features namespace
*/
----
****



[[Elasticsearch_Clientgraph_graph]]
.`graph()`
****
[source,php]
----
/*
Returns the graph namespace
*/
----
****



[[Elasticsearch_Clientilm_ilm]]
.`ilm()`
****
[source,php]
----
/*
Returns the ilm namespace
*/
----
****



[[Elasticsearch_Clientindices_indices]]
.`indices()`
****
[source,php]
----
/*
Returns the indices namespace
*/
----
****



[[Elasticsearch_Clientingest_ingest]]
.`ingest()`
****
[source,php]
----
/*
Returns the ingest namespace
*/
----
****



[[Elasticsearch_Clientlicense_license]]
.`license()`
****
[source,php]
----
/*
Returns the license namespace
*/
----
****



[[Elasticsearch_Clientlogstash_logstash]]
.`logstash()`
****
[source,php]
----
/*
Returns the logstash namespace
*/
----
****



[[Elasticsearch_Clientmigration_migration]]
.`migration()`
****
[source,php]
----
/*
Returns the migration namespace
*/
----
****



[[Elasticsearch_Clientml_ml]]
.`ml()`
****
[source,php]
----
/*
Returns the ml namespace
*/
----
****



[[Elasticsearch_Clientmonitoring_monitoring]]
.`monitoring()`
****
[source,php]
----
/*
Returns the monitoring namespace
*/
----
****



[[Elasticsearch_Clientnodes_nodes]]
.`nodes()`
****
[source,php]
----
/*
Returns the nodes namespace
*/
----
****



[[Elasticsearch_Clientrollup_rollup]]
.`rollup()`
****
[source,php]
----
/*
Returns the rollup namespace
*/
----
****



[[Elasticsearch_ClientsearchableSnapshots_searchableSnapshots]]
.`searchableSnapshots()`
****
[source,php]
----
/*
Returns the searchableSnapshots namespace
*/
----
****



[[Elasticsearch_Clientsecurity_security]]
.`security()`
****
[source,php]
----
/*
Returns the security namespace
*/
----
****



[[Elasticsearch_Clientslm_slm]]
.`slm()`
****
[source,php]
----
/*
Returns the slm namespace
*/
----
****



[[Elasticsearch_Clientsnapshot_snapshot]]
.`snapshot()`
****
[source,php]
----
/*
Returns the snapshot namespace
*/
----
****



[[Elasticsearch_Clientsql_sql]]
.`sql()`
****
[source,php]
----
/*
Returns the sql namespace
*/
----
****



[[Elasticsearch_Clientssl_ssl]]
.`ssl()`
****
[source,php]
----
/*
Returns the ssl namespace
*/
----
****



[[Elasticsearch_Clienttasks_tasks]]
.`tasks()`
****
[source,php]
----
/*
Returns the tasks namespace
*/
----
****



[[Elasticsearch_ClienttextStructure_textStructure]]
.`textStructure()`
****
[source,php]
----
/*
Returns the textStructure namespace
*/
----
****



[[Elasticsearch_Clienttransform_transform]]
.`transform()`
****
[source,php]
----
/*
Returns the transform namespace
*/
----
****



[[Elasticsearch_Clientwatcher_watcher]]
.`watcher()`
****
[source,php]
----
/*
Returns the watcher namespace
*/
----
****



[[Elasticsearch_Clientxpack_xpack]]
.`xpack()`
****
[source,php]
----
/*
Returns the xpack namespace
*/
----
****



[[Elasticsearch_Client-call-_call]]
.`__call(string $name, array $arguments)`
****
[source,php]
----
/*
Catchall for registered namespaces
*/
----
****



[[Elasticsearch_ClientextractArgument_extractArgument]]
.`extractArgument(array $params, string $arg)`
****
[source,php]
----
/*
Extract an argument from the array of parameters
*/
----
****


