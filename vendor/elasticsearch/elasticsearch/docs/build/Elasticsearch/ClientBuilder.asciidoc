

[[Elasticsearch_ClientBuilder]]
=== Elasticsearch\ClientBuilder




*Methods*

The class defines the following methods:

* <<Elasticsearch_ClientBuildercreate_create,`create()`>>
* <<Elasticsearch_ClientBuildergetTransport_getTransport,`getTransport()`>>
* <<Elasticsearch_ClientBuildergetEndpoint_getEndpoint,`getEndpoint()`>>
* <<Elasticsearch_ClientBuildergetRegisteredNamespacesBuilders_getRegisteredNamespacesBuilders,`getRegisteredNamespacesBuilders()`>>
* <<Elasticsearch_ClientBuilderfromConfig_fromConfig,`fromConfig()`>>
* <<Elasticsearch_ClientBuilderdefaultHandler_defaultHandler,`defaultHandler()`>>
* <<Elasticsearch_ClientBuildermultiHandler_multiHandler,`multiHandler()`>>
* <<Elasticsearch_ClientBuildersingleHandler_singleHandler,`singleHandler()`>>
* <<Elasticsearch_ClientBuildersetConnectionFactory_setConnectionFactory,`setConnectionFactory()`>>
* <<Elasticsearch_ClientBuildersetConnectionPool_setConnectionPool,`setConnectionPool()`>>
* <<Elasticsearch_ClientBuildersetEndpoint_setEndpoint,`setEndpoint()`>>
* <<Elasticsearch_ClientBuilderregisterNamespace_registerNamespace,`registerNamespace()`>>
* <<Elasticsearch_ClientBuildersetTransport_setTransport,`setTransport()`>>
* <<Elasticsearch_ClientBuildersetHandler_setHandler,`setHandler()`>>
* <<Elasticsearch_ClientBuildersetLogger_setLogger,`setLogger()`>>
* <<Elasticsearch_ClientBuildersetTracer_setTracer,`setTracer()`>>
* <<Elasticsearch_ClientBuildersetSerializer_setSerializer,`setSerializer()`>>
* <<Elasticsearch_ClientBuildersetHosts_setHosts,`setHosts()`>>
* <<Elasticsearch_ClientBuildersetApiKey_setApiKey,`setApiKey()`>>
* <<Elasticsearch_ClientBuildersetBasicAuthentication_setBasicAuthentication,`setBasicAuthentication()`>>
* <<Elasticsearch_ClientBuildersetElasticCloudId_setElasticCloudId,`setElasticCloudId()`>>
* <<Elasticsearch_ClientBuildersetConnectionParams_setConnectionParams,`setConnectionParams()`>>
* <<Elasticsearch_ClientBuildersetRetries_setRetries,`setRetries()`>>
* <<Elasticsearch_ClientBuildersetSelector_setSelector,`setSelector()`>>
* <<Elasticsearch_ClientBuildersetSniffOnStart_setSniffOnStart,`setSniffOnStart()`>>
* <<Elasticsearch_ClientBuildersetSSLCert_setSSLCert,`setSSLCert()`>>
* <<Elasticsearch_ClientBuildersetSSLKey_setSSLKey,`setSSLKey()`>>
* <<Elasticsearch_ClientBuildersetSSLVerification_setSSLVerification,`setSSLVerification()`>>
* <<Elasticsearch_ClientBuildersetElasticMetaHeader_setElasticMetaHeader,`setElasticMetaHeader()`>>
* <<Elasticsearch_ClientBuilderincludePortInHostHeader_includePortInHostHeader,`includePortInHostHeader()`>>
* <<Elasticsearch_ClientBuilderbuild_build,`build()`>>
* <<Elasticsearch_ClientBuilderinstantiate_instantiate,`instantiate()`>>



[[Elasticsearch_ClientBuildercreate_create]]
.`create()`
****
[source,php]
----
/*
Create an instance of ClientBuilder
*/
----
****



[[Elasticsearch_ClientBuildergetTransport_getTransport]]
.`getTransport()`
****
[source,php]
----
/*
Can supply first parm to Client::__construct() when invoking manually or with dependency injection
*/
----
****



[[Elasticsearch_ClientBuildergetEndpoint_getEndpoint]]
.`getEndpoint()`
****
[source,php]
----
/*
Can supply second parm to Client::__construct() when invoking manually or with dependency injection
*/
----
****



[[Elasticsearch_ClientBuildergetRegisteredNamespacesBuilders_getRegisteredNamespacesBuilders]]
.`getRegisteredNamespacesBuilders()`
****
[source,php]
----
/*
Can supply third parm to Client::__construct() when invoking manually or with dependency injection
*/
----
****



[[Elasticsearch_ClientBuilderfromConfig_fromConfig]]
.`fromConfig(array $config, bool $quiet = false)`
****
[source,php]
----
/*
Build a new client from the provided config.  Hash keys
should correspond to the method name e.g. ['connectionPool']
corresponds to setConnectionPool().
*/
----
****



[[Elasticsearch_ClientBuilderdefaultHandler_defaultHandler]]
.`defaultHandler(array $multiParams = [], array $singleParams = [])`
****
[source,php]
----
/*
Get the default handler
*/
----
****



[[Elasticsearch_ClientBuildermultiHandler_multiHandler]]
.`multiHandler(array $params = [])`
****
[source,php]
----
/*
Get the multi handler for async (CurlMultiHandler)
*/
----
****



[[Elasticsearch_ClientBuildersingleHandler_singleHandler]]
.`singleHandler()`
****
[source,php]
----
/*
Get the handler instance (CurlHandler)
*/
----
****



[[Elasticsearch_ClientBuildersetConnectionFactory_setConnectionFactory]]
.`setConnectionFactory(Elasticsearch\Connections\ConnectionFactoryInterface $connectionFactory)`
****
[source,php]
----
/*
Set connection Factory
*/
----
****



[[Elasticsearch_ClientBuildersetConnectionPool_setConnectionPool]]
.`setConnectionPool(AbstractConnectionPool|string $connectionPool, array $args = [])`
****
[source,php]
----
/*
Set the connection pool (default is StaticNoPingConnectionPool)
*/
----
****



[[Elasticsearch_ClientBuildersetEndpoint_setEndpoint]]
.`setEndpoint(callable $endpoint)`
****
[source,php]
----
/*
Set the endpoint
*/
----
****



[[Elasticsearch_ClientBuilderregisterNamespace_registerNamespace]]
.`registerNamespace(Elasticsearch\Namespaces\NamespaceBuilderInterface $namespaceBuilder)`
****
[source,php]
----
/*
Register namespace
*/
----
****



[[Elasticsearch_ClientBuildersetTransport_setTransport]]
.`setTransport(Elasticsearch\Transport $transport)`
****
[source,php]
----
/*
Set the transport
*/
----
****



[[Elasticsearch_ClientBuildersetHandler_setHandler]]
.`setHandler(mixed $handler)`
****
[source,php]
----
/*
Set the HTTP handler (cURL is default)
*/
----
****



[[Elasticsearch_ClientBuildersetLogger_setLogger]]
.`setLogger(Psr\Log\LoggerInterface $logger)`
****
[source,php]
----
/*
Set the PSR-3 Logger
*/
----
****



[[Elasticsearch_ClientBuildersetTracer_setTracer]]
.`setTracer(Psr\Log\LoggerInterface $tracer)`
****
[source,php]
----
/*
Set the PSR-3 tracer
*/
----
****



[[Elasticsearch_ClientBuildersetSerializer_setSerializer]]
.`setSerializer(Elasticsearch\Serializers\SerializerInterface|string $serializer)`
****
[source,php]
----
/*
Set the serializer
*/
----
****



[[Elasticsearch_ClientBuildersetHosts_setHosts]]
.`setHosts(array $hosts)`
****
[source,php]
----
/*
Set the hosts (nodes)
*/
----
****



[[Elasticsearch_ClientBuildersetApiKey_setApiKey]]
.`setApiKey(string $id, string $apiKey)`
****
[source,php]
----
/*
Set the APIKey Pair, consiting of the API Id and the ApiKey of the Response from /_security/api_key
*/
----
****



[[Elasticsearch_ClientBuildersetBasicAuthentication_setBasicAuthentication]]
.`setBasicAuthentication(string $username, string $password)`
****
[source,php]
----
/*
Set Basic access authentication
*/
----
****



[[Elasticsearch_ClientBuildersetElasticCloudId_setElasticCloudId]]
.`setElasticCloudId(string $cloudId)`
****
[source,php]
----
/*
Set Elastic Cloud ID to connect to Elastic Cloud
*/
----
****



[[Elasticsearch_ClientBuildersetConnectionParams_setConnectionParams]]
.`setConnectionParams(array $params)`
****
[source,php]
----
/*
Set connection parameters
*/
----
****



[[Elasticsearch_ClientBuildersetRetries_setRetries]]
.`setRetries(int $retries)`
****
[source,php]
----
/*
Set number or retries (default is equal to number of nodes)
*/
----
****



[[Elasticsearch_ClientBuildersetSelector_setSelector]]
.`setSelector(Elasticsearch\ConnectionPool\Selectors\SelectorInterface|string $selector)`
****
[source,php]
----
/*
Set the selector algorithm
*/
----
****



[[Elasticsearch_ClientBuildersetSniffOnStart_setSniffOnStart]]
.`setSniffOnStart(bool $sniffOnStart)`
****
[source,php]
----
/*
Set sniff on start
*/
----
****



[[Elasticsearch_ClientBuildersetSSLCert_setSSLCert]]
.`setSSLCert(string $cert, string $password = null)`
****
[source,php]
----
/*
Set SSL certificate
*/
----
****



[[Elasticsearch_ClientBuildersetSSLKey_setSSLKey]]
.`setSSLKey(string $key, string $password = null)`
****
[source,php]
----
/*
Set SSL key
*/
----
****



[[Elasticsearch_ClientBuildersetSSLVerification_setSSLVerification]]
.`setSSLVerification(bool|string $value = true)`
****
[source,php]
----
/*
Set SSL verification
*/
----
****



[[Elasticsearch_ClientBuildersetElasticMetaHeader_setElasticMetaHeader]]
.`setElasticMetaHeader($value = true)`
****
[source,php]
----
/*
Set or disable the x-elastic-client-meta header
*/
----
****



[[Elasticsearch_ClientBuilderincludePortInHostHeader_includePortInHostHeader]]
.`includePortInHostHeader(bool $enable)`
****
[source,php]
----
/*
Include the port in Host header
*/
----
****



[[Elasticsearch_ClientBuilderbuild_build]]
.`build()`
****
[source,php]
----
/*
Build and returns the Client object
*/
----
****



[[Elasticsearch_ClientBuilderinstantiate_instantiate]]
.`instantiate(Elasticsearch\Transport $transport, callable $endpoint, array $registeredNamespaces)`
****
[source,php]
----
/*
*/
----
****


