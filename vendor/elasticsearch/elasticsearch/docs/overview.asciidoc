[[overview]]
== Overview

This is the official PHP client for {es}. It is designed to be a low-level 
client that does not stray from the REST API.

All methods closely match the REST API, and furthermore, match the method 
structure of other language clients (Ruby, Python, and so on). We hope that this 
consistency makes it easy to get started with a client and to seamlessly switch 
from one language to the next with minimal effort.

The client is designed to be "unopinionated". There are a few universal niceties 
added to the client (cluster state sniffing, round-robin requests, and so on) 
but largely it is very barebones. This was intentional; we want a common base 
that more sophisticated libraries can build on top of.

* <<community_dsls>>
* <<community-integrations>>
* <<breaking_changes>>


include::community.asciidoc[]

include::breaking-changes.asciidoc[]