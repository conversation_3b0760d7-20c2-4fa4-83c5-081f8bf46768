[[host-config]]
=== Host Configuration

The client offers two options to configure hosts:

* <<inline-host-config>>
* <<extended-host-config>>


[discrete]
[[inline-host-config]]
==== Inline Host Configuration

The most common configuration is telling the client about your cluster: the 
number of nodes, their addresses, and ports. If no hosts are specified, the 
client attempts to connect to `localhost:9200`.

This behavior can be changed by using the `setHosts()` method on 
`ClientBuilder`. The method accepts an array of values, each entry corresponding 
to one node in your cluster. The format of the host can vary, depending on your 
needs (ip vs hostname, port, ssl, etc).

[source,php]
----
$hosts = [
    '***********:9200',         // IP + Port
    '***********',              // Just IP
    'mydomain.server.com:9201', // Domain + Port
    'mydomain2.server.com',     // Just Domain
    'https://localhost',        // SSL to localhost
    'https://***********:9200'  // SSL to IP + Port
];
$client = ClientBuilder::create()           // Instantiate a new ClientBuilder
                    ->setHosts($hosts)      // Set the hosts
                    ->build();              // Build the client object
----

Notice that the `ClientBuilder` object allows chaining method calls for brevity. 
It is also possible to call the methods individually:

[source,php]
----
$hosts = [
    '***********:9200',         // IP + Port
    '***********',              // Just IP
    'mydomain.server.com:9201', // Domain + Port
    'mydomain2.server.com',     // Just Domain
    'https://localhost',        // SSL to localhost
    'https://***********:9200'  // SSL to IP + Port
];
$clientBuilder = ClientBuilder::create();   // Instantiate a new ClientBuilder
$clientBuilder->setHosts($hosts);           // Set the hosts
$client = $clientBuilder->build();          // Build the client object
----


[discrete]
[[extended-host-config]]
==== Extended Host Configuration

The client also supports an _extended_ host configuration syntax. The inline 
configuration method relies on PHP's `filter_var()` and `parse_url()` methods to 
validate and extract the components of a URL. Unfortunately, these built-in 
methods run into problems with certain edge-cases. For example, `filter_var()` 
will not accept URL's that have underscores (which are questionably legal, 
depending on how you interpret the RFCs). Similarly, `parse_url()` will choke if 
a Basic Auth's password contains special characters such as a pound sign (`#`) 
or question-marks (`?`).

For this reason, the client supports an extended host syntax which provides 
greater control over host initialization. None of the components are validated, 
so edge-cases like underscores domain names will not cause problems.

The extended syntax is an array of parameters for each host. The structure of 
the parameter list is identical to the return values of a 
http://php.net/manual/en/function.parse-url.php#refsect1-function.parse-url-returnvalues[`parse_url()`] call:

[source,php]
----
$hosts = [
    // This is effectively equal to: "https://username:password!#$?*<EMAIL>:9200/elastic"
    [
        'host' => 'foo.com',
        'port' => '9200',
        'scheme' => 'https',
        'path' => '/elastic',
        'user' => 'username',
        'pass' => 'password!#$?*abc'
    ],

    // This is equal to "http://localhost:9200/"
    [
        'host' => 'localhost',    // Only host is required
    ]
];
$client = ClientBuilder::create()           // Instantiate a new ClientBuilder
                    ->setHosts($hosts)      // Set the hosts
                    ->build();              // Build the client object
----

Only the `host` parameter is required for each configured host. If not provided, 
the default port is `9200`. The default scheme is `http`.