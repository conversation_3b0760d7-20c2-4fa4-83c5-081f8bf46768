#!/usr/bin/env bash
#
# Version 1.1
# - Moved to .ci folder and seperated out `run-repository.sh`
# - Add `$RUNSCRIPTS` env var for running Elasticsearch dependent products
script_path=$(dirname $(realpath -s $0))
source $script_path/functions/imports.sh
set -euo pipefail

echo -e "\033[1m>>>>> Start [$STACK_VERSION container] >>>>>>>>>>>>>>>>>>>>>>>>>>>>>\033[0m"
DETACH=true bash .ci/run-elasticsearch.sh

if [[ -n "$RUNSCRIPTS" ]]; then
  for RUNSCRIPT in ${RUNSCRIPTS//,/ } ; do
    echo -e "\033[1m>>>>> Running run-$RUNSCRIPT.sh >>>>>>>>>>>>>>>>>>>>>>>>>>>>>\033[0m"
    CONTAINER_NAME=${RUNSCRIPT} \
      DETACH=true \
      bash .ci/run-${RUNSCRIPT}.sh
  done
fi

echo -e "\033[1m>>>>> Repository specific tests >>>>>>>>>>>>>>>>>>>>>>>>>>>>>\033[0m"
bash .ci/run-repository.sh