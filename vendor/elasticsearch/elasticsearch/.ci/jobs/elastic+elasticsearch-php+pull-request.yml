---
- job:
    name: elastic+elasticsearch-php+pull-request
    display-name: 'elastic / elasticsearch-php # pull-request'
    description: Testing of elasticsearch-php pull requests.
    junit_results: "tests/*-junit.xml"
    scm:
    - git:
        branches:
        - ${ghprbActualCommit}
        refspec: +refs/pull/*:refs/remotes/origin/pr/*
    triggers:
    - github-pull-request:
        org-list:
        - elastic
        allow-whitelist-orgs-as-admins: true
        github-hooks: true
        status-context: clients-ci
        cancel-builds-on-update: true
