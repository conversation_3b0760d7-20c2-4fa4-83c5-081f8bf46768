

##### GLOBAL METADATA

- meta:
    cluster: clients-ci

##### JOB DEFAULTS

- job:
    project-type: matrix
    logrotate:
      daysToKeep: 30
      numToKeep: 100
    properties:
    - github:
        url: https://github.com/elastic/elasticsearch-php/
    - inject:
        properties-content: HOME=$JENKINS_HOME
    concurrent: true
    node: flyweight
    scm:
    - git:
        name: origin
        credentials-id: f6c7695a-671e-4f4f-a331-acdce44ff9ba
        reference-repo: /var/lib/jenkins/.git-references/elasticsearch-php.git
        branches:
        - ${branch_specifier}
        url: https://github.com/elastic/elasticsearch-php.git
        wipe-workspace: 'True'
    triggers:
    - github
    vault:
      # vault read auth/approle/role/clients-ci/role-id
      role_id: ddbd0d44-0e51-105b-177a-c8fdfd445126
    axes:
    - axis:
        type: slave
        name: label
        values:
        - linux
    - axis:
        type: yaml
        filename: .ci/test-matrix.yml
        name: STACK_VERSION
    - axis:
        type: yaml
        filename: .ci/test-matrix.yml
        name: PHP_VERSION
    - axis:
        type: yaml
        filename: .ci/test-matrix.yml
        name: TEST_SUITE
    yaml-strategy:
      exclude-key: exclude
      filename: .ci/test-matrix.yml
    wrappers:
    - ansicolor
    - timeout:
        type: absolute
        timeout: 120
        fail: true
    - timestamps
    - workspace-cleanup
    builders:
    - shell: |-
        #!/usr/local/bin/runbld
        .ci/run-tests
    publishers:
    - email:
        recipients: <EMAIL>
    - junit:
        results: "tests/*-junit.xml"
        allow-empty-results: true
