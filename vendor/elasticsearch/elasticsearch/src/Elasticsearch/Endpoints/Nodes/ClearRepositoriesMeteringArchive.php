<?php
/**
 * Elasticsearch PHP client
 *
 * @link      https://github.com/elastic/elasticsearch-php/
 * @copyright Copyright (c) Elasticsearch B.V (https://www.elastic.co)
 * @license   http://www.apache.org/licenses/LICENSE-2.0 Apache License, Version 2.0
 * @license   https://www.gnu.org/licenses/lgpl-2.1.html GNU Lesser General Public License, Version 2.1 
 * 
 * Licensed to Elasticsearch B.V under one or more agreements.
 * Elasticsearch B.V licenses this file to you under the Apache 2.0 License or
 * the GNU Lesser General Public License, Version 2.1, at your option.
 * See the LICENSE file in the project root for more information.
 */
declare(strict_types = 1);

namespace Elasticsearch\Endpoints\Nodes;

use Elasticsearch\Common\Exceptions\RuntimeException;
use Elasticsearch\Endpoints\AbstractEndpoint;

/**
 * Class ClearRepositoriesMeteringArchive
 * Elasticsearch API name nodes.clear_repositories_metering_archive
 *
 * NOTE: this file is autogenerated using util/GenerateEndpoints.php
 * and Elasticsearch 7.17.0 (bee86328705acaa9a6daede7140defd4d9ec56bd)
 */
class ClearRepositoriesMeteringArchive extends AbstractEndpoint
{
    protected $node_id;
    protected $max_archive_version;

    public function getURI(): string
    {
        $node_id = $this->node_id ?? null;
        $max_archive_version = $this->max_archive_version ?? null;

        if (isset($node_id) && isset($max_archive_version)) {
            return "/_nodes/$node_id/_repositories_metering/$max_archive_version";
        }
        throw new RuntimeException('Missing parameter for the endpoint nodes.clear_repositories_metering_archive');
    }

    public function getParamWhitelist(): array
    {
        return [];
    }

    public function getMethod(): string
    {
        return 'DELETE';
    }

    public function setNodeId($node_id): ClearRepositoriesMeteringArchive
    {
        if (isset($node_id) !== true) {
            return $this;
        }
        if (is_array($node_id) === true) {
            $node_id = implode(",", $node_id);
        }
        $this->node_id = $node_id;

        return $this;
    }

    public function setMaxArchiveVersion($max_archive_version): ClearRepositoriesMeteringArchive
    {
        if (isset($max_archive_version) !== true) {
            return $this;
        }
        $this->max_archive_version = $max_archive_version;

        return $this;
    }
}
