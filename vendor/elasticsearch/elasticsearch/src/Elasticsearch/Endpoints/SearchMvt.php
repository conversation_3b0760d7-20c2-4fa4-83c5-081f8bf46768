<?php
/**
 * Elasticsearch PHP client
 *
 * @link      https://github.com/elastic/elasticsearch-php/
 * @copyright Copyright (c) Elasticsearch B.V (https://www.elastic.co)
 * @license   http://www.apache.org/licenses/LICENSE-2.0 Apache License, Version 2.0
 * @license   https://www.gnu.org/licenses/lgpl-2.1.html GNU Lesser General Public License, Version 2.1 
 * 
 * Licensed to Elasticsearch B.V under one or more agreements.
 * Elasticsearch B.V licenses this file to you under the Apache 2.0 License or
 * the GNU Lesser General Public License, Version 2.1, at your option.
 * See the LICENSE file in the project root for more information.
 */
declare(strict_types = 1);

namespace Elasticsearch\Endpoints;

use Elasticsearch\Common\Exceptions\RuntimeException;
use Elasticsearch\Endpoints\AbstractEndpoint;

/**
 * Class SearchMvt
 * Elasticsearch API name search_mvt
 *
 * NOTE: this file is autogenerated using util/GenerateEndpoints.php
 * and Elasticsearch 7.17.0 (bee86328705acaa9a6daede7140defd4d9ec56bd)
 */
class SearchMvt extends AbstractEndpoint
{
    protected $field;
    protected $zoom;
    protected $x;
    protected $y;

    public function getURI(): string
    {
        $index = $this->index ?? null;
        $field = $this->field ?? null;
        $zoom = $this->zoom ?? null;
        $x = $this->x ?? null;
        $y = $this->y ?? null;

        if (isset($index) && isset($field) && isset($zoom) && isset($x) && isset($y)) {
            return "/$index/_mvt/$field/$zoom/$x/$y";
        }
        throw new RuntimeException('Missing parameter for the endpoint search_mvt');
    }

    public function getParamWhitelist(): array
    {
        return [
            'exact_bounds',
            'extent',
            'grid_precision',
            'grid_type',
            'size',
            'track_total_hits'
        ];
    }

    public function getMethod(): string
    {
        return isset($this->body) ? 'POST' : 'GET';
    }

    public function setBody($body): SearchMvt
    {
        if (isset($body) !== true) {
            return $this;
        }
        $this->body = $body;

        return $this;
    }

    public function setField($field): SearchMvt
    {
        if (isset($field) !== true) {
            return $this;
        }
        $this->field = $field;

        return $this;
    }

    public function setZoom($zoom): SearchMvt
    {
        if (isset($zoom) !== true) {
            return $this;
        }
        $this->zoom = $zoom;

        return $this;
    }

    public function setX($x): SearchMvt
    {
        if (isset($x) !== true) {
            return $this;
        }
        $this->x = $x;

        return $this;
    }

    public function setY($y): SearchMvt
    {
        if (isset($y) !== true) {
            return $this;
        }
        $this->y = $y;

        return $this;
    }
}
