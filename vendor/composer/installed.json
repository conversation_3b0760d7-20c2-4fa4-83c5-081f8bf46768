{"packages": [{"name": "carbonphp/carbon-doctrine-types", "version": "3.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon-doctrine-types.git", "reference": "18ba5ddfec8976260ead6e866180bd5d2f71aa1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/18ba5ddfec8976260ead6e866180bd5d2f71aa1d", "reference": "18ba5ddfec8976260ead6e866180bd5d2f71aa1d", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"doctrine/dbal": "<4.0.0 || >=5.0.0"}, "require-dev": {"doctrine/dbal": "^4.0.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "time": "2024-02-09T16:56:22+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "description": "Types to use Carbon in Doctrine", "keywords": ["carbon", "date", "datetime", "doctrine", "time"], "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/3.2.0"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "install-path": "../carbonphp/carbon-doctrine-types"}, {"name": "clue/ndjson-react", "version": "v1.3.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/clue/reactphp-ndjson.git", "reference": "392dc165fce93b5bb5c637b67e59619223c931b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/reactphp-ndjson/zipball/392dc165fce93b5bb5c637b67e59619223c931b0", "reference": "392dc165fce93b5bb5c637b67e59619223c931b0", "shasum": ""}, "require": {"php": ">=5.3", "react/stream": "^1.2"}, "require-dev": {"phpunit/phpunit": "^9.5 || ^5.7 || ^4.8.35", "react/event-loop": "^1.2"}, "time": "2022-12-23T10:58:28+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Clue\\React\\NDJson\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Streaming newline-delimited JSON (NDJSON) parser and encoder for ReactPHP.", "homepage": "https://github.com/clue/reactphp-ndjson", "keywords": ["NDJSON", "json", "jsonlines", "newline", "reactphp", "streaming"], "support": {"issues": "https://github.com/clue/reactphp-ndjson/issues", "source": "https://github.com/clue/reactphp-ndjson/tree/v1.3.0"}, "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "install-path": "../clue/ndjson-react"}, {"name": "composer/pcre", "version": "3.3.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"phpstan/phpstan": "<1.11.10"}, "require-dev": {"phpstan/phpstan": "^1.12 || ^2", "phpstan/phpstan-strict-rules": "^1 || ^2", "phpunit/phpunit": "^8 || ^9"}, "time": "2024-11-12T16:29:46+00:00", "type": "library", "extra": {"phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-main": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/3.3.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./pcre"}, {"name": "composer/semver", "version": "3.4.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "symfony/phpunit-bridge": "^3 || ^7"}, "time": "2024-09-19T14:15:21+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./semver"}, {"name": "composer/xdebug-handler", "version": "3.0.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/6c1925561632e83d60a44492e0b344cf48ab85ef", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef", "shasum": ""}, "require": {"composer/pcre": "^1 || ^2 || ^3", "php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^8.5 || ^9.6 || ^10.5"}, "time": "2024-05-06T16:37:16+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.5"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./xdebug-handler"}, {"name": "doctrine/inflector", "version": "2.0.10", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/5817d0659c5b50c9b950feb9af7b9668e2c436bc", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^11.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25 || ^5.4"}, "time": "2024-02-18T20:23:39+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.10"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "install-path": "../doctrine/inflector"}, {"name": "doctrine/instantiator", "version": "1.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/0a0fa9780f5d4e507415a065172d26a98d02047b", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.16 || ^1", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.30 || ^5.4"}, "time": "2022-12-30T00:15:36+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.5.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "install-path": "../doctrine/instantiator"}, {"name": "easyswoole/spl", "version": "2.1.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/easy-swoole/spl.git", "reference": "6ca7321e476a40a3b70b15b836830ff030eec516"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/easy-swoole/spl/zipball/6ca7321e476a40a3b70b15b836830ff030eec516", "reference": "6ca7321e476a40a3b70b15b836830ff030eec516", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-simplexml": "*", "php": ">=8.1.0"}, "require-dev": {"easyswoole/phpunit": "^1.0", "easyswoole/swoole-ide-helper": "^1.0"}, "time": "2024-07-09T14:44:25+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"EasySwoole\\Spl\\": "src/", "EasySwoole\\Spl\\Test\\": "test/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "YF", "email": "<EMAIL>"}], "description": "php stander lib", "homepage": "https://www.easyswoole.com/", "keywords": ["async", "easyswoole", "framework", "swoole"], "support": {"issues": "https://github.com/easy-swoole/spl/issues", "source": "https://github.com/easy-swoole/spl/tree/2.1.3"}, "install-path": "../easyswoole/spl"}, {"name": "easyswoole/verifycode", "version": "3.1.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/easy-swoole/verify-code.git", "reference": "cfd7c1a7218e8b5f2319d9b3b73cf7a588c22ee6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/easy-swoole/verify-code/zipball/cfd7c1a7218e8b5f2319d9b3b73cf7a588c22ee6", "reference": "cfd7c1a7218e8b5f2319d9b3b73cf7a588c22ee6", "shasum": ""}, "require": {"easyswoole/spl": "^2.0", "ext-gd": "*", "php": ">=8.1"}, "time": "2023-09-06T06:45:56+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"EasySwoole\\VerifyCode\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "YF", "email": "<EMAIL>"}, {"name": "evalor", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/easy-swoole/verify-code/issues", "source": "https://github.com/easy-swoole/verify-code/tree/3.1.2"}, "install-path": "../easyswoole/verifycode"}, {"name": "elasticsearch/elasticsearch", "version": "v7.17.2", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/elastic/elasticsearch-php.git", "reference": "2d302233f2bb0926812d82823bb820d405e130fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elastic/elasticsearch-php/zipball/2d302233f2bb0926812d82823bb820d405e130fc", "reference": "2d302233f2bb0926812d82823bb820d405e130fc", "shasum": ""}, "require": {"ext-json": ">=1.3.7", "ezimuel/ringphp": "^1.1.2", "php": "^7.3 || ^8.0", "psr/log": "^1|^2|^3"}, "require-dev": {"ext-yaml": "*", "ext-zip": "*", "mockery/mockery": "^1.2", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.3", "squizlabs/php_codesniffer": "^3.4", "symfony/finder": "~4.0"}, "suggest": {"ext-curl": "*", "monolog/monolog": "Allows for client-level logging and tracing"}, "time": "2023-04-21T15:31:12+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/autoload.php"], "psr-4": {"Elasticsearch\\": "src/Elasticsearch/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0", "LGPL-2.1-only"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "PHP Client for Elasticsearch", "keywords": ["client", "elasticsearch", "search"], "support": {"issues": "https://github.com/elastic/elasticsearch-php/issues", "source": "https://github.com/elastic/elasticsearch-php/tree/v7.17.2"}, "install-path": "../elasticsearch/elasticsearch"}, {"name": "evenement/evenement", "version": "v3.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/igorw/evenement.git", "reference": "0a16b0d71ab13284339abb99d9d2bd813640efbc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/igorw/evenement/zipball/0a16b0d71ab13284339abb99d9d2bd813640efbc", "reference": "0a16b0d71ab13284339abb99d9d2bd813640efbc", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^9 || ^6"}, "time": "2023-08-08T05:53:35+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Evenement\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}], "description": "Événement is a very simple event dispatching library for PHP", "keywords": ["event-dispatcher", "event-emitter"], "support": {"issues": "https://github.com/igorw/evenement/issues", "source": "https://github.com/igorw/evenement/tree/v3.0.2"}, "install-path": "../evenement/evenement"}, {"name": "ezimuel/guzzlestreams", "version": "3.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ezimuel/guzzlestreams.git", "reference": "b4b5a025dfee70d6cd34c780e07330eb93d5b997"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/guzzlestreams/zipball/b4b5a025dfee70d6cd34c780e07330eb93d5b997", "reference": "b4b5a025dfee70d6cd34c780e07330eb93d5b997", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~9.0"}, "time": "2022-10-24T12:58:50+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/streams (abandoned) to be used with elasticsearch-php", "homepage": "http://guzzlephp.org/", "keywords": ["Guzzle", "stream"], "support": {"source": "https://github.com/ezimuel/guzzlestreams/tree/3.1.0"}, "install-path": "../ezimuel/guzzlestreams"}, {"name": "ezimuel/ringphp", "version": "1.2.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ezimuel/ringphp.git", "reference": "7887fc8488013065f72f977dcb281994f5fde9f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/ringphp/zipball/7887fc8488013065f72f977dcb281994f5fde9f4", "reference": "7887fc8488013065f72f977dcb281994f5fde9f4", "shasum": ""}, "require": {"ezimuel/guzzlestreams": "^3.0.1", "php": ">=5.4.0", "react/promise": "~2.0"}, "replace": {"guzzlehttp/ringphp": "self.version"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "~9.0"}, "suggest": {"ext-curl": "Guzzle will use specific adapters if cURL is present"}, "time": "2022-12-07T11:28:53+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Ring\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/RingPHP (abandoned) to be used with elasticsearch-php", "support": {"source": "https://github.com/ezimuel/ringphp/tree/1.2.2"}, "install-path": "../ezimuel/ringphp"}, {"name": "ezyang/htmlpurifier", "version": "v4.18.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "cb56001e54359df7ae76dc522d08845dc741621b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/cb56001e54359df7ae76dc522d08845dc741621b", "reference": "cb56001e54359df7ae76dc522d08845dc741621b", "shasum": ""}, "require": {"php": "~5.6.0 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"cerdic/css-tidy": "^1.7 || ^2.0", "simpletest/simpletest": "dev-master"}, "suggest": {"cerdic/css-tidy": "If you want to use the filter 'Filter.ExtractStyleBlocks'.", "ext-bcmath": "Used for unit conversion and imagecrash protection", "ext-iconv": "Converts text to and from non-UTF-8 encodings", "ext-tidy": "Used for pretty-printing HTML"}, "time": "2024-11-01T03:51:45+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "support": {"issues": "https://github.com/ezyang/htmlpurifier/issues", "source": "https://github.com/ezyang/htmlpurifier/tree/v4.18.0"}, "install-path": "../ezyang/htmlpurifier"}, {"name": "fidry/cpu-core-counter", "version": "1.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/theofidry/cpu-core-counter.git", "reference": "8520451a140d3f46ac33042715115e290cf5785f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theofidry/cpu-core-counter/zipball/8520451a140d3f46ac33042715115e290cf5785f", "reference": "8520451a140d3f46ac33042715115e290cf5785f", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"fidry/makefile": "^0.2.0", "fidry/php-cs-fixer-config": "^1.1.2", "phpstan/extension-installer": "^1.2.0", "phpstan/phpstan": "^1.9.2", "phpstan/phpstan-deprecation-rules": "^1.0.0", "phpstan/phpstan-phpunit": "^1.2.2", "phpstan/phpstan-strict-rules": "^1.4.4", "phpunit/phpunit": "^8.5.31 || ^9.5.26", "webmozarts/strict-phpunit": "^7.5"}, "time": "2024-08-06T10:04:20+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Fidry\\CpuCoreCounter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Théo FIDRY", "email": "<EMAIL>"}], "description": "Tiny utility to get the number of CPU cores.", "keywords": ["CPU", "core"], "support": {"issues": "https://github.com/theofidry/cpu-core-counter/issues", "source": "https://github.com/theofidry/cpu-core-counter/tree/1.2.0"}, "funding": [{"url": "https://github.com/theofidry", "type": "github"}], "install-path": "../fidry/cpu-core-counter"}, {"name": "fig/http-message-util", "version": "1.1.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message-util.git", "reference": "9d94dc0154230ac39e5bf89398b324a86f63f765"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message-util/zipball/9d94dc0154230ac39e5bf89398b324a86f63f765", "reference": "9d94dc0154230ac39e5bf89398b324a86f63f765", "shasum": ""}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "suggest": {"psr/http-message": "The package containing the PSR-7 interfaces"}, "time": "2020-11-24T22:02:12+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Fig\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Utility classes and constants for use with PSR-7 (psr/http-message)", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"issues": "https://github.com/php-fig/http-message-util/issues", "source": "https://github.com/php-fig/http-message-util/tree/1.1.5"}, "install-path": "../fig/http-message-util"}, {"name": "friendsofphp/php-cs-fixer", "version": "v3.67.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/PHP-CS-Fixer/PHP-CS-Fixer.git", "reference": "0ad34c75d1172f7d30320460e803887981830cbf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-CS-Fixer/PHP-CS-Fixer/zipball/0ad34c75d1172f7d30320460e803887981830cbf", "reference": "0ad34c75d1172f7d30320460e803887981830cbf", "shasum": ""}, "require": {"clue/ndjson-react": "^1.0", "composer/semver": "^3.4", "composer/xdebug-handler": "^3.0.3", "ext-filter": "*", "ext-json": "*", "ext-tokenizer": "*", "fidry/cpu-core-counter": "^1.2", "php": "^7.4 || ^8.0", "react/child-process": "^0.6.5", "react/event-loop": "^1.0", "react/promise": "^2.0 || ^3.0", "react/socket": "^1.0", "react/stream": "^1.0", "sebastian/diff": "^4.0 || ^5.1 || ^6.0", "symfony/console": "^5.4 || ^6.4 || ^7.0", "symfony/event-dispatcher": "^5.4 || ^6.4 || ^7.0", "symfony/filesystem": "^5.4 || ^6.4 || ^7.0", "symfony/finder": "^5.4 || ^6.4 || ^7.0", "symfony/options-resolver": "^5.4 || ^6.4 || ^7.0", "symfony/polyfill-mbstring": "^1.31", "symfony/polyfill-php80": "^1.31", "symfony/polyfill-php81": "^1.31", "symfony/process": "^5.4 || ^6.4 || ^7.2", "symfony/stopwatch": "^5.4 || ^6.4 || ^7.0"}, "require-dev": {"facile-it/paraunit": "^1.3.1 || ^2.4", "infection/infection": "^0.29.8", "justinrainbow/json-schema": "^5.3 || ^6.0", "keradus/cli-executor": "^2.1", "mikey179/vfsstream": "^1.6.12", "php-coveralls/php-coveralls": "^2.7", "php-cs-fixer/accessible-object": "^1.1", "php-cs-fixer/phpunit-constraint-isidenticalstring": "^1.5", "php-cs-fixer/phpunit-constraint-xmlmatchesxsd": "^1.5", "phpunit/phpunit": "^9.6.22 || ^10.5.40 || ^11.5.2", "symfony/var-dumper": "^5.4.48 || ^6.4.15 || ^7.2.0", "symfony/yaml": "^5.4.45 || ^6.4.13 || ^7.2.0"}, "suggest": {"ext-dom": "For handling output formats in XML", "ext-mbstring": "For handling non-UTF8 characters."}, "time": "2025-01-08T10:17:40+00:00", "bin": ["php-cs-fixer"], "type": "application", "installation-source": "dist", "autoload": {"psr-4": {"PhpCsFixer\\": "src/"}, "exclude-from-classmap": ["src/Fixer/Internal/*"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A tool to automatically fix PHP code style", "keywords": ["Static code analysis", "fixer", "standards", "static analysis"], "support": {"issues": "https://github.com/PHP-CS-Fixer/PHP-CS-Fixer/issues", "source": "https://github.com/PHP-CS-Fixer/PHP-CS-Fixer/tree/v3.67.0"}, "funding": [{"url": "https://github.com/keradus", "type": "github"}], "install-path": "../friendsofphp/php-cs-fixer"}, {"name": "graham-campbell/result-type", "version": "v1.1.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/GrahamCampbell/Result-Type.git", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/3ba905c11371512af9d9bdd27d99b782216b6945", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3"}, "require-dev": {"phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "time": "2024-07-20T21:45:45+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"GrahamCampbell\\ResultType\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "An Implementation Of The Result Type", "keywords": ["<PERSON>", "Graham<PERSON><PERSON><PERSON>", "Result Type", "Result-Type", "result"], "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.1.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/graham-campbell/result-type", "type": "tidelift"}], "install-path": "../graham-campbell/result-type"}, {"name": "guzzlehttp/guzzle", "version": "7.9.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "d281ed313b989f213357e3be1a179f02196ac99b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/d281ed313b989f213357e3be1a179f02196ac99b", "reference": "d281ed313b989f213357e3be1a179f02196ac99b", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "time": "2024-07-24T11:22:20+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "install-path": "../guzzlehttp/guzzle"}, {"name": "guzzlehttp/promises", "version": "2.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "f9c436286ab2892c7db7be8c8da4ef61ccf7b455"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/f9c436286ab2892c7db7be8c8da4ef61ccf7b455", "reference": "f9c436286ab2892c7db7be8c8da4ef61ccf7b455", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "time": "2024-10-17T10:06:22+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.4"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "install-path": "../guzzlehttp/promises"}, {"name": "guzzlehttp/psr7", "version": "2.7.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "a70f5c95fb43bc83f07c9c948baa0dc1829bf201"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/a70f5c95fb43bc83f07c9c948baa0dc1829bf201", "reference": "a70f5c95fb43bc83f07c9c948baa0dc1829bf201", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "time": "2024-07-18T11:15:46+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "install-path": "../guzzlehttp/psr7"}, {"name": "hamcrest/hamcrest-php", "version": "v2.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "shasum": ""}, "require": {"php": "^5.3|^7.0|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"}, "time": "2020-07-09T08:09:16+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.0.1"}, "install-path": "../hamcrest/hamcrest-php"}, {"name": "hyperf/amqp", "version": "v3.1.50", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/amqp.git", "reference": "db5e24822f2dcc3af055b5c985702f59901be380"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/amqp/zipball/db5e24822f2dcc3af055b5c985702f59901be380", "reference": "db5e24822f2dcc3af055b5c985702f59901be380", "shasum": ""}, "require": {"doctrine/instantiator": "^1.2.0", "hyperf/codec": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/coroutine": "~3.1.0", "hyperf/pool": "~3.1.0", "hyperf/process": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "php-amqplib/php-amqplib": "^3.5", "psr/container": "^1.0 || ^2.0", "psr/event-dispatcher": "^1.0", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "suggest": {"hyperf/di": "Required to use annotations.", "hyperf/event": "Declare queue and start consumers automatically."}, "time": "2025-01-08T08:57:09+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Amqp\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Amqp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A amqplib for hyperf.", "homepage": "https://hyperf.io", "keywords": ["AMQP", "hyperf", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/amqp"}, {"name": "hyperf/async-queue", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/async-queue.git", "reference": "1cd25666ac1e1f23c9eab6be642e86802a96307b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/async-queue/zipball/1cd25666ac1e1f23c9eab6be642e86802a96307b", "reference": "1cd25666ac1e1f23c9eab6be642e86802a96307b", "shasum": ""}, "require": {"hyperf/codec": "~3.1.0", "hyperf/collection": "~3.1.0", "hyperf/command": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/event-dispatcher": "^1.0"}, "suggest": {"hyperf/di": "Required to use annotations.", "hyperf/event": "Required to dispatch a event.", "hyperf/logger": "Required to use QueueHandleListener.", "hyperf/process": "Auto register the consumer process for server."}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\AsyncQueue\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\AsyncQueue\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A async queue component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["async-queue", "hyperf", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/async-queue"}, {"name": "hyperf/cache", "version": "v3.1.43", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/cache.git", "reference": "1e3cc54cee776c8d32cf40912dee5d366383bc33"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/cache/zipball/1e3cc54cee776c8d32cf40912dee5d366383bc33", "reference": "1e3cc54cee776c8d32cf40912dee5d366383bc33", "shasum": ""}, "require": {"hyperf/codec": "~3.1.0", "hyperf/collection": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"}, "suggest": {"hyperf/di": "Use cache annotations.", "hyperf/event": "Use listener to delete annotation cache."}, "time": "2024-10-09T10:22:39+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Cache\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A cache component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["cache", "hyperf", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/cache"}, {"name": "hyperf/code-parser", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/code-parser.git", "reference": "81953c4ea9035ac5f0a4740ae157310ca4e18ff2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/code-parser/zipball/81953c4ea9035ac5f0a4740ae157310ca4e18ff2", "reference": "81953c4ea9035ac5f0a4740ae157310ca4e18ff2", "shasum": ""}, "require": {"hyperf/collection": "~3.1.0", "hyperf/stringable": "~3.1.0", "hyperf/support": "~3.1.0", "php": ">=8.1"}, "suggest": {"jean85/pretty-package-versions": "Required to use PrettyVersions. (^1.2|^2.0)", "nikic/php-parser": "Required to use PhpParser. (^4.0)"}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\CodeParser\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A code parser component for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["code-parser", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/code-parser"}, {"name": "hyperf/codec", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/codec.git", "reference": "effc71c25e2d53c00fcf41da8bca083ac8a0db0e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/codec/zipball/effc71c25e2d53c00fcf41da8bca083ac8a0db0e", "reference": "effc71c25e2d53c00fcf41da8bca083ac8a0db0e", "shasum": ""}, "require": {"ext-json": "*", "ext-xml": "*", "hyperf/contract": "~3.1.0", "php": ">=8.1"}, "suggest": {"ext-igbinary": "Required to use IgbinarySerializerPacker."}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Codec\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A codec component for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["codec", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/codec"}, {"name": "hyperf/collection", "version": "v3.1.50", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/collection.git", "reference": "9ec6c151c6e1ce8407d617b7813eb52f4fb3c363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/collection/zipball/9ec6c151c6e1ce8407d617b7813eb52f4fb3c363", "reference": "9ec6c151c6e1ce8407d617b7813eb52f4fb3c363", "shasum": ""}, "require": {"hyperf/conditionable": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/macroable": "~3.1.0", "hyperf/stringable": "~3.1.0", "php": ">=8.1"}, "time": "2025-01-08T06:57:58+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Collection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Hyperf Collection package which come from illuminate/collections", "homepage": "https://hyperf.io", "keywords": ["collection", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/collection"}, {"name": "hyperf/command", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/command.git", "reference": "43047270c15bce06e19d217dc5ba02b284830e25"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/command/zipball/43047270c15bce06e19d217dc5ba02b284830e25", "reference": "43047270c15bce06e19d217dc5ba02b284830e25", "shasum": ""}, "require": {"hyperf/collection": "~3.1.0", "hyperf/context": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/coroutine": "~3.1.0", "hyperf/di": "~3.1.0", "hyperf/stringable": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/tappable": "~3.1.0", "php": ">=8.1", "psr/event-dispatcher": "^1.0", "symfony/console": "^5.0 || ^6.0 || ^7.0"}, "suggest": {"hyperf/di": "Required to use annotations.", "hyperf/event": "Required to use listeners."}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Command\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Command\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Command for hyperf", "keywords": ["command", "php", "swoole"], "support": {"issues": "https://github.com/hyperf/command/issues", "source": "https://github.com/hyperf/command/tree/v3.1.42"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/command"}, {"name": "hyperf/conditionable", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/conditionable.git", "reference": "dec9dec9dbde14e20f3d7ba000c3302381019de1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/conditionable/zipball/dec9dec9dbde14e20f3d7ba000c3302381019de1", "reference": "dec9dec9dbde14e20f3d7ba000c3302381019de1", "shasum": ""}, "require": {"php": ">=8.1"}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Conditionable\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Hyperf Macroable package which come from illuminate/conditionable", "homepage": "https://hyperf.io", "keywords": ["conditionable", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/conditionable"}, {"name": "hyperf/config", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/config.git", "reference": "1df5e310aab752d6195f89f5cc98daf3cdc4bb6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/config/zipball/1df5e310aab752d6195f89f5cc98daf3cdc4bb6e", "reference": "1df5e310aab752d6195f89f5cc98daf3cdc4bb6e", "shasum": ""}, "require": {"hyperf/collection": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/support": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "symfony/finder": "^5.0 || ^6.0 || ^7.0"}, "suggest": {"hyperf/context": "Required to use config()", "hyperf/di": "Allows using @Value annotation", "hyperf/event": "Allows using @Value annotation", "hyperf/framework": "Allows using @Value annotation", "vlucas/phpdotenv": "Allows using enviroment value to override the config"}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Config\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"files": ["./src/Functions.php"], "psr-4": {"Hyperf\\Config\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "An independent component that provides configuration container.", "homepage": "https://hyperf.io", "keywords": ["config", "configuration", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/config"}, {"name": "hyperf/constants", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/constants.git", "reference": "e1e1184779cd163f9603ce234e1ecccb6fe382ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/constants/zipball/e1e1184779cd163f9603ce234e1ecccb6fe382ae", "reference": "e1e1184779cd163f9603ce234e1ecccb6fe382ae", "shasum": ""}, "require": {"hyperf/di": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1"}, "suggest": {"hyperf/translation": "Required to use translation."}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Constants\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Constants\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A constants component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["constants", "hyperf", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/constants"}, {"name": "hyperf/context", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/context.git", "reference": "ac666862d644db7d813342c880826a1fda599bdf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/context/zipball/ac666862d644db7d813342c880826a1fda599bdf", "reference": "ac666862d644db7d813342c880826a1fda599bdf", "shasum": ""}, "require": {"hyperf/engine": "^2.0", "php": ">=8.1"}, "suggest": {"swow/psr7-plus": "Required to use RequestContext and ResponseContext"}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Context\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A coroutine/application context library.", "homepage": "https://hyperf.io", "keywords": ["Context", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/context"}, {"name": "hyperf/contract", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/contract.git", "reference": "6ef2c7f98917c52ccda3a37ae65b190848dde6c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/contract/zipball/6ef2c7f98917c52ccda3a37ae65b190848dde6c4", "reference": "6ef2c7f98917c52ccda3a37ae65b190848dde6c4", "shasum": ""}, "require": {"php": ">=8.1"}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Contract\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "The contracts of Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/contract"}, {"name": "hyperf/coordinator", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/coordinator.git", "reference": "a0497d2a260f166ab53fed2eca6bb4e48b49ef56"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/coordinator/zipball/a0497d2a260f166ab53fed2eca6bb4e48b49ef56", "reference": "a0497d2a260f166ab53fed2eca6bb4e48b49ef56", "shasum": ""}, "require": {"hyperf/engine": "^2.0", "php": ">=8.1"}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Coordinator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Hyperf Coordinator", "homepage": "https://hyperf.io", "keywords": ["Coordinator", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/coordinator"}, {"name": "hyperf/coroutine", "version": "v3.1.50", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/coroutine.git", "reference": "c353b3fbd86e30b5b51219e8867d479ea11e6811"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/coroutine/zipball/c353b3fbd86e30b5b51219e8867d479ea11e6811", "reference": "c353b3fbd86e30b5b51219e8867d479ea11e6811", "shasum": ""}, "require": {"hyperf/context": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/engine": "^2.0", "php": ">=8.1"}, "time": "2024-12-24T08:59:48+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Coroutine\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Hyperf Coroutine", "homepage": "https://hyperf.io", "keywords": ["coroutine", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/coroutine"}, {"name": "hyperf/crontab", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/crontab.git", "reference": "be1187515aabbfe96b2f6a5330b4ddd742e971c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/crontab/zipball/be1187515aabbfe96b2f6a5330b4ddd742e971c7", "reference": "be1187515aabbfe96b2f6a5330b4ddd742e971c7", "shasum": ""}, "require": {"hyperf/conditionable": "~3.1.0", "hyperf/framework": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/tappable": "~3.1.0", "hyperf/utils": "~3.1.0", "nesbot/carbon": "^2.0", "php": ">=8.1"}, "suggest": {"hyperf/command": "Required to use command trigger.", "hyperf/process": "Auto register the Crontab process for server."}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Crontab\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Crontab\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A crontab component for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["crontab", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/crontab"}, {"name": "hyperf/database", "version": "v3.1.48", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/database.git", "reference": "a16b070ee2ac2ec580a4c6f5bb6243350bed69e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/database/zipball/a16b070ee2ac2ec580a4c6f5bb6243350bed69e6", "reference": "a16b070ee2ac2ec580a4c6f5bb6243350bed69e6", "shasum": ""}, "require": {"hyperf/code-parser": "~3.1.0", "hyperf/collection": "~3.1.23", "hyperf/conditionable": "~3.1.0", "hyperf/macroable": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/tappable": "~3.1.0", "hyperf/utils": "~3.1.0", "nesbot/carbon": "^2.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/event-dispatcher": "^1.0"}, "suggest": {"doctrine/dbal": "Required to rename columns (^3.0).", "hyperf/paginator": "Required to paginate the result set (~3.1.0).", "nikic/php-parser": "Required to use ModelCommand. (^4.0)", "php-di/phpdoc-reader": "Required to use ModelCommand. (^2.2)"}, "time": "2024-12-12T01:58:16+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Database\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A flexible database library.", "homepage": "https://hyperf.io", "keywords": ["database", "hyperf", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/database"}, {"name": "hyperf/db-connection", "version": "v3.1.44", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/db-connection.git", "reference": "95dbb713fda5556106b803d0201e1631645985b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/db-connection/zipball/95dbb713fda5556106b803d0201e1631645985b5", "reference": "95dbb713fda5556106b803d0201e1631645985b5", "shasum": ""}, "require": {"hyperf/database": "~3.1.0", "hyperf/di": "~3.1.0", "hyperf/framework": "~3.1.0", "hyperf/model-listener": "~3.1.0", "hyperf/pool": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0"}, "time": "2024-10-11T08:58:16+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\DbConnection\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\DbConnection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A hyperf db connection handler for hyperf/database.", "homepage": "https://hyperf.io", "keywords": ["Connection", "database", "hyperf", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/db-connection"}, {"name": "hyperf/devtool", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/devtool.git", "reference": "ae1c8f547c21eb591a94ae3fbacf054542de82d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/devtool/zipball/ae1c8f547c21eb591a94ae3fbacf054542de82d3", "reference": "ae1c8f547c21eb591a94ae3fbacf054542de82d3", "shasum": ""}, "require": {"hyperf/code-parser": "~3.1.0", "hyperf/command": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/di": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1"}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Devtool\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Devtool\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A Devtool for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["dev", "devtool", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/devtool"}, {"name": "hyperf/di", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/di.git", "reference": "72b65de5022e3dca79ae1902c058048b9519aa72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/di/zipball/72b65de5022e3dca79ae1902c058048b9519aa72", "reference": "72b65de5022e3dca79ae1902c058048b9519aa72", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0", "hyperf/code-parser": "~3.1.0", "hyperf/pipeline": "~3.1.0", "hyperf/stdlib": "~3.1.0", "hyperf/support": "~3.1.0", "nikic/php-parser": "^4.1", "php": ">=8.1", "php-di/phpdoc-reader": "^2.2", "psr/container": "^1.0 || ^2.0", "symfony/finder": "^5.0 || ^6.0 || ^7.0", "vlucas/phpdotenv": "^5.0"}, "suggest": {"ext-pcntl": "Required to scan annotations.", "hyperf/config": "Require this component for annotation scan progress to retrieve the scan path."}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Di\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Di\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A DI for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["annotation", "di", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/di"}, {"name": "hyperf/dispatcher", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/dispatcher.git", "reference": "5cbdfd586bb8c3bbbabed5a23cec7faf52b744b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/dispatcher/zipball/5cbdfd586bb8c3bbbabed5a23cec7faf52b744b0", "reference": "5cbdfd586bb8c3bbbabed5a23cec7faf52b744b0", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/http-message": "^1.0 || ^2.0", "psr/http-server-middleware": "^1.0"}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Dispatcher\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Dispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A HTTP Server for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["dispatcher", "filter", "hyperf", "middleware", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/dispatcher"}, {"name": "hyperf/elasticsearch", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/elasticsearch.git", "reference": "ed654ef85e1b53deca8b128bb1ebbdc6cdba52b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/elasticsearch/zipball/ed654ef85e1b53deca8b128bb1ebbdc6cdba52b0", "reference": "ed654ef85e1b53deca8b128bb1ebbdc6cdba52b0", "shasum": ""}, "require": {"elasticsearch/elasticsearch": "^7.0", "hyperf/guzzle": "~3.1.0", "php": ">=8.1"}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Elasticsearch\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Elasticsearch client for hyperf", "keywords": ["elasticsearch", "php", "swoole"], "support": {"issues": "https://github.com/hyperf/elasticsearch/issues", "source": "https://github.com/hyperf/elasticsearch/tree/v3.1.42"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/elasticsearch"}, {"name": "hyperf/engine", "version": "v2.12.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/engine.git", "reference": "90be8143841482dcd00051050986251e126c6132"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/engine/zipball/90be8143841482dcd00051050986251e126c6132", "reference": "90be8143841482dcd00051050986251e126c6132", "shasum": ""}, "require": {"hyperf/engine-contract": "~1.11.0", "php": ">=8.0"}, "conflict": {"ext-swoole": "<5.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "hyperf/guzzle": "^3.0", "hyperf/http-message": "^3.0", "mockery/mockery": "^1.5", "phpstan/phpstan": "^1.0", "phpunit/phpunit": "^9.4", "swoole/ide-helper": "5.*"}, "suggest": {"ext-sockets": "*", "ext-swoole": ">=5.0", "hyperf/http-message": "Required to use ResponseEmitter.", "psr/http-message": "Required to use WebSocket Frame."}, "time": "2024-12-17T12:36:21+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Engine\\ConfigProvider"}, "branch-alias": {"dev-master": "2.12-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Engine\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Coroutine engine provided by swoole.", "keywords": ["engine", "hyperf", "php", "swoole"], "support": {"issues": "https://github.com/hyperf/engine/issues", "source": "https://github.com/hyperf/engine/tree/v2.12.1"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/engine"}, {"name": "hyperf/engine-contract", "version": "v1.11.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/engine-contract.git", "reference": "d478052ed1c5304eef7be68aae6cf42392611a15"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/engine-contract/zipball/d478052ed1c5304eef7be68aae6cf42392611a15", "reference": "d478052ed1c5304eef7be68aae6cf42392611a15", "shasum": ""}, "require": {"php": ">=8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "mockery/mockery": "^1.0", "phpstan/phpstan": "^1.0", "phpunit/phpunit": ">=7.0", "psr/http-message": "^1.0", "swoole/ide-helper": "^4.5"}, "suggest": {"psr/http-message": "Required to use WebSocket Frame."}, "time": "2024-11-19T04:15:31+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Engine\\Contract\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Contract for Coroutine Engine", "keywords": ["contract", "coroutine", "engine", "hyperf", "php"], "support": {"issues": "https://github.com/hyperf/engine-contract/issues", "source": "https://github.com/hyperf/engine-contract/tree/v1.11.0"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/engine-contract"}, {"name": "hyperf/event", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/event.git", "reference": "2b5fbbc94674a1a5e1622896eb3f7ecc80aa38c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/event/zipball/2b5fbbc94674a1a5e1622896eb3f7ecc80aa38c4", "reference": "2b5fbbc94674a1a5e1622896eb3f7ecc80aa38c4", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/stdlib": "~3.1.0", "php": ">=8.1", "psr/event-dispatcher": "^1.0"}, "suggest": {"hyperf/di": "Required to use annotatioins."}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Event\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Event\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "an event manager that implements PSR-14.", "homepage": "https://hyperf.io", "keywords": ["event", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/event"}, {"name": "hyperf/exception-handler", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/exception-handler.git", "reference": "df2135fb0ffe0bb61032911038aea6488077cdef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/exception-handler/zipball/df2135fb0ffe0bb61032911038aea6488077cdef", "reference": "df2135fb0ffe0bb61032911038aea6488077cdef", "shasum": ""}, "require": {"hyperf/context": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/dispatcher": "~3.1.0", "hyperf/http-message": "~3.1.0", "hyperf/stdlib": "~3.1.0", "hyperf/support": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/http-message": "^1.0 || ^2.0", "swow/psr7-plus": "^1.0"}, "suggest": {"hyperf/di": "Required to use #[ExceptionHandler]", "hyperf/event": "Required to use listeners", "hyperf/framework": "Required to use listeners", "hyperf/stringable": "Required to use WhoopsExceptionHandler"}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\ExceptionHandler\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\ExceptionHandler\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Exception handler for hyperf", "homepage": "https://hyperf.io", "keywords": ["exception-handler", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/exception-handler"}, {"name": "hyperf/framework", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/framework.git", "reference": "7b317d3891698a1eb0308e7306730d2ada1d6ff4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/framework/zipball/7b317d3891698a1eb0308e7306730d2ada1d6ff4", "reference": "7b317d3891698a1eb0308e7306730d2ada1d6ff4", "shasum": ""}, "require": {"fig/http-message-util": "^1.1.2", "hyperf/contract": "~3.1.0", "hyperf/coordinator": "~3.1.0", "hyperf/coroutine": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/event-dispatcher": "^1.0", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "suggest": {"ext-swoole": "Required to use swoole engine.", "hyperf/command": "Required to use Command annotation.", "hyperf/di": "Required to use Command annotation.", "hyperf/dispatcher": "Required to use BootApplication event.", "symfony/event-dispatcher": "Required to use symfony event dispatcher (^5.0|^6.0)."}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Framework\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Framework\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A coroutine framework that focuses on hyperspeed and flexible, specifically use for build microservices and middlewares.", "homepage": "https://hyperf.io", "keywords": ["Microservice", "framework", "hyperf", "middleware", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/framework"}, {"name": "hyperf/guzzle", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/guzzle.git", "reference": "fe838557530bf7b2d39dc604563c3a3ff8d5618f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/guzzle/zipball/fe838557530bf7b2d39dc604563c3a3ff8d5618f", "reference": "fe838557530bf7b2d39dc604563c3a3ff8d5618f", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.3 || ^7.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/http-message": "^1.0 || ^2.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "hyperf/pool": "Required to use pool handler."}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Guzzle\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Guzzle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Swoole coroutine handler for guzzle", "keywords": ["Guzzle", "handler", "php", "swoole"], "support": {"issues": "https://github.com/hyperf/guzzle/issues", "source": "https://github.com/hyperf/guzzle/tree/v3.1.42"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/guzzle"}, {"name": "hyperf/http-message", "version": "v3.1.48", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/http-message.git", "reference": "534ce81af0feaa0c4a9e132af1c6a9c5527a8d85"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/http-message/zipball/534ce81af0feaa0c4a9e132af1c6a9c5527a8d85", "reference": "534ce81af0feaa0c4a9e132af1c6a9c5527a8d85", "shasum": ""}, "require": {"hyperf/codec": "~3.1.0", "hyperf/engine": "^2.11", "hyperf/support": "~3.1.0", "laminas/laminas-mime": "^2.7", "php": ">=8.1", "psr/http-message": "^1.0 || ^2.0", "swow/psr7-plus": "^1.0"}, "suggest": {"psr/container": "Required to replace RequestParserInterface."}, "time": "2024-12-05T02:41:08+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\HttpMessage\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\HttpMessage\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "microservice framework base on swoole", "keywords": ["http-message", "hyperf", "php", "swoole"], "support": {"issues": "https://github.com/hyperf/http-message/issues", "source": "https://github.com/hyperf/http-message/tree/v3.1.48"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/http-message"}, {"name": "hyperf/http-server", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/http-server.git", "reference": "4727f15a743c6e9ca0a6b3c8494c5c62bae82f5a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/http-server/zipball/4727f15a743c6e9ca0a6b3c8494c5c62bae82f5a", "reference": "4727f15a743c6e9ca0a6b3c8494c5c62bae82f5a", "shasum": ""}, "require": {"hyperf/codec": "~3.1.0", "hyperf/collection": "~3.1.0", "hyperf/context": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/coroutine": "~3.1.0", "hyperf/dispatcher": "~3.1.0", "hyperf/event": "~3.1.0", "hyperf/exception-handler": "~3.1.0", "hyperf/http-message": "~3.1.0", "hyperf/macroable": "~3.1.0", "hyperf/serializer": "~3.1.0", "hyperf/server": "~3.1.0", "hyperf/stdlib": "~3.1.0", "hyperf/support": "~3.1.0", "nikic/fast-route": "^1.3", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "swow/psr7-plus": "^1.0"}, "suggest": {"hyperf/di": "Required to use annotations."}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\HttpServer\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\HttpServer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A HTTP Server for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["http", "http-server", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/http-server"}, {"name": "hyperf/logger", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/logger.git", "reference": "c96d32fae44bf350ef903ebca19c91a315458d72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/logger/zipball/c96d32fae44bf350ef903ebca19c91a315458d72", "reference": "c96d32fae44bf350ef903ebca19c91a315458d72", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "monolog/monolog": "^2.7 || ^3.1", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Logger\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Logger\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A logger component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "logger", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/logger"}, {"name": "hyperf/macroable", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/macroable.git", "reference": "0be650165b9e8ea073e199fac788ece70f16b6a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/macroable/zipball/0be650165b9e8ea073e199fac788ece70f16b6a4", "reference": "0be650165b9e8ea073e199fac788ece70f16b6a4", "shasum": ""}, "require": {"php": ">=8.1"}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Macroable\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Hyperf Macroable package which come from illuminate/macroable", "homepage": "https://hyperf.io", "keywords": ["hyperf", "macroable", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/macroable"}, {"name": "hyperf/memory", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/memory.git", "reference": "ccf25783d63a2610a4d797ec34c1e0093b755da2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/memory/zipball/ccf25783d63a2610a4d797ec34c1e0093b755da2", "reference": "ccf25783d63a2610a4d797ec34c1e0093b755da2", "shasum": ""}, "require": {"php": ">=8.1"}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Memory\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Memory\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "An independent component that use to operate and manage memory.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "memory", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/memory"}, {"name": "hyperf/model-cache", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/model-cache.git", "reference": "f1af97e63d12f9e2149bdef478541689cd31775f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/model-cache/zipball/f1af97e63d12f9e2149bdef478541689cd31775f", "reference": "f1af97e63d12f9e2149bdef478541689cd31775f", "shasum": ""}, "require": {"hyperf/codec": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/db-connection": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"}, "suggest": {"hyperf/event": "Required to use DeleteCacheListener."}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\ModelCache\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\ModelCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A model cache component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "model-cache", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/model-cache"}, {"name": "hyperf/model-listener", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/model-listener.git", "reference": "0181882fb6034cf2eac81b84b5c65c187af9f3a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/model-listener/zipball/0181882fb6034cf2eac81b84b5c65c187af9f3a4", "reference": "0181882fb6034cf2eac81b84b5c65c187af9f3a4", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/database": "~3.1.0", "hyperf/di": "~3.1.0", "hyperf/event": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0"}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\ModelListener\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\ModelListener\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A model listener for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "model-listener", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/model-listener"}, {"name": "hyperf/paginator", "version": "v3.1.49", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/paginator.git", "reference": "dd9d59f5601fbdaa69f488348c7debd1931feb7b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/paginator/zipball/dd9d59f5601fbdaa69f488348c7debd1931feb7b", "reference": "dd9d59f5601fbdaa69f488348c7debd1931feb7b", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1"}, "suggest": {"hyperf/event": "Reqiured to use PageResolverListener.", "hyperf/framework": "Reqiured to use PageResolverListener.", "hyperf/http-server": "Reqiured to use PageResolverListener."}, "time": "2024-12-17T09:42:13+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Paginator\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Paginator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A paginator component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "paginator", "php"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/paginator"}, {"name": "hyperf/pipeline", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/pipeline.git", "reference": "096d9a9f87ddea33209f134d30ae8d8867a195c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/pipeline/zipball/096d9a9f87ddea33209f134d30ae8d8867a195c7", "reference": "096d9a9f87ddea33209f134d30ae8d8867a195c7", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.0 || ^2.0"}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Pipeline\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Hyperf Macroable package which come from illuminate/pipeline", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "pipeline", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/pipeline"}, {"name": "hyperf/pool", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/pool.git", "reference": "004dd811bf760ea0032913a31284102742abb737"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/pool/zipball/004dd811bf760ea0032913a31284102742abb737", "reference": "004dd811bf760ea0032913a31284102742abb737", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0"}, "suggest": {"psr/event-dispatcher": "Required to use events."}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Pool\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Pool\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "An independent universal connection pool component.", "homepage": "https://hyperf.io", "keywords": ["connection-pool", "hyperf", "php", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/pool"}, {"name": "hyperf/process", "version": "v3.1.48", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/process.git", "reference": "8d68398bdb4f2623af1bec846399b6ce29bd7d2c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/process/zipball/8d68398bdb4f2623af1bec846399b6ce29bd7d2c", "reference": "8d68398bdb4f2623af1bec846399b6ce29bd7d2c", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/event-dispatcher": "^1.0"}, "suggest": {"hyperf/di": "Required to use annotations.", "hyperf/event": "Required to dump the message before and after process.", "hyperf/framework": "Required to use BootProcessListener."}, "time": "2024-12-02T10:54:30+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Process\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Process\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A process component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "process"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/process"}, {"name": "hyperf/redis", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/redis.git", "reference": "973a92c34be60353e978d85c434e65f366a817dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/redis/zipball/973a92c34be60353e978d85c434e65f366a817dd", "reference": "973a92c34be60353e978d85c434e65f366a817dd", "shasum": ""}, "require": {"ext-redis": "^5.0 || ^6.0", "hyperf/contract": "~3.1.0", "hyperf/pool": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/tappable": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0"}, "suggest": {"ext-redis": "Required to use sentinel mode (>=5.2).", "hyperf/di": "Create the RedisPool via dependency injection."}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Redis\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Redis\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A redis component for hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "pool", "redis"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/redis"}, {"name": "hyperf/serializer", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/serializer.git", "reference": "03c8a4840e0a7be83670c2fb0f850a2204fad076"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/serializer/zipball/03c8a4840e0a7be83670c2fb0f850a2204fad076", "reference": "03c8a4840e0a7be83670c2fb0f850a2204fad076", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "php": ">=8.1"}, "suggest": {"hyperf/di": "Required to use ExceptionNormalizer", "symfony/property-access": "Required to use SymfonyNormalizer (^5.0|^6.0)", "symfony/serializer": "Required to use SymfonyNormalizer (^5.0|^6.0)"}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Serializer\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Serializer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A serializer component for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swoole", "tappable"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/serializer"}, {"name": "hyperf/server", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/server.git", "reference": "e10c5ce6d9b72d3ca9ad16d36977e2e64d975460"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/server/zipball/e10c5ce6d9b72d3ca9ad16d36977e2e64d975460", "reference": "e10c5ce6d9b72d3ca9ad16d36977e2e64d975460", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/coordinator": "~3.1.0", "hyperf/engine": "^2.8", "hyperf/support": "~3.1.0", "hyperf/tappable": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/event-dispatcher": "^1.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "symfony/console": "^5.0 || ^6.0 || ^7.0"}, "suggest": {"hyperf/event": "Dump the info after server start.", "hyperf/framework": "Dump the info after server start."}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Server\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A base server library for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "server", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/server"}, {"name": "hyperf/session", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/session.git", "reference": "6875e7317d548cb2f28cbb92332a772ed0abeb9f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/session/zipball/6875e7317d548cb2f28cbb92332a772ed0abeb9f", "reference": "6875e7317d548cb2f28cbb92332a772ed0abeb9f", "shasum": ""}, "require": {"hyperf/collection": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/http-server-middleware": "^1.0"}, "suggest": {"hyperf/config": "Required to load session config.", "psr/container": "Required to use SessionFactory."}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Session\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Session\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A session library", "homepage": "https://hyperf.io", "keywords": ["php", "session"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/session"}, {"name": "hyperf/stdlib", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/stdlib.git", "reference": "13393734a4cc6c9878390b1f6b0fc7e5202c6b59"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/stdlib/zipball/13393734a4cc6c9878390b1f6b0fc7e5202c6b59", "reference": "13393734a4cc6c9878390b1f6b0fc7e5202c6b59", "shasum": ""}, "require": {"php": ">=8.1"}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Stdlib\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A stdlib component for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "stdlib", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/stdlib"}, {"name": "hyperf/stringable", "version": "v3.1.50", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/stringable.git", "reference": "89ab60e9ccabf024f5afc81a72f630cc67ae6687"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/stringable/zipball/89ab60e9ccabf024f5afc81a72f630cc67ae6687", "reference": "89ab60e9ccabf024f5afc81a72f630cc67ae6687", "shasum": ""}, "require": {"ext-mbstring": "*", "hyperf/collection": "~3.1.0", "hyperf/conditionable": "~3.1.0", "hyperf/macroable": "~3.1.0", "hyperf/tappable": "~3.1.0", "php": ">=8.1"}, "suggest": {"doctrine/inflector": "Required to use plural and singular methods.(^2.0|^3.0)", "ramsey/uuid": "Required to use uuid and orderedUuid methods.(^4.7|^5.0)", "symfony/uid": "Required to use ulid method.(^5.0|^6.0)"}, "time": "2025-01-08T06:57:58+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Stringable\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Hyperf Stringable package which come from illuminate/support", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "stringable", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/stringable"}, {"name": "hyperf/support", "version": "v3.1.50", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/support.git", "reference": "899b7dbfe39b60d25ec71da3ad445d9db0bbce1a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/support/zipball/899b7dbfe39b60d25ec71da3ad445d9db0bbce1a", "reference": "899b7dbfe39b60d25ec71da3ad445d9db0bbce1a", "shasum": ""}, "require": {"hyperf/collection": "~3.1.0", "hyperf/context": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/coroutine": "~3.1.0", "hyperf/macroable": "~3.1.0", "hyperf/stringable": "~3.1.0", "php": ">=8.1"}, "suggest": {"nesbot/carbon": "Use Carbon as DateTime object.(^2.0)"}, "time": "2025-01-08T07:28:13+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Support\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A support component for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "support", "swoole"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/support"}, {"name": "hyperf/tappable", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/tappable.git", "reference": "f5c5d343c95238dcb3fe500876ceadc175e221f8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/tappable/zipball/f5c5d343c95238dcb3fe500876ceadc175e221f8", "reference": "f5c5d343c95238dcb3fe500876ceadc175e221f8", "shasum": ""}, "require": {"php": ">=8.1"}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/Functions.php"], "psr-4": {"Hyperf\\Tappable\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Hyperf Macroable package which come from illuminate/tappable", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swoole", "tappable"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/tappable"}, {"name": "hyperf/testing", "version": "v3.1.48", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/testing.git", "reference": "e5e5eba5c304a876dd251e774ecdb3a8ebf97edd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/testing/zipball/e5e5eba5c304a876dd251e774ecdb3a8ebf97edd", "reference": "e5e5eba5c304a876dd251e774ecdb3a8ebf97edd", "shasum": ""}, "require": {"hyperf/codec": "~3.1.0", "hyperf/collection": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/coroutine": "~3.1.0", "hyperf/http-message": "~3.1.0", "hyperf/http-server": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "phpunit/phpunit": "^10.0", "psr/container": "^1.0 || ^2.0", "symfony/http-foundation": "^5.4 || ^6.0"}, "suggest": {"fakerphp/faker": "Required to use Faker feature.(^1.23)"}, "time": "2024-12-12T02:12:29+00:00", "bin": ["co-php<PERSON>t"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Testing\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Testing for hyperf", "keywords": ["dev", "php", "swoole", "testing"], "support": {"source": "https://github.com/hyperf/testing/tree/v3.1.48"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/testing"}, {"name": "hyperf/tracer", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/tracer.git", "reference": "8ec4b4f2499592a3d748e3760aa63bca31c78883"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/tracer/zipball/8ec4b4f2499592a3d748e3760aa63bca31c78883", "reference": "8ec4b4f2499592a3d748e3760aa63bca31c78883", "shasum": ""}, "require": {"hyperf/contract": "~3.1.0", "hyperf/di": "~3.1.0", "hyperf/guzzle": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "jcchavezs/zipkin-opentracing": "^2.0", "opentracing/opentracing": "^1.0", "php": ">=8.1", "psr/http-message": "^1.0 || ^2.0"}, "suggest": {"hyperf/event": "Required to use DbQueryExecutedListener.", "jonahgeorge/jaeger-client-php": "Required (^0.6) to use jaeger tracing.", "longlang/phpkafka": "Required (^1.2) to use Kafka Producer."}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\Tracer\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\Tracer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A open tracing system implemented for Hyperf or other coroutine framework", "homepage": "https://hyperf.io", "keywords": ["hyperf", "open-tracing", "php", "zipkin"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/tracer"}, {"name": "hyperf/utils", "version": "v3.1.42", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/utils.git", "reference": "4b13a567a61d08a3c4d058499e28a5b26fc18f1c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/utils/zipball/4b13a567a61d08a3c4d058499e28a5b26fc18f1c", "reference": "4b13a567a61d08a3c4d058499e28a5b26fc18f1c", "shasum": ""}, "require": {"doctrine/inflector": "^2.0", "hyperf/code-parser": "~3.1.0", "hyperf/codec": "~3.1.0", "hyperf/collection": "~3.1.0", "hyperf/context": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/coordinator": "~3.1.0", "hyperf/coroutine": "~3.1.0", "hyperf/engine": "^2.0", "hyperf/macroable": "~3.1.0", "hyperf/serializer": "~3.1.0", "hyperf/stringable": "~3.1.0", "hyperf/support": "~3.1.0", "php": ">=8.1"}, "time": "2024-09-25T02:54:12+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A tools package that could help developer solved the problem quickly.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swoole", "utils"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/utils"}, {"name": "hyperf/websocket-server", "version": "v3.1.50", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hyperf/websocket-server.git", "reference": "c770ebf0208738bbb21000db01f09a48e15023a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hyperf/websocket-server/zipball/c770ebf0208738bbb21000db01f09a48e15023a5", "reference": "c770ebf0208738bbb21000db01f09a48e15023a5", "shasum": ""}, "require": {"hyperf/collection": "~3.1.0", "hyperf/contract": "~3.1.0", "hyperf/exception-handler": "~3.1.0", "hyperf/http-server": "~3.1.0", "hyperf/support": "~3.1.0", "hyperf/utils": "~3.1.0", "php": ">=8.1", "psr/container": "^1.0 || ^2.0", "psr/event-dispatcher": "^1.0"}, "time": "2025-01-08T02:18:30+00:00", "type": "library", "extra": {"hyperf": {"config": "Hyperf\\WebSocketServer\\ConfigProvider"}, "branch-alias": {"dev-master": "3.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Hyperf\\WebSocketServer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A websocket server library for Hyperf.", "homepage": "https://hyperf.io", "keywords": ["hyperf", "php", "swoole", "websocket"], "support": {"docs": "https://hyperf.wiki", "issues": "https://github.com/hyperf/hyperf/issues", "pull-request": "https://github.com/hyperf/hyperf/pulls", "source": "https://github.com/hyperf/hyperf"}, "funding": [{"url": "https://hyperf.wiki/#/zh-cn/donate", "type": "custom"}, {"url": "https://opencollective.com/hyperf", "type": "open_collective"}], "install-path": "../hyperf/websocket-server"}, {"name": "jcchavezs/zipkin-opentracing", "version": "2.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/jcchavezs/zipkin-php-opentracing.git", "reference": "6bd908f84ff611dff4d64c5e7d510bd6a1107575"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jcchavezs/zipkin-php-opentracing/zipball/6bd908f84ff611dff4d64c5e7d510bd6a1107575", "reference": "6bd908f84ff611dff4d64c5e7d510bd6a1107575", "shasum": ""}, "require": {"opentracing/opentracing": "^1.0.1", "openzipkin/zipkin": "^3.0.0", "php": ">=7.4 || ^8.0"}, "provide": {"opentracing/opentracing": "1.0.0"}, "require-dev": {"phpspec/prophecy-phpunit": "^2.0", "phpstan/phpstan": "^0.12.26", "phpunit/phpunit": "^9.0", "squizlabs/php_codesniffer": "3.*"}, "time": "2023-11-03T22:38:29+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ZipkinOpenTracing\\": "./src/ZipkinOpenTracing/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A Zipkin bridge with OpenTracing", "support": {"issues": "https://github.com/jcchavezs/zipkin-php-opentracing/issues", "source": "https://github.com/jcchavezs/zipkin-php-opentracing/tree/2.0.4"}, "funding": [{"url": "https://www.paypal.me/jcchavezs", "type": "paypal"}], "install-path": "../jcchavezs/zipkin-opentracing"}, {"name": "laminas/laminas-mime", "version": "2.12.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/laminas/laminas-mime.git", "reference": "08cc544778829b7d68d27a097885bd6e7130135e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-mime/zipball/08cc544778829b7d68d27a097885bd6e7130135e", "reference": "08cc544778829b7d68d27a097885bd6e7130135e", "shasum": ""}, "require": {"laminas/laminas-stdlib": "^2.7 || ^3.0", "php": "~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0"}, "conflict": {"zendframework/zend-mime": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.4.0", "laminas/laminas-mail": "^2.19.0", "phpunit/phpunit": "~9.5.25"}, "suggest": {"laminas/laminas-mail": "Laminas\\Mail component"}, "time": "2023-11-02T16:47:19+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Laminas\\Mime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Create and parse MIME messages and parts", "homepage": "https://laminas.dev", "keywords": ["laminas", "mime"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-mime/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-mime/issues", "rss": "https://github.com/laminas/laminas-mime/releases.atom", "source": "https://github.com/laminas/laminas-mime"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "abandoned": "symfony/mime", "install-path": "../laminas/laminas-mime"}, {"name": "laminas/laminas-stdlib", "version": "3.20.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/laminas/laminas-stdlib.git", "reference": "8974a1213be42c3e2f70b2c27b17f910291ab2f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-stdlib/zipball/8974a1213be42c3e2f70b2c27b17f910291ab2f4", "reference": "8974a1213be42c3e2f70b2c27b17f910291ab2f4", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"zendframework/zend-stdlib": "*"}, "require-dev": {"laminas/laminas-coding-standard": "^3.0", "phpbench/phpbench": "^1.3.1", "phpunit/phpunit": "^10.5.38", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.26.1"}, "time": "2024-10-29T13:46:07+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Laminas\\Stdlib\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "SPL extensions, array utilities, error handlers, and more", "homepage": "https://laminas.dev", "keywords": ["laminas", "stdlib"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-stdlib/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-stdlib/issues", "rss": "https://github.com/laminas/laminas-stdlib/releases.atom", "source": "https://github.com/laminas/laminas-stdlib"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "install-path": "../laminas/laminas-stdlib"}, {"name": "maennchen/zipstream-php", "version": "3.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "6187e9cc4493da94b9b63eb2315821552015fca9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/6187e9cc4493da94b9b63eb2315821552015fca9", "reference": "6187e9cc4493da94b9b63eb2315821552015fca9", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-zlib": "*", "php-64bit": "^8.1"}, "require-dev": {"ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.16", "guzzlehttp/guzzle": "^7.5", "mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.5", "phpunit/phpunit": "^10.0", "vimeo/psalm": "^5.0"}, "suggest": {"guzzlehttp/psr7": "^2.4", "psr/http-message": "^2.0"}, "time": "2024-10-10T12:33:01+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "support": {"issues": "https://github.com/maennchen/ZipStream-PHP/issues", "source": "https://github.com/maennchen/ZipStream-PHP/tree/3.1.1"}, "funding": [{"url": "https://github.com/maennchen", "type": "github"}], "install-path": "../maennchen/zipstream-php"}, {"name": "markbaker/complex", "version": "3.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.7"}, "time": "2022-12-06T16:21:08+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "support": {"issues": "https://github.com/MarkBaker/PHPComplex/issues", "source": "https://github.com/MarkBaker/PHPComplex/tree/3.0.2"}, "install-path": "../markbaker/complex"}, {"name": "markbaker/matrix", "version": "3.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "728434227fe21be27ff6d86621a1b13107a2562c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/728434227fe21be27ff6d86621a1b13107a2562c", "reference": "728434227fe21be27ff6d86621a1b13107a2562c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.7"}, "time": "2022-12-02T22:17:43+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "support": {"issues": "https://github.com/MarkBaker/PHPMatrix/issues", "source": "https://github.com/MarkBaker/PHPMatrix/tree/3.0.1"}, "install-path": "../markbaker/matrix"}, {"name": "mockery/mockery", "version": "1.6.12", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/1f4efdd7d3beafe9807b08156dfcb176d18f1699", "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": ">=7.3"}, "conflict": {"phpunit/phpunit": "<8.0"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.6.17", "symplify/easy-coding-standard": "^12.1.14"}, "time": "2024-05-16T03:13:13+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["library/helpers.php", "library/Mockery.php"], "psr-4": {"Mockery\\": "library/Mockery"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/padraic", "role": "Author"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://davedevelopment.co.uk", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/ghostwriter", "role": "Lead Developer"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"docs": "https://docs.mockery.io/", "issues": "https://github.com/mockery/mockery/issues", "rss": "https://github.com/mockery/mockery/releases.atom", "security": "https://github.com/mockery/mockery/security/advisories", "source": "https://github.com/mockery/mockery"}, "install-path": "../mockery/mockery"}, {"name": "monolog/monolog", "version": "3.8.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "aef6ee73a77a66e404dd6540934a9ef1b3c855b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/aef6ee73a77a66e404dd6540934a9ef1b3c855b4", "reference": "aef6ee73a77a66e404dd6540934a9ef1b3c855b4", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.8", "phpstan/phpstan": "^2", "phpstan/phpstan-deprecation-rules": "^2", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "^10.5.17 || ^11.0.7", "predis/predis": "^1.1 || ^2", "rollbar/rollbar": "^4.0", "ruflin/elastica": "^7 || ^8", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "time": "2024-12-05T17:15:07+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.8.1"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "install-path": "../monolog/monolog"}, {"name": "myclabs/deep-copy", "version": "1.12.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "123267b2c49fbf30d78a7b2d333f6be754b94845"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/123267b2c49fbf30d78a7b2d333f6be754b94845", "reference": "123267b2c49fbf30d78a7b2d333f6be754b94845", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "time": "2024-11-08T17:47:46+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.12.1"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "install-path": "../myclabs/deep-copy"}, {"name": "nesbot/carbon", "version": "2.72.6", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon.git", "reference": "1e9d50601e7035a4c61441a208cb5bed73e108c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/1e9d50601e7035a4c61441a208cb5bed73e108c5", "reference": "1e9d50601e7035a4c61441a208cb5bed73e108c5", "shasum": ""}, "require": {"carbonphp/carbon-doctrine-types": "*", "ext-json": "*", "php": "^7.1.8 || ^8.0", "psr/clock": "^1.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"doctrine/dbal": "^2.0 || ^3.1.4 || ^4.0", "doctrine/orm": "^2.7 || ^3.0", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "<6", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}, "time": "2024-12-27T09:28:11+00:00", "bin": ["bin/carbon"], "type": "library", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-2.x": "2.x-dev", "dev-master": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "install-path": "../nesbot/carbon"}, {"name": "nikic/fast-route", "version": "v1.3.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/nikic/FastRoute.git", "reference": "181d480e08d9476e61381e04a71b34dc0432e812"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/FastRoute/zipball/181d480e08d9476e61381e04a71b34dc0432e812", "reference": "181d480e08d9476e61381e04a71b34dc0432e812", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35|~5.7"}, "time": "2018-02-13T20:26:39+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/functions.php"], "psr-4": {"FastRoute\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Fast request router for PHP", "keywords": ["router", "routing"], "support": {"issues": "https://github.com/nikic/FastRoute/issues", "source": "https://github.com/nikic/FastRoute/tree/master"}, "install-path": "../nikic/fast-route"}, {"name": "nikic/php-parser", "version": "v4.19.4", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/715f4d25e225bc47b293a8b997fe6ce99bf987d2", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "time": "2024-09-29T15:01:53+00:00", "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.19.4"}, "install-path": "../nikic/php-parser"}, {"name": "opentracing/opentracing", "version": "1.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/opentracing/opentracing-php.git", "reference": "cd60bd1fb2a25280600bc74c7f9e0c13881a9116"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opentracing/opentracing-php/zipball/cd60bd1fb2a25280600bc74c7f9e0c13881a9116", "reference": "cd60bd1fb2a25280600bc74c7f9e0c13881a9116", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"phpstan/phpstan": "~0.12", "phpunit/phpunit": "^7.0 || ^9.0", "squizlabs/php_codesniffer": "3.*"}, "time": "2022-01-27T19:59:21+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/OpenTracing/Tags.php", "src/OpenTracing/Formats.php"], "psr-4": {"OpenTracing\\": "src/OpenTracing/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "OpenTracing API for PHP", "support": {"issues": "https://github.com/opentracing/opentracing-php/issues", "source": "https://github.com/opentracing/opentracing-php/tree/1.0.2"}, "install-path": "../opentracing/opentracing"}, {"name": "openzipkin/zipkin", "version": "3.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/openzipkin/zipkin-php.git", "reference": "e2809f8b6775796d2116b3ca73576a1734296ff6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/openzipkin/zipkin-php/zipball/e2809f8b6775796d2116b3ca73576a1734296ff6", "reference": "e2809f8b6775796d2116b3ca73576a1734296ff6", "shasum": ""}, "require": {"ext-curl": "*", "php": "^7.4 || ^8.0", "psr/http-message": "~1.0 || ~2.0", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"ext-mysqli": "*", "jcchavezs/httptest": "~0.2", "middlewares/fast-route": "^2.0", "middlewares/request-handler": "^2.0", "nyholm/psr7": "^1.4", "phpspec/prophecy-phpunit": "^2.0", "phpstan/phpstan": "^0.12.26", "phpunit/phpunit": "~9", "psr/http-client": "^1.0", "psr/http-server-middleware": "^1.0", "squizlabs/php_codesniffer": "3.*"}, "suggest": {"ext-mysqli": "Allows to use mysqli instrumentation.", "psr/http-client": "Allows to instrument HTTP clients following PSR18.", "psr/http-server-middleware": "Allows to instrument HTTP servers via middlewares following PSR15."}, "time": "2023-09-28T20:54:04+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["./src/Zipkin/Propagation/Id.php", "./src/Zipkin/Timestamp.php", "./src/Zipkin/Kind.php", "./src/Zipkin/Tags.php", "./src/Zipkin/Annotations.php", "./src/Zipkin/SpanName.php"], "psr-4": {"Zipkin\\": "./src/<PERSON>ip<PERSON>/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A Zipkin instrumentation for PHP", "homepage": "https://github.com/openzipkin/zipkin-php", "keywords": ["distributed-tracing", "openzipkin", "tracing", "zipkin"], "support": {"issues": "https://github.com/openzipkin/zipkin-php/issues", "source": "https://github.com/openzipkin/zipkin-php/tree/3.2.0"}, "install-path": "../openzipkin/zipkin"}, {"name": "paragonie/constant_time_encoding", "version": "v3.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "df1e7fde177501eee2037dd159cf04f5f301a512"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/df1e7fde177501eee2037dd159cf04f5f301a512", "reference": "df1e7fde177501eee2037dd159cf04f5f301a512", "shasum": ""}, "require": {"php": "^8"}, "require-dev": {"phpunit/phpunit": "^9", "vimeo/psalm": "^4|^5"}, "time": "2024-05-08T12:36:18+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/constant_time_encoding/issues", "source": "https://github.com/paragonie/constant_time_encoding"}, "install-path": "../paragonie/constant_time_encoding"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "version_normalized": "**********", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "time": "2020-10-15T08:29:30+00:00", "type": "library", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "install-path": "../paragonie/random_compat"}, {"name": "phar-io/manifest", "version": "2.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "54750ef60c58e43759730615a392c31c80e23176"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/54750ef60c58e43759730615a392c31c80e23176", "reference": "54750ef60c58e43759730615a392c31c80e23176", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "time": "2024-03-03T12:33:53+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.4"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "install-path": "../phar-io/manifest"}, {"name": "phar-io/version", "version": "3.2.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "time": "2022-02-21T01:04:05+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "install-path": "../phar-io/version"}, {"name": "php-amqplib/php-amqplib", "version": "v3.7.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-amqplib/php-amqplib.git", "reference": "738a73eb0019b6c99d9bc25d7a0c0dd8f56a5199"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-amqplib/php-amqplib/zipball/738a73eb0019b6c99d9bc25d7a0c0dd8f56a5199", "reference": "738a73eb0019b6c99d9bc25d7a0c0dd8f56a5199", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-sockets": "*", "php": "^7.2||^8.0", "phpseclib/phpseclib": "^2.0|^3.0"}, "conflict": {"php": "7.4.0 - 7.4.1"}, "replace": {"videlalvaro/php-amqplib": "self.version"}, "require-dev": {"ext-curl": "*", "nategood/httpful": "^0.2.20", "phpunit/phpunit": "^7.5|^9.5", "squizlabs/php_codesniffer": "^3.6"}, "time": "2024-11-21T09:21:41+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"PhpAmqpLib\\": "PhpAmqpLib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "Original Maintainer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Maintainer"}], "description": "Formerly videlalvaro/php-amqplib.  This library is a pure PHP implementation of the AMQP protocol. It's been tested against RabbitMQ.", "homepage": "https://github.com/php-amqplib/php-amqplib/", "keywords": ["message", "queue", "rabbitmq"], "support": {"issues": "https://github.com/php-amqplib/php-amqplib/issues", "source": "https://github.com/php-amqplib/php-amqplib/tree/v3.7.2"}, "install-path": "../php-amqplib/php-amqplib"}, {"name": "php-di/phpdoc-reader", "version": "2.2.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHP-DI/PhpDocReader.git", "reference": "66daff34cbd2627740ffec9469ffbac9f8c8185c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-DI/PhpDocReader/zipball/66daff34cbd2627740ffec9469ffbac9f8c8185c", "reference": "66daff34cbd2627740ffec9469ffbac9f8c8185c", "shasum": ""}, "require": {"php": ">=7.2.0"}, "require-dev": {"mnapoli/hard-mode": "~0.3.0", "phpunit/phpunit": "^8.5|^9.0"}, "time": "2020-10-12T12:39:22+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PhpDocReader\\": "src/PhpDocReader"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PhpDocReader parses @var and @param values in PHP docblocks (supports namespaced class names with the same resolution rules as PHP)", "keywords": ["phpdoc", "reflection"], "support": {"issues": "https://github.com/PHP-DI/PhpDocReader/issues", "source": "https://github.com/PHP-DI/PhpDocReader/tree/2.2.1"}, "install-path": "../php-di/phpdoc-reader"}, {"name": "phpoffice/phpspreadsheet", "version": "1.29.9", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "ffb47b639649fc9c8a6fa67977a27b756592ed85"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/ffb47b639649fc9c8a6fa67977a27b756592ed85", "reference": "ffb47b639649fc9c8a6fa67977a27b756592ed85", "shasum": ""}, "require": {"composer/pcre": "^3.3", "ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "ezyang/htmlpurifier": "^4.15", "maennchen/zipstream-php": "^2.1 || ^3.0", "markbaker/complex": "^3.0", "markbaker/matrix": "^3.0", "php": "^7.4 || ^8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-main", "dompdf/dompdf": "^1.0 || ^2.0 || ^3.0", "friendsofphp/php-cs-fixer": "^3.2", "mitoteam/jpgraph": "^10.3", "mpdf/mpdf": "^8.1.1", "phpcompatibility/php-compatibility": "^9.3", "phpstan/phpstan": "^1.1", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^8.5 || ^9.0", "squizlabs/php_codesniffer": "^3.7", "tecnickcom/tcpdf": "^6.5"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "ext-intl": "PHP Internationalization Functions", "mitoteam/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "time": "2025-01-26T04:55:00+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues", "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/1.29.9"}, "install-path": "../phpoffice/phpspreadsheet"}, {"name": "phpoption/phpoption", "version": "1.9.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/e3fac8b24f56113f7cb96af14958c0dd16330f54", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "time": "2024-07-20T21:41:07+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "1.9-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "install-path": "../phpoption/phpoption"}, {"name": "phpseclib/phpseclib", "version": "3.0.43", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "709ec107af3cb2f385b9617be72af8cf62441d02"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/709ec107af3cb2f385b9617be72af8cf62441d02", "reference": "709ec107af3cb2f385b9617be72af8cf62441d02", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1|^2|^3", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": ">=5.6.1"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-dom": "Install the DOM extension to load XML formatted public keys.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "time": "2024-12-14T21:12:59+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib3\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.43"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "install-path": "../phpseclib/phpseclib"}, {"name": "phpstan/phpstan", "version": "1.12.15", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "c91d4e8bc056f46cf653656e6f71004b254574d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/c91d4e8bc056f46cf653656e6f71004b254574d1", "reference": "c91d4e8bc056f46cf653656e6f71004b254574d1", "shasum": ""}, "require": {"php": "^7.2|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "time": "2025-01-05T16:40:22+00:00", "bin": ["phpstan", "phpstan.phar"], "type": "library", "installation-source": "dist", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "support": {"docs": "https://phpstan.org/user-guide/getting-started", "forum": "https://github.com/phpstan/phpstan/discussions", "issues": "https://github.com/phpstan/phpstan/issues", "security": "https://github.com/phpstan/phpstan/security/policy", "source": "https://github.com/phpstan/phpstan-src"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}], "install-path": "../phpstan/phpstan"}, {"name": "phpunit/php-code-coverage", "version": "10.1.16", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "7e308268858ed6baedc8704a304727d20bc07c77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/7e308268858ed6baedc8704a304727d20bc07c77", "reference": "7e308268858ed6baedc8704a304727d20bc07c77", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.19.1 || ^5.1.0", "php": ">=8.1", "phpunit/php-file-iterator": "^4.1.0", "phpunit/php-text-template": "^3.0.1", "sebastian/code-unit-reverse-lookup": "^3.0.0", "sebastian/complexity": "^3.2.0", "sebastian/environment": "^6.1.0", "sebastian/lines-of-code": "^2.0.2", "sebastian/version": "^4.0.1", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^10.1"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "time": "2024-08-22T04:31:57+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "10.1.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.16"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../phpunit/php-code-coverage"}, {"name": "phpunit/php-file-iterator", "version": "4.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "a95037b6d9e608ba092da1b23931e537cadc3c3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/a95037b6d9e608ba092da1b23931e537cadc3c3c", "reference": "a95037b6d9e608ba092da1b23931e537cadc3c3c", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "time": "2023-08-31T06:24:48+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/4.1.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../phpunit/php-file-iterator"}, {"name": "phpunit/php-invoker", "version": "4.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7", "reference": "f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^10.0"}, "suggest": {"ext-pcntl": "*"}, "time": "2023-02-03T06:56:09+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/4.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../phpunit/php-invoker"}, {"name": "phpunit/php-text-template", "version": "3.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "0c7b06ff49e3d5072f057eb1fa59258bf287a748"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-text-template/zipball/0c7b06ff49e3d5072f057eb1fa59258bf287a748", "reference": "0c7b06ff49e3d5072f057eb1fa59258bf287a748", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "time": "2023-08-31T14:07:24+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/3.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../phpunit/php-text-template"}, {"name": "phpunit/php-timer", "version": "6.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "e2a2d67966e740530f4a3343fe2e030ffdc1161d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/e2a2d67966e740530f4a3343fe2e030ffdc1161d", "reference": "e2a2d67966e740530f4a3343fe2e030ffdc1161d", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "time": "2023-02-03T06:57:52+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-timer/tree/6.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../phpunit/php-timer"}, {"name": "phpunit/phpunit", "version": "10.5.40", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "e6ddda95af52f69c1e0c7b4f977cccb58048798c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/e6ddda95af52f69c1e0c7b4f977cccb58048798c", "reference": "e6ddda95af52f69c1e0c7b4f977cccb58048798c", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.12.1", "phar-io/manifest": "^2.0.4", "phar-io/version": "^3.2.1", "php": ">=8.1", "phpunit/php-code-coverage": "^10.1.16", "phpunit/php-file-iterator": "^4.1.0", "phpunit/php-invoker": "^4.0.0", "phpunit/php-text-template": "^3.0.1", "phpunit/php-timer": "^6.0.0", "sebastian/cli-parser": "^2.0.1", "sebastian/code-unit": "^2.0.0", "sebastian/comparator": "^5.0.3", "sebastian/diff": "^5.1.1", "sebastian/environment": "^6.1.0", "sebastian/exporter": "^5.1.2", "sebastian/global-state": "^6.0.2", "sebastian/object-enumerator": "^5.0.0", "sebastian/recursion-context": "^5.0.0", "sebastian/type": "^4.0.0", "sebastian/version": "^4.0.1"}, "suggest": {"ext-soap": "To be able to generate mocks based on WSDL files"}, "time": "2024-12-21T05:49:06+00:00", "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-main": "10.5-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "security": "https://github.com/sebastian<PERSON>mann/phpunit/security/policy", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/10.5.40"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit", "type": "tidelift"}], "install-path": "../phpunit/phpunit"}, {"name": "psr/clock", "version": "1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "time": "2022-11-25T14:36:26+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "install-path": "../psr/clock"}, {"name": "psr/container", "version": "2.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "time": "2021-11-05T16:47:00+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "install-path": "../psr/container"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "time": "2019-01-08T18:20:26+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "install-path": "../psr/event-dispatcher"}, {"name": "psr/http-client", "version": "1.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "time": "2023-09-23T14:17:50+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "install-path": "../psr/http-client"}, {"name": "psr/http-factory", "version": "1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "time": "2024-04-15T12:06:14+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "install-path": "../psr/http-factory"}, {"name": "psr/http-message", "version": "2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "time": "2023-04-04T09:54:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "install-path": "../psr/http-message"}, {"name": "psr/http-server-handler", "version": "1.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-handler.git", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-handler/zipball/84c4fb66179be4caaf8e97bd239203245302e7d4", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0"}, "time": "2023-04-10T20:06:20+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side request handler", "keywords": ["handler", "http", "http-interop", "psr", "psr-15", "psr-7", "request", "response", "server"], "support": {"source": "https://github.com/php-fig/http-server-handler/tree/1.0.2"}, "install-path": "../psr/http-server-handler"}, {"name": "psr/http-server-middleware", "version": "1.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-middleware.git", "reference": "c1481f747daaa6a0782775cd6a8c26a1bf4a3829"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-middleware/zipball/c1481f747daaa6a0782775cd6a8c26a1bf4a3829", "reference": "c1481f747daaa6a0782775cd6a8c26a1bf4a3829", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0", "psr/http-server-handler": "^1.0"}, "time": "2023-04-11T06:14:47+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side middleware", "keywords": ["http", "http-interop", "middleware", "psr", "psr-15", "psr-7", "request", "response"], "support": {"issues": "https://github.com/php-fig/http-server-middleware/issues", "source": "https://github.com/php-fig/http-server-middleware/tree/1.0.2"}, "install-path": "../psr/http-server-middleware"}, {"name": "psr/log", "version": "3.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "time": "2024-09-11T13:17:53+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "install-path": "../psr/log"}, {"name": "psr/simple-cache", "version": "3.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "time": "2021-10-29T13:26:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "install-path": "../psr/simple-cache"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "time": "2019-03-08T08:55:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "install-path": "../ralouphie/getallheaders"}, {"name": "react/cache", "version": "v1.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/reactphp/cache.git", "reference": "d47c472b64aa5608225f47965a484b75c7817d5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/cache/zipball/d47c472b64aa5608225f47965a484b75c7817d5b", "reference": "d47c472b64aa5608225f47965a484b75c7817d5b", "shasum": ""}, "require": {"php": ">=5.3.0", "react/promise": "^3.0 || ^2.0 || ^1.1"}, "require-dev": {"phpunit/phpunit": "^9.5 || ^5.7 || ^4.8.35"}, "time": "2022-11-30T15:59:55+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"React\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Async, Promise-based cache interface for ReactPHP", "keywords": ["cache", "caching", "promise", "reactphp"], "support": {"issues": "https://github.com/reactphp/cache/issues", "source": "https://github.com/reactphp/cache/tree/v1.2.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "install-path": "../react/cache"}, {"name": "react/child-process", "version": "v0.6.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/reactphp/child-process.git", "reference": "1721e2b93d89b745664353b9cfc8f155ba8a6159"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/child-process/zipball/1721e2b93d89b745664353b9cfc8f155ba8a6159", "reference": "1721e2b93d89b745664353b9cfc8f155ba8a6159", "shasum": ""}, "require": {"evenement/evenement": "^3.0 || ^2.0 || ^1.0", "php": ">=5.3.0", "react/event-loop": "^1.2", "react/stream": "^1.4"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36", "react/socket": "^1.16", "sebastian/environment": "^5.0 || ^3.0 || ^2.0 || ^1.0"}, "time": "2025-01-01T16:37:48+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"React\\ChildProcess\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Event-driven library for executing child processes with ReactPHP.", "keywords": ["event-driven", "process", "reactphp"], "support": {"issues": "https://github.com/reactphp/child-process/issues", "source": "https://github.com/reactphp/child-process/tree/v0.6.6"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "install-path": "../react/child-process"}, {"name": "react/dns", "version": "v1.13.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/reactphp/dns.git", "reference": "eb8ae001b5a455665c89c1df97f6fb682f8fb0f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/dns/zipball/eb8ae001b5a455665c89c1df97f6fb682f8fb0f5", "reference": "eb8ae001b5a455665c89c1df97f6fb682f8fb0f5", "shasum": ""}, "require": {"php": ">=5.3.0", "react/cache": "^1.0 || ^0.6 || ^0.5", "react/event-loop": "^1.2", "react/promise": "^3.2 || ^2.7 || ^1.2.1"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36", "react/async": "^4.3 || ^3 || ^2", "react/promise-timer": "^1.11"}, "time": "2024-06-13T14:18:03+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"React\\Dns\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Async DNS resolver for ReactPHP", "keywords": ["async", "dns", "dns-resolver", "reactphp"], "support": {"issues": "https://github.com/reactphp/dns/issues", "source": "https://github.com/reactphp/dns/tree/v1.13.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "install-path": "../react/dns"}, {"name": "react/event-loop", "version": "v1.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/reactphp/event-loop.git", "reference": "bbe0bd8c51ffc05ee43f1729087ed3bdf7d53354"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/event-loop/zipball/bbe0bd8c51ffc05ee43f1729087ed3bdf7d53354", "reference": "bbe0bd8c51ffc05ee43f1729087ed3bdf7d53354", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "suggest": {"ext-pcntl": "For signal handling support when using the StreamSelectLoop"}, "time": "2023-11-13T13:48:05+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"React\\EventLoop\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "ReactPHP's core reactor event loop that libraries can use for evented I/O.", "keywords": ["asynchronous", "event-loop"], "support": {"issues": "https://github.com/reactphp/event-loop/issues", "source": "https://github.com/reactphp/event-loop/tree/v1.5.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "install-path": "../react/event-loop"}, {"name": "react/promise", "version": "v2.11.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/1a8460931ea36dc5c76838fec5734d55c88c6831", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "time": "2023-11-16T16:16:50+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v2.11.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "install-path": "../react/promise"}, {"name": "react/socket", "version": "v1.16.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/reactphp/socket.git", "reference": "23e4ff33ea3e160d2d1f59a0e6050e4b0fb0eac1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/socket/zipball/23e4ff33ea3e160d2d1f59a0e6050e4b0fb0eac1", "reference": "23e4ff33ea3e160d2d1f59a0e6050e4b0fb0eac1", "shasum": ""}, "require": {"evenement/evenement": "^3.0 || ^2.0 || ^1.0", "php": ">=5.3.0", "react/dns": "^1.13", "react/event-loop": "^1.2", "react/promise": "^3.2 || ^2.6 || ^1.2.1", "react/stream": "^1.4"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36", "react/async": "^4.3 || ^3.3 || ^2", "react/promise-stream": "^1.4", "react/promise-timer": "^1.11"}, "time": "2024-07-26T10:38:09+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"React\\Socket\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Async, streaming plaintext TCP/IP and secure TLS socket server and client connections for ReactPHP", "keywords": ["Connection", "Socket", "async", "reactphp", "stream"], "support": {"issues": "https://github.com/reactphp/socket/issues", "source": "https://github.com/reactphp/socket/tree/v1.16.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "install-path": "../react/socket"}, {"name": "react/stream", "version": "v1.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/reactphp/stream.git", "reference": "1e5b0acb8fe55143b5b426817155190eb6f5b18d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/stream/zipball/1e5b0acb8fe55143b5b426817155190eb6f5b18d", "reference": "1e5b0acb8fe55143b5b426817155190eb6f5b18d", "shasum": ""}, "require": {"evenement/evenement": "^3.0 || ^2.0 || ^1.0", "php": ">=5.3.8", "react/event-loop": "^1.2"}, "require-dev": {"clue/stream-filter": "~1.2", "phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "time": "2024-06-11T12:45:25+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"React\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Event-driven readable and writable streams for non-blocking I/O in ReactPHP", "keywords": ["event-driven", "io", "non-blocking", "pipe", "reactphp", "readable", "stream", "writable"], "support": {"issues": "https://github.com/reactphp/stream/issues", "source": "https://github.com/reactphp/stream/tree/v1.4.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "install-path": "../react/stream"}, {"name": "sebastian/cli-parser", "version": "2.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "c34583b87e7b7a8055bf6c450c2c77ce32a24084"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/c34583b87e7b7a8055bf6c450c2c77ce32a24084", "reference": "c34583b87e7b7a8055bf6c450c2c77ce32a24084", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "time": "2024-03-02T07:12:49+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/2.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/cli-parser"}, {"name": "sebastian/code-unit", "version": "2.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "reference": "a81fee9eef0b7a76af11d121767abc44c104e503"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/a81fee9eef0b7a76af11d121767abc44c104e503", "reference": "a81fee9eef0b7a76af11d121767abc44c104e503", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "time": "2023-02-03T06:58:43+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/2.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/code-unit"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "3.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "5e3a687f7d8ae33fb362c5c0743794bbb2420a1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/5e3a687f7d8ae33fb362c5c0743794bbb2420a1d", "reference": "5e3a687f7d8ae33fb362c5c0743794bbb2420a1d", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "time": "2023-02-03T06:59:15+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/3.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/code-unit-reverse-lookup"}, {"name": "sebastian/comparator", "version": "5.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "a18251eb0b7a2dcd2f7aa3d6078b18545ef0558e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/a18251eb0b7a2dcd2f7aa3d6078b18545ef0558e", "reference": "a18251eb0b7a2dcd2f7aa3d6078b18545ef0558e", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "php": ">=8.1", "sebastian/diff": "^5.0", "sebastian/exporter": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.5"}, "time": "2024-10-18T14:56:07+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/5.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/comparator"}, {"name": "sebastian/complexity", "version": "3.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "68ff824baeae169ec9f2137158ee529584553799"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/complexity/zipball/68ff824baeae169ec9f2137158ee529584553799", "reference": "68ff824baeae169ec9f2137158ee529584553799", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "time": "2023-12-21T08:37:17+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.2-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "security": "https://github.com/sebastian<PERSON>mann/complexity/security/policy", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/3.2.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/complexity"}, {"name": "sebastian/diff", "version": "5.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "c41e007b4b62af48218231d6c2275e4c9b975b2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/diff/zipball/c41e007b4b62af48218231d6c2275e4c9b975b2e", "reference": "c41e007b4b62af48218231d6c2275e4c9b975b2e", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0", "symfony/process": "^6.4"}, "time": "2024-03-02T07:15:17+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "5.1-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "security": "https://github.com/sebastian<PERSON>mann/diff/security/policy", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/5.1.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/diff"}, {"name": "sebastian/environment", "version": "6.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "8074dbcd93529b357029f5cc5058fd3e43666984"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastianbergmann/environment/zipball/8074dbcd93529b357029f5cc5058fd3e43666984", "reference": "8074dbcd93529b357029f5cc5058fd3e43666984", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "suggest": {"ext-posix": "*"}, "time": "2024-03-23T08:47:14+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "6.1-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "https://github.com/sebastian<PERSON>mann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/environment/security/policy", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/6.1.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/environment"}, {"name": "sebastian/exporter", "version": "5.1.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "955288482d97c19a372d3f31006ab3f37da47adf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/955288482d97c19a372d3f31006ab3f37da47adf", "reference": "955288482d97c19a372d3f31006ab3f37da47adf", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=8.1", "sebastian/recursion-context": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "time": "2024-03-02T07:17:12+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "5.1-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "security": "https://github.com/sebastian<PERSON>mann/exporter/security/policy", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/5.1.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/exporter"}, {"name": "sebastian/global-state", "version": "6.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "987bafff24ecc4c9ac418cab1145b96dd6e9cbd9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/987bafff24ecc4c9ac418cab1145b96dd6e9cbd9", "reference": "987bafff24ecc4c9ac418cab1145b96dd6e9cbd9", "shasum": ""}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^10.0"}, "time": "2024-03-02T07:19:19+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "https://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/global-state/security/policy", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/6.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/global-state"}, {"name": "sebastian/lines-of-code", "version": "2.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "856e7f6a75a84e339195d48c556f23be2ebf75d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/lines-of-code/zipball/856e7f6a75a84e339195d48c556f23be2ebf75d0", "reference": "856e7f6a75a84e339195d48c556f23be2ebf75d0", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "time": "2023-12-21T08:38:20+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/2.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/lines-of-code"}, {"name": "sebastian/object-enumerator", "version": "5.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "202d0e344a580d7f7d04b3fafce6933e59dae906"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/202d0e344a580d7f7d04b3fafce6933e59dae906", "reference": "202d0e344a580d7f7d04b3fafce6933e59dae906", "shasum": ""}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "time": "2023-02-03T07:08:32+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/object-enumerator"}, {"name": "sebastian/object-reflector", "version": "3.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "24ed13d98130f0e7122df55d06c5c4942a577957"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/object-reflector/zipball/24ed13d98130f0e7122df55d06c5c4942a577957", "reference": "24ed13d98130f0e7122df55d06c5c4942a577957", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "time": "2023-02-03T07:06:18+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/3.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/object-reflector"}, {"name": "sebastian/recursion-context", "version": "5.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "05909fb5bc7df4c52992396d0116aed689f93712"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/05909fb5bc7df4c52992396d0116aed689f93712", "reference": "05909fb5bc7df4c52992396d0116aed689f93712", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "time": "2023-02-03T07:05:40+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/recursion-context"}, {"name": "sebastian/type", "version": "4.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "462699a16464c3944eefc02ebdd77882bd3925bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/type/zipball/462699a16464c3944eefc02ebdd77882bd3925bf", "reference": "462699a16464c3944eefc02ebdd77882bd3925bf", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "time": "2023-02-03T07:10:45+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/4.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/type"}, {"name": "sebastian/version", "version": "4.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c51fa83a5d8f43f1402e3f32a005e6262244ef17"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c51fa83a5d8f43f1402e3f32a005e6262244ef17", "reference": "c51fa83a5d8f43f1402e3f32a005e6262244ef17", "shasum": ""}, "require": {"php": ">=8.1"}, "time": "2023-02-07T11:34:05+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/4.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/version"}, {"name": "swoole/ide-helper", "version": "5.1.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/swoole/ide-helper.git", "reference": "c5149a01c00e4ed56fc7b3ffeb6823e69acb4a76"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swoole/ide-helper/zipball/c5149a01c00e4ed56fc7b3ffeb6823e69acb4a76", "reference": "c5149a01c00e4ed56fc7b3ffeb6823e69acb4a76", "shasum": ""}, "time": "2024-11-29T07:21:36+00:00", "type": "library", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Team Swoole", "email": "<EMAIL>"}], "description": "IDE help files for Swoole.", "support": {"issues": "https://github.com/swoole/ide-helper/issues", "source": "https://github.com/swoole/ide-helper/tree/5.1.6"}, "install-path": "../swoole/ide-helper"}, {"name": "swow/psr7-plus", "version": "v1.1.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/swow/psr7-plus.git", "reference": "7acc4924be907d2ff64edee5a2bd116620e56364"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swow/psr7-plus/zipball/7acc4924be907d2ff64edee5a2bd116620e56364", "reference": "7acc4924be907d2ff64edee5a2bd116620e56364", "shasum": ""}, "require": {"php": ">=8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1|^2.0"}, "time": "2023-06-15T09:18:11+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Swow\\Psr7\\Message\\": "src/Message/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "twose", "email": "<EMAIL>"}], "description": "Modern strong-typed interfaces for Psr7, not only HTTP but also WebSocket", "keywords": ["http", "psr17", "psr7", "swow", "websocket"], "support": {"issues": "https://github.com/swow/swow", "source": "https://github.com/swow/psr7-plus/tree/v1.1.2"}, "install-path": "../swow/psr7-plus"}, {"name": "symfony/console", "version": "v6.4.17", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "799445db3f15768ecc382ac5699e6da0520a0a04"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/799445db3f15768ecc382ac5699e6da0520a0a04", "reference": "799445db3f15768ecc382ac5699e6da0520a0a04", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/dotenv": "<5.4", "symfony/event-dispatcher": "<5.4", "symfony/lock": "<5.4", "symfony/process": "<5.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "time": "2024-12-07T12:07:30+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v6.4.17"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/console"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "shasum": ""}, "require": {"php": ">=8.1"}, "time": "2024-09-25T14:20:29+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "installation-source": "dist", "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/deprecation-contracts"}, {"name": "symfony/event-dispatcher", "version": "v6.4.13", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e", "reference": "0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e", "shasum": ""}, "require": {"php": ">=8.1", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^5.4|^6.0|^7.0"}, "time": "2024-09-25T14:18:03+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/event-dispatcher"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.5.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/7642f5e970b672283b7823222ae8ef8bbc160b9f", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "time": "2024-09-25T14:20:29+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/event-dispatcher-contracts"}, {"name": "symfony/filesystem", "version": "v6.4.13", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "4856c9cf585d5a0313d8d35afd681a526f038dd3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/4856c9cf585d5a0313d8d35afd681a526f038dd3", "reference": "4856c9cf585d5a0313d8d35afd681a526f038dd3", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "require-dev": {"symfony/process": "^5.4|^6.4|^7.0"}, "time": "2024-10-25T15:07:50+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/filesystem"}, {"name": "symfony/finder", "version": "v6.4.17", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7", "reference": "1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"symfony/filesystem": "^6.0|^7.0"}, "time": "2024-12-29T13:51:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v6.4.17"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/finder"}, {"name": "symfony/http-foundation", "version": "v6.4.16", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "431771b7a6f662f1575b3cfc8fd7617aa9864d57"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/431771b7a6f662f1575b3cfc8fd7617aa9864d57", "reference": "431771b7a6f662f1575b3cfc8fd7617aa9864d57", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php83": "^1.27"}, "conflict": {"symfony/cache": "<6.4.12|>=7.0,<7.1.5"}, "require-dev": {"doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "symfony/cache": "^6.4.12|^7.1.5", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4.12|^6.0.12|^6.1.4|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/rate-limiter": "^5.4|^6.0|^7.0"}, "time": "2024-11-13T18:58:10+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v6.4.16"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/http-foundation"}, {"name": "symfony/options-resolver", "version": "v6.4.16", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "368128ad168f20e22c32159b9f761e456cec0c78"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/368128ad168f20e22c32159b9f761e456cec0c78", "reference": "368128ad168f20e22c32159b9f761e456cec0c78", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "time": "2024-11-20T10:57:02+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v6.4.16"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/options-resolver"}, {"name": "symfony/polyfill-ctype", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-ctype"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-intl-grapheme"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-intl-normalizer"}, {"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/85181ba99b2345b0ef10ce42ecac37612d9fd341", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-mbstring"}, {"name": "symfony/polyfill-php80", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "shasum": ""}, "require": {"php": ">=7.2"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php80"}, {"name": "symfony/polyfill-php81", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "shasum": ""}, "require": {"php": ">=7.2"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php81"}, {"name": "symfony/polyfill-php83", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/2fb86d65e2d424369ad2905e83b236a8805ba491", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491", "shasum": ""}, "require": {"php": ">=7.2"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php83"}, {"name": "symfony/process", "version": "v6.4.15", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "3cb242f059c14ae08591c5c4087d1fe443564392"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/3cb242f059c14ae08591c5c4087d1fe443564392", "reference": "3cb242f059c14ae08591c5c4087d1fe443564392", "shasum": ""}, "require": {"php": ">=8.1"}, "time": "2024-11-06T14:19:14+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v6.4.15"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/process"}, {"name": "symfony/service-contracts", "version": "v3.5.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "time": "2024-09-25T14:20:29+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/service-contracts"}, {"name": "symfony/stopwatch", "version": "v6.4.13", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "2cae0a6f8d04937d02f6d19806251e2104d54f92"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/2cae0a6f8d04937d02f6d19806251e2104d54f92", "reference": "2cae0a6f8d04937d02f6d19806251e2104d54f92", "shasum": ""}, "require": {"php": ">=8.1", "symfony/service-contracts": "^2.5|^3"}, "time": "2024-09-25T14:18:03+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a way to profile code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/stopwatch/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/stopwatch"}, {"name": "symfony/string", "version": "v6.4.15", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "73a5e66ea2e1677c98d4449177c5a9cf9d8b4c6f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/73a5e66ea2e1677c98d4449177c5a9cf9d8b4c6f", "reference": "73a5e66ea2e1677c98d4449177c5a9cf9d8b4c6f", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/intl": "^6.2|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^5.4|^6.0|^7.0"}, "time": "2024-11-13T13:31:12+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v6.4.15"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/string"}, {"name": "symfony/translation", "version": "v6.4.13", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "bee9bfabfa8b4045a66bf82520e492cddbaffa66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/bee9bfabfa8b4045a66bf82520e492cddbaffa66", "reference": "bee9bfabfa8b4045a66bf82520e492cddbaffa66", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.5|^3.0"}, "conflict": {"symfony/config": "<5.4", "symfony/console": "<5.4", "symfony/dependency-injection": "<5.4", "symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<5.4", "symfony/service-contracts": "<2.5", "symfony/twig-bundle": "<5.4", "symfony/yaml": "<5.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "require-dev": {"nikic/php-parser": "^4.18|^5.0", "psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/intl": "^5.4|^6.0|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "time": "2024-09-27T18:14:25+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/translation"}, {"name": "symfony/translation-contracts", "version": "v3.5.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/4667ff3bd513750603a09c8dedbea942487fb07c", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c", "shasum": ""}, "require": {"php": ">=8.1"}, "time": "2024-09-25T14:20:29+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/translation-contracts"}, {"name": "theseer/tokenizer", "version": "1.2.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "time": "2024-03-03T12:36:25+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.3"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "install-path": "../theseer/tokenizer"}, {"name": "vlucas/phpdotenv", "version": "v5.6.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "a59a13791077fe3d44f90e7133eb68e7d22eaff2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/a59a13791077fe3d44f90e7133eb68e7d22eaff2", "reference": "a59a13791077fe3d44f90e7133eb68e7d22eaff2", "shasum": ""}, "require": {"ext-pcre": "*", "graham-campbell/result-type": "^1.1.3", "php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3", "symfony/polyfill-ctype": "^1.24", "symfony/polyfill-mbstring": "^1.24", "symfony/polyfill-php80": "^1.24"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-filter": "*", "phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"}, "suggest": {"ext-filter": "Required to use the boolean validator."}, "time": "2024-07-20T21:52:34+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "5.6-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.6.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "install-path": "../vlucas/phpdotenv"}], "dev": true, "dev-package-names": ["clue/ndjson-react", "composer/semver", "composer/xdebug-handler", "evenement/evenement", "fidry/cpu-core-counter", "friendsofphp/php-cs-fixer", "hamcrest/hamcrest-php", "hyperf/devtool", "hyperf/testing", "mockery/mockery", "myclabs/deep-copy", "phar-io/manifest", "phar-io/version", "phpstan/phpstan", "phpunit/php-code-coverage", "phpunit/php-file-iterator", "phpunit/php-invoker", "phpunit/php-text-template", "phpunit/php-timer", "phpunit/phpunit", "react/cache", "react/child-process", "react/dns", "react/event-loop", "react/socket", "react/stream", "sebastian/cli-parser", "sebastian/code-unit", "sebastian/code-unit-reverse-lookup", "sebastian/comparator", "sebastian/complexity", "sebastian/diff", "sebastian/environment", "sebastian/exporter", "sebastian/global-state", "sebastian/lines-of-code", "sebastian/object-enumerator", "sebastian/object-reflector", "sebastian/recursion-context", "sebastian/type", "sebastian/version", "swoole/ide-helper", "symfony/event-dispatcher", "symfony/event-dispatcher-contracts", "symfony/filesystem", "symfony/http-foundation", "symfony/options-resolver", "symfony/polyfill-php81", "symfony/polyfill-php83", "symfony/process", "symfony/stopwatch", "theseer/tokenizer"]}