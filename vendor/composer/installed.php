<?php return array(
    'root' => array(
        'name' => 'hyperf/hyperf-skeleton',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'carbonphp/carbon-doctrine-types' => array(
            'pretty_version' => '3.2.0',
            'version' => '*******',
            'reference' => '18ba5ddfec8976260ead6e866180bd5d2f71aa1d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../carbonphp/carbon-doctrine-types',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'clue/ndjson-react' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'reference' => '392dc165fce93b5bb5c637b67e59619223c931b0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../clue/ndjson-react',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/pcre' => array(
            'pretty_version' => '3.3.2',
            'version' => '3.3.2.0',
            'reference' => 'b2bed4734f0cc156ee1fe9c0da2550420d99a21e',
            'type' => 'library',
            'install_path' => __DIR__ . '/./pcre',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/semver' => array(
            'pretty_version' => '3.4.3',
            'version' => '3.4.3.0',
            'reference' => '4313d26ada5e0c4edfbd1dc481a92ff7bff91f12',
            'type' => 'library',
            'install_path' => __DIR__ . '/./semver',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/xdebug-handler' => array(
            'pretty_version' => '3.0.5',
            'version' => '3.0.5.0',
            'reference' => '6c1925561632e83d60a44492e0b344cf48ab85ef',
            'type' => 'library',
            'install_path' => __DIR__ . '/./xdebug-handler',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'cordoval/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'davedevelopment/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'doctrine/inflector' => array(
            'pretty_version' => '2.0.10',
            'version' => '2.0.10.0',
            'reference' => '5817d0659c5b50c9b950feb9af7b9668e2c436bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/inflector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/instantiator' => array(
            'pretty_version' => '1.5.0',
            'version' => '1.5.0.0',
            'reference' => '0a0fa9780f5d4e507415a065172d26a98d02047b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/instantiator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'easyswoole/spl' => array(
            'pretty_version' => '2.1.3',
            'version' => '2.1.3.0',
            'reference' => '6ca7321e476a40a3b70b15b836830ff030eec516',
            'type' => 'library',
            'install_path' => __DIR__ . '/../easyswoole/spl',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'easyswoole/verifycode' => array(
            'pretty_version' => '3.1.2',
            'version' => '3.1.2.0',
            'reference' => 'cfd7c1a7218e8b5f2319d9b3b73cf7a588c22ee6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../easyswoole/verifycode',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'elasticsearch/elasticsearch' => array(
            'pretty_version' => 'v7.17.2',
            'version' => '7.17.2.0',
            'reference' => '2d302233f2bb0926812d82823bb820d405e130fc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../elasticsearch/elasticsearch',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'evenement/evenement' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '*******',
            'reference' => '0a16b0d71ab13284339abb99d9d2bd813640efbc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../evenement/evenement',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'ezimuel/guzzlestreams' => array(
            'pretty_version' => '3.1.0',
            'version' => '3.1.0.0',
            'reference' => 'b4b5a025dfee70d6cd34c780e07330eb93d5b997',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ezimuel/guzzlestreams',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ezimuel/ringphp' => array(
            'pretty_version' => '1.2.2',
            'version' => '1.2.2.0',
            'reference' => '7887fc8488013065f72f977dcb281994f5fde9f4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ezimuel/ringphp',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ezyang/htmlpurifier' => array(
            'pretty_version' => 'v4.18.0',
            'version' => '4.18.0.0',
            'reference' => 'cb56001e54359df7ae76dc522d08845dc741621b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ezyang/htmlpurifier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fidry/cpu-core-counter' => array(
            'pretty_version' => '1.2.0',
            'version' => '1.2.0.0',
            'reference' => '8520451a140d3f46ac33042715115e290cf5785f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fidry/cpu-core-counter',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'fig/http-message-util' => array(
            'pretty_version' => '1.1.5',
            'version' => '1.1.5.0',
            'reference' => '9d94dc0154230ac39e5bf89398b324a86f63f765',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fig/http-message-util',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'friendsofphp/php-cs-fixer' => array(
            'pretty_version' => 'v3.67.0',
            'version' => '3.67.0.0',
            'reference' => '0ad34c75d1172f7d30320460e803887981830cbf',
            'type' => 'application',
            'install_path' => __DIR__ . '/../friendsofphp/php-cs-fixer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'graham-campbell/result-type' => array(
            'pretty_version' => 'v1.1.3',
            'version' => '1.1.3.0',
            'reference' => '3ba905c11371512af9d9bdd27d99b782216b6945',
            'type' => 'library',
            'install_path' => __DIR__ . '/../graham-campbell/result-type',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.2',
            'version' => '7.9.2.0',
            'reference' => 'd281ed313b989f213357e3be1a179f02196ac99b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.0.4',
            'version' => '*******',
            'reference' => 'f9c436286ab2892c7db7be8c8da4ef61ccf7b455',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.0',
            'version' => '2.7.0.0',
            'reference' => 'a70f5c95fb43bc83f07c9c948baa0dc1829bf201',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/ringphp' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '1.2.2',
            ),
        ),
        'hamcrest/hamcrest-php' => array(
            'pretty_version' => 'v2.0.1',
            'version' => '2.0.1.0',
            'reference' => '8c3d0a3f6af734494ad8f6fbbee0ba92422859f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hamcrest/hamcrest-php',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'hyperf/amqp' => array(
            'pretty_version' => 'v3.1.50',
            'version' => '********',
            'reference' => 'db5e24822f2dcc3af055b5c985702f59901be380',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/amqp',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/async-queue' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => '1cd25666ac1e1f23c9eab6be642e86802a96307b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/async-queue',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/cache' => array(
            'pretty_version' => 'v3.1.43',
            'version' => '3.1.43.0',
            'reference' => '1e3cc54cee776c8d32cf40912dee5d366383bc33',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/code-parser' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => '81953c4ea9035ac5f0a4740ae157310ca4e18ff2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/code-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/codec' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => 'effc71c25e2d53c00fcf41da8bca083ac8a0db0e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/codec',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/collection' => array(
            'pretty_version' => 'v3.1.50',
            'version' => '********',
            'reference' => '9ec6c151c6e1ce8407d617b7813eb52f4fb3c363',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/collection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/command' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => '43047270c15bce06e19d217dc5ba02b284830e25',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/command',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/conditionable' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => 'dec9dec9dbde14e20f3d7ba000c3302381019de1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/conditionable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/config' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => '1df5e310aab752d6195f89f5cc98daf3cdc4bb6e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/constants' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => 'e1e1184779cd163f9603ce234e1ecccb6fe382ae',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/constants',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/context' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => 'ac666862d644db7d813342c880826a1fda599bdf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/context',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/contract' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => '6ef2c7f98917c52ccda3a37ae65b190848dde6c4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/contract',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/coordinator' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => 'a0497d2a260f166ab53fed2eca6bb4e48b49ef56',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/coordinator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/coroutine' => array(
            'pretty_version' => 'v3.1.50',
            'version' => '********',
            'reference' => 'c353b3fbd86e30b5b51219e8867d479ea11e6811',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/coroutine',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/crontab' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => 'be1187515aabbfe96b2f6a5330b4ddd742e971c7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/crontab',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/database' => array(
            'pretty_version' => 'v3.1.48',
            'version' => '********',
            'reference' => 'a16b070ee2ac2ec580a4c6f5bb6243350bed69e6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/database',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/db-connection' => array(
            'pretty_version' => 'v3.1.44',
            'version' => '3.1.44.0',
            'reference' => '95dbb713fda5556106b803d0201e1631645985b5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/db-connection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/devtool' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => 'ae1c8f547c21eb591a94ae3fbacf054542de82d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/devtool',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'hyperf/di' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => '72b65de5022e3dca79ae1902c058048b9519aa72',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/di',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/dispatcher' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => '5cbdfd586bb8c3bbbabed5a23cec7faf52b744b0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/elasticsearch' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => 'ed654ef85e1b53deca8b128bb1ebbdc6cdba52b0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/elasticsearch',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/engine' => array(
            'pretty_version' => 'v2.12.1',
            'version' => '2.12.1.0',
            'reference' => '90be8143841482dcd00051050986251e126c6132',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/engine',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/engine-contract' => array(
            'pretty_version' => 'v1.11.0',
            'version' => '1.11.0.0',
            'reference' => 'd478052ed1c5304eef7be68aae6cf42392611a15',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/engine-contract',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/event' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => '2b5fbbc94674a1a5e1622896eb3f7ecc80aa38c4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/event',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/exception-handler' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => 'df2135fb0ffe0bb61032911038aea6488077cdef',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/exception-handler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/framework' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => '7b317d3891698a1eb0308e7306730d2ada1d6ff4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/framework',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/guzzle' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => 'fe838557530bf7b2d39dc604563c3a3ff8d5618f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/http-message' => array(
            'pretty_version' => 'v3.1.48',
            'version' => '********',
            'reference' => '534ce81af0feaa0c4a9e132af1c6a9c5527a8d85',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/http-server' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => '4727f15a743c6e9ca0a6b3c8494c5c62bae82f5a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/http-server',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/hyperf-skeleton' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/logger' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => 'c96d32fae44bf350ef903ebca19c91a315458d72',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/logger',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/macroable' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => '0be650165b9e8ea073e199fac788ece70f16b6a4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/macroable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/memory' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => 'ccf25783d63a2610a4d797ec34c1e0093b755da2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/memory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/model-cache' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => 'f1af97e63d12f9e2149bdef478541689cd31775f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/model-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/model-listener' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => '0181882fb6034cf2eac81b84b5c65c187af9f3a4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/model-listener',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/paginator' => array(
            'pretty_version' => 'v3.1.49',
            'version' => '********',
            'reference' => 'dd9d59f5601fbdaa69f488348c7debd1931feb7b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/paginator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/pipeline' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => '096d9a9f87ddea33209f134d30ae8d8867a195c7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/pipeline',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/pool' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => '004dd811bf760ea0032913a31284102742abb737',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/pool',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/process' => array(
            'pretty_version' => 'v3.1.48',
            'version' => '********',
            'reference' => '8d68398bdb4f2623af1bec846399b6ce29bd7d2c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/redis' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => '973a92c34be60353e978d85c434e65f366a817dd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/redis',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/serializer' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => '03c8a4840e0a7be83670c2fb0f850a2204fad076',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/serializer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/server' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => 'e10c5ce6d9b72d3ca9ad16d36977e2e64d975460',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/server',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/session' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => '6875e7317d548cb2f28cbb92332a772ed0abeb9f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/session',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/stdlib' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => '13393734a4cc6c9878390b1f6b0fc7e5202c6b59',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/stdlib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/stringable' => array(
            'pretty_version' => 'v3.1.50',
            'version' => '********',
            'reference' => '89ab60e9ccabf024f5afc81a72f630cc67ae6687',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/stringable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/support' => array(
            'pretty_version' => 'v3.1.50',
            'version' => '********',
            'reference' => '899b7dbfe39b60d25ec71da3ad445d9db0bbce1a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/support',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/tappable' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => 'f5c5d343c95238dcb3fe500876ceadc175e221f8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/tappable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/testing' => array(
            'pretty_version' => 'v3.1.48',
            'version' => '********',
            'reference' => 'e5e5eba5c304a876dd251e774ecdb3a8ebf97edd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/testing',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'hyperf/tracer' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => '8ec4b4f2499592a3d748e3760aa63bca31c78883',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/tracer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/utils' => array(
            'pretty_version' => 'v3.1.42',
            'version' => '********',
            'reference' => '4b13a567a61d08a3c4d058499e28a5b26fc18f1c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/utils',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hyperf/websocket-server' => array(
            'pretty_version' => 'v3.1.50',
            'version' => '********',
            'reference' => 'c770ebf0208738bbb21000db01f09a48e15023a5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hyperf/websocket-server',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'jcchavezs/zipkin-opentracing' => array(
            'pretty_version' => '2.0.4',
            'version' => '*******',
            'reference' => '6bd908f84ff611dff4d64c5e7d510bd6a1107575',
            'type' => 'library',
            'install_path' => __DIR__ . '/../jcchavezs/zipkin-opentracing',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kodova/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'laminas/laminas-mime' => array(
            'pretty_version' => '2.12.0',
            'version' => '********',
            'reference' => '08cc544778829b7d68d27a097885bd6e7130135e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laminas/laminas-mime',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laminas/laminas-stdlib' => array(
            'pretty_version' => '3.20.0',
            'version' => '********',
            'reference' => '8974a1213be42c3e2f70b2c27b17f910291ab2f4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laminas/laminas-stdlib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'maennchen/zipstream-php' => array(
            'pretty_version' => '3.1.1',
            'version' => '*******',
            'reference' => '6187e9cc4493da94b9b63eb2315821552015fca9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../maennchen/zipstream-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'markbaker/complex' => array(
            'pretty_version' => '3.0.2',
            'version' => '*******',
            'reference' => '95c56caa1cf5c766ad6d65b6344b807c1e8405b9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/complex',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'markbaker/matrix' => array(
            'pretty_version' => '3.0.1',
            'version' => '*******',
            'reference' => '728434227fe21be27ff6d86621a1b13107a2562c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/matrix',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mockery/mockery' => array(
            'pretty_version' => '1.6.12',
            'version' => '********',
            'reference' => '1f4efdd7d3beafe9807b08156dfcb176d18f1699',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mockery/mockery',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '3.8.1',
            'version' => '3.8.1.0',
            'reference' => 'aef6ee73a77a66e404dd6540934a9ef1b3c855b4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'myclabs/deep-copy' => array(
            'pretty_version' => '1.12.1',
            'version' => '1.12.1.0',
            'reference' => '123267b2c49fbf30d78a7b2d333f6be754b94845',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/deep-copy',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nesbot/carbon' => array(
            'pretty_version' => '2.72.6',
            'version' => '2.72.6.0',
            'reference' => '1e9d50601e7035a4c61441a208cb5bed73e108c5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nesbot/carbon',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nikic/fast-route' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'reference' => '181d480e08d9476e61381e04a71b34dc0432e812',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/fast-route',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nikic/php-parser' => array(
            'pretty_version' => 'v4.19.4',
            'version' => '4.19.4.0',
            'reference' => '715f4d25e225bc47b293a8b997fe6ce99bf987d2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/php-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'opentracing/opentracing' => array(
            'pretty_version' => '1.0.2',
            'version' => '*******',
            'reference' => 'cd60bd1fb2a25280600bc74c7f9e0c13881a9116',
            'type' => 'library',
            'install_path' => __DIR__ . '/../opentracing/opentracing',
            'aliases' => array(),
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0.0',
            ),
        ),
        'openzipkin/zipkin' => array(
            'pretty_version' => '3.2.0',
            'version' => '*******',
            'reference' => 'e2809f8b6775796d2116b3ca73576a1734296ff6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../openzipkin/zipkin',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/constant_time_encoding' => array(
            'pretty_version' => 'v3.0.0',
            'version' => '*******',
            'reference' => 'df1e7fde177501eee2037dd159cf04f5f301a512',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/constant_time_encoding',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.100',
            'version' => '**********',
            'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phar-io/manifest' => array(
            'pretty_version' => '2.0.4',
            'version' => '*******',
            'reference' => '54750ef60c58e43759730615a392c31c80e23176',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/manifest',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phar-io/version' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'reference' => '4f7fd7836c6f332bb2933569e566a0d6c4cbed74',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'php-amqplib/php-amqplib' => array(
            'pretty_version' => 'v3.7.2',
            'version' => '3.7.2.0',
            'reference' => '738a73eb0019b6c99d9bc25d7a0c0dd8f56a5199',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-amqplib/php-amqplib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-di/phpdoc-reader' => array(
            'pretty_version' => '2.2.1',
            'version' => '2.2.1.0',
            'reference' => '66daff34cbd2627740ffec9469ffbac9f8c8185c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-di/phpdoc-reader',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoffice/phpspreadsheet' => array(
            'pretty_version' => '1.29.9',
            'version' => '1.29.9.0',
            'reference' => 'ffb47b639649fc9c8a6fa67977a27b756592ed85',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/phpspreadsheet',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoption/phpoption' => array(
            'pretty_version' => '1.9.3',
            'version' => '1.9.3.0',
            'reference' => 'e3fac8b24f56113f7cb96af14958c0dd16330f54',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoption/phpoption',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpseclib/phpseclib' => array(
            'pretty_version' => '3.0.43',
            'version' => '3.0.43.0',
            'reference' => '709ec107af3cb2f385b9617be72af8cf62441d02',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpseclib/phpseclib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpstan/phpstan' => array(
            'pretty_version' => '1.12.15',
            'version' => '1.12.15.0',
            'reference' => 'c91d4e8bc056f46cf653656e6f71004b254574d1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpstan/phpstan',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-code-coverage' => array(
            'pretty_version' => '10.1.16',
            'version' => '10.1.16.0',
            'reference' => '7e308268858ed6baedc8704a304727d20bc07c77',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-code-coverage',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-file-iterator' => array(
            'pretty_version' => '4.1.0',
            'version' => '4.1.0.0',
            'reference' => 'a95037b6d9e608ba092da1b23931e537cadc3c3c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-file-iterator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-invoker' => array(
            'pretty_version' => '4.0.0',
            'version' => '4.0.0.0',
            'reference' => 'f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-invoker',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-text-template' => array(
            'pretty_version' => '3.0.1',
            'version' => '*******',
            'reference' => '0c7b06ff49e3d5072f057eb1fa59258bf287a748',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-text-template',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-timer' => array(
            'pretty_version' => '6.0.0',
            'version' => '6.0.0.0',
            'reference' => 'e2a2d67966e740530f4a3343fe2e030ffdc1161d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-timer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/phpunit' => array(
            'pretty_version' => '10.5.40',
            'version' => '10.5.40.0',
            'reference' => 'e6ddda95af52f69c1e0c7b4f977cccb58048798c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/phpunit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => true,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-server-handler' => array(
            'pretty_version' => '1.0.2',
            'version' => '*******',
            'reference' => '84c4fb66179be4caaf8e97bd239203245302e7d4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-server-handler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-server-middleware' => array(
            'pretty_version' => '1.0.2',
            'version' => '*******',
            'reference' => 'c1481f747daaa6a0782775cd6a8c26a1bf4a3829',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-server-middleware',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '*******',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
                1 => '3.0.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '*******',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'react/cache' => array(
            'pretty_version' => 'v1.2.0',
            'version' => '1.2.0.0',
            'reference' => 'd47c472b64aa5608225f47965a484b75c7817d5b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/cache',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'react/child-process' => array(
            'pretty_version' => 'v0.6.6',
            'version' => '0.6.6.0',
            'reference' => '1721e2b93d89b745664353b9cfc8f155ba8a6159',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/child-process',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'react/dns' => array(
            'pretty_version' => 'v1.13.0',
            'version' => '1.13.0.0',
            'reference' => 'eb8ae001b5a455665c89c1df97f6fb682f8fb0f5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/dns',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'react/event-loop' => array(
            'pretty_version' => 'v1.5.0',
            'version' => '1.5.0.0',
            'reference' => 'bbe0bd8c51ffc05ee43f1729087ed3bdf7d53354',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/event-loop',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'react/promise' => array(
            'pretty_version' => 'v2.11.0',
            'version' => '2.11.0.0',
            'reference' => '1a8460931ea36dc5c76838fec5734d55c88c6831',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/promise',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'react/socket' => array(
            'pretty_version' => 'v1.16.0',
            'version' => '1.16.0.0',
            'reference' => '23e4ff33ea3e160d2d1f59a0e6050e4b0fb0eac1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/socket',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'react/stream' => array(
            'pretty_version' => 'v1.4.0',
            'version' => '1.4.0.0',
            'reference' => '1e5b0acb8fe55143b5b426817155190eb6f5b18d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/stream',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/cli-parser' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => 'c34583b87e7b7a8055bf6c450c2c77ce32a24084',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/cli-parser',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit' => array(
            'pretty_version' => '2.0.0',
            'version' => '2.0.0.0',
            'reference' => 'a81fee9eef0b7a76af11d121767abc44c104e503',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit-reverse-lookup' => array(
            'pretty_version' => '3.0.0',
            'version' => '*******',
            'reference' => '5e3a687f7d8ae33fb362c5c0743794bbb2420a1d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit-reverse-lookup',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/comparator' => array(
            'pretty_version' => '5.0.3',
            'version' => '5.0.3.0',
            'reference' => 'a18251eb0b7a2dcd2f7aa3d6078b18545ef0558e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/comparator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/complexity' => array(
            'pretty_version' => '3.2.0',
            'version' => '*******',
            'reference' => '68ff824baeae169ec9f2137158ee529584553799',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/complexity',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/diff' => array(
            'pretty_version' => '5.1.1',
            'version' => '5.1.1.0',
            'reference' => 'c41e007b4b62af48218231d6c2275e4c9b975b2e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/diff',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/environment' => array(
            'pretty_version' => '6.1.0',
            'version' => '6.1.0.0',
            'reference' => '8074dbcd93529b357029f5cc5058fd3e43666984',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/environment',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/exporter' => array(
            'pretty_version' => '5.1.2',
            'version' => '5.1.2.0',
            'reference' => '955288482d97c19a372d3f31006ab3f37da47adf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/exporter',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/global-state' => array(
            'pretty_version' => '6.0.2',
            'version' => '6.0.2.0',
            'reference' => '987bafff24ecc4c9ac418cab1145b96dd6e9cbd9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/global-state',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/lines-of-code' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => '856e7f6a75a84e339195d48c556f23be2ebf75d0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/lines-of-code',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-enumerator' => array(
            'pretty_version' => '5.0.0',
            'version' => '5.0.0.0',
            'reference' => '202d0e344a580d7f7d04b3fafce6933e59dae906',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-enumerator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-reflector' => array(
            'pretty_version' => '3.0.0',
            'version' => '*******',
            'reference' => '24ed13d98130f0e7122df55d06c5c4942a577957',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-reflector',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/recursion-context' => array(
            'pretty_version' => '5.0.0',
            'version' => '5.0.0.0',
            'reference' => '05909fb5bc7df4c52992396d0116aed689f93712',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/recursion-context',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/type' => array(
            'pretty_version' => '4.0.0',
            'version' => '4.0.0.0',
            'reference' => '462699a16464c3944eefc02ebdd77882bd3925bf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/type',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/version' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => 'c51fa83a5d8f43f1402e3f32a005e6262244ef17',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'swoole/ide-helper' => array(
            'pretty_version' => '5.1.6',
            'version' => '5.1.6.0',
            'reference' => 'c5149a01c00e4ed56fc7b3ffeb6823e69acb4a76',
            'type' => 'library',
            'install_path' => __DIR__ . '/../swoole/ide-helper',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'swow/psr7-plus' => array(
            'pretty_version' => 'v1.1.2',
            'version' => '1.1.2.0',
            'reference' => '7acc4924be907d2ff64edee5a2bd116620e56364',
            'type' => 'library',
            'install_path' => __DIR__ . '/../swow/psr7-plus',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v6.4.17',
            'version' => '6.4.17.0',
            'reference' => '799445db3f15768ecc382ac5699e6da0520a0a04',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v6.4.13',
            'version' => '6.4.13.0',
            'reference' => '0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '7642f5e970b672283b7823222ae8ef8bbc160b9f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => true,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'symfony/filesystem' => array(
            'pretty_version' => 'v6.4.13',
            'version' => '6.4.13.0',
            'reference' => '4856c9cf585d5a0313d8d35afd681a526f038dd3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/filesystem',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v6.4.17',
            'version' => '6.4.17.0',
            'reference' => '1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v6.4.16',
            'version' => '6.4.16.0',
            'reference' => '431771b7a6f662f1575b3cfc8fd7617aa9864d57',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/options-resolver' => array(
            'pretty_version' => 'v6.4.16',
            'version' => '6.4.16.0',
            'reference' => '368128ad168f20e22c32159b9f761e456cec0c78',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/options-resolver',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '85181ba99b2345b0ef10ce42ecac37612d9fd341',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '60328e362d4c2c802a54fcbf04f9d3fb892b4cf8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php81' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php81',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-php83' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '2fb86d65e2d424369ad2905e83b236a8805ba491',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php83',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v6.4.15',
            'version' => '6.4.15.0',
            'reference' => '3cb242f059c14ae08591c5c4087d1fe443564392',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => 'e53260aabf78fb3d63f8d79d69ece59f80d5eda0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/stopwatch' => array(
            'pretty_version' => 'v6.4.13',
            'version' => '6.4.13.0',
            'reference' => '2cae0a6f8d04937d02f6d19806251e2104d54f92',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/stopwatch',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v6.4.15',
            'version' => '6.4.15.0',
            'reference' => '73a5e66ea2e1677c98d4449177c5a9cf9d8b4c6f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation' => array(
            'pretty_version' => 'v6.4.13',
            'version' => '6.4.13.0',
            'reference' => 'bee9bfabfa8b4045a66bf82520e492cddbaffa66',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '4667ff3bd513750603a09c8dedbea942487fb07c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.3|3.0',
            ),
        ),
        'theseer/tokenizer' => array(
            'pretty_version' => '1.2.3',
            'version' => '1.2.3.0',
            'reference' => '737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../theseer/tokenizer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'videlalvaro/php-amqplib' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v3.7.2',
            ),
        ),
        'vlucas/phpdotenv' => array(
            'pretty_version' => 'v5.6.1',
            'version' => '5.6.1.0',
            'reference' => 'a59a13791077fe3d44f90e7133eb68e7d22eaff2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vlucas/phpdotenv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
