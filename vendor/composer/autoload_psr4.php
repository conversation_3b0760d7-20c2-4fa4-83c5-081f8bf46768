<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'phpseclib3\\' => array($vendorDir . '/phpseclib/phpseclib/phpseclib'),
    'Zipkin\\' => array($vendorDir . '/openzipkin/zipkin/src/Zipkin'),
    'ZipkinOpenTracing\\' => array($vendorDir . '/jcchavezs/zipkin-opentracing/src/ZipkinOpenTracing'),
    'ZipStream\\' => array($vendorDir . '/maennchen/zipstream-php/src'),
    'Symfony\\Polyfill\\Php83\\' => array($vendorDir . '/symfony/polyfill-php83'),
    'Symfony\\Polyfill\\Php81\\' => array($vendorDir . '/symfony/polyfill-php81'),
    'Symfony\\Polyfill\\Php80\\' => array($vendorDir . '/symfony/polyfill-php80'),
    'Symfony\\Polyfill\\Mbstring\\' => array($vendorDir . '/symfony/polyfill-mbstring'),
    'Symfony\\Polyfill\\Intl\\Normalizer\\' => array($vendorDir . '/symfony/polyfill-intl-normalizer'),
    'Symfony\\Polyfill\\Intl\\Grapheme\\' => array($vendorDir . '/symfony/polyfill-intl-grapheme'),
    'Symfony\\Polyfill\\Ctype\\' => array($vendorDir . '/symfony/polyfill-ctype'),
    'Symfony\\Contracts\\Translation\\' => array($vendorDir . '/symfony/translation-contracts'),
    'Symfony\\Contracts\\Service\\' => array($vendorDir . '/symfony/service-contracts'),
    'Symfony\\Contracts\\EventDispatcher\\' => array($vendorDir . '/symfony/event-dispatcher-contracts'),
    'Symfony\\Component\\Translation\\' => array($vendorDir . '/symfony/translation'),
    'Symfony\\Component\\String\\' => array($vendorDir . '/symfony/string'),
    'Symfony\\Component\\Stopwatch\\' => array($vendorDir . '/symfony/stopwatch'),
    'Symfony\\Component\\Process\\' => array($vendorDir . '/symfony/process'),
    'Symfony\\Component\\OptionsResolver\\' => array($vendorDir . '/symfony/options-resolver'),
    'Symfony\\Component\\HttpFoundation\\' => array($vendorDir . '/symfony/http-foundation'),
    'Symfony\\Component\\Finder\\' => array($vendorDir . '/symfony/finder'),
    'Symfony\\Component\\Filesystem\\' => array($vendorDir . '/symfony/filesystem'),
    'Symfony\\Component\\EventDispatcher\\' => array($vendorDir . '/symfony/event-dispatcher'),
    'Symfony\\Component\\Console\\' => array($vendorDir . '/symfony/console'),
    'Swow\\Psr7\\Message\\' => array($vendorDir . '/swow/psr7-plus/src/Message'),
    'React\\Stream\\' => array($vendorDir . '/react/stream/src'),
    'React\\Socket\\' => array($vendorDir . '/react/socket/src'),
    'React\\Promise\\' => array($vendorDir . '/react/promise/src'),
    'React\\EventLoop\\' => array($vendorDir . '/react/event-loop/src'),
    'React\\Dns\\' => array($vendorDir . '/react/dns/src'),
    'React\\ChildProcess\\' => array($vendorDir . '/react/child-process/src'),
    'React\\Cache\\' => array($vendorDir . '/react/cache/src'),
    'Psr\\SimpleCache\\' => array($vendorDir . '/psr/simple-cache/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/src'),
    'Psr\\Http\\Server\\' => array($vendorDir . '/psr/http-server-middleware/src', $vendorDir . '/psr/http-server-handler/src'),
    'Psr\\Http\\Message\\' => array($vendorDir . '/psr/http-factory/src', $vendorDir . '/psr/http-message/src'),
    'Psr\\Http\\Client\\' => array($vendorDir . '/psr/http-client/src'),
    'Psr\\EventDispatcher\\' => array($vendorDir . '/psr/event-dispatcher/src'),
    'Psr\\Container\\' => array($vendorDir . '/psr/container/src'),
    'Psr\\Clock\\' => array($vendorDir . '/psr/clock/src'),
    'PhpParser\\' => array($vendorDir . '/nikic/php-parser/lib/PhpParser'),
    'PhpOption\\' => array($vendorDir . '/phpoption/phpoption/src/PhpOption'),
    'PhpOffice\\PhpSpreadsheet\\' => array($vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet'),
    'PhpDocReader\\' => array($vendorDir . '/php-di/phpdoc-reader/src/PhpDocReader'),
    'PhpCsFixer\\' => array($vendorDir . '/friendsofphp/php-cs-fixer/src'),
    'PhpAmqpLib\\' => array($vendorDir . '/php-amqplib/php-amqplib/PhpAmqpLib'),
    'ParagonIE\\ConstantTime\\' => array($vendorDir . '/paragonie/constant_time_encoding/src'),
    'OpenTracing\\' => array($vendorDir . '/opentracing/opentracing/src/OpenTracing'),
    'Monolog\\' => array($vendorDir . '/monolog/monolog/src/Monolog'),
    'Mockery\\' => array($vendorDir . '/mockery/mockery/library/Mockery'),
    'Matrix\\' => array($vendorDir . '/markbaker/matrix/classes/src'),
    'Laminas\\Stdlib\\' => array($vendorDir . '/laminas/laminas-stdlib/src'),
    'Laminas\\Mime\\' => array($vendorDir . '/laminas/laminas-mime/src'),
    'Hyperf\\WebSocketServer\\' => array($vendorDir . '/hyperf/websocket-server/src'),
    'Hyperf\\Tracer\\' => array($vendorDir . '/hyperf/tracer/src'),
    'Hyperf\\Testing\\' => array($vendorDir . '/hyperf/testing/src'),
    'Hyperf\\Tappable\\' => array($vendorDir . '/hyperf/tappable/src'),
    'Hyperf\\Support\\' => array($vendorDir . '/hyperf/support/src'),
    'Hyperf\\Stringable\\' => array($vendorDir . '/hyperf/stringable/src'),
    'Hyperf\\Stdlib\\' => array($vendorDir . '/hyperf/stdlib/src'),
    'Hyperf\\Session\\' => array($vendorDir . '/hyperf/session/src'),
    'Hyperf\\Server\\' => array($vendorDir . '/hyperf/server/src'),
    'Hyperf\\Serializer\\' => array($vendorDir . '/hyperf/serializer/src'),
    'Hyperf\\Redis\\' => array($vendorDir . '/hyperf/redis/src'),
    'Hyperf\\Process\\' => array($vendorDir . '/hyperf/process/src'),
    'Hyperf\\Pool\\' => array($vendorDir . '/hyperf/pool/src'),
    'Hyperf\\Pipeline\\' => array($vendorDir . '/hyperf/pipeline/src'),
    'Hyperf\\Paginator\\' => array($vendorDir . '/hyperf/paginator/src'),
    'Hyperf\\ModelListener\\' => array($vendorDir . '/hyperf/model-listener/src'),
    'Hyperf\\ModelCache\\' => array($vendorDir . '/hyperf/model-cache/src'),
    'Hyperf\\Memory\\' => array($vendorDir . '/hyperf/memory/src'),
    'Hyperf\\Macroable\\' => array($vendorDir . '/hyperf/macroable/src'),
    'Hyperf\\Logger\\' => array($vendorDir . '/hyperf/logger/src'),
    'Hyperf\\HttpServer\\' => array($vendorDir . '/hyperf/http-server/src'),
    'Hyperf\\HttpMessage\\' => array($vendorDir . '/hyperf/http-message/src'),
    'Hyperf\\Guzzle\\' => array($vendorDir . '/hyperf/guzzle/src'),
    'Hyperf\\Framework\\' => array($vendorDir . '/hyperf/framework/src'),
    'Hyperf\\ExceptionHandler\\' => array($vendorDir . '/hyperf/exception-handler/src'),
    'Hyperf\\Event\\' => array($vendorDir . '/hyperf/event/src'),
    'Hyperf\\Engine\\Contract\\' => array($vendorDir . '/hyperf/engine-contract/src'),
    'Hyperf\\Engine\\' => array($vendorDir . '/hyperf/engine/src'),
    'Hyperf\\Elasticsearch\\' => array($vendorDir . '/hyperf/elasticsearch/src'),
    'Hyperf\\Dispatcher\\' => array($vendorDir . '/hyperf/dispatcher/src'),
    'Hyperf\\Di\\' => array($vendorDir . '/hyperf/di/src'),
    'Hyperf\\Devtool\\' => array($vendorDir . '/hyperf/devtool/src'),
    'Hyperf\\DbConnection\\' => array($vendorDir . '/hyperf/db-connection/src'),
    'Hyperf\\Database\\' => array($vendorDir . '/hyperf/database/src'),
    'Hyperf\\Crontab\\' => array($vendorDir . '/hyperf/crontab/src'),
    'Hyperf\\Coroutine\\' => array($vendorDir . '/hyperf/coroutine/src'),
    'Hyperf\\Coordinator\\' => array($vendorDir . '/hyperf/coordinator/src'),
    'Hyperf\\Contract\\' => array($vendorDir . '/hyperf/contract/src'),
    'Hyperf\\Context\\' => array($vendorDir . '/hyperf/context/src'),
    'Hyperf\\Constants\\' => array($vendorDir . '/hyperf/constants/src'),
    'Hyperf\\Config\\' => array($vendorDir . '/hyperf/config/src'),
    'Hyperf\\Conditionable\\' => array($vendorDir . '/hyperf/conditionable/src'),
    'Hyperf\\Command\\' => array($vendorDir . '/hyperf/command/src'),
    'Hyperf\\Collection\\' => array($vendorDir . '/hyperf/collection/src'),
    'Hyperf\\Codec\\' => array($vendorDir . '/hyperf/codec/src'),
    'Hyperf\\CodeParser\\' => array($vendorDir . '/hyperf/code-parser/src'),
    'Hyperf\\Cache\\' => array($vendorDir . '/hyperf/cache/src'),
    'Hyperf\\AsyncQueue\\' => array($vendorDir . '/hyperf/async-queue/src'),
    'Hyperf\\Amqp\\' => array($vendorDir . '/hyperf/amqp/src'),
    'HyperfTest\\' => array($baseDir . '/test'),
    'GuzzleHttp\\Stream\\' => array($vendorDir . '/ezimuel/guzzlestreams/src'),
    'GuzzleHttp\\Ring\\' => array($vendorDir . '/ezimuel/ringphp/src'),
    'GuzzleHttp\\Psr7\\' => array($vendorDir . '/guzzlehttp/psr7/src'),
    'GuzzleHttp\\Promise\\' => array($vendorDir . '/guzzlehttp/promises/src'),
    'GuzzleHttp\\' => array($vendorDir . '/guzzlehttp/guzzle/src'),
    'GrahamCampbell\\ResultType\\' => array($vendorDir . '/graham-campbell/result-type/src'),
    'Fig\\Http\\Message\\' => array($vendorDir . '/fig/http-message-util/src'),
    'Fidry\\CpuCoreCounter\\' => array($vendorDir . '/fidry/cpu-core-counter/src'),
    'FastRoute\\' => array($vendorDir . '/nikic/fast-route/src'),
    'Evenement\\' => array($vendorDir . '/evenement/evenement/src'),
    'Elasticsearch\\' => array($vendorDir . '/elasticsearch/elasticsearch/src/Elasticsearch'),
    'EasySwoole\\VerifyCode\\' => array($vendorDir . '/easyswoole/verifycode/src'),
    'EasySwoole\\Spl\\Test\\' => array($vendorDir . '/easyswoole/spl/test'),
    'EasySwoole\\Spl\\' => array($vendorDir . '/easyswoole/spl/src'),
    'Dotenv\\' => array($vendorDir . '/vlucas/phpdotenv/src'),
    'Doctrine\\Instantiator\\' => array($vendorDir . '/doctrine/instantiator/src/Doctrine/Instantiator'),
    'Doctrine\\Inflector\\' => array($vendorDir . '/doctrine/inflector/lib/Doctrine/Inflector'),
    'DeepCopy\\' => array($vendorDir . '/myclabs/deep-copy/src/DeepCopy'),
    'Composer\\XdebugHandler\\' => array($vendorDir . '/composer/xdebug-handler/src'),
    'Composer\\Semver\\' => array($vendorDir . '/composer/semver/src'),
    'Composer\\Pcre\\' => array($vendorDir . '/composer/pcre/src'),
    'Complex\\' => array($vendorDir . '/markbaker/complex/classes/src'),
    'Clue\\React\\NDJson\\' => array($vendorDir . '/clue/ndjson-react/src'),
    'Carbon\\Doctrine\\' => array($vendorDir . '/carbonphp/carbon-doctrine-types/src/Carbon/Doctrine'),
    'Carbon\\' => array($vendorDir . '/nesbot/carbon/src/Carbon'),
    'App\\' => array($baseDir . '/app'),
);
