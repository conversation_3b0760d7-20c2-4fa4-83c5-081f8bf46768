<?php

// autoload_files.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    '9c7a683baffd24f5595c1dc5f5273030' => $vendorDir . '/hyperf/engine/src/Functions.php',
    'f0334cce41da231af374e1df9dc548c9' => $vendorDir . '/hyperf/collection/src/Functions.php',
    '3ac0459b8c20ccf3a7319b7cba59c914' => $vendorDir . '/hyperf/tappable/src/Functions.php',
    'e45471c4161dad9820dfacbc5735c3f5' => $vendorDir . '/hyperf/stringable/src/Functions.php',
    '6c17036e92b20070dc14f563311a06a3' => $vendorDir . '/hyperf/coroutine/src/Functions.php',
    'ffe5873ab2256a6c3a4c92b3488528cb' => $vendorDir . '/hyperf/support/src/Functions.php',
    '0e6d7bf4a5811bfa5cf40c5ccd6fae6a' => $vendorDir . '/symfony/polyfill-mbstring/bootstrap.php',
    '3ef245790d3389cf1f32f98f11abff00' => $vendorDir . '/hyperf/coordinator/src/Functions.php',
    '6e3fae29631ef280660b3cdad06f25a8' => $vendorDir . '/symfony/deprecation-contracts/function.php',
    '320cde22f66dd4f5d3fd621d3e88b98f' => $vendorDir . '/symfony/polyfill-ctype/bootstrap.php',
    'a4a119a56e50fbb293281d9a48007e0e' => $vendorDir . '/symfony/polyfill-php80/bootstrap.php',
    'ad155f8f1cf0d418fe49e248db8c661b' => $vendorDir . '/react/promise/src/functions_include.php',
    '8825ede83f2f289127722d4e842cf7e8' => $vendorDir . '/symfony/polyfill-intl-grapheme/bootstrap.php',
    'e69f7f6ee287b969198c3c9d6777bd38' => $vendorDir . '/symfony/polyfill-intl-normalizer/bootstrap.php',
    'b6b991a57620e2fb6b2f66f03fe9ddc2' => $vendorDir . '/symfony/string/Resources/functions.php',
    'a1105708a18b76903365ca1c4aa61b02' => $vendorDir . '/symfony/translation/Resources/functions.php',
    '7b11c4dc42b3b3023073cb14e519683c' => $vendorDir . '/ralouphie/getallheaders/src/getallheaders.php',
    '37a3dc5111fe8f707ab4c132ef1dbc62' => $vendorDir . '/guzzlehttp/guzzle/src/functions_include.php',
    '253c157292f75eb38082b5acb06f3f01' => $vendorDir . '/nikic/fast-route/src/functions.php',
    'ff1b7935a93a4a9517db3ebe0533892a' => $vendorDir . '/opentracing/opentracing/src/OpenTracing/Tags.php',
    '0db36546c71c357f5ee70c39bb03966f' => $vendorDir . '/opentracing/opentracing/src/OpenTracing/Formats.php',
    '6124b4c8570aa390c21fafd04a26c69f' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/deep_copy.php',
    '198ca788260cba849b8d9061ef2afeb7' => $vendorDir . '/openzipkin/zipkin/src/Zipkin/Propagation/Id.php',
    '1078d5a0858ccde97f683b735c9f5473' => $vendorDir . '/openzipkin/zipkin/src/Zipkin/Timestamp.php',
    '948c9b6b6a769d2db468357f07afb9ed' => $vendorDir . '/openzipkin/zipkin/src/Zipkin/Kind.php',
    'f99ca7ab6b69ea13de674def3146fa4b' => $vendorDir . '/openzipkin/zipkin/src/Zipkin/Tags.php',
    '809f512612033e8c2eaab5c48aa38d39' => $vendorDir . '/openzipkin/zipkin/src/Zipkin/Annotations.php',
    '0956408cbb3b629c2aedbc429d54f919' => $vendorDir . '/openzipkin/zipkin/src/Zipkin/SpanName.php',
    'decc78cc4436b1292c6c0d151b19445c' => $vendorDir . '/phpseclib/phpseclib/phpseclib/bootstrap.php',
    '662a729f963d39afe703c9d9b7ab4a8c' => $vendorDir . '/symfony/polyfill-php83/bootstrap.php',
    '8592c7b0947d8a0965a9e8c3d16f9c24' => $vendorDir . '/elasticsearch/elasticsearch/src/autoload.php',
    '2cffec82183ee1cea088009cef9a6fc3' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier.composer.php',
    'ec07570ca5a812141189b1fa81503674' => $vendorDir . '/phpunit/phpunit/src/Framework/Assert/Functions.php',
    '23c18046f52bef3eea034657bafda50f' => $vendorDir . '/symfony/polyfill-php81/bootstrap.php',
    'f3f8e38fc3681ba0ec89c7039db537dd' => $vendorDir . '/hyperf/async-queue/src/Functions.php',
    'fdea4480df6c5882835d3b60a113de4d' => $vendorDir . '/hyperf/config/src/Functions.php',
    'c72349b1fe8d0deeedd3a52e8aa814d8' => $vendorDir . '/mockery/mockery/library/helpers.php',
    'ce9671a430e4846b44e1c68c7611f9f5' => $vendorDir . '/mockery/mockery/library/Mockery.php',
    '9b38cf48e83f5d8f60375221cd213eee' => $vendorDir . '/phpstan/phpstan/bootstrap.php',
);
