body {
  margin: 0;
}

html {
  overflow-x: hidden;
  overflow-y: scroll;
}

#loading-bg {
  position: absolute;
  display: block;
  background: var(--initial-loader-bg, #fff);
  block-size: 100%;
  inline-size: 100%;
}

.loading-logo {
  position: absolute;
  inset-block-start: 40%;
  inset-inline-start: calc(50% - 45px);
}

.loading {
  position: absolute;
  box-sizing: border-box;
  border: 3px solid transparent;
  block-size: 55px;
  border-radius: 50%;
  inline-size: 55px;
  inset-block-start: 50%;
  inset-inline-start: calc(50% - 35px);
}

.loading .effect-1,
.loading .effect-2,
.loading .effect-3 {
  position: absolute;
  box-sizing: border-box;
  border: 3px solid transparent;
  block-size: 100%;
  border-inline-start: 3px solid var(--initial-loader-color, #eee);
  border-radius: 50%;
  inline-size: 100%;
}

.loading .effect-1 {
  animation: rotate 1s ease infinite;
}

.loading .effect-2 {
  animation: rotate-opacity 1s ease infinite 0.1s;
}

.loading .effect-3 {
  animation: rotate-opacity 1s ease infinite 0.2s;
}

.loading .effects {
  transition: all 0.3s ease;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(1turn);
  }
}

@keyframes rotate-opacity {
  0% {
    opacity: 0.1;
    transform: rotate(0deg);
  }

  100% {
    opacity: 1;
    transform: rotate(1turn);
  }
}


.init-loading {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
}

.init-loading-spinner {
  position: relative;
  height: 32px;
  width: 32px;
}

.init-loading-spinner .init-loading-spinner-container {
  position: absolute;
  top: 50%;
  width: 0;
  z-index: 1;
  transform: scale(.15);
  right: 50%;
}

.init-loading-spinner .init-loading-spinner-nib {
  height: 28px;
  position: absolute;
  top: -12.5px;
  width: 66px;
  background: transparent;
  border-radius: 25%/50%;
  transform-origin: right center;
}

.init-loading-spinner .init-loading-spinner-nib:before {
  content: "";
  display: block;
  height: 100%;
  width: 100%;
  background: #000;
  border-radius: 25%/50%;
  animation-direction: normal;
  animation-duration: .8s;
  animation-fill-mode: none;
  animation-iteration-count: infinite;
  animation-name: init-loading-spinner-line-fade-default;
  animation-play-state: running;
  animation-timing-function: linear;
}

.init-loading-spinner .init-loading-spinner-nib.init-loading-spinner-nib-1 {
  transform: rotate(0deg) translateX(-40px)
}

.init-loading-spinner .init-loading-spinner-nib.init-loading-spinner-nib-1:before {
  animation-delay: -.8s
}

.init-loading-spinner .init-loading-spinner-nib.init-loading-spinner-nib-2 {
  transform: rotate(-45deg) translateX(-40px)
}

.init-loading-spinner .init-loading-spinner-nib.init-loading-spinner-nib-2:before {
  animation-delay: -.7s
}

.init-loading-spinner .init-loading-spinner-nib.init-loading-spinner-nib-3 {
  transform: rotate(-90deg) translateX(-40px)
}

.init-loading-spinner .init-loading-spinner-nib.init-loading-spinner-nib-3:before {
  animation-delay: -.6s
}

.init-loading-spinner .init-loading-spinner-nib.init-loading-spinner-nib-4 {
  transform: rotate(-135deg) translateX(-40px)
}

.init-loading-spinner .init-loading-spinner-nib.init-loading-spinner-nib-4:before {
  animation-delay: -.5s
}

.init-loading-spinner .init-loading-spinner-nib.init-loading-spinner-nib-5 {
  transform: rotate(-180deg) translateX(-40px)
}

.init-loading-spinner .init-loading-spinner-nib.init-loading-spinner-nib-5:before {
  animation-delay: -.4s
}

.init-loading-spinner .init-loading-spinner-nib.init-loading-spinner-nib-6 {
  transform: rotate(-225deg) translateX(-40px)
}

.init-loading-spinner .init-loading-spinner-nib.init-loading-spinner-nib-6:before {
  animation-delay: -.3s
}

.init-loading-spinner .init-loading-spinner-nib.init-loading-spinner-nib-7 {
  transform: rotate(-270deg) translateX(-40px)
}

.init-loading-spinner .init-loading-spinner-nib.init-loading-spinner-nib-7:before {
  animation-delay: -.2s
}

.init-loading-spinner .init-loading-spinner-nib.init-loading-spinner-nib-8 {
  transform: rotate(-315deg) translateX(-40px)
}

.init-loading-spinner .init-loading-spinner-nib.init-loading-spinner-nib-8:before {
  animation-delay: -.1s
}

@keyframes init-loading-spinner-line-fade-default {

  0%,
  to {
      opacity: .55
  }

  95% {
      opacity: .08
  }

  1% {
      opacity: .55
  }
}

.init-loading-spinner .init-loading-spinner-label {
  clip: rect(1px, 1px, 1px, 1px);
  -webkit-clip-path: inset(0 0 99.9% 99.9%);
  clip-path: inset(0 0 99.9% 99.9%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  width: 1px;
  border: 0;
  padding: 0;
}