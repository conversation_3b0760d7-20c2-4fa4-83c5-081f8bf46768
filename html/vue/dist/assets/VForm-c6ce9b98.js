import{i as b,j as p}from"./VTextField-3e2d458d.js";import{I as v,K as h,l as y,a7 as R,q as V,bL as F}from"./index-9a5dc664.js";const C=v()({name:"VForm",props:{...h(),...b()},emits:{"update:modelValue":n=>!0,submit:n=>!0},setup(n,i){let{slots:r,emit:f}=i;const o=p(n),s=y();function l(t){t.preventDefault(),o.reset()}function u(t){const a=t,e=o.validate();a.then=e.then.bind(e),a.catch=e.catch.bind(e),a.finally=e.finally.bind(e),f("submit",a),a.defaultPrevented||e.then(c=>{var m;let{valid:d}=c;d&&((m=s.value)==null||m.submit())}),a.preventDefault()}return R(()=>{var t;return V("form",{ref:s,class:["v-form",n.class],style:n.style,novalidate:!0,onReset:l,onSubmit:u},[(t=r.default)==null?void 0:t.call(r,o)])}),F(o,s)}});export{C as V};
