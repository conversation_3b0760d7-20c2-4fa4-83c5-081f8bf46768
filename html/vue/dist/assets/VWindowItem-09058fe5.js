import{bb as le,aQ as Z,K as U,O as q,b9 as ye,I as O,Q as se,Y as Se,ba as ie,l as T,B as y,ca as p,bJ as xe,E as ue,a7 as A,q as m,ck as ee,ag as te,bM as Ce,bP as Te,aZ as ke,cl as we,aU as ze,ah as Y,a8 as D,b_ as Be,c1 as Ee,bo as Pe,a0 as Re,bp as Ve,U as _e,W as I,a6 as Ie,C as j,cm as ce,P as $e,R as Me,aT as He,bk as Oe,aC as re,be as Ae,bi as Xe,bf as Ye,bd as Le,bj as We,a1 as Fe,bg as Ge,aD as De,aX as je,a3 as Ue}from"./index-9a5dc664.js";function ne(e){const t=Math.abs(e);return Math.sign(e)*(t/((1/.501-2)*(1-t)+1))}function oe(e){let{selectedElement:o,containerSize:t,contentSize:n,isRtl:r,currentScrollOffset:s,isHorizontal:a}=e;const l=a?o.clientWidth:o.clientHeight,u=a?o.offsetLeft:o.offsetTop,c=r&&a?n-u-l:u,d=t+s,x=l+c,S=l*.4;return c<=s?s=Math.max(c-S,0):d<=x&&(s=Math.min(s-(d-x-S),n-t)),s}function qe(e){let{selectedElement:o,containerSize:t,contentSize:n,isRtl:r,isHorizontal:s}=e;const a=s?o.clientWidth:o.clientHeight,l=s?o.offsetLeft:o.offsetTop,u=r&&s?n-l-a/2-t/2:l+a/2-t/2;return Math.min(n-t,Math.max(0,u))}const Ke=Symbol.for("vuetify:v-slide-group"),ve=le({centerActive:Boolean,direction:{type:String,default:"horizontal"},symbol:{type:null,default:Ke},nextIcon:{type:Z,default:"$next"},prevIcon:{type:Z,default:"$prev"},showArrows:{type:[Boolean,String],validator:e=>typeof e=="boolean"||["always","desktop","mobile"].includes(e)},...U(),...q(),...ye({selectedClass:"v-slide-group-item--active"})},"v-slide-group"),ae=O()({name:"VSlideGroup",props:ve(),emits:{"update:modelValue":e=>!0},setup(e,o){let{slots:t}=o;const{isRtl:n}=se(),{mobile:r}=Se(),s=ie(e,e.symbol),a=T(!1),l=T(0),u=T(0),c=T(0),d=y(()=>e.direction==="horizontal"),{resizeRef:x,contentRect:S}=p(),{resizeRef:h,contentRect:g}=p(),P=y(()=>s.selected.value.length?s.items.value.findIndex(i=>i.id===s.selected.value[0]):-1),V=y(()=>s.selected.value.length?s.items.value.findIndex(i=>i.id===s.selected.value[s.selected.value.length-1]):-1);if(xe){let i=-1;ue(()=>[s.selected.value,S.value,g.value,d.value],()=>{cancelAnimationFrame(i),i=requestAnimationFrame(()=>{if(S.value&&g.value){const v=d.value?"width":"height";u.value=S.value[v],c.value=g.value[v],a.value=u.value+1<c.value}if(P.value>=0&&h.value){const v=h.value.children[V.value];P.value===0||!a.value?l.value=0:e.centerActive?l.value=qe({selectedElement:v,containerSize:u.value,contentSize:c.value,isRtl:n.value,isHorizontal:d.value}):a.value&&(l.value=oe({selectedElement:v,containerSize:u.value,contentSize:c.value,isRtl:n.value,currentScrollOffset:l.value,isHorizontal:d.value}))}})})}const R=T(!1);let w=0,z=0;function f(i){const v=d.value?"clientX":"clientY";z=(n.value&&d.value?-1:1)*l.value,w=i.touches[0][v],R.value=!0}function b(i){if(!a.value)return;const v=d.value?"clientX":"clientY",E=n.value&&d.value?-1:1;l.value=E*(z+w-i.touches[0][v])}function C(i){const v=c.value-u.value;l.value<0||!a.value?l.value=0:l.value>=v&&(l.value=v),R.value=!1}function B(){x.value&&(x.value[d.value?"scrollLeft":"scrollTop"]=0)}const $=T(!1);function L(i){if($.value=!0,!(!a.value||!h.value)){for(const v of i.composedPath())for(const E of h.value.children)if(E===v){l.value=oe({selectedElement:E,containerSize:u.value,contentSize:c.value,isRtl:n.value,currentScrollOffset:l.value,isHorizontal:d.value});return}}}function _(i){$.value=!1}function W(i){var v;!$.value&&!(i.relatedTarget&&((v=h.value)!=null&&v.contains(i.relatedTarget)))&&k()}function K(i){h.value&&(d.value?i.key==="ArrowRight"?k(n.value?"prev":"next"):i.key==="ArrowLeft"&&k(n.value?"next":"prev"):i.key==="ArrowDown"?k("next"):i.key==="ArrowUp"&&k("prev"),i.key==="Home"?k("first"):i.key==="End"&&k("last"))}function k(i){var v,E,N,Q,J;if(h.value)if(!i)(v=Ce(h.value)[0])==null||v.focus();else if(i==="next"){const M=(E=h.value.querySelector(":focus"))==null?void 0:E.nextElementSibling;M?M.focus():k("first")}else if(i==="prev"){const M=(N=h.value.querySelector(":focus"))==null?void 0:N.previousElementSibling;M?M.focus():k("last")}else i==="first"?(Q=h.value.firstElementChild)==null||Q.focus():i==="last"&&((J=h.value.lastElementChild)==null||J.focus())}function H(i){const v=l.value+(i==="prev"?-1:1)*u.value;l.value=Te(v,0,c.value-u.value)}const X=y(()=>{let i=l.value>c.value-u.value?-(c.value-u.value)+ne(c.value-u.value-l.value):-l.value;l.value<=0&&(i=ne(-l.value));const v=n.value&&d.value?-1:1;return{transform:`translate${d.value?"X":"Y"}(${v*i}px)`,transition:R.value?"none":"",willChange:R.value?"transform":""}}),F=y(()=>({next:s.next,prev:s.prev,select:s.select,isSelected:s.isSelected})),G=y(()=>{switch(e.showArrows){case"always":return!0;case"desktop":return!r.value;case!0:return a.value||Math.abs(l.value)>0;case"mobile":return r.value||a.value||Math.abs(l.value)>0;default:return!r.value&&(a.value||Math.abs(l.value)>0)}}),ge=y(()=>Math.abs(l.value)>0),be=y(()=>c.value>Math.abs(l.value)+u.value);return A(()=>m(e.tag,{class:["v-slide-group",{"v-slide-group--vertical":!d.value,"v-slide-group--has-affixes":G.value,"v-slide-group--is-overflowing":a.value},e.class],style:e.style,tabindex:$.value||s.selected.value.length?-1:0,onFocus:W},{default:()=>{var i,v,E;return[G.value&&m("div",{key:"prev",class:["v-slide-group__prev",{"v-slide-group__prev--disabled":!ge.value}],onClick:()=>H("prev")},[((i=t.prev)==null?void 0:i.call(t,F.value))??m(ee,null,{default:()=>[m(te,{icon:n.value?e.nextIcon:e.prevIcon},null)]})]),m("div",{key:"container",ref:x,class:"v-slide-group__container",onScroll:B},[m("div",{ref:h,class:"v-slide-group__content",style:X.value,onTouchstartPassive:f,onTouchmovePassive:b,onTouchendPassive:C,onFocusin:L,onFocusout:_,onKeydown:K},[(v=t.default)==null?void 0:v.call(t,F.value)])]),G.value&&m("div",{key:"next",class:["v-slide-group__next",{"v-slide-group__next--disabled":!be.value}],onClick:()=>H("next")},[((E=t.next)==null?void 0:E.call(t,F.value))??m(ee,null,{default:()=>[m(te,{icon:n.value?e.prevIcon:e.nextIcon},null)]})])]}})),{selected:s.selected,scrollTo:H,scrollOffset:l,focus:k}}});const de=Symbol.for("vuetify:v-tabs"),Ne=O()({name:"VTab",props:{fixed:Boolean,sliderColor:String,hideSlider:Boolean,direction:{type:String,default:"horizontal"},...ke(we({selectedClass:"v-tab--selected",variant:"text"}),["active","block","flat","location","position","symbol"])},setup(e,o){let{slots:t,attrs:n}=o;const{textColorClasses:r,textColorStyles:s}=ze(e,"sliderColor"),a=y(()=>e.direction==="horizontal"),l=T(!1),u=T(),c=T();function d(x){var h,g;let{value:S}=x;if(l.value=S,S){const P=(g=(h=u.value)==null?void 0:h.$el.parentElement)==null?void 0:g.querySelector(".v-tab--selected .v-tab__slider"),V=c.value;if(!P||!V)return;const R=getComputedStyle(P).color,w=P.getBoundingClientRect(),z=V.getBoundingClientRect(),f=a.value?"x":"y",b=a.value?"X":"Y",C=a.value?"right":"bottom",B=a.value?"width":"height",$=w[f],L=z[f],_=$>L?w[C]-z[C]:w[f]-z[f],W=Math.sign(_)>0?a.value?"right":"bottom":Math.sign(_)<0?a.value?"left":"top":"center",k=(Math.abs(_)+(Math.sign(_)<0?w[B]:z[B]))/Math.max(w[B],z[B]),H=w[B]/z[B],X=1.5;Be(V,{backgroundColor:[R,""],transform:[`translate${b}(${_}px) scale${b}(${H})`,`translate${b}(${_/X}px) scale${b}(${(k-1)/X+1})`,""],transformOrigin:Array(3).fill(W)},{duration:225,easing:Ee})}}return A(()=>{const[x]=Y.filterProps(e);return m(Y,D({symbol:de,ref:u,class:["v-tab",e.class],style:e.style,tabindex:l.value?0:-1,role:"tab","aria-selected":String(l.value),active:!1,block:e.fixed,maxWidth:e.fixed?300:void 0,rounded:0},x,n,{"onGroup:selected":d}),{default:()=>{var S;return[((S=t.default)==null?void 0:S.call(t))??e.text,!e.hideSlider&&m("div",{ref:c,class:["v-tab__slider",r.value],style:s.value},null)]}})}),{}}});function Qe(e){return e?e.map(o=>typeof o=="string"?{title:o,value:o}:o):[]}const it=O()({name:"VTabs",props:{alignTabs:{type:String,default:"start"},color:String,fixedTabs:Boolean,items:{type:Array,default:()=>[]},stacked:Boolean,bgColor:String,grow:Boolean,height:{type:[Number,String],default:void 0},hideSlider:Boolean,sliderColor:String,...ve({mandatory:"force"}),...Pe(),...q()},emits:{"update:modelValue":e=>!0},setup(e,o){let{slots:t}=o;const n=Re(e,"modelValue"),r=y(()=>Qe(e.items)),{densityClasses:s}=Ve(e),{backgroundColorClasses:a,backgroundColorStyles:l}=_e(I(e,"bgColor"));return Ie({VTab:{color:I(e,"color"),direction:I(e,"direction"),stacked:I(e,"stacked"),fixed:I(e,"fixedTabs"),sliderColor:I(e,"sliderColor"),hideSlider:I(e,"hideSlider")}}),A(()=>{const[u]=ae.filterProps(e);return m(ae,D(u,{modelValue:n.value,"onUpdate:modelValue":c=>n.value=c,class:["v-tabs",`v-tabs--${e.direction}`,`v-tabs--align-tabs-${e.alignTabs}`,{"v-tabs--fixed-tabs":e.fixedTabs,"v-tabs--grow":e.grow,"v-tabs--stacked":e.stacked},s.value,a.value,e.class],style:[{"--v-tabs-height":j(e.height)},l.value,e.style],role:"tablist",symbol:de}),{default:()=>[t.default?t.default():r.value.map(c=>m(Ne,D(c,{key:c.title}),null))]})}),{}}});const Je=e=>{const{touchstartX:o,touchendX:t,touchstartY:n,touchendY:r}=e,s=.5,a=16;e.offsetX=t-o,e.offsetY=r-n,Math.abs(e.offsetY)<s*Math.abs(e.offsetX)&&(e.left&&t<o-a&&e.left(e),e.right&&t>o+a&&e.right(e)),Math.abs(e.offsetX)<s*Math.abs(e.offsetY)&&(e.up&&r<n-a&&e.up(e),e.down&&r>n+a&&e.down(e))};function Ze(e,o){var n;const t=e.changedTouches[0];o.touchstartX=t.clientX,o.touchstartY=t.clientY,(n=o.start)==null||n.call(o,{originalEvent:e,...o})}function pe(e,o){var n;const t=e.changedTouches[0];o.touchendX=t.clientX,o.touchendY=t.clientY,(n=o.end)==null||n.call(o,{originalEvent:e,...o}),Je(o)}function et(e,o){var n;const t=e.changedTouches[0];o.touchmoveX=t.clientX,o.touchmoveY=t.clientY,(n=o.move)==null||n.call(o,{originalEvent:e,...o})}function tt(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const o={touchstartX:0,touchstartY:0,touchendX:0,touchendY:0,touchmoveX:0,touchmoveY:0,offsetX:0,offsetY:0,left:e.left,right:e.right,up:e.up,down:e.down,start:e.start,move:e.move,end:e.end};return{touchstart:t=>Ze(t,o),touchend:t=>pe(t,o),touchmove:t=>et(t,o)}}function nt(e,o){var l;const t=o.value,n=t!=null&&t.parent?e.parentElement:e,r=(t==null?void 0:t.options)??{passive:!0},s=(l=o.instance)==null?void 0:l.$.uid;if(!n||!s)return;const a=tt(o.value);n._touchHandlers=n._touchHandlers??Object.create(null),n._touchHandlers[s]=a,ce(a).forEach(u=>{n.addEventListener(u,a[u],r)})}function ot(e,o){var s,a;const t=(s=o.value)!=null&&s.parent?e.parentElement:e,n=(a=o.instance)==null?void 0:a.$.uid;if(!(t!=null&&t._touchHandlers)||!n)return;const r=t._touchHandlers[n];ce(r).forEach(l=>{t.removeEventListener(l,r[l])}),delete t._touchHandlers[n]}const fe={mounted:nt,unmounted:ot},at=fe,he=Symbol.for("vuetify:v-window"),me=Symbol.for("vuetify:v-window-group"),lt=le({continuous:Boolean,nextIcon:{type:[Boolean,String,Function,Object],default:"$next"},prevIcon:{type:[Boolean,String,Function,Object],default:"$prev"},reverse:Boolean,showArrows:{type:[Boolean,String],validator:e=>typeof e=="boolean"||e==="hover"},touch:{type:[Object,Boolean],default:void 0},direction:{type:String,default:"horizontal"},modelValue:null,disabled:Boolean,selectedClass:{type:String,default:"v-window-item--active"},mandatory:{default:"force"},...U(),...q(),...$e()},"v-window"),ut=O()({name:"VWindow",directives:{Touch:fe},props:lt(),emits:{"update:modelValue":e=>!0},setup(e,o){let{slots:t}=o;const{themeClasses:n}=Me(e),{isRtl:r}=se(),{t:s}=He(),a=ie(e,me),l=T(),u=y(()=>r.value?!e.reverse:e.reverse),c=T(!1),d=y(()=>{const f=e.direction==="vertical"?"y":"x",C=(u.value?!c.value:c.value)?"-reverse":"";return`v-window-${f}${C}-transition`}),x=T(0),S=T(void 0),h=y(()=>a.items.value.findIndex(f=>a.selected.value.includes(f.id)));ue(h,(f,b)=>{const C=a.items.value.length,B=C-1;C<=2?c.value=f<b:f===B&&b===0?c.value=!0:f===0&&b===B?c.value=!1:c.value=f<b}),Oe(he,{transition:d,isReversed:c,transitionCount:x,transitionHeight:S,rootRef:l});const g=y(()=>e.continuous||h.value!==0),P=y(()=>e.continuous||h.value!==a.items.value.length-1);function V(){g.value&&a.prev()}function R(){P.value&&a.next()}const w=y(()=>{const f=[],b={icon:r.value?e.nextIcon:e.prevIcon,class:`v-window__${u.value?"right":"left"}`,onClick:a.prev,ariaLabel:s("$vuetify.carousel.prev")};f.push(g.value?t.prev?t.prev({props:b}):m(Y,b,null):m("div",null,null));const C={icon:r.value?e.prevIcon:e.nextIcon,class:`v-window__${u.value?"left":"right"}`,onClick:a.next,ariaLabel:s("$vuetify.carousel.next")};return f.push(P.value?t.next?t.next({props:C}):m(Y,C,null):m("div",null,null)),f}),z=y(()=>e.touch===!1?e.touch:{...{left:()=>{u.value?V():R()},right:()=>{u.value?R():V()},start:b=>{let{originalEvent:C}=b;C.stopPropagation()}},...e.touch===!0?{}:e.touch});return A(()=>re(m(e.tag,{ref:l,class:["v-window",{"v-window--show-arrows-on-hover":e.showArrows==="hover"},n.value,e.class],style:e.style},{default:()=>{var f,b;return[m("div",{class:"v-window__container",style:{height:S.value}},[(f=t.default)==null?void 0:f.call(t,{group:a}),e.showArrows!==!1&&m("div",{class:"v-window__controls"},[w.value])]),(b=t.additional)==null?void 0:b.call(t,{group:a})]}}),[[Ae("touch"),z.value]])),{group:a}}}),ct=O()({name:"VWindowItem",directives:{Touch:at},props:{reverseTransition:{type:[Boolean,String],default:void 0},transition:{type:[Boolean,String],default:void 0},...U(),...Xe(),...Ye()},emits:{"group:selected":e=>!0},setup(e,o){let{slots:t}=o;const n=Le(he),r=We(e,me),{isBooted:s}=Fe();if(!n||!r)throw new Error("[Vuetify] VWindowItem must be used inside VWindow");const a=T(!1),l=y(()=>n.isReversed.value?e.reverseTransition!==!1:e.transition!==!1);function u(){!a.value||!n||(a.value=!1,n.transitionCount.value>0&&(n.transitionCount.value-=1,n.transitionCount.value===0&&(n.transitionHeight.value=void 0)))}function c(){var g;a.value||!n||(a.value=!0,n.transitionCount.value===0&&(n.transitionHeight.value=j((g=n.rootRef.value)==null?void 0:g.clientHeight)),n.transitionCount.value+=1)}function d(){u()}function x(g){a.value&&Ue(()=>{!l.value||!a.value||!n||(n.transitionHeight.value=j(g.clientHeight))})}const S=y(()=>{const g=n.isReversed.value?e.reverseTransition:e.transition;return l.value?{name:typeof g!="string"?n.transition.value:g,onBeforeEnter:c,onAfterEnter:u,onEnterCancelled:d,onBeforeLeave:c,onAfterLeave:u,onLeaveCancelled:d,onEnter:x}:!1}),{hasContent:h}=Ge(e,r.isSelected);return A(()=>m(je,{transition:S.value,disabled:!s.value},{default:()=>{var g;return[re(m("div",{class:["v-window-item",r.selectedClass.value,e.class],style:e.style},[h.value&&((g=t.default)==null?void 0:g.call(t))]),[[De,r.isSelected.value]])]}})),{}}});export{it as V,Ne as a,ut as b,ct as c,Ke as d,ae as e};
