import{I as _,by as Z,K as G,b9 as H,O as R,P as z,bz as T,R as F,ba as U,a6 as Y,W as r,a7 as ee,q as l,bc as ae,aQ as h,bA as D,J as le,bo as te,L as ne,bi as se,N as ie,bB as ce,bq as de,aT as oe,S as ue,bC as re,bp as ve,X as fe,Z as be,br as ke,a0 as me,bj as pe,bD as ye,B as C,aC as I,be as x,bE as he,bF as Ce,ag as v,bs as f,aD as Ie,F as E,aE as L,a8 as Ve}from"./index-9a5dc664.js";const w=Symbol.for("vuetify:v-chip-group");_()({name:"VChipGroup",props:{column:Boolean,filter:Boolean,valueComparator:{type:Function,default:Z},...G(),...H({selectedClass:"v-chip--selected"}),...R(),...z(),...T({variant:"tonal"})},emits:{"update:modelValue":e=>!0},setup(e,b){let{slots:c}=b;const{themeClasses:o}=F(e),{isSelected:t,select:k,next:m,prev:p,selected:y}=U(e,w);return Y({VChip:{color:r(e,"color"),disabled:r(e,"disabled"),filter:r(e,"filter"),variant:r(e,"variant")}}),ee(()=>l(e.tag,{class:["v-chip-group",{"v-chip-group--column":e.column},o.value,e.class],style:e.style},{default:()=>{var u;return[(u=c.default)==null?void 0:u.call(c,{isSelected:t,select:k,next:m,prev:p,selected:y.value})]}})),{}}});const Pe=_()({name:"VChip",directives:{Ripple:ae},props:{activeClass:String,appendAvatar:String,appendIcon:h,closable:Boolean,closeIcon:{type:h,default:"$delete"},closeLabel:{type:String,default:"$vuetify.close"},draggable:Boolean,filter:Boolean,filterIcon:{type:String,default:"$complete"},label:Boolean,link:{type:Boolean,default:void 0},pill:Boolean,prependAvatar:String,prependIcon:h,ripple:{type:Boolean,default:!0},text:String,modelValue:{type:Boolean,default:!0},onClick:D(),onClickOnce:D(),...le(),...G(),...te(),...ne(),...se(),...ie(),...ce(),...de(),...R({tag:"span"}),...z(),...T({variant:"tonal"})},emits:{"click:close":e=>!0,"update:modelValue":e=>!0,"group:selected":e=>!0,click:e=>!0},setup(e,b){let{attrs:c,emit:o,slots:t}=b;const{t:k}=oe(),{borderClasses:m}=ue(e),{colorClasses:p,colorStyles:y,variantClasses:u}=re(e),{densityClasses:$}=ve(e),{elevationClasses:q}=fe(e),{roundedClasses:K}=be(e),{sizeClasses:M}=ke(e),{themeClasses:O}=F(e),V=me(e,"modelValue"),a=pe(e,w,!1),s=ye(e,c),N=C(()=>e.link!==!1&&s.isLink.value),i=C(()=>!e.disabled&&e.link!==!1&&(!!a||e.link||s.isClickable.value)),X=C(()=>({"aria-label":k(e.closeLabel),onClick(n){V.value=!1,o("click:close",n)}}));function g(n){var d;o("click",n),i.value&&((d=s.navigate)==null||d.call(s,n),a==null||a.toggle())}function j(n){(n.key==="Enter"||n.key===" ")&&(n.preventDefault(),g(n))}return()=>{const n=s.isLink.value?"a":e.tag,d=!!(e.appendIcon||e.appendAvatar),J=!!(d||t.append),Q=!!(t.close||e.closable),P=!!(t.filter||e.filter)&&a,S=!!(e.prependIcon||e.prependAvatar),W=!!(S||t.prepend),A=!a||a.isSelected.value;return V.value&&I(l(n,{class:["v-chip",{"v-chip--disabled":e.disabled,"v-chip--label":e.label,"v-chip--link":i.value,"v-chip--filter":P,"v-chip--pill":e.pill},O.value,m.value,A?p.value:void 0,$.value,q.value,K.value,M.value,u.value,a==null?void 0:a.selectedClass.value,e.class],style:[A?y.value:void 0,e.style],disabled:e.disabled||void 0,draggable:e.draggable,href:s.href.value,tabindex:i.value?0:void 0,onClick:g,onKeydown:i.value&&!N.value&&j},{default:()=>{var B;return[he(i.value,"v-chip"),P&&l(Ce,{key:"filter"},{default:()=>[I(l("div",{class:"v-chip__filter"},[t.filter?I(l(f,{key:"filter-defaults",disabled:!e.filterIcon,defaults:{VIcon:{icon:e.filterIcon}}},null),[[x("slot"),t.filter,"default"]]):l(v,{key:"filter-icon",icon:e.filterIcon},null)]),[[Ie,a.isSelected.value]])]}),W&&l("div",{key:"prepend",class:"v-chip__prepend"},[t.prepend?l(f,{key:"prepend-defaults",disabled:!S,defaults:{VAvatar:{image:e.prependAvatar,start:!0},VIcon:{icon:e.prependIcon,start:!0}}},t.prepend):l(E,null,[e.prependIcon&&l(v,{key:"prepend-icon",icon:e.prependIcon,start:!0},null),e.prependAvatar&&l(L,{key:"prepend-avatar",image:e.prependAvatar,start:!0},null)])]),((B=t.default)==null?void 0:B.call(t,{isSelected:a==null?void 0:a.isSelected.value,selectedClass:a==null?void 0:a.selectedClass.value,select:a==null?void 0:a.select,toggle:a==null?void 0:a.toggle,value:a==null?void 0:a.value.value,disabled:e.disabled}))??e.text,J&&l("div",{key:"append",class:"v-chip__append"},[t.append?l(f,{key:"append-defaults",disabled:!d,defaults:{VAvatar:{end:!0,image:e.appendAvatar},VIcon:{end:!0,icon:e.appendIcon}}},t.append):l(E,null,[e.appendIcon&&l(v,{key:"append-icon",end:!0,icon:e.appendIcon},null),e.appendAvatar&&l(L,{key:"append-avatar",end:!0,image:e.appendAvatar},null)])]),Q&&l("div",Ve({key:"close",class:"v-chip__close"},X.value),[t.close?l(f,{key:"close-defaults",defaults:{VIcon:{icon:e.closeIcon,size:"x-small"}}},t.close):l(v,{key:"close-icon",icon:e.closeIcon,size:"x-small"},null)])]}}),[[x("ripple"),i.value&&e.ripple,null]])}}});export{Pe as V};
