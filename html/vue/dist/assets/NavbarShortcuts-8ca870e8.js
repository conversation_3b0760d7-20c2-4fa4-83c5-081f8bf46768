import{k as f,r as h,o as n,b as l,w as e,q as t,ag as o,ar as g,as as y,at as x,au as V,aj as v,z as C,s as d,ai as S,al as A,c as k,F as I,a as w,am as B,t as z,aE as D,n as p,y as _}from"./index-9a5dc664.js";const N={class:"text-base font-weight-medium mt-2 mb-0"},q={class:"text-sm"},F={__name:"Shortcuts",props:{togglerIcon:{type:String,required:!1,default:"tabler-layout-grid-add"},shortcuts:{type:Array,required:!0}},setup(r){const s=r,c=f();return(m,i)=>{const u=h("IconBtn");return n(),l(u,null,{default:e(()=>[t(o,{size:"26",icon:s.togglerIcon},null,8,["icon"]),t(g,{activator:"parent",offset:"14px",location:"bottom end"},{default:e(()=>[t(y,{width:"340","max-height":"560",class:"d-flex flex-column"},{default:e(()=>[t(x,{class:"py-4"},{append:e(()=>[t(u,null,{default:e(()=>[t(o,{icon:"tabler-layout-grid-add"})]),_:1})]),default:e(()=>[t(V,null,{default:e(()=>[v("Shortcuts")]),_:1})]),_:1}),t(C),t(d(S),{options:{wheelPropagation:!1}},{default:e(()=>[t(A,{class:"ma-0 mt-n1"},{default:e(()=>[(n(!0),k(I,null,w(s.shortcuts,(a,b)=>(n(),l(B,{key:a.title,cols:"6",class:z(["text-center border-t cursor-pointer pa-4 shortcut-icon",(b+1)%2?"border-e":""]),onClick:M=>d(c).push(a.to)},{default:e(()=>[t(D,{variant:"tonal",size:"48"},{default:e(()=>[t(o,{icon:a.icon},null,8,["icon"])]),_:2},1024),p("h6",N,_(a.title),1),p("span",q,_(a.subtitle),1)]),_:2},1032,["class","onClick"]))),128))]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})}}},E={__name:"NavbarShortcuts",setup(r){const s=[{icon:"tabler-calendar",title:"Calendar",subtitle:"Appointments",to:{name:"apps-calendar"}},{icon:"tabler-file",title:"Invoice App",subtitle:"Manage Accounts",to:{name:"apps-invoice-list"}},{icon:"tabler-user",title:"Users",subtitle:"Manage Users",to:{name:"apps-user-list"}},{icon:"tabler-layout",title:"Dashboard",subtitle:"Dashboard Analytics",to:{name:"dashboards-analytics"}},{icon:"tabler-settings",title:"Settings",subtitle:"Account Settings",to:{name:"pages-account-settings-tab",params:{tab:"account"}}},{icon:"tabler-help",title:"Help Center",subtitle:"FAQs & Articles",to:{name:"pages-help-center"}}];return(c,m)=>{const i=F;return n(),l(i,{shortcuts:s})}}};export{E as default};
