import{aJ as j,bw as E,l as g,cv as f,E as I,aK as P,r as O,o as r,b as m,w as t,q as a,as as J,av as C,s as l,ak as X,cp as B,n as i,ag as u,z as G,ai as H,aC as D,az as z,c as d,F as v,a as x,cw as W,aj as K,y as b,p as A,aA as L,aB as Y,aD as $,al as Z,am as ee,A as R,aN as se,aO as te}from"./index-9a5dc664.js";import{e as ae}from"./VTextField-3e2d458d.js";import{V as le}from"./VDialog-0870f7b8.js";const Q=y=>(se("data-v-c4522287"),y=y(),te(),y),re={class:"d-flex align-center text-high-emphasis me-1"},ie={class:"d-flex align-center"},oe={class:"h-100"},ne={class:"text-xs text-disabled text-uppercase"},ce={class:"h-100"},ue={class:"app-bar-search-suggestions d-flex flex-column align-center justify-center text-high-emphasis h-100"},de={class:"d-flex align-center flex-wrap justify-center gap-2 text-h6 my-3"},pe=Q(()=>i("span",null,"No Result For ",-1)),he={key:0,class:"mt-8"},fe=Q(()=>i("span",{class:"d-flex justify-center text-disabled"},"Try searching for",-1)),ye=["onClick"],_e={class:"text-sm"},ge={__name:"AppBarSearch",props:{isDialogVisible:{type:Boolean,required:!0},searchQuery:{type:String,required:!0},searchResults:{type:Array,required:!0},suggestions:{type:Array,required:!1},noDataSuggestion:{type:Array,required:!1}},emits:["update:isDialogVisible","update:searchQuery","itemSelected"],setup(y,{emit:_}){const o=y,{ctrl_k:F,meta_k:T}=E({passive:!1,onEventFired(e){e.ctrlKey&&e.key==="k"&&e.type==="keydown"&&e.preventDefault()}}),S=g(),n=g(structuredClone(f(o.searchQuery))),q=g(),w=g(structuredClone(f(o.isDialogVisible))),p=g(structuredClone(f(o.searchResults)));I(o,()=>{w.value=structuredClone(f(o.isDialogVisible)),p.value=structuredClone(f(o.searchResults)),n.value=structuredClone(f(o.searchQuery))}),I([F,T],()=>{w.value=!0,_("update:isDialogVisible",!0)});const k=()=>{_("update:isDialogVisible",!1),_("update:searchQuery","")};P(()=>{n.value.length||(p.value=[])});const N=e=>{var c,V;e.key==="ArrowDown"?(e.preventDefault(),(c=S.value)==null||c.focus("next")):e.key==="ArrowUp"&&(e.preventDefault(),(V=S.value)==null||V.focus("prev"))},U=e=>{_("update:isDialogVisible",e),_("update:searchQuery","")},M=e=>e==="dashboards"?"Dashboards":e==="appsPages"?"Apps & Pages":e==="userInterface"?"User Interface":e==="formsTables"?"Forms Tables":e==="chartsMisc"?"Charts Misc":"Misc";return(e,c)=>{const V=O("IconBtn");return r(),m(le,{"max-width":"600","model-value":l(w),height:e.$vuetify.display.smAndUp?"550":"100%",fullscreen:e.$vuetify.display.width<600,class:"app-bar-search-dialog","onUpdate:modelValue":U,onKeyup:B(k,["esc"])},{default:t(()=>[a(J,{height:"100%",width:"100%",class:"position-relative"},{default:t(()=>[a(C,{class:"pt-1",style:{"min-block-size":"65px"}},{default:t(()=>[a(ae,{ref_key:"refSearchInput",ref:q,modelValue:l(n),"onUpdate:modelValue":[c[0]||(c[0]=s=>X(n)?n.value=s:null),c[1]||(c[1]=s=>e.$emit("update:searchQuery",l(n)))],autofocus:"",density:"comfortable",variant:"plain",class:"app-bar-autocomplete-box",onKeyup:B(k,["esc"]),onKeydown:N},{"prepend-inner":t(()=>[i("div",re,[a(u,{size:"22",icon:"tabler-search",class:"mt-1",style:{opacity:"1"}})])]),"append-inner":t(()=>[i("div",ie,[i("div",{class:"text-base text-disabled cursor-pointer me-1",onClick:k}," [esc] "),a(V,{size:"small",onClick:k},{default:t(()=>[a(u,{icon:"tabler-x"})]),_:1})])]),_:1},8,["modelValue","onKeyup"])]),_:1}),a(G),a(l(H),{options:{wheelPropagation:!1,suppressScrollX:!0},class:"h-100"},{default:t(()=>[D(a(l(z),{ref_key:"refSearchList",ref:S,density:"compact",class:"app-bar-search-list"},{default:t(()=>[(r(!0),d(v,null,x(l(p),s=>(r(),d(v,{key:s.title},["header"in s?(r(),m(l(W),{key:0,class:"text-disabled"},{default:t(()=>[K(b(M(s.title)),1)]),_:2},1024)):A(e.$slots,"searchResult",{key:1,item:s},()=>[a(l(L),{link:"",onClick:h=>e.$emit("itemSelected",s)},{prepend:t(()=>[a(u,{size:"20",icon:s.icon,class:"me-3"},null,8,["icon"])]),append:t(()=>[a(u,{size:"20",icon:"tabler-corner-down-left",class:"enter-icon text-disabled"})]),default:t(()=>[a(Y,null,{default:t(()=>[K(b(s.title),1)]),_:2},1024)]),_:2},1032,["onClick"])],!0)],64))),128))]),_:3},512),[[$,l(n).length&&!!l(p).length]]),D(i("div",oe,[A(e.$slots,"suggestions",{},()=>[a(C,{class:"app-bar-search-suggestions h-100 pa-10"},{default:t(()=>[o.suggestions?(r(),m(Z,{key:0,class:"gap-y-4"},{default:t(()=>[(r(!0),d(v,null,x(o.suggestions,s=>(r(),m(ee,{key:s.title,cols:"12",sm:"6",class:"ps-6"},{default:t(()=>[i("p",ne,b(s.title),1),a(l(z),{class:"card-list"},{default:t(()=>[(r(!0),d(v,null,x(s.content,h=>(r(),m(l(L),{key:h.title,link:"",title:h.title,class:"app-bar-search-suggestion",onClick:me=>e.$emit("itemSelected",h)},{prepend:t(()=>[a(u,{icon:h.icon,size:"20",class:"me-2"},null,8,["icon"])]),_:2},1032,["title","onClick"]))),128))]),_:2},1024)]),_:2},1024))),128))]),_:1})):R("",!0)]),_:1})],!0)],512),[[$,!!l(p)&&!l(n)]]),D(i("div",ce,[A(e.$slots,"noData",{},()=>[a(C,{class:"h-100"},{default:t(()=>[i("div",ue,[a(u,{size:"75",icon:"tabler-file-x"}),i("div",de,[pe,i("span",null,'"'+b(l(n))+'"',1)]),o.noDataSuggestion?(r(),d("div",he,[fe,(r(!0),d(v,null,x(o.noDataSuggestion,s=>(r(),d("h6",{key:s.title,class:"app-bar-search-suggestion text-sm font-weight-regular cursor-pointer mt-3",onClick:h=>e.$emit("itemSelected",s)},[a(u,{size:"20",icon:s.icon,class:"me-3"},null,8,["icon"]),i("span",_e,b(s.title),1)],8,ye))),128))])):R("",!0)])]),_:1})],!0)],512),[[$,!l(p).length&&l(n).length]])]),_:3})]),_:3})]),_:3},8,["model-value","height","fullscreen","onKeyup"])}}},Ve=j(ge,[["__scopeId","data-v-c4522287"]]);export{Ve as default};
