import{_ as y}from"./AppTextField-8c148b8f.js";import{aJ as g,a9 as x,l as o,E as b,o as r,b as w,w as a,q as e,am as d,as as A,av as i,al as u,s as I,ak as $,az as q,c as F,F as B,a as L,aA as R,ag as S,ah as p,aj as m,y as _,aG as D}from"./index-9a5dc664.js";import{V as T}from"./VForm-c6ce9b98.js";import"./VTextField-3e2d458d.js";const z={__name:"domain",setup(E){const{t,locale:f}=x();o(!1);const V=o(t("Inactivated")),n=o("");b(f,()=>{V.value=t("Inactivated")});const v=[t("Application certificate requirements 1"),t("Application certificate requirements 2"),t("Application certificate requirements 3")],C=()=>{},h=()=>{};return(l,c)=>{const k=y;return r(),w(u,null,{default:a(()=>[e(d,{cols:"12"},{default:a(()=>[e(A,{title:l.$t("Apply for an SSL certificate")},{default:a(()=>[e(T,null,{default:a(()=>[e(i,{class:"pt-0"},{default:a(()=>[e(u,null,{default:a(()=>[e(d,{cols:"12",md:"6"},{default:a(()=>[e(k,{modelValue:I(n),"onUpdate:modelValue":c[0]||(c[0]=s=>$(n)?n.value=s:null),label:l.$t("Domain"),placeholder:l.$t("Please enter domain name")},null,8,["modelValue","label","placeholder"])]),_:1})]),_:1})]),_:1}),e(i,null,{default:a(()=>[e(q,{class:"card-list"},{default:a(()=>[(r(),F(B,null,L(v,s=>e(R,{key:s,title:s,class:"text-medium-emphasis"},{prepend:a(()=>[e(S,{size:"8",icon:"tabler-circle",class:"me-3"})]),_:2},1032,["title"])),64))]),_:1})]),_:1}),e(i,{class:"d-flex flex-wrap gap-4"},{default:a(()=>[e(p,{onClick:C},{default:a(()=>[m(_(l.$t("Save changes")),1)]),_:1}),e(p,{color:"secondary",variant:"tonal",type:"reset",onClick:D(h,["prevent"])},{default:a(()=>[m(_(l.$t("Reset")),1)]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1},8,["title"])]),_:1})]),_:1})}}},M=g(z,[["__scopeId","data-v-dd6c9bec"]]);export{M as default};
