import{m as U,V as c}from"./VSelectionControl-182ca2ca.js";import{m as q,u as z,a as v}from"./VTextField-3e2d458d.js";import{I as M,a0 as m,bY as N,B as f,a_ as Y,a7 as j,a$ as E,l as G,q as t,a8 as h,b$ as H,c7 as J}from"./index-9a5dc664.js";const X=M()({name:"VSwitch",inheritAttrs:!1,props:{indeterminate:Boolean,inset:Boolean,flat:Boolean,loading:{type:[<PERSON>olean,String],default:!1},...q(),...U()},emits:{"update:focused":e=>!0,"update:modelValue":()=>!0,"update:indeterminate":e=>!0},setup(e,V){let{attrs:g,slots:a}=V;const l=m(e,"indeterminate"),r=m(e,"modelValue"),{loaderClasses:C}=N(e),{isFocused:w,focus:y,blur:b}=z(e),k=f(()=>typeof e.loading=="string"&&e.loading!==""?e.loading:e.color),P=Y(),S=f(()=>e.id||`switch-${P}`);function _(){l.value&&(l.value=!1)}return j(()=>{const[x,B]=E(g),[A,K]=v.filterProps(e),[I,O]=c.filterProps(e),d=G();function $(o){var s,n;o.stopPropagation(),o.preventDefault(),(n=(s=d.value)==null?void 0:s.input)==null||n.click()}return t(v,h({class:["v-switch",{"v-switch--inset":e.inset},{"v-switch--indeterminate":l.value},C.value,e.class],style:e.style},x,A,{id:S.value,focused:w.value}),{...a,default:o=>{let{id:s,messagesId:n,isDisabled:p,isReadonly:F,isValid:D}=o;return t(c,h({ref:d},I,{modelValue:r.value,"onUpdate:modelValue":[i=>r.value=i,_],id:s.value,"aria-describedby":n.value,type:"checkbox","aria-checked":l.value?"mixed":void 0,disabled:p.value,readonly:F.value,onFocus:y,onBlur:b},B),{...a,default:()=>t("div",{class:"v-switch__track",onClick:$},null),input:i=>{let{textColorClasses:L,textColorStyles:R}=i;return t("div",{class:["v-switch__thumb",L.value],style:R.value},[e.loading&&t(H,{name:"v-switch",active:!0,color:D.value===!1?void 0:k.value},{default:u=>a.loader?a.loader(u):t(J,{active:u.isActive,color:u.color,indeterminate:!0,size:"16",width:"2"},null)})])}})}})}),{}}});export{X as V};
