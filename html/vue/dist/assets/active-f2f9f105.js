import{l as V,r as v,o as b,c as y,n as o,q as e,s as a,h as d,V as l,w as t,at as x,x as u,au as g,aj as c,y as k,av as p,aG as C,al as w,am as i,ah as T,ag as B,as as S}from"./index-9a5dc664.js";import{_ as j}from"./AppTextField-8c148b8f.js";import{a as z,b as L}from"./auth-v1-top-shape-c5f58476.js";import{b as m}from"./route-block-83d24a4e.js";import{V as N}from"./VForm-c6ce9b98.js";import"./VTextField-3e2d458d.js";const R={class:"auth-wrapper d-flex align-center justify-center pa-4"},A={class:"position-relative my-sm-16"},M={class:"d-flex"},E=o("h5",{class:"text-h5 mb-1"}," Authorization 🚀 ",-1),F=o("p",{class:"mb-0"}," Enter your authorization code and the adventure begins here ",-1),H=o("span",null,"Back to login",-1),I={__name:"active",setup(q){const r=V({email:""});return(s,n)=>{const f=j,_=v("RouterLink");return b(),y("div",R,[o("div",A,[e(a(l),{nodes:("h"in s?s.h:a(d))("div",{innerHTML:a(z)}),class:"text-primary auth-v1-top-shape d-none d-sm-block"},null,8,["nodes"]),e(a(l),{nodes:("h"in s?s.h:a(d))("div",{innerHTML:a(L)}),class:"text-primary auth-v1-bottom-shape d-none d-sm-block"},null,8,["nodes"]),e(S,{class:"auth-card pa-4","max-width":"448"},{default:t(()=>[e(x,{class:"justify-center"},{prepend:t(()=>[o("div",M,[e(a(l),{nodes:a(u).app.logo},null,8,["nodes"])])]),default:t(()=>[e(g,{class:"font-weight-bold text-capitalize text-h5 py-1"},{default:t(()=>[c(k(a(u).app.title),1)]),_:1})]),_:1}),e(p,{class:"pt-2"},{default:t(()=>[E,F]),_:1}),e(p,null,{default:t(()=>[e(N,{onSubmit:n[1]||(n[1]=C(()=>{},["prevent"]))},{default:t(()=>[e(w,null,{default:t(()=>[e(i,{cols:"12"},{default:t(()=>[e(f,{modelValue:a(r).email,"onUpdate:modelValue":n[0]||(n[0]=h=>a(r).email=h),autofocus:"",label:"Authorization code",type:"text"},null,8,["modelValue"])]),_:1}),e(i,{cols:"12"},{default:t(()=>[e(T,{block:"",type:"submit"},{default:t(()=>[c(" Submit ")]),_:1})]),_:1}),e(i,{cols:"12"},{default:t(()=>[e(_,{class:"d-flex align-center justify-center",to:{name:"login"}},{default:t(()=>[e(B,{icon:"tabler-chevron-left",class:"flip-in-rtl"}),H]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})])])}}};typeof m=="function"&&m(I);export{I as default};
