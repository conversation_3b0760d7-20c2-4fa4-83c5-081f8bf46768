import{_ as ce}from"./DialogCloseBtn-e6f97e88.js";import{_ as pe}from"./AppTextField-8c148b8f.js";import{_ as me}from"./AppDateTimePicker-6623c419.js";import{p as fe,V as ve,_ as ge}from"./VPagination-964310e8.js";import{aJ as _e,ad as ye,l as n,a9 as xe,E as F,D as Ve,r as be,o as J,c as Q,q as e,w as a,am as x,as as A,av as D,al as U,s as o,ak as V,ah as v,aj as c,y as d,z as h,n as g,A as Pe,ag as ke,a8 as G,ao as De,ap as H}from"./index-9a5dc664.js";import{V as $e}from"./VSpacer-d7832670.js";import{V as we}from"./VDataTableServer-26da2b3a.js";import{V as Ce}from"./VChip-a30ee730.js";import{V as K}from"./VDialog-0870f7b8.js";import"./VTextField-3e2d458d.js";import"./VDataTable-d690b508.js";import"./VSelectionControl-182ca2ca.js";const Le={class:"me-3 d-flex gap-3"},Se={key:0,class:"app-user-search-filter d-flex align-center flex-wrap gap-4",style:{"justify-content":"flex-end"}},Ae={class:"text-wrap"},Ue={class:"d-block text-wrap user-list-name"},he={class:"text-wrap mt-2 mb-2"},Oe={class:"d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3"},Ee={class:"text-sm text-disabled mb-0"},Te={class:""},Ie={__name:"index",setup(Re){const L=ye().permissions,$=n(""),W=n("all");n("");const X=n(1),w=n(0),O=n([]),S=n("primary");n([]);const b=n(""),f=n(!1),P=n(""),k=n("warning"),m=n(!1),C=n(!1),u=n({page:1,itemsPerPage:10}),p=n(""),{t:s,locale:Y}=xe(),E=n(s("Path")),T=n(s("Action")),Z=n(s("Method")),I=n(s("Params")),R=n(s("Operator")),ee=n(s("Status")),B=n(s("created_at")),te=n(s("updated_at")),M=n(s("Actions")),N=n(s("No Data Text"));F(Y,()=>{E.value=s("Path"),T.value=s("Action"),Z.value=s("Method"),I.value=s("Params"),R.value=s("Operator"),ee.value=s("Status"),B.value=s("created_at"),te.value=s("updated_at"),M.value=s("Actions"),N.value=s("No Data Text")});const ae=[{title:"ID",key:"id",align:"center",sortable:!1},{title:E,key:"action",sortable:!1},{title:I,key:"params",sortable:!1},{title:T,key:"path",sortable:!1},{title:R,key:"admin_user_one.name",sortable:!1},{title:"IP",key:"ip",sortable:!1},{title:"User-Agent",key:"ua",sortable:!1},{title:B,key:"created_at",sortable:!1},{title:M,key:"actions",align:"center",sortable:!1}],le=[{title:s("All"),value:""},{title:s("Log orderExport"),value:"orderExport"},{title:s("Log list"),value:"listData"},{title:s("Log doEmpty"),value:"doEmpty"},{title:s("Log statistics"),value:"statistics"},{title:s("Log postData"),value:"postData"}],_=()=>{S.value="primary";let i="",l="";p.value&&p.value.indexOf("to")!=-1&&(i=p.value.split("to")[0].trim()),p.value&&p.value.indexOf("to")!=-1&&(l=p.value.split("to")[1].trim()),L.includes(6)&&H.get("/api/operationLog/index",{params:{action:$.value,createdName:b.value,page:u.value.page,pagesize:u.value.itemsPerPage,startTime:i,endTime:l}}).then(r=>{r.data.code===200&&(O.value=r.data.data.data,X.value=r.data.data.last_page,w.value=r.data.data.total,u.value.page=r.data.data.current_page)}).catch(r=>{P.value=s("Request failed"),k.value="error",f.value=!0}).finally(()=>{S.value=!1})};Ve(()=>{_()});const oe=()=>{_()},se=()=>{u.value.page=1,u.value.itemsPerPage=10,b.value="",p.value="",W.value="all",_()},y=n(!1),j=n(""),ne=i=>{y.value=!y.value,j.value=i};F(()=>u.value.itemsPerPage,(i,l)=>{u.value.page=1,_()});const q=i=>{const r=i.split("/").pop();return r=="index"?{color:" secondary",text:"Log index"}:r=="postData"?{color:"success",text:"Log postData"}:r=="referer"?{color:" secondary",text:"Log referer"}:r=="listData"?{color:"primary",text:"Log list"}:r=="orderExport"?{color:"warning",text:"Log orderExport"}:r=="loginOut"?{color:"secondary",text:"Log loginOut"}:r=="doEmpty"?{color:"warning",text:"Log doEmpty"}:r=="getRoleList"?{color:" secondary",text:"Log getRoleList"}:r=="statistics"?{color:" secondary",text:"Log statistics"}:{color:" secondary",text:"Else"}},ie=()=>{C.value=!0,L.includes(7)&&H.post("/api/operationLog/doEmpty",{ids:""}).then(i=>{i.data.code===200?(P.value=s("Operation successful"),k.value="success",f.value=!0,_()):(P.value=s("Operation failed"),k.value="error",f.value=!0)}).catch(i=>{P.value=s("Request failed"),k.value="error",f.value=!0}).finally(()=>{C.value=!1,m.value=!1})};return(i,l)=>{const r=ge,re=me,ue=pe,de=be("IconBtn"),z=ce;return J(),Q("section",null,[e(U,null,{default:a(()=>[e(x,{cols:"12"},{default:a(()=>[e(A,{title:i.$t("Search Filter")},{default:a(()=>[e(D,null,{default:a(()=>[e(U,null,{default:a(()=>[e(x,{cols:"12",sm:"2"},{default:a(()=>[e(r,{modelValue:o($),"onUpdate:modelValue":l[0]||(l[0]=t=>V($)?$.value=t:null),label:i.$t("Action"),items:le,"clear-icon":"tabler-x"},null,8,["modelValue","label"])]),_:1}),e(x,{cols:"12",sm:"2"},{default:a(()=>[e(re,{modelValue:o(p),"onUpdate:modelValue":l[1]||(l[1]=t=>V(p)?p.value=t:null),label:i.$t("Select Date"),config:{mode:"range"},clearable:"","clear-icon":"tabler-x"},null,8,["modelValue","label"])]),_:1}),e(x,{cols:"12",sm:"2"},{default:a(()=>[e(ue,{modelValue:o(b),"onUpdate:modelValue":l[2]||(l[2]=t=>V(b)?b.value=t:null),label:i.$t("Operator"),density:"compact",clearable:"","clear-icon":"tabler-x"},null,8,["modelValue","label"])]),_:1}),e(x,{cols:"12",sm:"2",class:"d-flex align-end"},{default:a(()=>[e(v,{"prepend-icon":"tabler-search",style:{"margin-bottom":"1px"},onClick:oe},{default:a(()=>[c(d(i.$t("Search")),1)]),_:1}),e(v,{style:{"margin-bottom":"1px"},type:"reset",color:"secondary",variant:"tonal",class:"ml-2",onClick:se},{default:a(()=>[c(d(i.$t("Reset")),1)]),_:1})]),_:1})]),_:1})]),_:1}),e(h),e(D,{class:"d-flex flex-wrap py-4 gap-4"},{default:a(()=>[g("div",Le,[e(r,{"model-value":o(u).itemsPerPage,items:[{value:10,title:"10"},{value:25,title:"25"},{value:50,title:"50"},{value:100,title:"100"},{value:500,title:"500"}],style:{width:"6.25rem"},"onUpdate:modelValue":l[3]||(l[3]=t=>o(u).itemsPerPage=parseInt(t,10))},null,8,["model-value"])]),e($e),o(L).includes(7)?(J(),Q("div",Se,[e(v,{variant:"tonal",color:"error","prepend-icon":"tabler-trash",onClick:l[4]||(l[4]=t=>m.value=!o(m))},{default:a(()=>[c(d(i.$t("One-click clearing")),1)]),_:1})])):Pe("",!0)]),_:1}),e(h),e(o(we),{"items-per-page":o(u).itemsPerPage,"onUpdate:itemsPerPage":l[6]||(l[6]=t=>o(u).itemsPerPage=t),page:o(u).page,"onUpdate:page":l[7]||(l[7]=t=>o(u).page=t),items:o(O),"items-length":o(w),headers:ae,class:"text-no-wrap",loading:o(S),"onUpdate:options":l[8]||(l[8]=t=>u.value=t),"item-value":"id","no-data-text":o(N),hover:""},{"item.params":a(({item:t})=>[g("div",Ae,[g("span",Ue,d(t.raw.params),1)])]),"item.path":a(({item:t})=>[e(Ce,{color:q(t.raw.action).color,class:"font-weight-medium v-chip--label",size:"small"},{default:a(()=>[c(d(i.$t(q(t.raw.action).text))+" ",1)]),_:2},1032,["color"])]),"item.ua":a(({item:t})=>[g("div",he,d(t.raw.ua),1)]),"item.actions":a(({item:t})=>[e(de,{onClick:Me=>ne(t.raw)},{default:a(()=>[e(ke,{icon:"tabler-eye"})]),_:2},1032,["onClick"])]),bottom:a(()=>[e(h),g("div",Oe,[g("p",Ee,d(o(fe)(o(u),o(w))),1),e(ve,{modelValue:o(u).page,"onUpdate:modelValue":l[5]||(l[5]=t=>o(u).page=t),length:Math.ceil(o(w)/o(u).itemsPerPage),"total-visible":"5",onClick:_},{prev:a(t=>[e(v,G({variant:"tonal",color:"default"},t,{icon:!1}),{default:a(()=>[c(d(i.$t("$vuetify.pagination.ariaLabel.previous")),1)]),_:2},1040)]),next:a(t=>[e(v,G({variant:"tonal",color:"default"},t,{icon:!1}),{default:a(()=>[c(d(i.$t("$vuetify.pagination.ariaLabel.next")),1)]),_:2},1040)]),_:1},8,["modelValue","length"])])]),_:1},8,["items-per-page","page","items","items-length","loading","no-data-text"])]),_:1},8,["title"])]),_:1})]),_:1}),e(K,{modelValue:o(y),"onUpdate:modelValue":l[10]||(l[10]=t=>V(y)?y.value=t:null),class:"v-dialog-sm","min-width":"880","retain-focus":!1},{default:a(()=>[e(z,{onClick:l[9]||(l[9]=t=>y.value=!1)}),e(A,{title:i.$t("Params")},{default:a(()=>[e(D,null,{default:a(()=>[e(U,null,{default:a(()=>[e(x,{cols:"12",md:"12"},{default:a(()=>[c(d(o(j).params),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"]),e(De,{modelValue:o(f),"onUpdate:modelValue":l[12]||(l[12]=t=>V(f)?f.value=t:null),transition:"scale-transition",location:"top",timeout:2500,color:o(k),variant:"tonal"},{actions:a(()=>[e(v,{color:"secondary",onClick:l[11]||(l[11]=t=>f.value=!1)},{default:a(()=>[c(" ❤️ ")]),_:1})]),default:a(()=>[c(d(o(P))+" ",1)]),_:1},8,["modelValue","color"]),e(K,{modelValue:o(m),"onUpdate:modelValue":l[15]||(l[15]=t=>V(m)?m.value=t:null),persistent:"",class:"v-dialog-sm"},{default:a(()=>[e(z,{onClick:l[13]||(l[13]=t=>m.value=!o(m))}),e(A,{title:i.$t("Operation tips")},{default:a(()=>[e(D,null,{default:a(()=>[g("span",Te,d(i.$t("One-click clearing notice")),1)]),_:1}),e(D,{class:"d-flex justify-end gap-3 flex-wrap"},{default:a(()=>[e(v,{color:"secondary",variant:"tonal",onClick:l[14]||(l[14]=t=>m.value=!1)},{default:a(()=>[c(d(i.$t("Cancel")),1)]),_:1}),e(v,{loading:o(C),disabled:o(C),onClick:ie},{default:a(()=>[c(d(i.$t("Confirm")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"])])}}},Ye=_e(Ie,[["__scopeId","data-v-5e969476"]]);export{Ye as default};
