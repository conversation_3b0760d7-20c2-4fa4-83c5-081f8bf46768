import{_ as Se}from"./AppTextarea-36c843b3.js";import{_ as Re}from"./DialogCloseBtn-e6f97e88.js";import{p as Ae,V as Le,_ as xe}from"./VPagination-964310e8.js";import{_ as Ne}from"./AppTextField-8c148b8f.js";import{ad as qe,a9 as ze,l as n,E as ue,D as Oe,o as _,b as C,w as s,q as t,am as b,n as p,y as i,as as O,av as $,al as E,s as o,ak as D,ah as f,aj as g,z as M,A as L,c as I,a8 as ne,ag as F,ao as Ie,b0 as Te,ap as x}from"./index-9a5dc664.js";import{u as Be}from"./index-9465fde1.js";import{V as je}from"./VDataTable-d690b508.js";import{V as Ee}from"./VDataTableServer-26da2b3a.js";import{V as ie}from"./VChip-a30ee730.js";import{V as W}from"./VDialog-0870f7b8.js";import"./VTextField-3e2d458d.js";import"./VSelectionControl-182ca2ca.js";const Me={class:"text-h4 mb-6"},Fe={class:"mb-2"},We={class:"mb-0"},Ge={class:"mb-0"},He={class:"mb-0"},Je={class:"mb-0 text-error"},Ke={class:"d-flex align-center gap-4 flex-wrap"},Qe={key:0},Xe={key:1},Ye={key:2},Ze={key:3},ea={class:"text-wrap mt-2 mb-2"},aa={class:"d-flex align-center justify-space-between flex-wrap gap-3 pa-5 pt-3"},la={class:"text-sm text-medium-emphasis mb-0"},ta={class:"mr-2"},sa={class:""},oa=p("span",{class:"ml-2"},"?",-1),wa={__name:"index",setup(ra){const U=qe().permissions,{t:u,locale:de}=ze(),G=n(u("Username")),H=n(u("Password")),J=n(u("Name 1")),K=n(u("Role")),Q=n(u("Last login")),X=n(u("Remark")),Y=n(u("Status")),Z=n(u("created_at")),me=n(u("updated_at")),ee=n(u("Actions")),ve=n(u("No Data Text"));ue(de,()=>{G.value=u("Username"),H.value=u("Password"),J.value=u("Name 1"),K.value=u("Role"),Q.value=u("Last login"),X.value=u("Remark"),Y.value=u("Status"),Z.value=u("created_at"),me.value=u("updated_at"),ee.value=u("Actions"),ve.value=u("No Data Text")});const ce=[{title:G,key:"username",sortable:!1},{title:H,key:"showword",sortable:!1},{title:K,key:"user_role",sortable:!1},{title:J,key:"name",sortable:!1},{title:X,key:"remark",sortable:!1},{title:"IP",key:"ip",sortable:!1},{title:"User-Agent",key:"ua",sortable:!1},{title:Q,key:"last_login_at",sortable:!1},{title:Z,key:"created_at",sortable:!1},{title:Y,key:"status",sortable:!1,align:"center"},{title:ee,key:"actions",sortable:!1,align:"center"}],ae=n([]),h=n(""),N=n(0),pe=n(1),S=n(""),T=n("primary"),m=n(""),v=n("warning"),d=n(!1),a=n({username:"",password:"",name:"",email:"",avatar:"",role_id:"",remark:"",status:0}),le=[{title:u("Enable"),value:0},{title:u("Disable"),value:1}],c=n({page:1,itemsPerPage:10}),fe=e=>e==1?{color:"primary"}:e==2?{color:"success"}:e==999?{color:"secondary"}:{color:"info"},V=n(!1),te=e=>e==1?{color:"error",text:u("Disable")}:{color:"success",text:u("Enable")},w=()=>{T.value="primary",U.includes(9)&&x.get("/api/adminUser/userList",{params:{username:h.value,status:S.value,page:c.value.page,pagesize:c.value.itemsPerPage}}).then(e=>{e.data.code===200?(ae.value=e.data.data.data,N.value=e.data.data.total,pe.value=e.data.data.last_page,c.value.page=e.data.data.current_page):(m.value=u("Request failed"),v.value="error",d.value=!0)}).catch(e=>{m.value=u("Request failed"),v.value="error",d.value=!0}).finally(()=>{T.value=!1})},se=n([]),ge=()=>{x.get("/api/adminUser/userDetail").then(e=>{e.data.code===200&&(se.value=e.data.data.role_list)}).catch(e=>{m.value=u("Request failed"),v.value="error",d.value=!0})};Oe(()=>{ge(),w()}),ue(()=>c.value.itemsPerPage,(e,r)=>{c.value.page=1,w()});const be=()=>{w()},Ve=()=>{h.value="",c.value.page=1,c.value.itemsPerPage=10,S.value=null,w()},P=n(!1),k=n(""),q=n(!1),_e=()=>{if(a.value.password.length<6){m.value=u("Password Requirements 4"),v.value="error",d.value=!0;return}if(!a.value.name){m.value=u("Name Requirements"),v.value="error",d.value=!0;return}P.value=!0,q.value=!1,U.includes(10)&&x.post("/api/adminUser/addUser",a.value).then(e=>{e.data.code===200?(m.value=u("Operation successful"),v.value="success",d.value=!0,w()):(m.value=u("Operation failed"),v.value="error",d.value=!0)}).catch(e=>{m.value=u("Request failed"),v.value="error",d.value=!0}).finally(()=>{P.value=!1,V.value=!1,a.value.username="",a.value.password="",a.value.name="",a.value.email="",a.value.avatar="",a.value.role_id="",a.value.remark="",a.value.status=0,k.value=""})},B=n(!1),we=e=>{if(e.id=="")return!1;q.value=!0,k.value=e.id,a.value.username=e.username,a.value.password=e.showword,a.value.name=e.name,a.value.email=e.email,a.value.avatar=e.avatar?e.avatar:"",a.value.role_id=e.user_role.id,a.value.remark=e.remark,a.value.status=e.status,setTimeout(()=>{V.value=!0},300)},ke=()=>{if(a.value.password.length<6){m.value=u("Password Requirements 4"),v.value="error",d.value=!0;return}if(!a.value.name){m.value=u("Name Requirements"),v.value="error",d.value=!0;return}P.value=!0,a.value.id=k.value,U.includes(11)&&x.post("/api/adminUser/editUser",a.value).then(e=>{e.data.code===200?(m.value=u("Operation successful"),v.value="success",d.value=!0,w()):(m.value=u("Operation failed"),v.value="error",d.value=!0)}).catch(e=>{m.value=u("Request failed"),v.value="error",d.value=!0}).finally(()=>{P.value=!1,V.value=!1,a.value.username="",a.value.password="",a.value.name="",a.value.email="",a.value.avatar="",a.value.role_id="",a.value.remark="",a.value.status=0,k.value="",delete a.value.id})},ye=()=>{q.value?ke():_e()},Ue=()=>{a.value.username="",a.value.password="",a.value.name="",a.value.email="",a.value.avatar="",a.value.role_id="",a.value.remark="",a.value.status=0,a.value.id="",a.value.is_del="",q.value=!1,k.value="",V.value=!0},y=n(!1),z=n(!1),oe=n(null),$e=e=>{oe.value=e.id,a.value.username=e.username,y.value=!0},Pe=()=>{z.value=!0,a.value.password=null,U.includes(19)&&x.get("/api/adminUser/delUser?id="+oe.value).then(e=>{e.data.code===200?(m.value=u("Operation successful"),v.value="success",d.value=!0,w()):(m.value=u("Operation failed"),v.value="error",d.value=!0)}).catch(e=>{m.value=u("Request failed"),v.value="error",d.value=!0}).finally(()=>{z.value=!1,y.value=!1,a.value.username="",a.value.password="",a.value.name="",a.value.email="",a.value.avatar="",a.value.role_id="",a.value.remark="",a.value.status=0,k.value="",a.value.id="",a.value.is_del=""})},{toClipboard:Ce}=Be(),De=async e=>{const A=`Website URL: ${window.location.href.split("#")[0]}
Username: ${e.username}
Password: ${e.showword}`;try{await Ce(A),m.value=u("Copy successful"),v.value="success",d.value=!0}catch{m.value=u("Copy failed"),v.value="error",d.value=!0}};return(e,r)=>{const R=Ne,A=xe,j=Re,he=Se;return _(),C(E,null,{default:s(()=>[t(b,{cols:"12"},{default:s(()=>[p("h5",Me,i(e.$t("Account list")),1),p("p",Fe,i(e.$t("Account list Desc1")),1),p("p",We,i(e.$t("Account list Desc2")),1),p("p",Ge,i(e.$t("Account list Desc3")),1),p("p",He,i(e.$t("Account list Desc4")),1),p("p",Je,i(e.$t("Account list Desc5")),1)]),_:1}),t(b,{cols:"12"},{default:s(()=>[t(O,{title:e.$t("Search Filter")},{default:s(()=>[t($,null,{default:s(()=>[t(E,null,{default:s(()=>[t(b,{cols:"12",sm:"2"},{default:s(()=>[t(je,{modelValue:o(S),"onUpdate:modelValue":r[0]||(r[0]=l=>D(S)?S.value=l:null),label:e.$t("Select Status"),items:le,density:"compact",clearable:"","clear-icon":"tabler-x"},null,8,["modelValue","label"])]),_:1}),t(b,{cols:"12",sm:"2"},{default:s(()=>[t(R,{modelValue:o(h),"onUpdate:modelValue":r[1]||(r[1]=l=>D(h)?h.value=l:null),placeholder:e.$t("Search username"),density:"compact"},null,8,["modelValue","placeholder"])]),_:1}),t(b,{cols:"12",sm:"2",class:"d-flex align-end"},{default:s(()=>[t(f,{"prepend-icon":"tabler-search",style:{"margin-bottom":"1px"},onClick:be},{default:s(()=>[g(i(e.$t("Search")),1)]),_:1}),t(f,{style:{"margin-bottom":"1px"},type:"reset",color:"secondary",variant:"tonal",class:"ml-2",onClick:Ve},{default:s(()=>[g(i(e.$t("Reset")),1)]),_:1})]),_:1})]),_:1})]),_:1}),t(M),t($,{class:"d-flex align-center justify-space-between flex-wrap gap-4"},{default:s(()=>[t(A,{"model-value":o(c).itemsPerPage,items:[{value:10,title:"10"},{value:25,title:"25"},{value:50,title:"50"},{value:100,title:"100"}],style:{width:"5rem"},"onUpdate:modelValue":r[2]||(r[2]=l=>o(c).itemsPerPage=parseInt(l,10))},null,8,["model-value"]),p("div",Ke,[o(U).includes(10)?(_(),C(f,{key:0,density:"default",onClick:Ue,"prepend-icon":"tabler-plus"},{default:s(()=>[g(i(e.$t("Add User")),1)]),_:1})):L("",!0)])]),_:1}),t(M),t(o(Ee),{"items-per-page":o(c).itemsPerPage,"onUpdate:itemsPerPage":r[5]||(r[5]=l=>o(c).itemsPerPage=l),page:o(c).page,"onUpdate:page":r[6]||(r[6]=l=>o(c).page=l),"items-length":o(N),headers:ce,items:o(ae),loading:o(T),class:"text-medium-emphasis text-no-wrap","onUpdate:options":r[7]||(r[7]=l=>c.value=l)},{"item.user_role":s(({item:l})=>[t(ie,{color:fe(l.raw.user_role.id).color,class:"font-weight-medium v-chip--label",size:"small"},{default:s(()=>[l.raw.role_id==1?(_(),I("span",Qe,i(e.$t("Superadmin")),1)):l.raw.role_id==2?(_(),I("span",Xe,i(e.$t("Administrator")),1)):l.raw.role_id==999?(_(),I("span",Ye,i(e.$t("Visitor")),1)):(_(),I("span",Ze,i(l.raw.user_role.name),1))]),_:2},1032,["color"])]),"item.ua":s(({item:l})=>[p("div",ea,i(l.raw.ua),1)]),"item.status":s(({item:l})=>[t(ie,{color:te(l.raw.status).color,class:"font-weight-medium v-chip--label",size:"small"},{default:s(()=>[g(i(te(l.raw.status).text),1)]),_:2},1032,["color"])]),bottom:s(()=>[t(M),p("div",aa,[p("p",la,i(o(Ae)(o(c),o(N))),1),t(Le,{modelValue:o(c).page,"onUpdate:modelValue":r[3]||(r[3]=l=>o(c).page=l),length:Math.ceil(o(N)/o(c).itemsPerPage),"total-visible":"5",onClick:r[4]||(r[4]=l=>w())},{prev:s(l=>[t(f,ne({variant:"tonal",color:"default"},l,{icon:!1}),{default:s(()=>[g(i(e.$t("$vuetify.pagination.ariaLabel.previous")),1)]),_:2},1040)]),next:s(l=>[t(f,ne({variant:"tonal",color:"default"},l,{icon:!1}),{default:s(()=>[g(i(e.$t("$vuetify.pagination.ariaLabel.next")),1)]),_:2},1040)]),_:1},8,["modelValue","length"])])]),"item.createdDate":s(({item:l})=>[p("span",null,i(l.raw.createdDate),1)]),"item.actions":s(({item:l})=>[o(U).includes(11)?(_(),C(f,{key:0,icon:"",size:"small",color:"medium-emphasis",variant:"text",onClick:re=>we(l.raw)},{default:s(()=>[t(F,{size:"22",icon:"tabler-edit"})]),_:2},1032,["onClick"])):L("",!0),o(U).includes(19)&&l.raw.id!=1?(_(),C(f,{key:1,icon:"",size:"small",variant:"text",color:"medium-emphasis",onClick:re=>$e(l.raw)},{default:s(()=>[t(F,{size:"22",icon:"tabler-trash"})]),_:2},1032,["onClick"])):L("",!0),t(f,{icon:"",size:"small",variant:"text",color:"medium-emphasis",onClick:re=>De(l.raw)},{default:s(()=>[t(F,{size:"22",icon:"tabler-share"})]),_:2},1032,["onClick"])]),_:1},8,["items-per-page","page","items-length","items","loading"])]),_:1},8,["title"]),t(Ie,{modelValue:o(d),"onUpdate:modelValue":r[9]||(r[9]=l=>D(d)?d.value=l:null),transition:"scale-transition",location:"top",timeout:2500,color:o(v),variant:"tonal"},{actions:s(()=>[t(f,{color:"secondary",onClick:r[8]||(r[8]=l=>d.value=!1)},{default:s(()=>[g(" ❤️ ")]),_:1})]),default:s(()=>[g(i(o(m))+" ",1)]),_:1},8,["modelValue","color"]),t(W,{modelValue:o(V),"onUpdate:modelValue":r[18]||(r[18]=l=>D(V)?V.value=l:null),"max-width":"600"},{default:s(()=>[t(j,{onClick:r[10]||(r[10]=l=>V.value=!o(V))}),t(O,{title:e.$t("User Profile")},{default:s(()=>[t($,null,{default:s(()=>[t(E,null,{default:s(()=>[t(b,{cols:"12"},{default:s(()=>[t(R,{modelValue:o(a).username,"onUpdate:modelValue":r[11]||(r[11]=l=>o(a).username=l),label:e.$t("Username")},null,8,["modelValue","label"])]),_:1}),t(b,{cols:"12"},{default:s(()=>[t(R,{modelValue:o(a).password,"onUpdate:modelValue":r[12]||(r[12]=l=>o(a).password=l),label:e.$t("Password")},null,8,["modelValue","label"])]),_:1}),o(k)!=1?(_(),C(b,{key:0,cols:"12"},{default:s(()=>[t(A,{modelValue:o(a).role_id,"onUpdate:modelValue":r[13]||(r[13]=l=>o(a).role_id=l),label:e.$t("Select Role"),items:o(se),"item-value":"id","item-title":"name"},null,8,["modelValue","label","items"])]),_:1})):L("",!0),t(b,{cols:"12"},{default:s(()=>[t(R,{modelValue:o(a).name,"onUpdate:modelValue":r[14]||(r[14]=l=>o(a).name=l),label:e.$t("Name 2")},null,8,["modelValue","label"])]),_:1}),o(k)!=1?(_(),C(b,{key:1,cols:"12"},{default:s(()=>[t(A,{modelValue:o(a).status,"onUpdate:modelValue":r[15]||(r[15]=l=>o(a).status=l),label:e.$t("Select Status"),items:le,"item-value":"value","item-title":"title"},null,8,["modelValue","label"])]),_:1})):L("",!0),t(b,{cols:"12"},{default:s(()=>[t(he,{rows:"2",modelValue:o(a).remark,"onUpdate:modelValue":r[16]||(r[16]=l=>o(a).remark=l),label:e.$t("Remark")},null,8,["modelValue","label"])]),_:1})]),_:1})]),_:1}),t($,{class:"d-flex justify-end flex-wrap gap-3"},{default:s(()=>[t(f,{variant:"tonal",color:"secondary",onClick:r[17]||(r[17]=l=>V.value=!1)},{default:s(()=>[g(i(e.$t("Cancel")),1)]),_:1}),t(f,{loading:o(P),disabled:o(P),onClick:ye},{default:s(()=>[g(i(e.$t("Confirm")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"]),t(W,{modelValue:o(B),"onUpdate:modelValue":r[19]||(r[19]=l=>D(B)?B.value=l:null),width:"300"},{default:s(()=>[t(O,{color:"primary",width:"300"},{default:s(()=>[t($,{class:"pt-3"},{default:s(()=>[g(" Please stand by "),t(Te,{indeterminate:"",class:"mb-0"})]),_:1})]),_:1})]),_:1},8,["modelValue"]),t(W,{modelValue:o(y),"onUpdate:modelValue":r[22]||(r[22]=l=>D(y)?y.value=l:null),persistent:"",class:"v-dialog-sm"},{default:s(()=>[t(j,{onClick:r[20]||(r[20]=l=>y.value=!o(y))}),t(O,{title:e.$t("Operation tips")},{default:s(()=>[t($,null,{default:s(()=>[p("span",ta,i(e.$t("Delete Account Msg"))+": ",1),p("span",sa,i(o(a).username),1),oa]),_:1}),t($,{class:"d-flex justify-end gap-3 flex-wrap"},{default:s(()=>[t(f,{color:"secondary",variant:"tonal",onClick:r[21]||(r[21]=l=>y.value=!1)},{default:s(()=>[g(i(e.$t("Cancel")),1)]),_:1}),t(f,{loading:o(z),disabled:o(z),onClick:Pe},{default:s(()=>[g(i(e.$t("Confirm")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"])]),_:1})]),_:1})}}};export{wa as default};
