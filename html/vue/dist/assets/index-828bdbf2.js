import{_ as I}from"./AppTextField-8c148b8f.js";import{aJ as q,a9 as N,l as n,o as x,b as B,w as a,q as e,am as y,as as k,av as w,al as g,s as t,ak as _,n as L,y as b,az as S,c as U,F as h,a as F,aA as T,ag as D,ah as P,aj as m,ao as z,b0 as A,ap as j}from"./index-9a5dc664.js";import{V as E}from"./VForm-c6ce9b98.js";import{V as J}from"./VDialog-0870f7b8.js";import"./VTextField-3e2d458d.js";const M={class:"text-base font-weight-medium mb-3"},G={__name:"index",setup(H){const{t:d,locale:K}=N();n(!1);const v=n(!1),V=n(!1);n("");const u=n(""),i=n(""),$=[d("Password Requirements 1")],p=n(""),f=n("warning"),o=n(!1),c=n(!1),R=()=>{if(!u.value||!i.value){p.value=d("Please enter password"),f.value="error",o.value=!0;return}if(u.value.length<6||i.value.length<6){p.value=d("Password Requirements 4"),f.value="error",o.value=!0;return}if(u.value!=i.value){p.value=d("The two password inputs are inconsistent"),f.value="error",o.value=!0;return}c.value=!0,j.post("/api/adminUser/updatePass",{name:null,password:u.value}).then(r=>{r.data.code===200?(p.value=d("Change password successfully"),f.value="success",o.value=!0,u.value=i.value=""):(p.value=d("Password modification failed"),f.value="error",o.value=!0)}).catch(r=>{p.value=d("Request failed"),f.value="error",o.value=!0}).finally(()=>{c.value=!1})};return(r,l)=>{const C=I;return x(),B(g,null,{default:a(()=>[e(y,{cols:"12"},{default:a(()=>[e(k,{title:r.$t("Change Password")},{default:a(()=>[e(E,null,{default:a(()=>[e(w,{class:"pt-0"},{default:a(()=>[e(g,null,{default:a(()=>[e(y,{cols:"12",md:"6"},{default:a(()=>[e(C,{modelValue:t(u),"onUpdate:modelValue":l[0]||(l[0]=s=>_(u)?u.value=s:null),type:t(v)?"text":"password","append-inner-icon":t(v)?"tabler-eye-off":"tabler-eye",label:r.$t("New Password"),"onClick:appendInner":l[1]||(l[1]=s=>v.value=!t(v))},null,8,["modelValue","type","append-inner-icon","label"])]),_:1})]),_:1}),e(g,null,{default:a(()=>[e(y,{cols:"12",md:"6"},{default:a(()=>[e(C,{modelValue:t(i),"onUpdate:modelValue":l[2]||(l[2]=s=>_(i)?i.value=s:null),type:t(V)?"text":"password","append-inner-icon":t(V)?"tabler-eye-off":"tabler-eye",label:r.$t("Confirm New Password"),"onClick:appendInner":l[3]||(l[3]=s=>V.value=!t(V))},null,8,["modelValue","type","append-inner-icon","label"])]),_:1})]),_:1})]),_:1}),e(w,null,{default:a(()=>[L("h6",M,b(r.$t("Password Requirements"))+" : ",1),e(S,{class:"card-list"},{default:a(()=>[(x(),U(h,null,F($,s=>e(T,{key:s,title:s,class:"text-medium-emphasis"},{prepend:a(()=>[e(D,{size:"8",icon:"tabler-circle",class:"me-3"})]),_:2},1032,["title"])),64))]),_:1})]),_:1}),e(w,{class:"d-flex flex-wrap gap-4"},{default:a(()=>[e(P,{onClick:R},{default:a(()=>[m(b(r.$t("Save changes")),1)]),_:1}),e(P,{type:"reset",color:"secondary",variant:"tonal"},{default:a(()=>[m(b(r.$t("Reset")),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["title"])]),_:1}),e(z,{modelValue:t(o),"onUpdate:modelValue":l[5]||(l[5]=s=>_(o)?o.value=s:null),transition:"scale-transition",location:"top",timeout:2500,color:t(f),variant:"tonal"},{actions:a(()=>[e(P,{color:"secondary",onClick:l[4]||(l[4]=s=>o.value=!1)},{default:a(()=>[m(" ❤️ ")]),_:1})]),default:a(()=>[m(b(t(p))+" ",1)]),_:1},8,["modelValue","color"]),e(J,{modelValue:t(c),"onUpdate:modelValue":l[6]||(l[6]=s=>_(c)?c.value=s:null),width:"300"},{default:a(()=>[e(k,{color:"primary",width:"300"},{default:a(()=>[e(w,{class:"pt-3"},{default:a(()=>[m(" Please stand by "),e(A,{indeterminate:"",class:"mb-0"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})}}},Z=q(G,[["__scopeId","data-v-c5dab0de"]]);export{Z as default};
