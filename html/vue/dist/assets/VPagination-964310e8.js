import{V as j}from"./VTextField-3e2d458d.js";import{V as G}from"./VDataTable-d690b508.js";import{d as J,B as f,bl as B,o as C,c as U,s as x,b as X,A as Y,q as r,b5 as H,a as Z,w as ee,p as ae,b6 as R,b7 as w,t as te,l as F,c9 as ne,I as le,aQ as V,J as ie,K as se,bo as re,L as ue,N as oe,bq as ve,O as ce,P as de,bz as fe,a0 as ge,aT as be,Q as me,R as ye,Y as pe,a6 as M,ca as he,cb as S,W as y,a7 as Le,ah as P,a8 as _,cc as N,a3 as D}from"./index-9a5dc664.js";const Pe=J({name:"AppSelect",inheritAttrs:!1}),Se=Object.assign(Pe,{setup(e){const d=f(()=>{const s=B(),n=s.id||s.label;return n?`app-select-${n}-${Math.random().toString(36).slice(2,7)}`:void 0}),i=f(()=>B().label);return(s,n)=>(C(),U("div",{class:te(["app-select flex-grow-1",s.$attrs.class])},[x(i)?(C(),X(j,{key:0,for:x(d),class:"mb-1 text-body-2 text-high-emphasis",text:x(i)},null,8,["for","text"])):Y("",!0),r(G,R(w({...s.$attrs,class:null,label:void 0,variant:"outlined",id:x(d),menuProps:{contentClass:["app-inner-list","app-select__content","v-select__content",s.$attrs.multiple!==void 0?"v-list-select-multiple":""]}})),H({_:2},[Z(s.$slots,(m,k)=>({name:k,fn:ee(p=>[ae(s.$slots,k,R(w(p||{})))])}))]),1040)],2))}}),$e=f(()=>(e,d)=>{const i=(e.page-1)*e.itemsPerPage+1,s=Math.min(e.page*e.itemsPerPage,d);return`Showing ${i} to ${s} of ${d} entries`});function _e(){const e=F([]);ne(()=>e.value=[]);function d(i,s){e.value[s]=i}return{refs:e,updateRef:d}}const Ie=le()({name:"VPagination",props:{activeColor:String,start:{type:[Number,String],default:1},modelValue:{type:Number,default:e=>e.start},disabled:Boolean,length:{type:[Number,String],default:1,validator:e=>e%1===0},totalVisible:[Number,String],firstIcon:{type:V,default:"$first"},prevIcon:{type:V,default:"$prev"},nextIcon:{type:V,default:"$next"},lastIcon:{type:V,default:"$last"},ariaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.root"},pageAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.page"},currentPageAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.currentPage"},firstAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.first"},previousAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.previous"},nextAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.next"},lastAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.last"},ellipsis:{type:String,default:"..."},showFirstLastPage:Boolean,...ie(),...se(),...re(),...ue(),...oe(),...ve(),...ce({tag:"nav"}),...de(),...fe({variant:"text"})},emits:{"update:modelValue":e=>!0,first:e=>!0,prev:e=>!0,next:e=>!0,last:e=>!0},setup(e,d){let{slots:i,emit:s}=d;const n=ge(e,"modelValue"),{t:m,n:k}=be(),{isRtl:p}=me(),{themeClasses:z}=ye(e),{width:T}=pe(),$=F(-1);M(void 0,{scoped:!0});const{resizeRef:E}=he(a=>{if(!a.length)return;const{target:t,contentRect:l}=a[0],v=t.querySelector(".v-pagination__list > *");if(!v)return;const c=l.width,L=v.offsetWidth+parseFloat(getComputedStyle(v).marginRight)*2;$.value=I(c,L)}),u=f(()=>parseInt(e.length,10)),o=f(()=>parseInt(e.start,10)),g=f(()=>e.totalVisible?parseInt(e.totalVisible,10):$.value>=0?$.value:I(T.value,58));function I(a,t){const l=e.showFirstLastPage?5:3;return Math.max(0,Math.floor(+((a-t*l)/t).toFixed(2)))}const q=f(()=>{if(u.value<=0||isNaN(u.value)||u.value>Number.MAX_SAFE_INTEGER)return[];if(g.value<=1)return[n.value];if(u.value<=g.value)return S(u.value,o.value);const a=g.value%2===0,t=a?g.value/2:Math.floor(g.value/2),l=a?t:t+1,v=u.value-t;if(l-n.value>=0)return[...S(Math.max(1,g.value-1),o.value),e.ellipsis,u.value];if(n.value-v>=(a?1:0)){const c=g.value-1,L=u.value-c+o.value;return[o.value,e.ellipsis,...S(c,L)]}else{const c=Math.max(1,g.value-3),L=c===1?n.value:n.value-Math.ceil(c/2)+o.value;return[o.value,e.ellipsis,...S(c,L),e.ellipsis,u.value]}});function h(a,t,l){a.preventDefault(),n.value=t,l&&s(l,t)}const{refs:K,updateRef:O}=_e();M({VPaginationBtn:{color:y(e,"color"),border:y(e,"border"),density:y(e,"density"),size:y(e,"size"),variant:y(e,"variant"),rounded:y(e,"rounded"),elevation:y(e,"elevation")}});const W=f(()=>q.value.map((a,t)=>{const l=v=>O(v,t);if(typeof a=="string")return{isActive:!1,key:`ellipsis-${t}`,page:a,props:{ref:l,ellipsis:!0,icon:!0,disabled:!0}};{const v=a===n.value;return{isActive:v,key:a,page:k(a),props:{ref:l,ellipsis:!1,icon:!0,disabled:!!e.disabled||+e.length<2,color:v?e.activeColor:e.color,ariaCurrent:v,ariaLabel:m(v?e.currentPageAriaLabel:e.pageAriaLabel,a),onClick:c=>h(c,a)}}}})),b=f(()=>{const a=!!e.disabled||n.value<=o.value,t=!!e.disabled||n.value>=o.value+u.value-1;return{first:e.showFirstLastPage?{icon:p.value?e.lastIcon:e.firstIcon,onClick:l=>h(l,o.value,"first"),disabled:a,ariaLabel:m(e.firstAriaLabel),ariaDisabled:a}:void 0,prev:{icon:p.value?e.nextIcon:e.prevIcon,onClick:l=>h(l,n.value-1,"prev"),disabled:a,ariaLabel:m(e.previousAriaLabel),ariaDisabled:a},next:{icon:p.value?e.prevIcon:e.nextIcon,onClick:l=>h(l,n.value+1,"next"),disabled:t,ariaLabel:m(e.nextAriaLabel),ariaDisabled:t},last:e.showFirstLastPage?{icon:p.value?e.firstIcon:e.lastIcon,onClick:l=>h(l,o.value+u.value-1,"last"),disabled:t,ariaLabel:m(e.lastAriaLabel),ariaDisabled:t}:void 0}});function A(){var t;const a=n.value-o.value;(t=K.value[a])==null||t.$el.focus()}function Q(a){a.key===N.left&&!e.disabled&&n.value>+e.start?(n.value=n.value-1,D(A)):a.key===N.right&&!e.disabled&&n.value<o.value+u.value-1&&(n.value=n.value+1,D(A))}return Le(()=>r(e.tag,{ref:E,class:["v-pagination",z.value,e.class],style:e.style,role:"navigation","aria-label":m(e.ariaLabel),onKeydown:Q,"data-test":"v-pagination-root"},{default:()=>[r("ul",{class:"v-pagination__list"},[e.showFirstLastPage&&r("li",{key:"first",class:"v-pagination__first","data-test":"v-pagination-first"},[i.first?i.first(b.value.first):r(P,_({_as:"VPaginationBtn"},b.value.first),null)]),r("li",{key:"prev",class:"v-pagination__prev","data-test":"v-pagination-prev"},[i.prev?i.prev(b.value.prev):r(P,_({_as:"VPaginationBtn"},b.value.prev),null)]),W.value.map((a,t)=>r("li",{key:a.key,class:["v-pagination__item",{"v-pagination__item--is-active":a.isActive}],"data-test":"v-pagination-item"},[i.item?i.item(a):r(P,_({_as:"VPaginationBtn"},a.props),{default:()=>[a.page]})])),r("li",{key:"next",class:"v-pagination__next","data-test":"v-pagination-next"},[i.next?i.next(b.value.next):r(P,_({_as:"VPaginationBtn"},b.value.next),null)]),e.showFirstLastPage&&r("li",{key:"last",class:"v-pagination__last","data-test":"v-pagination-last"},[i.last?i.last(b.value.last):r(P,_({_as:"VPaginationBtn"},b.value.last),null)])])]})),{}}});export{Ie as V,Se as _,$e as p};
