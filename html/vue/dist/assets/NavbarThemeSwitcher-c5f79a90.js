import{v as _,aI as d,E as x,r as T,o as m,b as l,w as c,q as r,ag as f,s as i,aq as g,n as v,y}from"./index-9a5dc664.js";const w={class:"text-capitalize"},N={__name:"ThemeSwitcher",props:{themes:{type:Array,required:!0}},setup(o){const e=o,{theme:t}=_(),{state:n,next:s,index:h}=d(e.themes.map(a=>a.name),{initialValue:t.value}),p=()=>{t.value=s()};return x(t,a=>{n.value=a}),(a,b)=>{const u=T("IconBtn");return m(),l(u,{onClick:p},{default:c(()=>[r(f,{size:"26",icon:e.themes[i(h)].icon},null,8,["icon"]),r(g,{activator:"parent","open-delay":"1000","scroll-strategy":"close"},{default:c(()=>[v("span",w,y(i(n)),1)]),_:1})]),_:1})}}},C={__name:"NavbarThemeSwitcher",setup(o){const e=[{name:"system",icon:"tabler-device-laptop"},{name:"light",icon:"tabler-sun-high"},{name:"dark",icon:"tabler-moon"}];return(t,n)=>{const s=N;return m(),l(s,{themes:e})}}};export{C as default};
