import{a9 as _,E as f,l as m,r as L,o as c,b as u,w as t,q as o,ag as h,ar as v,az as I,s as b,ak as C,c as k,F as B,a as V,aA as x,aB as y,aj as w,y as z,v as A}from"./index-9a5dc664.js";const E={__name:"I18n",props:{languages:{type:Array,required:!0},location:{type:null,required:!1,default:"bottom end"}},emits:["change"],setup(i,{emit:g}){const n=i,{locale:s}=_({useScope:"global"});f(s,r=>{document.documentElement.setAttribute("lang",r)});const a=localStorage.getItem("selectedLanguage"),l=a?m([a]):m(["zh"]);return(r,d)=>{const p=L("IconBtn");return c(),u(p,null,{default:t(()=>[o(h,{size:"26",icon:"tabler-language"}),o(v,{activator:"parent",location:n.location,offset:"14px"},{default:t(()=>[o(I,{selected:b(l),"onUpdate:selected":d[0]||(d[0]=e=>C(l)?l.value=e:null),"min-width":"175px"},{default:t(()=>[(c(!0),k(B,null,V(n.languages,e=>(c(),u(x,{key:e.i18nLang,value:e.i18nLang,onClick:S=>{s.value=e.i18nLang,r.$emit("change",e.i18nLang)}},{default:t(()=>[o(y,null,{default:t(()=>[w(z(e.label),1)]),_:2},1024)]),_:2},1032,["value","onClick"]))),128))]),_:1},8,["selected"])]),_:1},8,["location"])]),_:1})}}},N={__name:"NavBarI18n",setup(i){const{isAppRtl:g}=A(),n=[{label:"中文",i18nLang:"zh"},{label:"English",i18nLang:"en"},{label:"Русский",i18nLang:"ru"}],s=a=>{g.value=a==="ar",localStorage.setItem("selectedLanguage",a)};return(a,l)=>(c(),u(E,{languages:n,onChange:s}))}};export{N as default};
