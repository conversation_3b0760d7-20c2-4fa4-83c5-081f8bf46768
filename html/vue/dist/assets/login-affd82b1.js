import{aa as J,o as T,c as U,F as O,a as W,q as e,w as t,ag as G,s as a,ah as h,ay as K,bv as Q,k as X,l as i,a9 as Y,D as Z,n as r,h as L,V as k,at as ee,x as D,au as ae,aj as d,y as f,av as x,aG as R,al as te,am as v,z as q,as as A,t as se,ak as P,ao as oe,cn as le,ae as ne,ad as re}from"./index-9a5dc664.js";import{_ as ie}from"./DialogCloseBtn-e6f97e88.js";import{_ as ue}from"./AppTextField-8c148b8f.js";import{a as ce,b as de}from"./auth-v1-top-shape-c5f58476.js";import{b as F}from"./route-block-83d24a4e.js";import{V as pe}from"./VForm-c6ce9b98.js";import{V as me}from"./VDialog-0870f7b8.js";import"./VTextField-3e2d458d.js";const fe={class:"d-flex justify-center flex-wrap gap-3"},ve={__name:"AuthProvider",setup(N){const{global:_}=J(),b=[{icon:"fa-facebook",color:"#4267b2"},{icon:"fa-google",color:"#dd4b39"},{icon:"fa-twitter",color:"#1da1f2"}];return(M,g)=>(T(),U("div",fe,[(T(),U(O,null,W(b,u=>e(h,{key:u.icon,icon:"",variant:"tonal",size:"38",color:a(_).name.value==="dark"?u.colorInDark:u.color,class:"rounded"},{default:t(()=>[e(G,{size:"18",icon:u.icon},null,8,["icon"])]),_:2},1032,["color"])),64))]))}};const he={class:"auth-wrapper d-flex align-center justify-center pa-4"},_e={class:"position-relative my-sm-16"},be={class:"d-flex"},ge={class:"text-h5 mb-1"},Ve={class:"text-capitalize"},ye=r("p",{class:"mb-0"},"Please sign-in to your account and start the adventure",-1),we=r("div",{class:"d-flex align-center justify-space-between flex-wrap mt-4 mb-4"},null,-1),xe=r("span",{class:"mx-4"},"or",-1),Ce={class:"text-h3 text-center mt-8",style:{"font-size":"30px !important","font-weight":"bold","letter-spacing":"20px !important"}},Se=["src"],$e=["onSubmit"],ke={__name:"login",setup(N){const _=K(),b=Q(),M=X(),g=i("/api/adminUser/captcha"),u=i(!1),l=i({username:"",password:"",captcha:""}),V=i(!1),p=i(!1),y=i(!1),C=i(!1),m=i(!1),S=i("warning"),$=i(""),{t:j,locale:De}=Y(),z=async()=>{if(!l.value.captcha)return;const n={username:l.value.username,password:l.value.password,captcha:l.value.captcha,version:"1.0",email:"<EMAIL>",phone:"********",currency:"JAVA",sign_type:"MD5"},s=Object.keys(n).sort();let c="";s.forEach(w=>{c===""?c+=`${w}=${n[w]}`:c+=`&${w}=${n[w]}`}),l.value.sign=le.MD5(c+"&key="+ne().isOpen).toString(),y.value=!0;const o=await re().login(l.value);if(o.success)M.replace(b.query.to?String(b.query.to):"/");else{if(y.value=!1,C.value=!1,o.code==203){B(),u.value=!0,C.value=!1;return}else p.value=!1;$.value=j(o.message),S.value="error",m.value=!0}},E=()=>{l.value.username&&l.value.password?(C.value=!0,l.value.captcha="",u.value=!1,H(),p.value=!0):($.value=j("Login notice"),S.value="warning",m.value=!0)},H=()=>{g.value=`/api/adminUser/captcha?requestTime=${Math.floor(Date.now()/1e3)}`},B=()=>{l.value.captcha="",g.value=`/api/adminUser/captcha?requestTime=${Math.floor(Date.now()/1e3)}`};return Z(()=>{_.clear(),setTimeout(()=>{_.clear()},3e3)}),(n,s)=>{const c=ue,I=ie;return T(),U("div",he,[r("div",_e,[e(a(k),{nodes:("h"in n?n.h:a(L))("div",{innerHTML:a(ce)}),class:"text-primary auth-v1-top-shape d-none d-sm-block"},null,8,["nodes"]),e(a(k),{nodes:("h"in n?n.h:a(L))("div",{innerHTML:a(de)}),class:"text-primary auth-v1-bottom-shape d-none d-sm-block"},null,8,["nodes"]),e(A,{class:"auth-card pa-4","max-width":"448"},{default:t(()=>[e(ee,{class:"justify-center"},{prepend:t(()=>[r("div",be,[e(a(k),{nodes:a(D).app.logo},null,8,["nodes"])])]),default:t(()=>[e(ae,{class:"font-weight-bold text-capitalize text-h5 py-1"},{default:t(()=>[d(f(a(D).app.title),1)]),_:1})]),_:1}),e(x,{class:"pt-1"},{default:t(()=>[r("h5",ge,[d(" Welcome to "),r("span",Ve,f(a(D).app.title),1),d("! 👋🏻 ")]),ye]),_:1}),e(x,null,{default:t(()=>[e(pe,{onSubmit:R(E,["prevent"])},{default:t(()=>[e(te,null,{default:t(()=>[e(v,{cols:"12"},{default:t(()=>[e(c,{modelValue:a(l).username,"onUpdate:modelValue":s[0]||(s[0]=o=>a(l).username=o),autofocus:"",label:"Account",type:"text"},null,8,["modelValue"])]),_:1}),e(v,{cols:"12"},{default:t(()=>[e(c,{modelValue:a(l).password,"onUpdate:modelValue":s[1]||(s[1]=o=>a(l).password=o),label:"Password",type:a(V)?"text":"password","append-inner-icon":a(V)?"tabler-eye-off":"tabler-eye","onClick:appendInner":s[2]||(s[2]=o=>V.value=!a(V))},null,8,["modelValue","type","append-inner-icon"]),we,e(h,{block:"",type:"submit"},{default:t(()=>[d(" Login ")]),_:1})]),_:1}),e(v,{cols:"12",class:"d-flex align-center"},{default:t(()=>[e(q),xe,e(q)]),_:1}),e(v,{cols:"12",class:"text-center"},{default:t(()=>[e(ve)]),_:1})]),_:1})]),_:1},8,["onSubmit"])]),_:1})]),_:1})]),e(me,{modelValue:a(p),"onUpdate:modelValue":s[6]||(s[6]=o=>P(p)?p.value=o:null),"max-width":"500"},{default:t(()=>[e(I,{onClick:s[3]||(s[3]=o=>p.value=!a(p))}),e(A,null,{default:t(()=>[r("h5",Ce,[r("img",{src:a(g),alt:"",srcset:""},null,8,Se)]),e(x,{class:"pb-0"},{default:t(()=>[r("form",{class:"v-form",novalidate:"",onSubmit:R(z,["prevent"])},[e(v,{cols:"12"},{default:t(()=>[e(c,{modelValue:a(l).captcha,"onUpdate:modelValue":s[4]||(s[4]=o=>a(l).captcha=o),class:"input-center",placeholder:n.$t("Captcha input"),onInput:s[5]||(s[5]=o=>u.value=!1),autofocus:""},null,8,["modelValue","placeholder"]),r("p",{class:se(["text-error mt-2 mb-0 text-center",a(u)?"visibility-visible":"visibility-hidden"])},f(n.$t("Captcha error")),3)]),_:1})],40,$e)]),_:1}),e(x,{class:"d-flex align-center justify-center gap-3 mt-6"},{default:t(()=>[e(h,{loading:a(y),disabled:a(y),onClick:z},{default:t(()=>[d(f(n.$t("Submit")),1)]),_:1},8,["loading","disabled"]),e(h,{variant:"tonal",color:"secondary",onClick:B},{default:t(()=>[d(f(n.$t("Refresh")),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"]),e(oe,{modelValue:a(m),"onUpdate:modelValue":s[8]||(s[8]=o=>P(m)?m.value=o:null),transition:"scale-transition",location:"top",timeout:2500,color:a(S),variant:"tonal"},{actions:t(()=>[e(h,{color:"secondary",onClick:s[7]||(s[7]=o=>m.value=!1)},{default:t(()=>[d(" ❤️ ")]),_:1})]),default:t(()=>[d(f(a($))+" ",1)]),_:1},8,["modelValue","color"])])}}};typeof F=="function"&&F(ke);export{ke as default};
