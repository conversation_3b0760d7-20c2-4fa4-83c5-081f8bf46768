import{_ as at}from"./AppTextarea-36c843b3.js";import{_ as lt}from"./AppTextField-8c148b8f.js";import{o as b,c as L,b as Y,z as Ne,A as G,n,y as u,p as nt,F as Q,l as I,B as T,C as ge,D as Pe,E as A,G as We,H as ot,I as st,J as it,K as ut,L as rt,M as dt,N as ct,O as mt,P as vt,Q as ft,R as ht,S as pt,U as Re,W as Ee,X as gt,Y as bt,Z as yt,$ as _t,a0 as wt,a1 as Vt,a2 as $t,a3 as kt,a4 as Ct,a5 as Tt,a6 as St,a7 as xt,q as e,a8 as Bt,T as Ae,a9 as It,v as Ye,aa as Dt,j as Fe,ab as Me,ac as we,ad as qe,ae as Z,af as Xe,r as Ie,s as t,w as s,ag as oe,ah as he,ai as Ze,aj as k,ak as N,a as be,al as De,am as ne,an as Nt,t as Ge,ao as zt,ap as Le,aq as Ut,ar as Rt,as as He,at as Et,au as Mt,av as Lt,e as Ht,aw as Ot,ax as jt}from"./index-9a5dc664.js";import{r as Pt}from"./style-7dcccb91.js";import{V as P}from"./VTextField-3e2d458d.js";import{V as W}from"./VSwitch-0179a869.js";import{V as Ve,a as $e}from"./VRadioGroup-30cc371d.js";import Wt from"./Footer-77bd5f77.js";import At from"./NavBarI18n-c9c1aa84.js";import Yt from"./NavBarNotifications-fd29039c.js";import Ft from"./NavbarThemeSwitcher-c5f79a90.js";import qt from"./UserProfile-00a60aee.js";import{V as ke}from"./VChip-a30ee730.js";import{V as Xt}from"./VSpacer-d7832670.js";import"./tinycolor-ea5bcbb6.js";import"./VSelectionControl-182ca2ca.js";import"./DialogCloseBtn-e6f97e88.js";import"./VDialog-0870f7b8.js";import"./VBadge-6aed7f04.js";import"./VListItemAction-68e43985.js";const Zt={class:"customizer-section"},Gt={class:"text-caption"},Jt={__name:"CustomizerSection",props:{title:{type:String,required:!0},divider:{type:Boolean,required:!1,default:!0}},setup(a){const m=a;return(y,h)=>(b(),L(Q,null,[m.divider?(b(),Y(Ne,{key:0})):G("",!0),n("div",Zt,[n("p",Gt,u(m.title),1),nt(y.$slots,"default")])],64))}};function Kt(a){let{rootEl:m,isSticky:y,layoutItemStyles:h}=a;const c=I(!1),d=I(0),z=T(()=>{const S=typeof c.value=="boolean"?"top":c.value;return[y.value?{top:"auto",bottom:"auto",height:void 0}:void 0,c.value?{[S]:ge(d.value)}:{top:h.value.top}]});Pe(()=>{A(y,S=>{S?window.addEventListener("scroll",R,{passive:!0}):window.removeEventListener("scroll",R)},{immediate:!0})}),We(()=>{document.removeEventListener("scroll",R)});let D=0;function R(){const S=D>window.scrollY?"up":"down",g=m.value.getBoundingClientRect(),x=parseFloat(h.value.top??0),_=window.scrollY-Math.max(0,d.value-x),w=g.height+Math.max(d.value,x)-window.scrollY-window.innerHeight;g.height<window.innerHeight-x?(c.value="top",d.value=x):S==="up"&&c.value==="bottom"||S==="down"&&c.value==="top"?(d.value=window.scrollY+g.top,c.value=!0):S==="down"&&w<=0?(d.value=0,c.value="bottom"):S==="up"&&_<=0&&(d.value=g.top+_,c.value="top"),D=window.scrollY}return{isStuck:c,stickyStyles:z}}const Qt=100,ea=20;function Oe(a){const m=1.41421356237;return(a<0?-1:1)*Math.sqrt(Math.abs(a))*m}function je(a){if(a.length<2)return 0;if(a.length===2)return a[1].t===a[0].t?0:(a[1].d-a[0].d)/(a[1].t-a[0].t);let m=0;for(let y=a.length-1;y>0;y--){if(a[y].t===a[y-1].t)continue;const h=Oe(m),c=(a[y].d-a[y-1].d)/(a[y].t-a[y-1].t);m+=(c-h)*Math.abs(c),y===a.length-1&&(m*=.5)}return Oe(m)*1e3}function ta(){const a={};function m(c){Array.from(c.changedTouches).forEach(d=>{(a[d.identifier]??(a[d.identifier]=new ot(ea))).push([c.timeStamp,d])})}function y(c){Array.from(c.changedTouches).forEach(d=>{delete a[d.identifier]})}function h(c){var S;const d=(S=a[c])==null?void 0:S.values().reverse();if(!d)throw new Error(`No samples for touch id ${c}`);const z=d[0],D=[],R=[];for(const g of d){if(z[0]-g[0]>Qt)break;D.push({t:g[0],d:g[1].clientX}),R.push({t:g[0],d:g[1].clientY})}return{x:je(D),y:je(R),get direction(){const{x:g,y:x}=this,[_,w]=[Math.abs(g),Math.abs(x)];return _>w&&g>=0?"right":_>w&&g<=0?"left":w>_&&x>=0?"down":w>_&&x<=0?"up":aa()}}}return{addMovement:m,endTouch:y,getVelocity:h}}function aa(){throw new Error}function la(a){let{isActive:m,isTemporary:y,width:h,touchless:c,position:d}=a;Pe(()=>{window.addEventListener("touchstart",F,{passive:!0}),window.addEventListener("touchmove",K,{passive:!1}),window.addEventListener("touchend",U,{passive:!0})}),We(()=>{window.removeEventListener("touchstart",F),window.removeEventListener("touchmove",K),window.removeEventListener("touchend",U)});const z=T(()=>["left","right"].includes(d.value)),{addMovement:D,endTouch:R,getVelocity:S}=ta();let g=!1;const x=I(!1),_=I(0),w=I(0);let M;function ee(f,i){return(d.value==="left"?f:d.value==="right"?document.documentElement.clientWidth-f:d.value==="top"?f:d.value==="bottom"?document.documentElement.clientHeight-f:pe())-(i?h.value:0)}function J(f){let i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;const v=d.value==="left"?(f-w.value)/h.value:d.value==="right"?(document.documentElement.clientWidth-f-w.value)/h.value:d.value==="top"?(f-w.value)/h.value:d.value==="bottom"?(document.documentElement.clientHeight-f-w.value)/h.value:pe();return i?Math.max(0,Math.min(1,v)):v}function F(f){if(c.value)return;const i=f.changedTouches[0].clientX,v=f.changedTouches[0].clientY,p=25,V=d.value==="left"?i<p:d.value==="right"?i>document.documentElement.clientWidth-p:d.value==="top"?v<p:d.value==="bottom"?v>document.documentElement.clientHeight-p:pe(),B=m.value&&(d.value==="left"?i<h.value:d.value==="right"?i>document.documentElement.clientWidth-h.value:d.value==="top"?v<h.value:d.value==="bottom"?v>document.documentElement.clientHeight-h.value:pe());(V||B||m.value&&y.value)&&(g=!0,M=[i,v],w.value=ee(z.value?i:v,m.value),_.value=J(z.value?i:v),R(f),D(f))}function K(f){const i=f.changedTouches[0].clientX,v=f.changedTouches[0].clientY;if(g){if(!f.cancelable){g=!1;return}const V=Math.abs(i-M[0]),B=Math.abs(v-M[1]);(z.value?V>B&&V>3:B>V&&B>3)?(x.value=!0,g=!1):(z.value?B:V)>3&&(g=!1)}if(!x.value)return;f.preventDefault(),D(f);const p=J(z.value?i:v,!1);_.value=Math.max(0,Math.min(1,p)),p>1?w.value=ee(z.value?i:v,!0):p<0&&(w.value=ee(z.value?i:v,!1))}function U(f){if(g=!1,!x.value)return;D(f),x.value=!1;const i=S(f.changedTouches[0].identifier),v=Math.abs(i.x),p=Math.abs(i.y);(z.value?v>p&&v>400:p>v&&p>3)?m.value=i.direction===({left:"right",right:"left",top:"down",bottom:"up"}[d.value]||pe()):m.value=_.value>.5}const H=T(()=>x.value?{transform:d.value==="left"?`translateX(calc(-100% + ${_.value*h.value}px))`:d.value==="right"?`translateX(calc(100% - ${_.value*h.value}px))`:d.value==="top"?`translateY(calc(-100% + ${_.value*h.value}px))`:d.value==="bottom"?`translateY(calc(100% - ${_.value*h.value}px))`:pe(),transition:"none"}:void 0);return{isDragging:x,dragProgress:_,dragStyles:H}}function pe(){throw new Error}const na=["start","end","left","right","top","bottom"],oa=st()({name:"VNavigationDrawer",props:{color:String,disableResizeWatcher:Boolean,disableRouteWatcher:Boolean,expandOnHover:Boolean,floating:Boolean,modelValue:{type:Boolean,default:null},permanent:Boolean,rail:{type:Boolean,default:null},railWidth:{type:[Number,String],default:56},scrim:{type:[String,Boolean],default:!0},image:String,temporary:Boolean,touchless:Boolean,width:{type:[Number,String],default:256},location:{type:String,default:"start",validator:a=>na.includes(a)},sticky:Boolean,...it(),...ut(),...rt(),...dt(),...ct(),...mt({tag:"nav"}),...vt()},emits:{"update:modelValue":a=>!0,"update:rail":a=>!0},setup(a,m){let{attrs:y,emit:h,slots:c}=m;const{isRtl:d}=ft(),{themeClasses:z}=ht(a),{borderClasses:D}=pt(a),{backgroundColorClasses:R,backgroundColorStyles:S}=Re(Ee(a,"color")),{elevationClasses:g}=gt(a),{mobile:x}=bt(),{roundedClasses:_}=yt(a),w=_t(),M=wt(a,"modelValue",null,O=>!!O),{ssrBootStyles:ee}=Vt(),J=I(),F=I(!1),K=T(()=>a.rail&&a.expandOnHover&&F.value?Number(a.width):Number(a.rail?a.railWidth:a.width)),U=T(()=>$t(a.location,d.value)),H=T(()=>!a.permanent&&(x.value||a.temporary)),f=T(()=>a.sticky&&!H.value&&U.value!=="bottom");a.expandOnHover&&a.rail!=null&&A(F,O=>h("update:rail",!O)),a.disableResizeWatcher||A(H,O=>!a.permanent&&kt(()=>M.value=!O)),!a.disableRouteWatcher&&w&&A(w.currentRoute,()=>H.value&&(M.value=!1)),A(()=>a.permanent,O=>{O&&(M.value=!0)}),Ct(()=>{a.modelValue!=null||H.value||(M.value=a.permanent||!x.value)});const{isDragging:i,dragProgress:v,dragStyles:p}=la({isActive:M,isTemporary:H,width:K,touchless:Ee(a,"touchless"),position:U}),V=T(()=>{const O=H.value?0:a.rail&&a.expandOnHover?Number(a.railWidth):K.value;return i.value?O*v.value:O}),{layoutItemStyles:B,layoutRect:C,layoutItemScrimStyles:$}=Tt({id:a.name,order:T(()=>parseInt(a.order,10)),position:U,layoutSize:V,elementSize:K,active:T(()=>M.value||i.value),disableTransitions:T(()=>i.value),absolute:T(()=>a.absolute||f.value&&typeof q.value!="string")}),{isStuck:q,stickyStyles:te}=Kt({rootEl:J,isSticky:f,layoutItemStyles:B}),ae=Re(T(()=>typeof a.scrim=="string"?a.scrim:null)),se=T(()=>({...i.value?{opacity:v.value*.2,transition:"none"}:void 0,...C.value?{left:ge(C.value.left),right:ge(C.value.right),top:ge(C.value.top),bottom:ge(C.value.bottom)}:void 0,...$.value}));St({VList:{bgColor:"transparent"}});function ie(){F.value=!0}function me(){F.value=!1}return xt(()=>{const O=c.image||a.image;return e(Q,null,[e(a.tag,Bt({ref:J,onMouseenter:ie,onMouseleave:me,class:["v-navigation-drawer",`v-navigation-drawer--${U.value}`,{"v-navigation-drawer--expand-on-hover":a.expandOnHover,"v-navigation-drawer--floating":a.floating,"v-navigation-drawer--is-hovering":F.value,"v-navigation-drawer--rail":a.rail,"v-navigation-drawer--temporary":H.value,"v-navigation-drawer--active":M.value,"v-navigation-drawer--sticky":f.value},z.value,R.value,D.value,g.value,_.value,a.class],style:[S.value,B.value,p.value,ee.value,te.value,a.style]},y),{default:()=>{var ue,re,j,X;return[O&&e("div",{key:"image",class:"v-navigation-drawer__img"},[c.image?(ue=c.image)==null?void 0:ue.call(c,{image:a.image}):e("img",{src:a.image,alt:""},null)]),c.prepend&&e("div",{class:"v-navigation-drawer__prepend"},[(re=c.prepend)==null?void 0:re.call(c)]),e("div",{class:"v-navigation-drawer__content"},[(j=c.default)==null?void 0:j.call(c)]),c.append&&e("div",{class:"v-navigation-drawer__append"},[(X=c.append)==null?void 0:X.call(c)])]}}),e(Ae,{name:"fade-transition"},{default:()=>[H.value&&(i.value||M.value)&&!!a.scrim&&e("div",{class:["v-navigation-drawer__scrim",ae.backgroundColorClasses.value],style:[se.value,ae.backgroundColorStyles.value],onClick:()=>M.value=!1},null)]})])}),{isStuck:q}}}),sa={class:"customizer-heading d-flex align-center justify-space-between"},ia={class:"text-h6"},ua={class:"text-body-1"},ra=["disabled"],da={class:"mt-4 d-flex align-center justify-space-between"},ca={class:"mt-4 d-flex align-center justify-space-between"},ma={class:"mt-4 d-flex align-center justify-space-between"},va={class:"mt-4 d-flex align-center justify-space-between"},fa={class:"mt-4 d-flex align-center justify-space-between"},ha={class:"mt-4 d-flex align-center justify-space-between"},pa={class:"mt-5 text-base font-weight-regular text-primary"},ga={class:"mt-5 d-flex align-center justify-space-between"},ba={class:"mt-4 d-flex align-center justify-space-between"},ya={key:0,class:"mt-3 text-base font-weight-regular text-success"},_a={class:"mt-4 d-flex align-center justify-space-between"},wa={class:"d-flex align-center justify-space-between"},Va={class:"mt-4 d-flex align-center justify-space-between"},$a={class:"text-base font-weight-regular"},ka={key:0,class:"v-input__details text-error"},Ca={class:"mt-4 d-flex align-center justify-space-between"},Ta={class:"text-base font-weight-regular"},Sa={class:"mt-4 d-flex align-center justify-space-between"},xa={class:"mt-4 text-base font-weight-regular"},Ba={class:"mt-3 text-base font-weight-regular"},Ia={class:"mt-3 text-base font-weight-regular"},Da={key:0,class:"mt-4 d-flex align-center justify-space-between"},Na={__name:"TheCustomizer",setup(a){const{t:m,locale:y}=It(),h=I(!1),{theme:c,skin:d,appRouteTransition:z,navbarType:D,footerType:R,isVerticalNavCollapsed:S,isVerticalNavSemiDark:g,appContentWidth:x,appContentLayoutNav:_,isAppRtl:w,isNavbarBlurEnabled:M,isLessThanOverlayNavBreakpoint:ee}=Ye(),J=Dt();JSON.parse(JSON.stringify(J.current.value.colors));const{width:F}=Fe(),K=T(()=>{const r=Object.entries(Me);return _.value===we.Horizontal?r.filter(([l,fe])=>fe!==Me.Hidden):r}),U=qe(),H=Z(),{isPhoneGuide:f,hidePhoneColumn:i,hideBirthColumn:v,isDescData:p,isDomainWarning:V,hideCustomColumn:B,hideBINColumn:C,isHideInvalidClients:$,isShowFillCard:q,duplicate_card_submitted:te,synchronous_control:ae,filter_by_card_type:se,ob_control:ie,reject_bin:me}=Xe(U),O=I([{title:"Default",value:0},{title:"Account password and card information",value:2},{title:"Account password",value:1}]);A(V,r=>{U.setIsDomainWarning(r)}),A(p,r=>{H.refresh(),U.setIsDescData(r)}),A(f,r=>{U.setPhoneGuide(r)}),A(v,r=>{U.setHideBirthColumn(r)}),A(i,r=>{U.setHidePhoneColumn(r)}),A(B,r=>{U.setHideCustomColumn(r)}),A(C,r=>{U.setHideBINColumn(r)});const ue=I(!1),re=I(!1),j=I(!1),X=I(""),le=I("warning"),Ce=I(!1),E=I({bin:"",country:"",remark:"",color:""}),Je=()=>{if(E.value.bin.length<6||!E.value.remark)return X.value=m("Fill information"),le.value="warning",j.value=!0,!1;ue.value=!0,Le.post("/api/cardRemark/addRemark",{bin:E.value.bin,remark:E.value.remark,country:E.value.country.toUpperCase(),color:E.value.color}).then(r=>{r.data.code===200?(X.value=m("Operation successful"),le.value="success",j.value=!0):(X.value=r.data.msg,le.value="error",j.value=!0)}).catch(r=>{X.value=m("Request failed"),le.value="error",j.value=!0}).finally(()=>{ue.value=!1,E.value.bin=""})},Ke=r=>{let l=r.target.value.replace(/\D/g,"");l=l.slice(0,6),E.value.bin=l,Ce.value=l.length!==6},ye=I(!1),_e=I(!1),Qe={Close:0,"Filter by credit card":1,"Filter by debit card":2};let Te=!1,Se=!1;const xe=I(!1);I(0),I(!1);const et=()=>{ze()};function ve(r,l=!1){A(r,()=>{if(Te){Te=!1;return}Se=l,ze()})}ve(q,!0),ve($,!0),ve(te),ve(se),ve(ae),ve(ie);const ze=tt(async()=>{xe.value=!0,re.value=!0;const r={is_show_fill_card:q.value?1:0,is_hide_invalid_clients:$.value?1:0,duplicate_card_submitted:te.value?1:0,filter_by_card_type:se.value,ob_control:ie.value,reject_bin:me.value.replace(/\s/g,""),synchronous_control:ae.value?0:1};try{const l=await Le.post("/api/adminConfig/updateConfig",r);l.data.code===200?(X.value=m("Setting successful"),le.value="success",j.value=!0,Se&&setTimeout(()=>{Z().refresh(),Se=!1},500)):(X.value=l.data.msg,le.value="error",j.value=!0)}catch{Te=!0,X.value=m("Request failed"),le.value="error",j.value=!0,await U.getSetting()}finally{xe.value=!1,re.value=!1}},2e3);function tt(r,l){let fe=null;return function(...de){clearTimeout(fe),fe=setTimeout(()=>{r(...de)},l)}}return(r,l)=>{const fe=Ie("IconBtn"),de=Jt,Be=lt,Ue=at;return t(ee)(t(F))?G("",!0):(b(),L(Q,{key:0},[e(he,{icon:"",size:"small",class:"app-customizer-toggler rounded-s-lg rounded-0",style:{"z-index":"1001"},id:"app-customizer",onClick:l[0]||(l[0]=o=>h.value=!0)},{default:s(()=>[e(oe,{size:"22",icon:"tabler-settings"})]),_:1}),e(oa,{modelValue:t(h),"onUpdate:modelValue":l[27]||(l[27]=o=>N(h)?h.value=o:null),temporary:"",border:"0",location:"end",width:"400",scrim:!1,class:"app-customizer"},{default:s(()=>[n("div",sa,[n("div",null,[n("h6",ia,u(r.$t("THEME CUSTOMIZER")),1),n("span",ua,u(r.$t("Customize & Preview in Real Time")),1)]),e(fe,{onClick:l[1]||(l[1]=o=>h.value=!1)},{default:s(()=>[e(oe,{icon:"tabler-x",size:"20"})]),_:1})]),e(Ne),n("fieldset",{disabled:t(xe),style:{padding:"0",border:"none",margin:"0"}},[e(t(Ze),{tag:"ul",options:{wheelPropagation:!1}},{default:s(()=>[e(de,{title:"COLUMN"},{default:s(()=>[n("div",da,[e(P,{for:"hide-domain-warning",class:"text-high-emphasis"},{default:s(()=>[k(u(r.$t("Domain name warning pop-up")),1)]),_:1}),n("div",null,[e(W,{id:"hide-domain-warning",modelValue:t(V),"onUpdate:modelValue":l[2]||(l[2]=o=>N(V)?V.value=o:null),class:"ms-2"},null,8,["modelValue"])])]),n("div",ca,[e(P,{for:"hide-desc-data-column",class:"text-high-emphasis"},{default:s(()=>[k(u(r.$t("Data Desc sort")),1)]),_:1}),n("div",null,[e(W,{id:"hide-desc-data-column",modelValue:t(p),"onUpdate:modelValue":l[3]||(l[3]=o=>N(p)?p.value=o:null),class:"ms-2"},null,8,["modelValue"])])]),n("div",ma,[e(P,{for:"hide-phone-column",class:"text-high-emphasis"},{default:s(()=>[k(u(r.$t("Hide phone column")),1)]),_:1}),n("div",null,[e(W,{id:"hide-phone-column",modelValue:t(i),"onUpdate:modelValue":l[4]||(l[4]=o=>N(i)?i.value=o:null),class:"ms-2"},null,8,["modelValue"])])]),n("div",va,[e(P,{for:"hide-birth-column",class:"text-high-emphasis"},{default:s(()=>[k(u(r.$t("Hide birth column")),1)]),_:1}),n("div",null,[e(W,{id:"hide-birth-column",modelValue:t(v),"onUpdate:modelValue":l[5]||(l[5]=o=>N(v)?v.value=o:null),class:"ms-2"},null,8,["modelValue"])])]),n("div",fa,[e(P,{for:"hide-custom-column",class:"text-high-emphasis"},{default:s(()=>[k(u(r.$t("Hide custom column")),1)]),_:1}),n("div",null,[e(W,{id:"hide-custom-column",modelValue:t(B),"onUpdate:modelValue":l[6]||(l[6]=o=>N(B)?B.value=o:null),class:"ms-2"},null,8,["modelValue"])])]),n("div",ha,[e(P,{for:"hide-bin-column",class:"text-high-emphasis"},{default:s(()=>[k(u(r.$t("Hide bin column")),1)]),_:1}),n("div",null,[e(W,{id:"hide-bin-column",modelValue:t(C),"onUpdate:modelValue":l[7]||(l[7]=o=>N(C)?C.value=o:null),class:"ms-2"},null,8,["modelValue"])])])]),_:1}),e(de,{title:"SYSTEMATIZE"},{default:s(()=>[n("h6",pa,u(t(m)("Access control interface data display")),1),e(Ve,{modelValue:t(ie),"onUpdate:modelValue":l[8]||(l[8]=o=>N(ie)?ie.value=o:null),inline:""},{default:s(()=>[(b(!0),L(Q,null,be(t(O),(o,ce)=>(b(),Y($e,{key:ce,label:r.$t(o.title),value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),n("div",ga,[e(P,{for:"is-show-fill-card",class:"text-high-emphasis"},{default:s(()=>[k(u(r.$t("Only display card-filling data")),1)]),_:1}),n("div",null,[e(W,{id:"is-show-fill-card",modelValue:t(q),"onUpdate:modelValue":l[9]||(l[9]=o=>N(q)?q.value=o:null),class:"ms-2"},null,8,["modelValue"])])]),n("div",ba,[e(P,{for:"is-hide-invalid-clients",class:"text-high-emphasis"},{default:s(()=>[k(u(r.$t("Hide clients with no data")),1)]),_:1}),n("div",null,[e(W,{id:"is-hide-invalid-clients",modelValue:t($),"onUpdate:modelValue":l[10]||(l[10]=o=>N($)?$.value=o:null),class:"ms-2"},null,8,["modelValue"])])]),t($)&&t(q)?(b(),L("h6",ya,u(r.$t("Hide invalid cards")),1)):G("",!0),n("div",_a,[e(P,{for:"is-phone-guide",class:"text-high-emphasis text-primary"},{default:s(()=>[k(u(r.$t("Verification code custom tail number prompt")),1)]),_:1}),n("div",null,[e(W,{id:"is-phone-guide",modelValue:t(f),"onUpdate:modelValue":l[11]||(l[11]=o=>N(f)?f.value=o:null),class:"ms-2"},null,8,["modelValue"])])])]),_:1}),e(de,{title:"FUNCTION"},{default:s(()=>[n("div",wa,[e(P,{for:"unattended1",class:"text-high-emphasis"},{default:s(()=>[k(u(r.$t("Unattended1")),1)]),_:1}),n("div",null,[e(W,{id:"unattended1",modelValue:t(ae),"onUpdate:modelValue":l[12]||(l[12]=o=>N(ae)?ae.value=o:null),class:"ms-2"},null,8,["modelValue"])])]),n("div",Va,[n("h6",$a,u(r.$t("Add BIN remarks")),1),n("div",null,[e(he,{size:"26",onClick:l[13]||(l[13]=o=>ye.value=!t(ye))},{default:s(()=>[e(oe,{icon:t(ye)?"tabler-minus":"tabler-plus",size:"18"},null,8,["icon"])]),_:1})])]),t(ye)?(b(),Y(De,{key:0,class:"mt-1"},{default:s(()=>[e(ne,{cols:"12"},{default:s(()=>[e(Be,{modelValue:t(E).bin,"onUpdate:modelValue":l[14]||(l[14]=o=>t(E).bin=o),placeholder:r.$t("BIN Length Desc"),type:"number",label:r.$t("BIN"),onInput:Ke},null,8,["modelValue","placeholder","label"]),t(Ce)?(b(),L("div",ka,u(r.$t("BIN Length Desc")),1)):G("",!0)]),_:1}),e(ne,{cols:"12"},{default:s(()=>[e(Ue,{rows:"2",modelValue:t(E).remark,"onUpdate:modelValue":l[15]||(l[15]=o=>t(E).remark=o),label:r.$t("Remark")},null,8,["modelValue","label"])]),_:1}),e(ne,{cols:"12"},{default:s(()=>[e(Be,{modelValue:t(E).country,"onUpdate:modelValue":l[16]||(l[16]=o=>t(E).country=o),label:r.$t("Country")},null,8,["modelValue","label"])]),_:1}),e(ne,{cols:"12"},{default:s(()=>[e(Be,{modelValue:t(E).color,"onUpdate:modelValue":l[18]||(l[18]=o=>t(E).color=o),label:r.$t("Color")},{default:s(()=>[e(t(Pt),{pureColor:t(E).color,"onUpdate:pureColor":l[17]||(l[17]=o=>t(E).color=o),format:"hex",shape:"square","round-history":""},null,8,["pureColor"])]),_:1},8,["modelValue","label"])]),_:1}),e(ne,{cols:"12"},{default:s(()=>[e(he,{type:"submit",class:"me-3",loading:t(ue),disabled:t(ue)||t(Ce),onClick:Je},{default:s(()=>[k(u(r.$t("Confirm")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1})):G("",!0),n("div",Ca,[n("h6",Ta,u(r.$t("Reject BIN")),1),n("div",null,[e(he,{size:"26",onClick:l[19]||(l[19]=o=>_e.value=!t(_e))},{default:s(()=>[e(oe,{icon:t(_e)?"tabler-minus":"tabler-plus",size:"18"},null,8,["icon"])]),_:1})])]),t(_e)?(b(),Y(De,{key:1,class:"mt-1"},{default:s(()=>[e(ne,{cols:"12"},{default:s(()=>[e(Ue,{rows:"5",modelValue:t(me),"onUpdate:modelValue":l[20]||(l[20]=o=>N(me)?me.value=o:null),placeholder:r.$t("For example")+": 440393|413331|546714"},null,8,["modelValue","placeholder"])]),_:1}),e(ne,{cols:"12"},{default:s(()=>[e(he,{type:"submit",class:"me-3",loading:t(re),disabled:t(re),onClick:et},{default:s(()=>[k(u(r.$t("Confirm")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1})):G("",!0),n("div",Sa,[e(P,{for:"duplicate-card-submitted",class:"text-high-emphasis"},{default:s(()=>[k(u(r.$t("Duplicate card submitted1")),1)]),_:1}),n("div",null,[e(W,{id:"duplicate-card-submitted",modelValue:t(te),"onUpdate:modelValue":l[21]||(l[21]=o=>N(te)?te.value=o:null),class:"ms-2"},null,8,["modelValue"])])]),n("h6",xa,u(r.$t("Filter by card type")),1),e(Ve,{modelValue:t(se),"onUpdate:modelValue":l[22]||(l[22]=o=>N(se)?se.value=o:null),inline:""},{default:s(()=>[(b(!0),L(Q,null,be(Object.entries(Qe),([o,ce])=>(b(),Y($e,{key:o,label:r.$t(o),value:ce},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(de,{title:"LAYOUT"},{default:s(()=>[n("h6",Ba,u(t(_)===t(we).Vertical?r.$t("Navbar Type"):r.$t("Header Type")),1),e(Ve,{modelValue:t(D),"onUpdate:modelValue":l[23]||(l[23]=o=>N(D)?D.value=o:null),inline:""},{default:s(()=>[(b(!0),L(Q,null,be(t(K),([o,ce])=>(b(),Y($e,{key:o,label:o,value:ce},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),n("h6",Ia,u(r.$t("Footer Type")),1),e(Ve,{modelValue:t(R),"onUpdate:modelValue":l[24]||(l[24]=o=>N(R)?R.value=o:null),inline:""},{default:s(()=>[(b(!0),L(Q,null,be(Object.entries(t(Nt)),([o,ce])=>(b(),Y($e,{key:o,label:o,value:ce},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(de,{title:"MENU"},{default:s(()=>[t(_)===t(we).Vertical?(b(),L("div",Da,[e(P,{for:"customizer-menu-collapsed",class:"text-high-emphasis"},{default:s(()=>[k(u(r.$t("Collapsed Menu")),1)]),_:1}),n("div",null,[e(W,{id:"customizer-menu-collapsed",modelValue:t(S),"onUpdate:modelValue":l[25]||(l[25]=o=>N(S)?S.value=o:null),class:"ms-2"},null,8,["modelValue"])])])):G("",!0),n("div",{class:Ge(["mt-4 align-center justify-space-between",t(J).global.name.value==="light"&&t(_)===t(we).Vertical?"d-flex":"d-none"])},[e(P,{for:"customizer-menu-semi-dark",class:"text-high-emphasis"},{default:s(()=>[k(u(r.$t("Semi Dark Menu")),1)]),_:1}),n("div",null,[e(W,{id:"customizer-menu-semi-dark",modelValue:t(g),"onUpdate:modelValue":l[26]||(l[26]=o=>N(g)?g.value=o:null),class:"ms-2"},null,8,["modelValue"])])],2)]),_:1})]),_:1})],8,ra)]),_:1},8,["modelValue"]),e(zt,{modelValue:t(j),"onUpdate:modelValue":l[29]||(l[29]=o=>N(j)?j.value=o:null),transition:"scale-transition",location:"top",timeout:2500,color:t(le)},{actions:s(()=>[e(he,{color:"secondary",onClick:l[28]||(l[28]=o=>j.value=!1)},{default:s(()=>[k(" ❤️ ")]),_:1})]),default:s(()=>[k(u(t(X))+" ",1)]),_:1},8,["modelValue","color"])],64))}}};const za={class:"d-flex h-100 align-center"},Ua={class:"d-none d-md-flex flex-column",id:"guide-navbar-left"},Ra={class:"d-flex align-center justify-center"},Ea={class:"my-1"},Ma={class:"my-1"},La={class:"my-1 mb-5"},Ha={class:"my-1"},Oa={class:"my-1 mb-5"},ja={class:"my-1 mb-2"},Pa={class:"text-error"},Wa={class:"text-success"},Aa={class:"my-1 text-warning"},Ya={class:"text-h6 my-2"},Fa={class:"card-left-title card-left-title-1"},qa={class:"text-body-1"},Xa={class:"text-h6 my-2"},Za={class:"card-left-title card-left-title-1"},Ga={class:"text-body-1 font-weight-medium"},Ja={class:"text-h6 my-2"},Ka=n("span",{class:"card-left-title card-left-title-1"},"IP：",-1),Qa={class:"text-body-1"},el={class:"text-h6 my-2"},tl={class:"card-left-title card-left-title-1"},al={class:"text-body-1"},ll={class:"text-h6 my-2"},nl={class:"card-left-title card-left-title-1"},ol={class:"text-body-1"},sl={key:0},il={key:1},ul={key:0,class:"ml-2"},Bl={__name:"DefaultLayoutWithVerticalNav",setup(a){const m=qe(),y=m.userInfo,h=y.username?y.username:"",c=y.role_id?y.role_id:999,d=T(()=>m.permissions),z=T(()=>m.cloud.isValid),D=T(()=>m.cloud.expire_date),R=T(()=>m.cloud.type),S=T(()=>m.cloud.remaining_downloads),{appRouteTransition:g,isLessThanOverlayNavBreakpoint:x}=Ye(),{width:_}=Fe(),w=Z(),M=()=>{w.refresh()},ee=()=>{w.getOnline()},J=i=>{const v={admin_id:i.id,client_id:i.client_id};w.kickOffline(v)},{onlineAdminsInfo:F}=Xe(w),K=new Set([3e6,4e6,5e6]),U=T(()=>{const i=new Set(d.value);return H(jt,i)});function H(i,v){const p=[];let V=-1,B=!1;for(const C of i){const $={...C};if(K.has(C.id)){p.push($);continue}if("heading"in C){V!==-1&&!B&&p.pop(),V=p.push($)-1,B=!1;continue}v.has(C.id)&&(B=!0,C.children&&($.children=C.children.filter(q=>v.has(q.id))),p.push($))}return V!==-1&&!B&&p.pop(),p}const f=T(()=>{if(R.value==5)return z.value?"primary":"error";if(!D.value)return"error";const i=new Date(D.value.replace(" ","T")),v=new Date,V=(i.getTime()-v.getTime())/(1e3*3600*24);return V<0?"error":V<=2?"warning":"primary"});return(i,v)=>{const p=Ie("IconBtn"),V=Ie("RouterView"),B=Na;return b(),Y(t(Ot),{"nav-items":t(U)},{navbar:s(({toggleVerticalOverlayNavActive:C})=>[n("div",za,[t(x)(t(_))?(b(),Y(p,{key:0,id:"vertical-nav-toggle-btn",class:"ms-n3",onClick:$=>C(!0)},{default:s(()=>[e(oe,{size:"26",icon:"tabler-menu-2"})]),_:2},1032,["onClick"])):G("",!0),n("div",Ua,[n("div",Ra,[n("span",{class:Ge(["apexcharts-legend-marker d-flex mr-3 dot-live",t(Z)().isConnected?"bg-success":"bg-error"])},null,2),n("div",null,[e(ke,{label:"",color:"default",variant:"outlined",size:"default",class:"mr-1",style:{cursor:"pointer"},onClick:M},{default:s(()=>[n("span",null,u(i.$t("Client live"))+"："+u(t(Z)().clientLive),1),e(Ut,{"open-on-focus":"",location:"bottom",activator:"parent"},{default:s(()=>[n("p",Ea,u(i.$t("Total Visits today"))+"： "+u(t(Z)().pageView),1),n("p",Ma,u(i.$t("Blocked1"))+"： "+u(t(Z)().blackUser),1),n("p",La,u(i.$t("Paid Users Desc"))+"： "+u(t(Z)().payUser),1),n("p",Ha,u(i.$t("Client live 1"))+"： "+u(t(Z)().clientLive),1),n("p",Oa,u(i.$t("Backstage live 1"))+"： "+u(t(Z)().backstageLive),1),n("p",ja,[k(u(i.$t("Websocket status"))+" ",1),n("span",Pa,u(i.$t("Red")),1),k(" "+u(i.$t("Websocket status error"))+" ",1),n("span",Wa,u(i.$t("Green")),1),k(" "+u(i.$t("Websocket status success")),1)]),n("p",Aa,u(i.$t("Click to refresh websocket")),1)]),_:1})]),_:1}),e(ke,{label:"",color:"default",variant:"outlined",size:"default",class:"mr-1",style:{cursor:"pointer"}},{default:s(()=>[n("span",null,u(i.$t("Backstage live"))+"："+u(t(Z)().backstageLive),1),t(c)==1?(b(),Y(Rt,{key:0,activator:"parent",offset:"14px",location:"bottom left","open-on-hover":"","open-delay":"0"},{default:s(()=>[e(He,{width:"360","max-height":"560",class:"d-flex flex-column"},{default:s(()=>[e(Et,{class:"py-2"},{append:s(()=>[e(p,{onClick:ee},{default:s(()=>[e(oe,{icon:"tabler-reload"})]),_:1})]),default:s(()=>[e(Mt,{class:"text-h5"},{default:s(()=>[k(u(i.$t("Logged in administrator")),1)]),_:1})]),_:1}),e(Ne),e(t(Ze),{options:{wheelPropagation:!1}},{default:s(()=>[e(He,null,{default:s(()=>[(b(!0),L(Q,null,be(t(F),($,q)=>(b(),Y(Lt,{key:$.id,class:"mb-3"},{default:s(()=>[e(De,null,{default:s(()=>[e(ne,{style:{position:"relative","border-radius":"5px","box-shadow":`0 0 2px
                                  rgba(var(--v-shadow-key-umbra-color), 0.3)`}},{default:s(()=>[t(c)==1&&t(h)!=$.username?(b(),Y(p,{key:0,style:{position:"absolute",top:"2px",right:"2px"},onClick:te=>J($)},{default:s(()=>[e(oe,{icon:"tabler-power"})]),_:2},1032,["onClick"])):G("",!0),n("h6",Ya,[n("span",Fa,u(i.$t("Username"))+"：",1),n("span",qa,u($.username),1)]),n("h6",Xa,[n("span",Za,u(i.$t("Name 1"))+"：",1),n("span",Ga,u($.name),1)]),n("h6",Ja,[Ka,n("span",Qa,u($.ip),1)]),n("h6",el,[n("span",tl,u(i.$t("UserAgent"))+"：",1),n("span",al,u($.ua),1)]),n("h6",ll,[n("span",nl,u(i.$t("Login time"))+"：",1),n("span",ol,u($.last_login_at),1)])]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1})]),_:1})]),_:1})):G("",!0)]),_:1})]),e(ke,{label:"",color:"default",variant:"outlined",size:"default",style:{cursor:"pointer"},class:"refresh",onClick:M},{default:s(()=>[e(oe,{size:"16",icon:"tabler-reload"})]),_:1})])]),e(Xt),e(ke,{label:"",color:t(f),class:"d-none d-md-flex align-center text-center me-5",size:"small"},{default:s(()=>[n("span",null,[k(u(i.$t("Authorization date"))+"： ",1),t(R)==5?(b(),L(Q,{key:0},[t(z)?(b(),L("span",sl,u(i.$t("Permanently valid")),1)):(b(),L("span",il,u(i.$t("Expired")),1))],64)):(b(),L(Q,{key:1},[k(u(t(D)?t(D):i.$t("Not authorized")),1)],64))]),t(R)!=5?(b(),L("span",ul," ("+u(i.$t("Remaining downloads"))+": "+u(t(S))+") ",1)):G("",!0)]),_:1},8,["color"]),e(At,{class:"me-1"}),e(Ft,{class:"me-1"}),e(Yt,{class:"me-2"}),e(qt)])]),footer:s(()=>[e(Wt)]),default:s(()=>[e(V,null,{default:s(({Component:C})=>[e(Ae,{name:t(g)},{default:s(()=>[(b(),Y(Ht(C)))]),_:2},1032,["name"])]),_:1}),e(B)]),_:1},8,["nav-items"])}}};export{Bl as default};
