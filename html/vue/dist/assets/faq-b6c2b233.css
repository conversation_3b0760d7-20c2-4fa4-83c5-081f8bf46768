.search-header{padding:4rem!important;background-size:cover!important}.search-header-input{border-radius:.25rem!important;border-radius:.375rem!important;background-color:rgb(var(--v-theme-surface));max-inline-size:40.125rem!important}.search-header-input .v-field__prepend-inner i{inset-block-start:3px!important}@media (max-width: 37.5rem){.search-header{padding:1.5rem!important}}.faq-v-window .v-window__container{z-index:0}.v-expansion-panel{background-color:rgb(var(--v-theme-surface));color:rgba(var(--v-theme-on-surface),var(--v-medium-emphasis-opacity))}.v-expansion-panel:not(:first-child):after{border-color:rgba(var(--v-border-color),var(--v-border-opacity))}.v-expansion-panel--disabled .v-expansion-panel-title{color:rgba(var(--v-theme-on-surface),.26)}.v-expansion-panel--disabled .v-expansion-panel-title .v-expansion-panel-title__overlay{opacity:.4615384615}.v-expansion-panels{display:flex;flex-wrap:wrap;justify-content:center;list-style-type:none;padding:0;width:100%;position:relative;z-index:1}.v-expansion-panels:not(.v-expansion-panels--variant-accordion)>:not(:first-child):not(:last-child):not(.v-expansion-panel--active):not(.v-expansion-panel--before-active){border-bottom-left-radius:0!important;border-bottom-right-radius:0!important}.v-expansion-panels:not(.v-expansion-panels--variant-accordion)>:not(:first-child):not(:last-child):not(.v-expansion-panel--active):not(.v-expansion-panel--after-active){border-top-left-radius:0!important;border-top-right-radius:0!important}.v-expansion-panels:not(.v-expansion-panels--variant-accordion)>:first-child:not(:last-child):not(.v-expansion-panel--active):not(.v-expansion-panel--before-active){border-bottom-left-radius:0!important;border-bottom-right-radius:0!important}.v-expansion-panels:not(.v-expansion-panels--variant-accordion)>:last-child:not(:first-child):not(.v-expansion-panel--active):not(.v-expansion-panel--after-active){border-top-left-radius:0!important;border-top-right-radius:0!important}.v-expansion-panels--variant-accordion>:first-child{border-bottom-left-radius:0!important;border-bottom-right-radius:0!important}.v-expansion-panels--variant-accordion>:last-child{border-top-left-radius:0!important;border-top-right-radius:0!important}.v-expansion-panels--variant-accordion>:last-child .v-expansion-panel-title--active{border-bottom-left-radius:initial;border-bottom-right-radius:initial}.v-expansion-panels--variant-accordion>:not(:first-child):not(:last-child){border-radius:0!important}.v-expansion-panels--variant-accordion .v-expansion-panel-title__overlay{transition:.3s border-radius cubic-bezier(.4,0,.2,1)}.v-expansion-panel{flex:1 0 100%;max-width:100%;position:relative;transition:.3s all cubic-bezier(.4,0,.2,1);transition-property:margin-top,border-radius,border,max-width;border-radius:6px}.v-expansion-panel:not(:first-child):after{border-top-style:solid;border-top-width:thin;content:"";left:0;position:absolute;right:0;top:0;transition:.3s opacity cubic-bezier(.4,0,.2,1)}.v-expansion-panel--disabled .v-expansion-panel-title{pointer-events:none}.v-expansion-panel--active:not(:first-child),.v-expansion-panel--active+.v-expansion-panel{margin-top:16px}.v-expansion-panel--active:not(:first-child):after,.v-expansion-panel--active+.v-expansion-panel:after{opacity:0}.v-expansion-panel--active>.v-expansion-panel-title{border-bottom-left-radius:0;border-bottom-right-radius:0;min-height:48px}.v-expansion-panel__shadow{position:absolute;top:0;left:0;width:100%;height:100%;box-shadow:0 2px 6px rgba(var(--v-shadow-key-umbra-color),.14),0 0 transparent,0 0 transparent;border-radius:inherit;z-index:-1}.v-expansion-panel-title{align-items:center;text-align:start;border-radius:inherit;display:flex;font-size:.9375rem;line-height:1;min-height:48px;outline:none;padding:15px 18px;position:relative;transition:.3s min-height cubic-bezier(.4,0,.2,1);width:100%;justify-content:space-between}.v-expansion-panel-title:hover>.v-expansion-panel-title__overlay{opacity:calc(var(--v-hover-opacity) * var(--v-theme-overlay-multiplier))}.v-expansion-panel-title:focus-visible>.v-expansion-panel-title__overlay{opacity:calc(var(--v-focus-opacity) * var(--v-theme-overlay-multiplier))}@supports not selector(:focus-visible){.v-expansion-panel-title:focus>.v-expansion-panel-title__overlay{opacity:calc(var(--v-focus-opacity) * var(--v-theme-overlay-multiplier))}}.v-expansion-panel-title--active>.v-expansion-panel-title__overlay,.v-expansion-panel-title[aria-haspopup=menu][aria-expanded=true]>.v-expansion-panel-title__overlay{opacity:calc(var(--v-activated-opacity) * var(--v-theme-overlay-multiplier))}.v-expansion-panel-title--active:hover>.v-expansion-panel-title__overlay,.v-expansion-panel-title[aria-haspopup=menu][aria-expanded=true]:hover>.v-expansion-panel-title__overlay{opacity:calc((var(--v-activated-opacity) + var(--v-hover-opacity)) * var(--v-theme-overlay-multiplier))}.v-expansion-panel-title--active:focus-visible>.v-expansion-panel-title__overlay,.v-expansion-panel-title[aria-haspopup=menu][aria-expanded=true]:focus-visible>.v-expansion-panel-title__overlay{opacity:calc((var(--v-activated-opacity) + var(--v-focus-opacity)) * var(--v-theme-overlay-multiplier))}@supports not selector(:focus-visible){.v-expansion-panel-title--active:focus>.v-expansion-panel-title__overlay,.v-expansion-panel-title[aria-haspopup=menu][aria-expanded=true]:focus>.v-expansion-panel-title__overlay{opacity:calc((var(--v-activated-opacity) + var(--v-focus-opacity)) * var(--v-theme-overlay-multiplier))}}.v-expansion-panel-title--active:before{opacity:.12}.v-expansion-panel-title__overlay{position:absolute;top:0;left:0;width:100%;height:100%;background-color:currentColor;border-radius:inherit;opacity:0}.v-expansion-panel-title__icon{display:inline-flex;margin-bottom:-4px;margin-top:-4px;-webkit-user-select:none;user-select:none;margin-inline-start:auto}.v-expansion-panel-text{display:flex}.v-expansion-panel-text__wrapper{padding:0 18px 12px;flex:1 1 auto;max-width:100%}.v-expansion-panels--variant-accordion>.v-expansion-panel{margin-top:0}.v-expansion-panels--variant-accordion>.v-expansion-panel:after{opacity:1}.v-expansion-panels--variant-popout>.v-expansion-panel{max-width:calc(100% - 32px)}.v-expansion-panels--variant-popout>.v-expansion-panel--active{max-width:calc(100% + 16px)}.v-expansion-panels--variant-inset>.v-expansion-panel{max-width:100%}.v-expansion-panels--variant-inset>.v-expansion-panel--active{max-width:calc(100% - 32px)}
