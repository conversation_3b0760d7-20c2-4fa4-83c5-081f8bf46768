import{_ as ta,p as la,V as aa}from"./VPagination-964310e8.js";import{g as oa,h as na,e as Jt,V as sa,m as ia,b as ra,u as ua,a as el,f as da,c as ca,d as pa}from"./VTextField-3e2d458d.js";import{y as Aa,D as ma,z as va,C as fa}from"./VDataTable-d690b508.js";import{I as Bt,aZ as ba,aS as ga,aT as fl,l as c,a0 as bt,B as G,ce as _a,aU as ya,c3 as bl,E as ze,a3 as ut,a7 as gl,q as e,F as be,ar as ha,a8 as Qe,az as _l,aA as gt,bs as wa,aj as w,bL as yl,d as At,bl as tl,o as m,c as V,s as t,b as O,A as k,b5 as ka,a as Ye,w as r,p as Va,b6 as ll,b7 as al,t as _t,bi as Ca,bj as xa,aK as $a,n as u,ag as Pe,y as d,ak as X,b2 as Ut,D as Et,G as Sa,T as Pt,bV as Da,cs as za,aG as Ia,ct as ol,a$ as Ua,bO as Pa,aJ as Ma,bv as hl,ad as lt,a9 as wl,ap as Ce,af as La,aF as vt,am as I,as as Se,av as W,aE as Ra,ah as B,cu as nl,ao as kl,al as $e,z as We,aq as sl,b0 as Vl,aN as Ba,aO as Ea,r as Ta,cp as Fa}from"./index-9a5dc664.js";import{V as Cl}from"./VChip-a30ee730.js";import{_ as xl}from"./AppTextField-8c148b8f.js";import{_ as Oa}from"./AppTextarea-36c843b3.js";import{d as Na,e as ja,b as $l,c as ct,a as qa,V as Ya}from"./VWindowItem-09058fe5.js";import{_ as Sl}from"./DialogCloseBtn-e6f97e88.js";import{_ as Qa}from"./ErrorHeader-d0ed5b30.js";import{e as Ha}from"./index-f0b62869.js";import{u as Ga,m as Ka,a as Wa}from"./misc-mask-light-eca946dc.js";import{p as Xa}from"./404-aa4980a7.js";import{u as Za}from"./index-9465fde1.js";import{x as Ja}from"./xterm-8ae096f4.js";import{v as eo,p as to,a as lo,b as ao,c as oo,d as no,V as pe}from"./filepond-plugin-image-preview.esm-68bdc77d.js";import{V as ft}from"./VSwitch-0179a869.js";import{V as Ee}from"./VDialog-0870f7b8.js";import{V as so}from"./VSpacer-d7832670.js";import{V as io}from"./VDataTableServer-26da2b3a.js";import{b as il}from"./route-block-83d24a4e.js";import"./VSelectionControl-182ca2ca.js";function ro(n,x,p){if(x==null)return n;if(Array.isArray(x))throw new Error("Multiple matches is not implemented");return typeof x=="number"&&~x?e(be,null,[e("span",{class:"v-autocomplete__unmask"},[n.substr(0,x)]),e("span",{class:"v-autocomplete__mask"},[n.substr(x,p)]),e("span",{class:"v-autocomplete__unmask"},[n.substr(x+p)])]):n}const uo=Bt()({name:"VAutocomplete",props:{search:String,...Aa({filterKeys:["title"]}),...ma(),...ba(oa({modelValue:null}),["validationValue","dirty","appendInnerIcon"]),...ga({transition:!1})},emits:{"update:focused":n=>!0,"update:search":n=>!0,"update:modelValue":n=>!0,"update:menu":n=>!0},setup(n,x){let{slots:p}=x;const{t:y}=fl(),U=c(),j=c(!1),S=c(!0),M=c(),v=bt(n,"menu"),f=G({get:()=>v.value,set:C=>{var Y;v.value&&!C&&((Y=M.value)!=null&&Y.ΨopenChildren)||(v.value=C)}}),b=c(-1),H=G(()=>{var C;return(C=U.value)==null?void 0:C.color}),{items:Z,transformIn:De,transformOut:E}=_a(n),{textColorClasses:Me,textColorStyles:Le}=ya(H),q=bt(n,"search",""),ne=bt(n,"modelValue",[],C=>De(bl(C)),C=>{const Y=E(C);return n.multiple?Y:Y[0]??null}),Ae=na(),{filteredItems:ge,getMatches:ye}=va(n,Z,G(()=>S.value?void 0:q.value)),me=G(()=>ne.value.map(C=>Z.value.find(Y=>n.valueComparator(Y.value,C.value))||C)),L=G(()=>n.hideSelected?ge.value.filter(C=>!me.value.some(Y=>Y.value===C.value)):ge.value),K=G(()=>me.value.map(C=>C.props.value)),$=G(()=>me.value[b.value]),D=c();function P(C){n.openOnClear&&(f.value=!0),q.value=""}function ie(){n.hideNoData&&!Z.value.length||n.readonly||Ae!=null&&Ae.isReadonly.value||(f.value=!0)}function te(C){var ke,T,o,z;if(n.readonly||Ae!=null&&Ae.isReadonly.value)return;const Y=U.value.selectionStart,le=K.value.length;if((b.value>-1||["Enter","ArrowDown","ArrowUp"].includes(C.key))&&C.preventDefault(),["Enter","ArrowDown"].includes(C.key)&&(f.value=!0),["Escape"].includes(C.key)&&(f.value=!1),["Enter","Escape","Tab"].includes(C.key)&&(S.value=!0),C.key==="ArrowDown"?(ke=D.value)==null||ke.focus("next"):C.key==="ArrowUp"&&((T=D.value)==null||T.focus("prev")),!!n.multiple){if(["Backspace","Delete"].includes(C.key)){if(b.value<0){C.key==="Backspace"&&!q.value&&(b.value=le-1);return}const R=b.value;$.value&&ve($.value),b.value=R>=le-1?le-2:R}if(C.key==="ArrowLeft"){if(b.value<0&&Y>0)return;const R=b.value>-1?b.value-1:le-1;me.value[R]?b.value=R:(b.value=-1,U.value.setSelectionRange((o=q.value)==null?void 0:o.length,(z=q.value)==null?void 0:z.length))}if(C.key==="ArrowRight"){if(b.value<0)return;const R=b.value+1;me.value[R]?b.value=R:(b.value=-1,U.value.setSelectionRange(0,0))}}}function se(C){q.value=C.target.value}function re(){j.value&&(S.value=!0)}function we(C){j.value=!0}function Fe(C){var Y;C.relatedTarget==null&&((Y=U.value)==null||Y.focus())}const ue=c(!1);function ve(C){if(n.multiple){const Y=K.value.findIndex(le=>n.valueComparator(le,C.value));if(Y===-1)ne.value=[...ne.value,C];else{const le=[...ne.value];le.splice(Y,1),ne.value=le}}else ne.value=[C],ue.value=!0,p.selection||(q.value=C.title),f.value=!1,S.value=!0,ut(()=>ue.value=!1)}return ze(j,C=>{var Y;C?(ue.value=!0,q.value=n.multiple||p.selection?"":String(((Y=me.value.at(-1))==null?void 0:Y.props.title)??""),S.value=!0,ut(()=>ue.value=!1)):(f.value=!1,q.value="")}),ze(q,C=>{!j.value||ue.value||(C&&(f.value=!0),S.value=!C)}),gl(()=>{const C=!!(n.chips||p.chip),Y=!!(!n.hideNoData||L.value.length||p.prepend||p.append||p["no-data"]),le=ne.value.length>0,[ke]=Jt.filterProps(n);return e(Jt,Qe({ref:U},ke,{modelValue:q.value,"onUpdate:modelValue":T=>{T==null&&(ne.value=[])},focused:j.value,"onUpdate:focused":T=>j.value=T,validationValue:ne.externalValue,dirty:le,onInput:se,class:["v-autocomplete",{"v-autocomplete--active-menu":f.value,"v-autocomplete--chips":!!n.chips,"v-autocomplete--selecting-index":b.value>-1,[`v-autocomplete--${n.multiple?"multiple":"single"}`]:!0,"v-autocomplete--selection-slot":!!p.selection},n.class],style:n.style,appendInnerIcon:n.menuIcon,readonly:n.readonly,placeholder:le?void 0:n.placeholder,"onClick:clear":P,"onMousedown:control":ie,onKeydown:te}),{...p,default:()=>e(be,null,[e(ha,Qe({ref:M,modelValue:f.value,"onUpdate:modelValue":T=>f.value=T,activator:"parent",contentClass:"v-autocomplete__content",eager:n.eager,maxHeight:310,openOnClick:!1,closeOnContentClick:!1,transition:n.transition,onAfterLeave:re},n.menuProps),{default:()=>[Y&&e(_l,{ref:D,selected:K.value,selectStrategy:n.multiple?"independent":"single-independent",onMousedown:T=>T.preventDefault(),onFocusin:we,onFocusout:Fe},{default:()=>{var T,o,z;return[!L.value.length&&!n.hideNoData&&(((T=p["no-data"])==null?void 0:T.call(p))??e(gt,{title:y(n.noDataText)},null)),(o=p["prepend-item"])==null?void 0:o.call(p),L.value.map(R=>{var Re;return((Re=p.item)==null?void 0:Re.call(p,{item:R,props:Qe(R.props,{onClick:()=>ve(R)})}))??e(gt,Qe({key:R.value},R.props,{onClick:()=>ve(R)}),{prepend:_e=>{let{isSelected:i}=_e;return n.multiple&&!n.hideSelected?e(fa,{modelValue:i,ripple:!1,tabindex:"-1"},null):void 0},title:()=>{var _e,i;return S.value?R.title:ro(R.title,(_e=ye(R))==null?void 0:_e.title,((i=q.value)==null?void 0:i.length)??0)}})}),(z=p["append-item"])==null?void 0:z.call(p)]}})]}),me.value.map((T,o)=>{var Re;function z(_e){_e.stopPropagation(),_e.preventDefault(),ve(T)}const R={"onClick:close":z,modelValue:!0,"onUpdate:modelValue":void 0};return e("div",{key:T.value,class:["v-autocomplete__selection",o===b.value&&["v-autocomplete__selection--selected",Me.value]],style:o===b.value?Le.value:{}},[C?p.chip?e(wa,{key:"chip-defaults",defaults:{VChip:{closable:n.closableChips,size:"small",text:T.title}}},{default:()=>{var _e;return[(_e=p.chip)==null?void 0:_e.call(p,{item:T,index:o,props:R})]}}):e(Cl,Qe({key:"chip",closable:n.closableChips,size:"small",text:T.title},R),null):((Re=p.selection)==null?void 0:Re.call(p,{item:T,index:o}))??e("span",{class:"v-autocomplete__selection-text"},[T.title,n.multiple&&o<me.value.length-1&&e("span",{class:"v-autocomplete__selection-comma"},[w(",")])])])})])})}),yl({isFocused:j,isPristine:S,menu:f,search:q,filteredItems:ge,select:ve},U)}}),co=At({name:"AppAutocomplete",inheritAttrs:!1}),po=Object.assign(co,{setup(n){const x=G(()=>{const y=tl(),U=y.id||y.label;return U?`app-autocomplete-${U}-${Math.random().toString(36).slice(2,7)}`:void 0}),p=G(()=>tl().label);return(y,U)=>(m(),V("div",{class:_t(["app-autocomplete flex-grow-1",y.$attrs.class])},[t(p)?(m(),O(sa,{key:0,for:t(x),class:"mb-1 text-body-2 text-high-emphasis",text:t(p)},null,8,["for","text"])):k("",!0),e(uo,ll(al({...y.$attrs,class:null,label:void 0,id:t(x),variant:"outlined",menuProps:{contentClass:["app-inner-list","app-autocomplete__content","v-autocomplete__content"]}})),ka({_:2},[Ye(y.$slots,(j,S)=>({name:S,fn:r(M=>[Va(y.$slots,S,ll(al(M||{})))])}))]),1040)],2))}});const Ao=Bt()({name:"VSlideGroupItem",props:{...Ca()},emits:{"group:selected":n=>!0},setup(n,x){let{slots:p}=x;const y=xa(n,Na);return()=>{var U;return(U=p.default)==null?void 0:U.call(p,{isSelected:y.isSelected.value,select:y.select,toggle:y.toggle,selectedClass:y.selectedClass.value})}}}),mo=["onClick"],vo={key:0,class:"stepper-icon-step text-high-emphasis d-flex align-center gap-2"},fo={class:"stepper-icon"},bo={class:"stepper-title font-weight-medium mb-0"},go={key:0,class:"stepper-subtitle"},_o={class:"text-sm"},yo={key:1,class:"d-flex align-center gap-x-2"},ho={class:"d-flex align-center gap-2"},wo={class:"d-flex align-center justify-center",style:{"block-size":"24px","inline-size":"24px"}},ko={key:0,class:"stepper-step-indicator"},Vo={class:"text-h4 step-number"},Co={style:{"line-height":"0"}},xo={class:"text-sm font-weight-medium step-title"},$o={key:0,class:"text-xs step-subtitle"},So={key:0,class:"stepper-step-line"},Do={__name:"AppStepper",props:{items:{type:Array,required:!0},currentStep:{type:Number,required:!1,default:0},direction:{type:String,required:!1,default:"horizontal"},iconSize:{type:[String,Number],required:!1,default:52},isActiveStepValid:{type:Boolean,required:!1,default:void 0}},emits:["update:currentStep"],setup(n,{emit:x}){const p=n,y=c(p.currentStep||0),U=G(()=>M=>M<y.value?"stepper-steps-completed":M===y.value?"stepper-steps-active":""),j=G(()=>M=>p.direction==="horizontal"&&p.items.length-1!==M),S=G(()=>p.isActiveStepValid!==void 0);return $a(()=>{p.currentStep!==void 0&&p.currentStep<p.items.length&&p.currentStep>=0&&(y.value=p.currentStep),x("update:currentStep",y.value)}),(M,v)=>(m(),O(ja,{modelValue:t(y),"onUpdate:modelValue":v[0]||(v[0]=f=>X(y)?y.value=f:null),class:"app-stepper","show-arrows":"",direction:p.direction},{default:r(()=>[(m(!0),V(be,null,Ye(p.items,(f,b)=>(m(),O(Ao,{key:f.title,value:b},{default:r(()=>[u("div",{class:_t(["cursor-pointer mx-1",[!p.isActiveStepValid&&t(S)&&"stepper-steps-invalid",t(U)(b)]]),onClick:H=>!t(S)&&x("update:currentStep",b)},[f.icon?(m(),V("div",vo,[u("div",{class:_t(["d-flex align-center gap-4 step-wrapper",[p.direction==="horizontal"&&"flex-column"]])},[u("div",fo,[e(Pe,{icon:f.icon,size:f.size||p.iconSize},null,8,["icon","size"])]),u("div",null,[u("p",bo,d(f.title),1),f.subtitle?(m(),V("span",go,[u("span",_o,d(f.subtitle),1)])):k("",!0)])],2),t(j)(b)?(m(),O(Pe,{key:0,class:"flip-in-rtl stepper-chevron-indicator mx-6",size:"24",icon:"tabler-chevron-right"})):k("",!0)])):(m(),V("div",yo,[u("div",ho,[u("div",wo,[b>=t(y)?(m(),V(be,{key:0},[!t(S)||p.isActiveStepValid||b!==t(y)?(m(),V("div",ko)):(m(),O(Pe,{key:1,icon:"tabler-alert-circle",size:"24",color:"error"}))],64)):(m(),O(Pe,{key:1,icon:"custom-check-circle",class:"stepper-step-icon",size:"24"}))]),u("h4",Vo,d((b+1).toString().padStart(2,"0")),1)]),u("div",Co,[u("h6",xo,d(f.title),1),f.subtitle?(m(),V("span",$o,d(f.subtitle),1)):k("",!0)]),t(j)(b)?(m(),V("div",So)):k("",!0)]))],10,mo)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue","direction"]))}},zo=""+new URL("mastercard-7eef4812.webp",import.meta.url).href,Io="data:image/gif;base64,R0lGODdhDgANAPcAAP+ZZv//zP/////MZv/Mmf8zAP+ZM/9mAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAAAAAAALAAAAAAOAA0AQAhHAA8IHFig4MCDBwkQQIiQwIADBQYIYHjQIEWBBggEEEDAwEWMGz1+DPDwI0YDIikS4LgywEWUBzK6/CgRgMkDAFJ+JGATYUAAOw==",Uo=""+new URL("ewm-b91c9e6f.webp",import.meta.url).href,Po="data:image/gif;base64,R0lGODlhUAAQAPABANfX1wAAACH5BAkPAAEAIf8LTkVUU0NBUEUyLjADAQAAACwAAAAAUAAQAAACUoyPqcvtD6OctNqLs968GwACVShS5BhCJDitZeS2q+Oyag3jd8zUL617+HK8xXA3Q56UqV4QWIQmhU9FlLokNrU2KaoryYa3nrL5jE6r1+y2pQAAIfkECQ8ABQAsAAAAAFAAEACCAL8ZBL8dCMAgLsRC19fXAAAAAAAAAAAAA2ZYutz+MMpJq7046827/2CoDEEwZERKoCqbUgIgA8Klttb95ro0zLMTRYebEHfGYyQAlAUqx9VQOSUum4Bntbe9QamPX1OYtHaLkqgkNqvxvG90WR4ukeMuqY0u6vv/gIGCg4SFFwkAIfkECQ8ABQAsAAAAAFAAEACCAL8ZBL8dCMAgLsRC19fXAAAAAAAAAAAAA3RYutz+MMpJq7046827/2CoDEEwZKSZESyxtpQAzIBwybRttfDOu5EBjXaiCIeA4uTXWzKBjwASEKhIkVXKEyrZRq7D7ARME3efFS/kOFRK2MQ0WjuH4Ga6yr12YfZ/RiVuFSmDdCwviCKLjI2Oj5CRkpMcCQAh+QQJDwAFACwAAAAAUAAQAIIAvxkEvx0IwCAuxELX19cAAAAAAAAAAAADeVi63P4wykmrvTjrzbv/YKgMQTBkpImWJ0a8BCUANCBcc31bOb1XMJhkUKu1JsQi4DhULoFBISTgDFSoSisFW9ROotIHt+aVjGnlyBmQhoBfkWSRGXfSIXIjFCzp2XBFPzKBF3xILCsqGCl3X2EikJGSk5SVlpeYGgkAIfkECQ8ABAAsAAAAAFAAEACCAL8ZBL8dCMAgLsRCAAAAAAAAAAAAAAAAA3lIutz+MMpJq7046827/2CoDEEwZKSJlieWtpIAzIBwybRt4bNe8TXJgEaDCYkzY2SIVEKYROcigAQEKlTklZIlbidd2tcRno0jZSu2eoak2wtosSJP0qtSRx2QVwB9FH83RIATghMvKyouLIp9IpCRkpOUlZaXmBQJACH5BAkPAAUALAAAAABQABAAggC/GQS/HQjAIC7EQtfX1wAAAAAAAAAAAANyWLrc/jDKSau9OOvNu/9gqBAkkQ1BMJzpiqEqVZaXANyAUOP5juuR2awy4N1ck6IRKVHymA0hjRIwAgKVqhFLtXIfUlLWO96WeV9H2ERx4qAQ95FohTOkvhuwYvvlexNDLy0sMYOGIomKi4yNjo+QkRsJACH5BAkPAAUALAAAAABQABAAggC/GQS/HQjAIC7EQtfX1wAAAAAAAAAAAANtWLrc/jDKSau9OOvNu/9gqBAkkZUmNgTBkK0thZLXnFYCoAPCle+9iI0mG1YGu52LgkwClo/hTSKtBJyAgBWrhVSLtq2zO7mOhUbw7IiFSppJtyNsoeOSwTtQfSq9WHJMgCKEhYaHiImKi4wdCQAh+QQJDwAFACwAAAAAUAAQAIIAvxkEvx0IwCAuxELX19cAAAAAAAAAAAADZ1i63P4wykmrvTjrzbv/YKgQJJGVJoZmQxAMFEpeclrVlwDsgCDVsxjwNqQMeDwYBGj7FSfMSgC5C0SiQlxWJqUCrMunUwsVR45UZXhlIW+DFR3PV4a3Syc8pvUS+f+AgYKDhIWGHQkAOw==",Mo=""+new URL("laptop-girl-5daa5751.webp",import.meta.url).href,rl=""+new URL("hsbc-926214c9.webp",import.meta.url).href;function dt(n,x){x===void 0&&(x={});var p=x.insertAt;if(n&&typeof document<"u"){var y=document.head||document.getElementsByTagName("head")[0],U=document.createElement("style");U.type="text/css",p==="top"&&y.firstChild?y.insertBefore(U,y.firstChild):y.appendChild(U),U.styleSheet?U.styleSheet.cssText=n:U.appendChild(document.createTextNode(n))}}dt(".vel-fade-enter-active,.vel-fade-leave-active{-webkit-transition:all .3s ease;transition:all .3s ease}.vel-fade-enter-from,.vel-fade-leave-to{opacity:0}.vel-img-swiper{display:block;position:relative}.vel-modal{background:rgba(0,0,0,.5);bottom:0;left:0;margin:0;position:fixed;right:0;top:0;z-index:9998}.vel-img-wrapper{left:50%;margin:0;position:absolute;top:50%;-webkit-transform:translate(-50% -50%);transform:translate(-50% -50%);-webkit-transition:.3s linear;transition:.3s linear;will-change:transform opacity}.vel-img,.vel-img-wrapper{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.vel-img{background-color:rgba(0,0,0,.7);-webkit-box-shadow:0 5px 20px 2px rgba(0,0,0,.7);box-shadow:0 5px 20px 2px rgba(0,0,0,.7);display:block;max-height:80vh;max-width:80vw;position:relative;-webkit-transition:-webkit-transform .3s ease-in-out;transition:-webkit-transform .3s ease-in-out;transition:transform .3s ease-in-out;transition:transform .3s ease-in-out,-webkit-transform .3s ease-in-out}@media (max-width:750px){.vel-img{max-height:95vh;max-width:85vw}}.vel-btns-wrapper{position:static}.vel-btns-wrapper .btn__close,.vel-btns-wrapper .btn__next,.vel-btns-wrapper .btn__prev{-webkit-tap-highlight-color:transparent;color:#fff;cursor:pointer;font-size:32px;opacity:.6;outline:none;position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);-webkit-transition:.15s linear;transition:.15s linear;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.vel-btns-wrapper .btn__close:hover,.vel-btns-wrapper .btn__next:hover,.vel-btns-wrapper .btn__prev:hover{opacity:1}.vel-btns-wrapper .btn__close.disable,.vel-btns-wrapper .btn__close.disable:hover,.vel-btns-wrapper .btn__next.disable,.vel-btns-wrapper .btn__next.disable:hover,.vel-btns-wrapper .btn__prev.disable,.vel-btns-wrapper .btn__prev.disable:hover{cursor:default;opacity:.2}.vel-btns-wrapper .btn__next{right:12px}.vel-btns-wrapper .btn__prev{left:12px}.vel-btns-wrapper .btn__close{right:10px;top:24px}@media (max-width:750px){.vel-btns-wrapper .btn__next,.vel-btns-wrapper .btn__prev{font-size:20px}.vel-btns-wrapper .btn__close{font-size:24px}.vel-btns-wrapper .btn__next{right:4px}.vel-btns-wrapper .btn__prev{left:4px}}.vel-modal.is-rtl .vel-btns-wrapper .btn__next{left:12px;right:auto}.vel-modal.is-rtl .vel-btns-wrapper .btn__prev{left:auto;right:12px}@media (max-width:750px){.vel-modal.is-rtl .vel-btns-wrapper .btn__next{left:4px;right:auto}.vel-modal.is-rtl .vel-btns-wrapper .btn__prev{left:auto;right:4px}}.vel-modal.is-rtl .vel-img-title{direction:rtl}");dt('.vel-loading{left:50%;position:absolute;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.vel-loading .ring{display:inline-block;height:64px;width:64px}.vel-loading .ring:after{-webkit-animation:ring 1.2s linear infinite;animation:ring 1.2s linear infinite;border-color:hsla(0,0%,100%,.7) transparent;border-radius:50%;border-style:solid;border-width:5px;content:" ";display:block;height:46px;margin:1px;width:46px}@-webkit-keyframes ring{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes ring{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}');dt(".vel-on-error{left:50%;position:absolute;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.vel-on-error .icon{color:#aaa;font-size:80px}");dt(".vel-img-title{bottom:60px;color:#ccc;cursor:default;font-size:12px;left:50%;line-height:1;max-width:80%;opacity:.8;overflow:hidden;position:absolute;text-align:center;text-overflow:ellipsis;-webkit-transform:translate(-50%);transform:translate(-50%);-webkit-transition:opacity .15s;transition:opacity .15s;white-space:nowrap}.vel-img-title:hover{opacity:1}");dt(".vel-icon{fill:currentColor;height:1em;overflow:hidden;vertical-align:-.15em;width:1em}");dt(".vel-toolbar{border-radius:4px;bottom:8px;display:-webkit-box;display:-ms-flexbox;display:flex;left:50%;opacity:.9;overflow:hidden;padding:0;position:absolute;-webkit-transform:translate(-50%);transform:translate(-50%)}.vel-toolbar,.vel-toolbar .toolbar-btn{background-color:#2d2d2d;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.vel-toolbar .toolbar-btn{-ms-flex-negative:0;-webkit-tap-highlight-color:transparent;color:#fff;cursor:pointer;flex-shrink:0;font-size:20px;outline:none;padding:6px 10px}.vel-toolbar .toolbar-btn:active,.vel-toolbar .toolbar-btn:hover{background-color:#3d3d3d}");const Ue="vel",Te=At({name:"SvgIcon",props:{type:{type:String,default:""}},setup:n=>()=>e("svg",{class:`${Ue}-icon icon`,"aria-hidden":"true"},[e("use",{"xlink:href":`#icon-${n.type}`},null)])}),yt=typeof window<"u",rt=()=>{};let Dl=!1;if(yt)try{const n={};Object.defineProperty(n,"passive",{get(){Dl=!0}}),window.addEventListener("test-passive",rt,n)}catch{}const ul=function(n,x,p){let y=arguments.length>3&&arguments[3]!==void 0&&arguments[3];yt&&n.addEventListener(x,p,!!Dl&&{capture:!1,passive:y})},dl=(n,x,p)=>{yt&&n.removeEventListener(x,p)},Lo=n=>{n.preventDefault()},Ro=Object.prototype.toString,Tt=n=>x=>Ro.call(x).slice(8,-1)===n,Bo=n=>!!n&&Tt("Object")(n),cl=n=>!!n&&Tt("String")(n);function Eo(n){return n!=null}const To=At({name:"Toolbar",props:{zoomIn:{type:Function,default:rt},zoomOut:{type:Function,default:rt},rotateLeft:{type:Function,default:rt},rotateRight:{type:Function,default:rt},resize:{type:Function,default:rt},rotateDisabled:{type:Boolean,default:!1},zoomDisabled:{type:Boolean,default:!1}},setup:n=>()=>e("div",{class:`${Ue}-toolbar`},[!n.zoomDisabled&&e(be,null,[e("div",{role:"button","aria-label":"zoom in button",class:"toolbar-btn toolbar-btn__zoomin",onClick:n.zoomIn},[e(Te,{type:"zoomin"},null)]),e("div",{role:"button","aria-label":"zoom out button",class:"toolbar-btn toolbar-btn__zoomout",onClick:n.zoomOut},[e(Te,{type:"zoomout"},null)])]),e("div",{role:"button","aria-label":"resize image button",class:"toolbar-btn toolbar-btn__resize",onClick:n.resize},[e(Te,{type:"resize"},null)]),!n.rotateDisabled&&e(be,null,[e("div",{role:"button","aria-label":"image rotate left button",class:"toolbar-btn toolbar-btn__rotate",onClick:n.rotateLeft},[e(Te,{type:"rotate-left"},null)]),e("div",{role:"button","aria-label":"image rotate right button",class:"toolbar-btn toolbar-btn__rotate",onClick:n.rotateRight},[e(Te,{type:"rotate-right"},null)])])])}),Fo=()=>e("div",{class:`${Ue}-loading`},[e("div",{class:"ring"},null)]),Oo=()=>e("div",{class:`${Ue}-on-error`},[e("div",{class:"ring"},null),e(Te,{type:"img-broken"},null)]),No=(n,x)=>{let{slots:p}=x;return e("div",{class:`${Ue}-img-title`},[p.default?p.default():""])},jo=At({name:"DefaultIcons",setup:()=>()=>e("svg",{"aria-hidden":!0,style:"position: absolute; width: 0; height: 0; overflow: hidden; visibility: hidden;"},[e("symbol",{id:"icon-rotate-right",viewBox:"0 0 1024 1024"},[e("path",{d:"M275.199914 450.496179v20.031994c0.384-38.079988 12.543996-67.423979 36.479989-87.967973 22.431993-20.351994 49.215985-30.55999 80.319975-30.55999 32.06399 0 59.295981 10.175997 81.759974 30.55999 22.815993 20.543994 34.591989 49.887984 35.359989 87.967973v123.935961c-0.768 37.887988-12.543996 67.135979-35.359989 87.679973-22.431993 20.351994-49.695984 30.75199-81.759974 31.10399a120.255962 120.255962 0 0 1-72.991978-24.895992c-21.503993-15.839995-35.359989-38.751988-41.567987-68.735979h60.831981c9.247997 23.007993 27.167992 34.495989 53.759983 34.49599 37.535988-0.384 56.863982-21.407993 57.983982-63.071981v-38.751988c-28.095991 8.863997-54.303983 13.119996-78.623975 12.735996a91.263971 91.263971 0 0 1-68.447979-27.711991c-18.847994-18.303994-28.095991-47.231985-27.711991-86.847973z m62.55998 24.863992c7.103998 24.799992 25.215992 37.343988 54.271983 37.663989 27.103992-0.288 44.703986-11.327996 52.831984-33.11999 3.135999-8.383997 2.655999-29.599991-1.28-38.559988-8.607997-19.615994-25.791992-29.695991-51.551984-30.20799-28.383991 0.576-46.303986 12.639996-53.759983 36.159988a58.719982 58.719982 0 0 0-0.512 28.063991z m390.335878 115.711964v-116.895963c-1.12-41.311987-20.447994-62.335981-57.983981-63.07198-37.727988 0.768-56.959982 21.791993-57.695982 63.07198v116.895963c0.768 41.663987 19.999994 62.68798 57.695982 63.071981 37.535988-0.384 56.863982-21.407993 57.983981-63.071981z m-174.815945 3.391999v-123.935961c0.384-38.079988 12.543996-67.423979 36.479989-87.967973 22.431993-20.351994 49.215985-30.55999 80.319975-30.55999 32.06399 0 59.295981 10.175997 81.759974 30.55999 22.815993 20.543994 34.591989 49.887984 35.359989 87.967973v123.935961c-0.768 37.887988-12.543996 67.135979-35.359989 87.679973-22.431993 20.351994-49.695984 30.75199-81.759974 31.10399-31.10399-0.384-57.887982-10.751997-80.319975-31.10399-23.935993-20.543994-36.127989-49.791984-36.479989-87.679973z m282.559912-479.07185A509.887841 509.887841 0 0 0 511.99984 0.00032C229.215928 0.00032 0 229.216248 0 512.00016s229.215928 511.99984 511.99984 511.99984 511.99984-229.215928 511.99984-511.99984c0-3.743999-0.032-7.455998-0.128-11.167997-1.631999-11.295996-8.159997-27.103992-31.87199-27.103991-27.487991 0-31.67999 21.247993-32.03199 32.06399l0.032 4.127999a30.62399 30.62399 0 0 0 0.16 2.079999H959.9997c0 247.423923-200.575937 447.99986-447.99986 447.99986S63.99998 759.424083 63.99998 512.00016 264.575917 64.0003 511.99984 64.0003a446.079861 446.079861 0 0 1 277.439913 96.22397l-94.91197 91.679971c-25.439992 24.607992-17.439995 44.991986 17.887994 45.599986l188.031942 3.295999a64.31998 64.31998 0 0 0 65.055979-62.84798l3.295999-188.127942C969.407697 15.040315 949.311703 5.792318 923.871711 30.368311l-87.999972 85.023973z",fill:""},null)]),e("symbol",{id:"icon-rotate-left",viewBox:"0 0 1024 1024"},[e("path",{d:"M275.199914 450.496179v20.031994c0.384-38.079988 12.543996-67.423979 36.479989-87.967973 22.431993-20.351994 49.215985-30.55999 80.319975-30.55999 32.06399 0 59.295981 10.175997 81.759974 30.55999 22.815993 20.543994 34.591989 49.887984 35.359989 87.967973v123.935961c-0.768 37.887988-12.543996 67.135979-35.359989 87.679973-22.431993 20.351994-49.695984 30.75199-81.759974 31.10399a120.255962 120.255962 0 0 1-72.991978-24.895992c-21.503993-15.839995-35.359989-38.751988-41.567987-68.735979h60.831981c9.247997 23.007993 27.167992 34.495989 53.759983 34.49599 37.535988-0.384 56.863982-21.407993 57.983982-63.071981v-38.751988c-28.095991 8.863997-54.303983 13.119996-78.623975 12.735996a91.263971 91.263971 0 0 1-68.447979-27.711991c-18.847994-18.303994-28.095991-47.231985-27.711991-86.847973z m62.55998 24.863992c7.103998 24.799992 25.215992 37.343988 54.271983 37.663989 27.103992-0.288 44.703986-11.327996 52.831984-33.11999 3.135999-8.383997 2.655999-29.599991-1.28-38.559988-8.607997-19.615994-25.791992-29.695991-51.551984-30.20799-28.383991 0.576-46.303986 12.639996-53.759983 36.159988a58.719982 58.719982 0 0 0-0.512 28.063991z m390.335878 115.711964v-116.895963c-1.12-41.311987-20.447994-62.335981-57.983981-63.07198-37.727988 0.768-56.959982 21.791993-57.695982 63.07198v116.895963c0.768 41.663987 19.999994 62.68798 57.695982 63.071981 37.535988-0.384 56.863982-21.407993 57.983981-63.071981z m-174.815945 3.391999v-123.935961c0.384-38.079988 12.543996-67.423979 36.479989-87.967973 22.431993-20.351994 49.215985-30.55999 80.319975-30.55999 32.06399 0 59.295981 10.175997 81.759974 30.55999 22.815993 20.543994 34.591989 49.887984 35.359989 87.967973v123.935961c-0.768 37.887988-12.543996 67.135979-35.359989 87.679973-22.431993 20.351994-49.695984 30.75199-81.759974 31.10399-31.10399-0.384-57.887982-10.751997-80.319975-31.10399-23.935993-20.543994-36.127989-49.791984-36.479989-87.679973zM188.159941 115.392284A509.887841 509.887841 0 0 1 511.99984 0.00032c282.783912 0 511.99984 229.215928 511.99984 511.99984s-229.215928 511.99984-511.99984 511.99984S0 794.784072 0 512.00016c0-3.743999 0.032-7.455998 0.128-11.167997 1.631999-11.295996 8.159997-27.103992 31.87199-27.103991 27.487991 0 31.67999 21.247993 32.03199 32.06399L63.99998 509.920161a30.62399 30.62399 0 0 1-0.16 2.079999H63.99998c0 247.423923 200.575937 447.99986 447.99986 447.99986s447.99986-200.575937 447.99986-447.99986S759.423763 64.0003 511.99984 64.0003a446.079861 446.079861 0 0 0-277.439913 96.22397l94.91197 91.679971c25.439992 24.607992 17.439995 44.991986-17.887994 45.599986L123.551961 300.800226a64.31998 64.31998 0 0 1-65.055979-62.84798l-3.295999-188.127942C54.591983 15.040315 74.687977 5.792318 100.127969 30.368311l87.999972 85.023973z",fill:""},null)]),e("symbol",{id:"icon-resize",viewBox:"0 0 1024 1024"},[e("path",{d:"M456.036919 791.8108 270.553461 791.8108 460.818829 601.572038l-39.593763-39.567157L231.314785 751.915162l0.873903-183.953615c0-15.465227-12.515035-27.981285-27.981285-27.981285s-27.981285 12.515035-27.981285 27.981285l0 251.829516c0 8.3072 3.415796 14.975063 8.826016 19.564591 5.082762 5.192256 12.132318 8.416693 19.947308 8.416693l251.036453 0c15.46625 0 27.981285-12.514012 27.981285-27.981285C484.018204 804.325835 471.504192 791.8108 456.036919 791.8108zM838.945819 184.644347c-5.082762-5.191232-12.132318-8.416693-19.947308-8.416693L567.961034 176.227654c-15.46625 0-27.981285 12.515035-27.981285 27.981285 0 15.46625 12.514012 27.981285 27.981285 27.981285l185.483458 0L563.206754 422.427962l39.567157 39.567157 189.910281-189.910281-0.873903 183.953615c0 15.46625 12.514012 27.981285 27.981285 27.981285s27.981285-12.514012 27.981285-27.981285L847.772858 204.208938C847.771835 195.902762 844.356039 189.234899 838.945819 184.644347zM847.771835 64.303538 176.227142 64.303538c-61.809741 0-111.924115 50.115398-111.924115 111.924115l0 671.544693c0 61.809741 50.114374 111.924115 111.924115 111.924115l671.544693 0c61.809741 0 111.924115-50.114374 111.924115-111.924115l0-671.544693C959.69595 114.418936 909.581576 64.303538 847.771835 64.303538zM903.733381 847.772346c0 30.878265-25.056676 55.962569-55.962569 55.962569L176.227142 903.734916c-30.90487 0-55.962569-25.084305-55.962569-55.962569l0-671.544693c0-30.9325 25.056676-55.962569 55.962569-55.962569l671.544693 0c30.90487 0 55.962569 25.03007 55.962569 55.962569L903.734404 847.772346z"},null)]),e("symbol",{id:"icon-img-broken",viewBox:"0 0 1024 1024"},[e("path",{d:"M810.666667 128H213.333333c-46.933333 0-85.333333 38.4-85.333333 85.333333v597.333334c0 46.933333 38.4 85.333333 85.333333 85.333333h597.333334c46.933333 0 85.333333-38.4 85.333333-85.333333V213.333333c0-46.933333-38.4-85.333333-85.333333-85.333333z m0 682.666667H213.333333v-195.413334l42.24 42.24 170.666667-170.666666 170.666667 170.666666 170.666666-170.24L810.666667 530.346667V810.666667z m0-401.493334l-43.093334-43.093333-170.666666 171.093333-170.666667-170.666666-170.666667 170.666666-42.24-42.666666V213.333333h597.333334v195.84z"},null)]),e("symbol",{id:"icon-prev",viewBox:"0 0 1024 1024"},[e("path",{d:"M784.652701 955.6957 346.601985 517.644983c-2.822492-2.822492-2.822492-7.902977 0-11.289967l439.179713-439.179713c6.77398-6.77398 10.725469-16.370452 10.725469-25.966924L796.507166 36.692393c0-20.32194-16.370452-36.692393-36.692393-36.692393l-4.515987 0c-9.596472 0-19.192944 3.951488-25.966924 10.725469L250.072767 489.420066c-12.418964 12.418964-12.418964 32.740904 0 45.159868l477.565601 477.565601c7.338479 7.338479 17.499449 11.854465 28.224917 11.854465l0 0c22.015436 0 40.079383-18.063947 40.079383-40.079383l0 0C796.507166 973.759647 791.99118 963.598677 784.652701 955.6957z"},null)]),e("symbol",{id:"icon-next",viewBox:"0 0 1024 1024"},[e("path",{d:"M246.121279 955.6957l438.050717-438.050717c2.822492-2.822492 2.822492-7.902977 0-11.289967L244.992282 67.175303c-6.77398-6.77398-10.725469-16.370452-10.725469-25.966924L234.266814 36.692393C234.266814 16.370452 250.637266 0 270.959206 0l4.515987 0c9.596472 0 19.192944 3.951488 25.966924 10.725469l478.694598 478.694598c12.418964 12.418964 12.418964 32.740904 0 45.159868l-477.565601 477.565601c-7.338479 7.338479-17.499449 11.854465-28.224917 11.854465l0 0c-22.015436 0-40.079383-18.063947-40.079383-40.079383l0 0C234.266814 973.759647 238.7828 963.598677 246.121279 955.6957z"},null)]),e("symbol",{id:"icon-zoomin",viewBox:"0 0 1024 1024"},[e("path",{d:"M725.504 652.864c46.4-61.44 71.744-136.448 71.744-218.752C797.248 230.464 632.768 64 430.656 64S64 230.464 64 434.112C64 639.36 228.48 805.76 430.656 805.76c86.656 0 164.48-30.144 227.52-81.088L889.984 960 960 891.264l-234.496-238.4z m-294.848 67.456c-155.776 0-282.624-128.896-282.624-286.208s126.848-286.208 282.624-286.208 282.624 128.896 282.624 286.208-126.912 286.208-282.624 286.208z"},null),e("path",{d:"M235.712 369.92h390.72v127.104H235.712z"},null),e("path",{d:"M367.488 238.144h127.104v390.72H367.488z"},null)]),e("symbol",{id:"icon-close",viewBox:"0 0 1024 1024"},[e("path",{d:"M570.24 512l259.2 259.2-58.88 58.24L512 570.24l-261.12 261.12-58.24-58.24L453.76 512 194.56 252.8l58.24-58.24L512 453.76l261.12-261.12 58.24 58.24z"},null)]),e("symbol",{id:"icon-zoomout",viewBox:"0 0 1024 1024"},[e("path",{d:"M725.504 652.864c46.4-61.44 71.744-136.448 71.744-218.752C797.248 230.464 632.768 64 430.656 64S64 230.464 64 434.112C64 639.36 228.48 805.76 430.656 805.76c86.656 0 164.48-30.144 227.52-81.088L889.984 960 960 891.264l-234.496-238.4z m-294.848 67.456c-155.776 0-282.624-128.896-282.624-286.208s126.848-286.208 282.624-286.208 282.624 128.896 282.624 286.208-126.912 286.208-282.624 286.208z"},null),e("path",{d:"M235.712 369.92h390.72v127.104H235.712z"},null)])])}),pt=yt?window:global;let pl=Date.now();function qo(n){const x=Date.now(),p=Math.max(0,16-(x-pl)),y=setTimeout(n,p);return pl=x+p,y}function Mt(n){return(pt.requestAnimationFrame||qo).call(pt,n)}function Al(n){(pt.cancelAnimationFrame||pt.clearTimeout).call(pt,n)}function ml(n,x){const p=n.clientX-x.clientX,y=n.clientY-x.clientY;return Math.sqrt(p*p+y*y)}function Lt(n){return typeof n=="function"||Object.prototype.toString.call(n)==="[object Object]"&&!za(n)}var Rt=At({name:"VueEasyLightbox",props:{imgs:{type:[Array,String],default:()=>""},visible:{type:Boolean,default:!1},index:{type:Number,default:0},scrollDisabled:{type:Boolean,default:!0},escDisabled:{type:Boolean,default:!1},moveDisabled:{type:Boolean,default:!1},titleDisabled:{type:Boolean,default:!1},maskClosable:{type:Boolean,default:!0},teleport:{type:[String,Object],default:null},swipeTolerance:{type:Number,default:50},loop:{type:Boolean,default:!1},rtl:{type:Boolean,default:!1},zoomScale:{type:Number,default:.12},maxZoom:{type:Number,default:3},minZoom:{type:Number,default:.1},rotateDisabled:{type:Boolean,default:!1},zoomDisabled:{type:Boolean,default:!1},pinchDisabled:{type:Boolean,default:!1},dblclickDisabled:{type:Boolean,default:!1}},emits:{hide:()=>!0,"on-error":n=>!0,"on-prev":(n,x)=>!0,"on-next":(n,x)=>!0,"on-prev-click":(n,x)=>!0,"on-next-click":(n,x)=>!0,"on-index-change":(n,x)=>!0,"on-rotate":n=>!0},setup(n,x){let{emit:p,slots:y}=x;const{imgRef:U,imgState:j,setImgSize:S}=(()=>{const A=c(),h=Ut({width:0,height:0,maxScale:1});return{imgRef:A,imgState:h,setImgSize:()=>{if(A.value){const{width:F,height:de,naturalWidth:fe}=A.value;h.maxScale=fe/F,h.width=F,h.height=de}}}})(),M=c(n.index),v=c(""),f=Ut({scale:1,lastScale:1,rotateDeg:0,top:0,left:0,initX:0,initY:0,lastX:0,lastY:0,touches:[]}),b=Ut({loadError:!1,loading:!1,dragging:!1,gesturing:!1,wheeling:!1}),H=G(()=>{return A=n.imgs,Tt("Array")(A)?n.imgs.map(h=>typeof h=="string"?{src:h}:function(F){return Bo(F)&&cl(F.src)}(h)?h:void 0).filter(Eo):cl(n.imgs)?[{src:n.imgs}]:[];var A}),Z=G(()=>H.value[M.value]),De=G(()=>{var A;return(A=H.value[M.value])==null?void 0:A.src}),E=G(()=>{var A;return(A=H.value[M.value])==null?void 0:A.title}),Me=G(()=>{var A;return(A=H.value[M.value])==null?void 0:A.alt}),Le=G(()=>({cursor:b.loadError?"default":n.moveDisabled?b.dragging?"grabbing":"grab":"move",top:`calc(50% + ${f.top}px)`,left:`calc(50% + ${f.left}px)`,transition:b.dragging||b.gesturing?"none":"",transform:`translate(-50%, -50%) scale(${f.scale}) rotate(${f.rotateDeg}deg)`})),q=()=>{p("hide")},ne=()=>{f.scale=1,f.lastScale=1,f.rotateDeg=0,f.top=0,f.left=0,b.loadError=!1,b.dragging=!1,b.loading=!0},Ae=(A,h)=>{const F=M.value;ne(),M.value=A,H.value[M.value]===H.value[A]&&ut(()=>{b.loading=!1}),n.visible&&F!==A&&(h&&h(F,A),p("on-index-change",F,A))},ge=()=>{const A=M.value,h=n.loop?(A+1)%H.value.length:A+1;!n.loop&&h>H.value.length-1||Ae(h,(F,de)=>{p("on-next",F,de),p("on-next-click",F,de)})},ye=()=>{const A=M.value;let h=A-1;if(A===0){if(!n.loop)return;h=H.value.length-1}Ae(h,(F,de)=>{p("on-prev",F,de),p("on-prev-click",F,de)})},me=A=>{Math.abs(1-A)<.05?A=1:Math.abs(j.maxScale-A)<.05&&(A=j.maxScale),f.lastScale=f.scale,f.scale=A},L=()=>{const A=f.scale+n.zoomScale;A<j.maxScale*n.maxZoom&&me(A)},K=()=>{const A=f.scale-n.zoomScale;A>n.minZoom&&me(A)},$=()=>{const A=f.rotateDeg%360;p("on-rotate",Math.abs(A<0?A+360:A))},D=()=>{f.rotateDeg-=90,$()},P=()=>{f.rotateDeg+=90,$()},ie=()=>{f.scale=1,f.top=0,f.left=0},te=function(){let A=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;return!n.moveDisabled&&A===0},{onMouseDown:se,onMouseMove:re,onMouseUp:we}=((A,h,F)=>{let de,fe=!1;return{onMouseDown:ae=>{A.initX=A.lastX=ae.clientX,A.initY=A.lastY=ae.clientY,h.dragging=!0,fe=!1,ae.stopPropagation()},onMouseUp:ae=>{F(ae.button)&&Al(de),h.dragging=!1,fe=!1},onMouseMove:ae=>{if(h.dragging)if(F(ae.button)){if(fe)return;fe=!0,de=Mt(()=>{const{top:Xe,left:Ie,lastY:oe,lastX:he}=A;A.top=Xe-oe+ae.clientY,A.left=Ie-he+ae.clientX,A.lastX=ae.clientX,A.lastY=ae.clientY,fe=!1})}else A.lastX=ae.clientX,A.lastY=ae.clientY;ae.stopPropagation()}}})(f,b,te),{onTouchStart:Fe,onTouchMove:ue,onTouchEnd:ve}=((A,h,F,de,fe)=>{let ae,Xe=!1;return{onTouchStart:Ie=>{const{touches:oe}=Ie;oe.length>1&&fe()?(F.gesturing=!0,h.touches=oe):(h.initX=h.lastX=oe[0].clientX,h.initY=h.lastY=oe[0].clientY,F.dragging=!0),Ie.stopPropagation()},onTouchMove:Ie=>{if(Xe)return;const{touches:oe}=Ie,{lastX:he,lastY:ht,left:wt,top:kt,scale:Oe}=h;if(!F.gesturing&&F.dragging){if(!oe[0])return;const{clientX:Ne,clientY:Ge}=oe[0];de()?ae=Mt(()=>{h.lastX=Ne,h.lastY=Ge,h.top=kt-ht+Ge,h.left=wt-he+Ne,Xe=!1}):(h.lastX=Ne,h.lastY=Ge)}else F.gesturing&&h.touches.length>1&&oe.length>1&&fe()&&(ae=Mt(()=>{const Ne=(ml(h.touches[0],h.touches[1])-ml(oe[0],oe[1]))/A.width;h.touches=oe;const Ge=Oe-1.3*Ne;Ge>.5&&Ge<1.5*A.maxScale&&(h.scale=Ge),Xe=!1}))},onTouchEnd:()=>{Al(ae),F.dragging=!1,F.gesturing=!1,Xe=!1}}})(j,f,b,te,()=>!n.pinchDisabled),C=()=>{n.dblclickDisabled||(f.scale!==j.maxScale?(f.lastScale=f.scale,f.scale=j.maxScale):f.scale=f.lastScale)},Y=A=>{b.loadError||b.gesturing||b.loading||b.dragging||b.wheeling||!n.scrollDisabled||n.zoomDisabled||(b.wheeling=!0,setTimeout(()=>{b.wheeling=!1},80),A.deltaY<0?L():K())},le=A=>{const h=A;n.visible&&(!n.escDisabled&&h.key==="Escape"&&n.visible&&q(),h.key==="ArrowLeft"&&(n.rtl?ge():ye()),h.key==="ArrowRight"&&(n.rtl?ye():ge()))},ke=()=>{n.maskClosable&&q()},T=()=>{S()},o=()=>{b.loading=!1},z=A=>{b.loading=!1,b.loadError=!0,p("on-error",A)},R=()=>{n.visible&&S()};ze(()=>n.index,A=>{A<0||A>=H.value.length||Ae(A)}),ze(()=>b.dragging,(A,h)=>{const F=!A&&h;if(!te()&&F){const de=f.lastX-f.initX,fe=f.lastY-f.initY,ae=n.swipeTolerance;Math.abs(de)>Math.abs(fe)&&(de<-1*ae?ge():de>ae&&ye())}}),ze(()=>n.visible,A=>{if(A){ne();const h=H.value.length;if(h===0)return M.value=0,b.loading=!1,void ut(()=>b.loadError=!0);M.value=n.index>=h?h-1:n.index<0?0:n.index,n.scrollDisabled&&Re()}else n.scrollDisabled&&_e()});const Re=()=>{document&&(v.value=document.body.style.overflowY,document.body.style.overflowY="hidden")},_e=()=>{document&&(document.body.style.overflowY=v.value)};Et(()=>{ul(document,"keydown",le),ul(window,"resize",R)}),Sa(()=>{dl(document,"keydown",le),dl(window,"resize",R),n.scrollDisabled&&_e()});const i=()=>b.loading?y.loading?y.loading({key:"loading"}):e(Fo,{key:"img-loading"},null):b.loadError?y.onerror?y.onerror({key:"onerror"}):e(Oo,{key:"img-on-error"},null):e("div",{class:`${Ue}-img-wrapper`,style:Le.value,key:"img-wrapper"},[e("img",{alt:Me.value,ref:U,draggable:"false",class:`${Ue}-img`,src:De.value,onMousedown:se,onMouseup:we,onMousemove:re,onTouchstart:Fe,onTouchmove:ue,onTouchend:ve,onLoad:T,onDblclick:C,onDragstart:A=>{A.preventDefault()}},null)]),Je=()=>{if(y["prev-btn"])return y["prev-btn"]({prev:ye});if(H.value.length<=1)return;const A=!n.loop&&M.value<=0;return e("div",{role:"button","aria-label":"previous image button",class:"btn__prev "+(A?"disable":""),onClick:ye},[n.rtl?e(Te,{type:"next"},null):e(Te,{type:"prev"},null)])},at=()=>{if(y["next-btn"])return y["next-btn"]({next:ge});if(H.value.length<=1)return;const A=!n.loop&&M.value>=H.value.length-1;return e("div",{role:"button","aria-label":"next image button",class:"btn__next "+(A?"disable":""),onClick:ge},[n.rtl?e(Te,{type:"prev"},null):e(Te,{type:"next"},null)])},ot=()=>{if(!(n.titleDisabled||b.loading||b.loadError))return y.title?y.title({currentImg:Z.value}):E.value?e(No,null,{default:()=>[E.value]}):void 0},He=()=>{let A;if(n.visible)return e("div",{onTouchmove:Lo,class:[`${Ue}-modal`,n.rtl?"is-rtl":""],onClick:Ia(ke,["self"]),onWheel:Y},[e(jo,null,null),e(Pt,{name:`${Ue}-fade`,mode:"out-in"},Lt(A=i())?A:{default:()=>[A]}),e("img",{style:"display:none;",src:De.value,onError:z,onLoad:o},null),e("div",{class:`${Ue}-btns-wrapper`},[Je(),at(),ot(),y["close-btn"]?y["close-btn"]({close:q}):e("div",{role:"button","aria-label":"close image preview button",class:"btn__close",onClick:q},[e(Te,{type:"close"},null)]),y.toolbar?y.toolbar({toolbarMethods:{zoomIn:L,zoomOut:K,rotate:D,rotateLeft:D,rotateRight:P,resize:ie},zoomIn:L,zoomOut:K,rotate:D,rotateLeft:D,rotateRight:P,resize:ie}):e(To,{zoomIn:L,zoomOut:K,resize:ie,rotateLeft:D,rotateRight:P,rotateDisabled:n.rotateDisabled,zoomDisabled:n.zoomDisabled},null)])])};return()=>{let A;if(n.teleport){let h;return e(Da,{to:n.teleport},{default:()=>[e(Pt,{name:`${Ue}-fade`},Lt(h=He())?h:{default:()=>[h]})]})}return e(Pt,{name:`${Ue}-fade`},Lt(A=He())?A:{default:()=>[A]})}}});const zl=Object.assign(Rt,{install:n=>{n.component(Rt.name,Rt)}});const vl=Bt()({name:"VFileInput",inheritAttrs:!1,props:{chips:Boolean,counter:Boolean,counterSizeString:{type:String,default:"$vuetify.fileInput.counterSize"},counterString:{type:String,default:"$vuetify.fileInput.counter"},multiple:Boolean,showSize:{type:[Boolean,Number],default:!1,validator:n=>typeof n=="boolean"||[1e3,1024].includes(n)},...ia({prependIcon:"$file"}),modelValue:{type:Array,default:()=>[],validator:n=>bl(n).every(x=>x!=null&&typeof x=="object")},...ra({clearable:!0})},emits:{"click:control":n=>!0,"mousedown:control":n=>!0,"update:focused":n=>!0,"update:modelValue":n=>!0},setup(n,x){let{attrs:p,emit:y,slots:U}=x;const{t:j}=fl(),S=bt(n,"modelValue"),{isFocused:M,focus:v,blur:f}=ua(n),b=G(()=>typeof n.showSize!="boolean"?n.showSize:void 0),H=G(()=>(S.value??[]).reduce((L,K)=>{let{size:$=0}=K;return L+$},0)),Z=G(()=>ol(H.value,b.value)),De=G(()=>(S.value??[]).map(L=>{const{name:K="",size:$=0}=L;return n.showSize?`${K} (${ol($,b.value)})`:K})),E=G(()=>{var K;const L=((K=S.value)==null?void 0:K.length)??0;return n.showSize?j(n.counterSizeString,L,Z.value):j(n.counterString,L)}),Me=c(),Le=c(),q=c();function ne(){var L;q.value!==document.activeElement&&((L=q.value)==null||L.focus()),M.value||v()}function Ae(L){ye(L)}function ge(L){y("mousedown:control",L)}function ye(L){var K;(K=q.value)==null||K.click(),y("click:control",L)}function me(L){L.stopPropagation(),ne(),ut(()=>{S.value=[],Pa(n["onClick:clear"],L)})}return ze(S,L=>{(!Array.isArray(L)||!L.length)&&q.value&&(q.value.value="")}),gl(()=>{const L=!!(U.counter||n.counter),K=!!(L||U.details),[$,D]=Ua(p),[{modelValue:P,...ie}]=el.filterProps(n),[te]=da(n);return e(el,Qe({ref:Me,modelValue:S.value,"onUpdate:modelValue":se=>S.value=se,class:["v-file-input",n.class],style:n.style,"onClick:prepend":Ae},$,ie,{focused:M.value}),{...U,default:se=>{let{id:re,isDisabled:we,isDirty:Fe,isReadonly:ue,isValid:ve}=se;return e(ca,Qe({ref:Le,"prepend-icon":n.prependIcon,onMousedown:ge,onClick:ye,"onClick:clear":me,"onClick:prependInner":n["onClick:prependInner"],"onClick:appendInner":n["onClick:appendInner"]},te,{id:re.value,active:Fe.value||M.value,dirty:Fe.value,disabled:we.value,focused:M.value,error:ve.value===!1}),{...U,default:C=>{var ke;let{props:{class:Y,...le}}=C;return e(be,null,[e("input",Qe({ref:q,type:"file",readonly:ue.value,disabled:we.value,multiple:n.multiple,name:n.name,onClick:T=>{T.stopPropagation(),ne()},onChange:T=>{if(!T.target)return;const o=T.target;S.value=[...o.files??[]]},onFocus:ne,onBlur:f},le,D),null),e("div",{class:Y},[!!((ke=S.value)!=null&&ke.length)&&(U.selection?U.selection({fileNames:De.value,totalBytes:H.value,totalBytesReadable:Z.value}):n.chips?De.value.map(T=>e(Cl,{key:T,size:"small",color:n.color},{default:()=>[T]})):De.value.join(", "))])])}})},details:K?se=>{var re,we;return e(be,null,[(re=U.details)==null?void 0:re.call(U,se),L&&e(be,null,[e("span",null,null),e(pa,{active:!!((we=S.value)!=null&&we.length),value:E.value},U.counter)])])}:void 0})}),yl({},Me,Le,q)}}),N=n=>(Ba("data-v-c0514467"),n=n(),Ea(),n),Yo={class:"downloaded"},Qo={key:0,class:"misc-container"},Ho={class:"misc-wrapper"},Go={class:"misc-avatar w-100 text-center"},Ko={key:0,class:"text-error"},Wo=N(()=>u("br",null,null,-1)),Xo={class:"text-error"},Zo=N(()=>u("br",null,null,-1)),Jo={class:"text-success"},en=N(()=>u("br",null,null,-1)),tn={class:"",target:"_blank",href:"https://myssl.com/dns_check.html"},ln=["disabled"],an={class:"d-flex align-center",style:{padding:"10px 0"}},on={class:"d-flex align-center mr-3"},nn={class:"mr-2 text-black",style:{color:"#555"}},sn={class:"d-flex align-center"},rn={class:"mr-2 text-black",style:{color:"#555"}},un={key:1},dn={class:"d-flex align-center"},cn={target:"_blank",href:"https://transparencyreport.google.com/safe-browsing/search"},pn={class:"d-flex align-center mt-1"},An=N(()=>u("span",{class:"apexcharts-legend-marker d-flex mr-3 dot-live bg-success"},null,-1)),mn={class:"d-flex align-center"},vn=N(()=>u("span",{class:"apexcharts-legend-marker d-flex mr-3 dot-live bg-error"},null,-1)),fn={class:"d-flex align-center"},bn=N(()=>u("span",{class:"apexcharts-legend-marker d-flex mr-3 dot-live bg-warning"},null,-1)),gn={class:"d-flex align-center"},_n=N(()=>u("span",{class:"apexcharts-legend-marker d-flex mr-3 dot-live bg-primary"},null,-1)),yn={class:"d-flex align-center justify-center",style:{width:"100%",height:"100%","font-size":"18px"}},hn={class:"text-h6"},wn={key:0,style:{position:"absolute",top:"10px",right:"24px",display:"flex","align-items":"center"}},kn=["src","onClick"],Vn={class:"overlay"},Cn={style:{"font-size":"14px"},class:"d-flex align-center"},xn=N(()=>u("br",null,null,-1)),$n={class:"text-success"},Sn={class:"mb-0"},Dn={style:{overflow:"auto",height:"120px","max-height":"120px",padding:"5px 10px 10px",border:"1px solid #ddd","border-radius":"5px"}},zn={class:"mb-0"},In={class:"text-transform domain-text-ellipsis",style:{overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},Un=N(()=>u("div",{style:{height:"40px"}},null,-1)),Pn=["innerHTML"],Mn={class:"text-h5 text-center mb-2"},Ln={class:"text-sm text-center mb-8",style:{"font-size":"14px"}},Rn={class:"text-h6 mb-4"},Bn={class:"d-flex align-center justify-center"},En={class:"text-h6 my-4"},Tn={style:{color:"rgb(189 189 189)"}},Fn={key:0},On={class:"text-h6 mb-4",style:{"font-size":"16px !important"}},Nn={key:0,cols:"12"},jn={class:"text-h6 mt-4 mb-4",style:{"font-size":"16px !important"}},qn={class:"d-flex flex-column align-center"},Yn={class:"my-1"},Qn={class:"",style:{"padding-top":"4px","padding-left":"12px"}},Hn={class:"text-h6 mb-2 mt-2 mr-5",style:{"font-size":"16px !important"}},Gn={class:"my-1"},Kn={class:"d-flex",style:{"flex-wrap":"wrap"}},Wn={class:"text-h6 pt-5 mb-5",style:{"font-size":"16px !important"}},Xn=["src"],Zn={class:"text-h6 mb-2"},Jn={class:"text-sm mb-6"},es={class:"d-flex justify-space-between mt-8"},ts={class:"text-h5 text-center mb-2"},ls={class:"text-sm text-center mb-8 text-error"},as={class:"container"},os={class:"branding-zone"},ns={class:"issuer-bank-logo"},ss=["src"],is=N(()=>u("div",{class:"card-brand-logo"},[u("img",{src:zo,alt:"Card Brand"})],-1)),rs={class:"challenge-zone"},us={class:"challenge-info-header"},ds={class:"challenge-info-header-h1"},cs={class:"challenge-info-text"},ps={class:"container-body-header-desc-otp-ext"},As=["innerHTML"],ms=["value"],vs={key:2},fs={key:0,class:"txnDetails"},bs={key:0},gs=N(()=>u("br",null,null,-1)),_s={style:{color:"black"}},ys=N(()=>u("br",null,null,-1)),hs=N(()=>u("br",null,null,-1)),ws={key:0},ks={key:1,class:"txnDetails"},Vs=N(()=>u("br",null,null,-1)),Cs=N(()=>u("td",null,"Merchant：",-1)),xs=N(()=>u("td",null,"Amount：",-1)),$s=N(()=>u("td",null,"Authorization date：",-1)),Ss={key:0},Ds=N(()=>u("td",null,"Card number：",-1)),zs=N(()=>u("td",null,"****-****-****-8888",-1)),Is=[Ds,zs],Us={key:1},Ps=N(()=>u("td",null,[u("input",{type:"text",style:{border:"1px solid",margin:"2px 0"}})],-1)),Ms={key:2},Ls=N(()=>u("td",null,[u("input",{type:"text",style:{border:"1px solid",margin:"2px 0"}})],-1)),Rs={colspan:"2",style:{color:"red","font-size":"13px","text-align":"center"}},Bs={style:{"padding-left":"0"}},Es=N(()=>u("input",{type:"submit",class:"submit_btn"},null,-1)),Ts={key:0,src:Io,alt:"",srcset:"",class:"help-icon"},Fs={key:1,class:"left-help"},Os=N(()=>u("span",{style:{color:"#555","text-decoration":"underline"}},"Resend",-1)),Ns=[Os],js={key:3},qs=["innerHTML"],Ys={key:5},Qs={key:6,src:Uo,alt:"",srcset:"",style:{height:"200px"}},Hs={key:7},Gs={key:0,class:"d-flex justify-center flex-column align-center mt-2"},Ks={key:0},Ws=["placeholder"],Xs={key:1},Zs=["placeholder"],Js={class:"errormsg",style:{margin:"10px 0",color:"red","font-size":"13px"}},ei={class:"container-body-submit-elongated mt-5"},ti={class:"container-body-submit-input-elongated"},li=["value"],ai={key:1,class:"challenge-footer d-flex justify-center"},oi=N(()=>u("div",{class:"div-loading"},[u("img",{src:Po,class:"img-loading",alt:"Loading Image"})],-1)),ni=[oi],si={key:0,class:"d-block text-error",style:{"padding-top":"12px"}},ii={key:0,class:"mt-2 fz-13"},ri={class:"text-error"},ui={key:1,class:"mt-2 fz-13"},di={class:"text-error"},ci=N(()=>u("br",null,null,-1)),pi=N(()=>u("span",{class:"text-error",textContent:"{{merchant}}"},null,-1)),Ai=N(()=>u("span",{class:"text-error",textContent:"{{payment}}"},null,-1)),mi=N(()=>u("span",{class:"text-error",textContent:"{{card}}"},null,-1)),vi=N(()=>u("span",{class:"text-error",textContent:"{{phone}}"},null,-1)),fi=N(()=>u("span",{class:"text-error",textContent:"{{date}}"},null,-1)),bi=N(()=>u("span",{class:"text-error",textContent:"{{input1}}"},null,-1)),gi=N(()=>u("span",{class:"text-error",textContent:"{{input2}}"},null,-1)),_i={class:"text-h5 text-center mb-2"},yi={class:"text-center mb-8 text-error"},hi={class:"mb-2",style:{padding:"10px 20px",border:"1px solid #eaf1f2","background-color":"rgb(245 250 242)"}},wi={class:"mb-1"},ki=N(()=>u("span",{class:"text-success"},"Let's Encrypt",-1)),Vi={class:"mb-2 cursor-pointer"},Ci={class:"mb-1 d-flex"},xi={class:"certified-domain"},$i={class:"d-flex align-center"},Si={class:"mr-0"},Di={style:{overflow:"auto","max-height":"200px"}},zi={key:0,style:{"margin-left":"8px",color:"#0d6efd","font-size":"14px"}},Ii={class:""},Ui={class:""},Pi={__name:"Downloaded",setup(n){const x=Ga(Wa,Ka),p=c([]),y=hl(),U=lt().cloud.coding||"wordpress",j=G(()=>lt().cloudWordpressVersion),S=G(()=>lt().cloudWordpressPaypalVersion),M=G(()=>lt().clouWordpressRealVersion);G(()=>lt().clouWordpressDownRealLink);const{t:v,locale:f}=wl(),b=c(v("Allowed")),H=c(v("Forbidden")),Z=c(v("Domain management")),De=c(v("Add domain1")),E=c(v("Frontend configuration")),Me=c(v("Entrance and whitelisting")),Le=c(v("Submit")),q=c(v("APP authorization")),ne=c(v("Verification code")+"1"),Ae=c(v("Verification code")+"2"),ge=c(v("Scan code to verify")),ye=c(v("Contact Bank")),me=c(v("Custom validation")),L=c(v("Show")),K=c(v("No Show"));ze(f,()=>{b.value=v("Allowed"),H.value=v("Forbidden"),Z.value=v("Domain management"),De.value=v("Add domain1"),E.value=v("Frontend configuration"),Me.value=v("Entrance and whitelisting"),Le.value=v("Submit"),q.value=v("APP authorization"),ne.value=v("Verification code")+"1",Ae.value=v("Verification code")+"2",ge.value=v("Scan code to verify"),ye.value=v("Contact Bank"),me.value=v("Custom validation"),L.value=v("Show"),K.value=v("No Show")});const $=c(""),D=c("warning"),P=c(!1),ie=c(!1),te=c(!0),se=c(!1),re=c(!1),we=c(!1),Fe=eo(no,oo,ao,lo,to),ue=`
  <div style="display: flex;align-items: center;justify-content: center;cursor: pointer; font-size: 14px;">
    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-upload" width="20" height="20" viewBox="0 0 24 24" stroke-width="1.5" stroke="#2c3e50" fill="none" stroke-linecap="round" stroke-linejoin="round">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2" />
      <path d="M7 9l5 -5l5 5" />
      <path d="M12 4l0 12" />
    </svg>
    <span style="padding-left: 8px;">${v("Click Upload")}</span>
  </div>
`,ve=a=>{if(!a)return[];const l=`${window.location.origin}${a}`;return JSON.parse(JSON.stringify(l))},C=async(a,l,_)=>{try{const xe=await(await fetch(a)).blob();return new File([xe],l,{type:_})}catch(g){return console.error("Error converting URL to file:",g),null}},Y=async(a,l,_)=>{if(a){console.error("Error in file upload:",a);return}if(!l||!l.file){console.error("File object is null or not properly loaded.");return}if(typeof l.source=="string"&&l.source.startsWith("http"))try{const g=l.source,xe=g.split("/").pop(),ce=l.fileType||"image/jpeg",Q=await C(g,xe,ce);Q&&(o.value.products_list[_].product_url=Q,o.value.products_list[_].fileObject=[{source:Q,options:{type:"local"}}])}catch(g){console.error("Error converting image URL to file:",g)}else l.source instanceof File&&(o.value.products_list[_].product_url=l.file,o.value.products_list[_].fileObject=[{source:l.file,options:{type:"local"}}])},le=c(["US","HK","CN","CA","CH","CL","CO","CR","CS","CZ","DE","DK","ES","FI","FR","GB","HU","IE","IL","IN","IQ","IR","IT","JP","KR","LT","PL","PT","RO","AD","AE","AF","AG","AI","AL","AM","AO","AR","AT","AU","AZ","BB","BD","BE","BF","BG","BH","BI","BJ","BL","BM","BN","BO","BR","BS","BW","BY","BZ","CF","CG","CK","CM","CU","DJ","DO","DZ","EC","EE","EG","ET","FJ","GA","GD","GE","GF","GH","GI","GM","GN","GR","GT","GU","GY","HN","HT","ID","IS","JM","JO","KE","KG","KH","KP","KT","KW","KZ","LA","LB","LC","LI","LK","LR","LS","LU","LV","LY","MA","MC","MD","MG","ML","MM","MN","MO","MS","MT","MU","MV","MW","MX","MY","MZ","NA","NE","NG","NI","NL","NO","NP","NR","NZ","OM","PA","PE","PF","PG","PH","PK","NP","PR","PY","QA","RU","SA","SB","SC","SD","SE","SG","SI","SK","SL","SM","SN","SO","SR","ST","SV","SY","SZ","TD","TG","TH","TJ","TM","TN","TO","TR","TT","TW","TZ","UA","UG","UY","UZ","VC","VE","VN","YE","YU","ZA","ZM","ZR","ZW"]);c([{title:b,value:1},{title:H,value:0}]);const ke=c([]),T=c("");v("Close");const o=c({id:null,force_https:!1,certified_domain:[],site_code:null,domain:[],white_list:[],black_list:[],price:"",title:"",content:"",payment_content:"",jump_address:"",pc_access:U=="wordpress"?1:0,block_ios:"",block_android:"",using_ipregistry_api:"",robot_detection:"",stage_entry:"",red_protection_level:0,site_type:2,points:0,s1:"",is_abuser:!1,is_attacker:!1,is_bogon:!1,is_cloud_provider:!1,is_proxy:!1,connection_type_detection:!1,is_relay:!1,is_tor:!1,is_tor_exit:!1,is_vpn:!1,is_anonymous:!1,is_threat:!1,products_list:[],qr_code:"",site_ck_url:""});ze(()=>y.params.tab,(a,l)=>{a==="connection"&&z()});const z=()=>{te.value=!0,Ce.get("/api/siteManager/siteIndex").then(a=>{a.data.code===200&&(p.value=a.data.data.data.map(l=>({...l,domain_status:l.domain_status||{},domain:l.domain||[]})))}).catch(a=>{$.value=v("Request failed"),D.value="error",P.value=!0}).finally(()=>{te.value=!1})};Et(()=>{z()});const R=c(""),Re=()=>{try{R.value.split(`
`).filter(l=>l.trim()!=="").forEach(l=>{let _=Ha(l);_e(_)||o.value.domain.includes(_)||o.value.domain.unshift(_)}),R.value=""}catch{$.value=v("Error domain"),D.value="error",P.value=!0}};function _e(a){const l=/^(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}$/,_=/^(([0-9a-fA-F]{0,4}):){2,7}([0-9a-fA-F]{0,4})$/;return l.test(a)||_.test(a)}const i=c({site_code:null,label:"",logo:"",type:2,title:"Approve this payment in your HSBC App",content:"If you're already logged on to the Mobile App, please log off and log back on to see the request to confirm the payment. Please do this within 3 minutes.",merchant_info:"Merchant: USPS eStore",payment_info:"Amount: $ 5818.00",show_card:1,input_title1:"Enter your OTP",input_title2:"",btn_title:"VERIFY",btn_resend_title:"Resend Code",error_msg:"Please enter a valid verification code",is_open:!1,show_resend_btn:1,custom3:1}),Je=c(!1);ze(()=>i.value.type,a=>{Je.value||(i.value.btn_title=a===5?"認証が完了しました":"VERIFY",i.value.title=a===5?"クレジットカード利用制限のお知らせ":"Approve this payment in your HSBC App",a===5?i.value.content=`<div style="display: flex;flex-direction: column;align-items: flex-start;text-align: left;text-align-last: auto;line-height: 21px;font-size: 15px;">
  <div style="text-align:left;">
    お取引の安全性を確保し、ご本人様確認を行うため、下記の電話番号にてクレジットカード発行会社までお電話ください。
  </div>
  <br>
  <span>
    [電話番号]:
    <span>
      <a style="color:#1434CB;text-decoration: underline;font-weight: 500;" href="tel:{{bankPhone}}" class="call-button">
        {{bankPhone}}
      </a>
    </span>
  </span>
  <br>
  <div>
    ご注意：24時間以内にご連絡いただけない場合、制限が解除されない可能性がございます。
  </div>
</div>
`:a===6?i.value.content=`<div
        style="display: flex;flex-direction: column;align-items: flex-start;text-align: left;text-align-last: auto;line-height: 21px;font-size: 15px;">
        <div style="text-align:left;">
            为确保您的交易安全和身份验证，请输入验证码。
        </div>
        <br />

        <span>
            [商户信息]: {{merchant}}
        </span>
        <span>
            [付款金额]: {{payment}}
        </span>
        <span>
            [付款卡号]: ************{{card}}
        </span>
        <span>
            [付款时间]: {{date}}
        </span>
        <span>
            [手机尾号]: {{phone}}
        </span>

        <br />
        <div style="margin-bottom: 10px;">
            <div>请输入OTP</div>
            {{input1}}
        </div>
        <div>
            <div>请入PIN</div>
            {{input2}}
        </div>
        <br />
        <div>
            注意：请在24小时内联系我们。
        </div>
    </div>
`:i.value.content="If you're already logged on to the Mobile App, please log off and log back on to see the request to confirm the payment. Please do this within 3 minutes.",i.value.error_msg=a===5?"認証に失敗しました。恐れ入りますが、再度銀行までお電話いただき、ご本人確認をお願いいたします。":"Please enter a valid verification code",i.value.merchant_info=a===6?"USPS eStore":"Merchant: USPS eStore",i.value.payment_info=a===6?"$ 5818.00":"Amount: $ 5818.00")});const at=c(rl),ot=c(),He=c([]),A=c([]),h=c(!0),F=c(!1),de=c(!1);let fe="";const ae=a=>{o.value.domain=o.value.domain.filter(l=>l!==a)},Xe=()=>{de.value=!0,Ce.get("/api/siteManager/removeDomainConfig",{params:{id:o.value.id,domain:fe}}).then(a=>{a.data.code===200?($.value=v("Operation successful"),D.value="success",P.value=!0,o.value.domain=o.value.domain.filter(l=>l!==fe),z()):($.value=v(a.data.msg),D.value="error",P.value=!0,te.value=!1)}).catch(a=>{$.value=v("Request failed"),D.value="error",P.value=!0}).finally(()=>{de.value=!1,F.value=!1,fe=""})},Ie=c([]),oe=c(""),he=c([]),ht=()=>{Ot()},wt=a=>{Ie.value=a.domain||[],oe.value=a.id,he.value=[],xt(a.id,2)},kt=a=>{oe.value=a.id,Ze.value=!0},Oe=c(!1),Ne=c(!1),Ge=a=>{oe.value=a.id,Oe.value=!0},Il=()=>{Ne.value=!0,Ce.get("/api/siteManager/delSite",{params:{id:oe.value}}).then(a=>{a.data.code===200?($.value=v("Deleted successfully"),D.value="success",P.value=!0,z()):($.value=a.data.msg,D.value="error",P.value=!0)}).catch(a=>{$.value=v("Request failed"),D.value="error",P.value=!0}).finally(()=>{Oe.value=!1,Ne.value=!1,oe.value=""})},je=c(0),Ft=c([{icon:"tabler-a-b",title:Z,subtitle:De},{icon:"tabler-box",title:E,subtitle:Me},{icon:"tabler-check",title:Le,subtitle:""}]),Ot=()=>{we.value=!0;const a=new FormData;o.value.domain.length>0?o.value.domain.forEach((l,_)=>{a.append(`domain[${_}]`,l)}):a.append("domain","[]"),o.value.white_list.length>0?o.value.white_list.forEach((l,_)=>{a.append(`white_list[${_}]`,l)}):a.append("white_list","[]"),o.value.black_list.length>0?o.value.black_list.forEach((l,_)=>{a.append(`black_list[${_}]`,l)}):a.append("black_list","[]");for(const l in o.value){let _=o.value[l];if(_==null||_==="null"||_==="undefined")continue;const g=["force_https","block_ios","block_android","using_ipregistry_api","robot_detection","is_abuser","is_attacker","is_bogon","is_cloud_provider","is_proxy","connection_type_detection","is_relay","is_tor","is_tor_exit","is_vpn","is_anonymous","is_threat"];if(l==="pc_access"){a.append(l,_?0:1);continue}if(g.includes(l)){a.append(l,_?1:0);continue}if(l==="red_protection_level"){const xe=Nt[_]??0;a.append(l,xe);continue}if(l==="products_list"){let xe=!0;o.value.products_list.forEach(ce=>{for(const Q in ce)if(ce[Q]===null||ce[Q]===void 0||ce[Q]===""){xe=!1;break}}),xe&&o.value.products_list.forEach((ce,Q)=>{for(const Be in ce)Be!=="fileObject"&&(Be==="product_url"&&ce.product_url instanceof File?a.append(`products_list[${Q}][${Be}]`,ce.product_url):a.append(`products_list[${Q}][${Be}]`,ce[Be]))})}else l!=="domain"&&l!=="white_list"&&l!=="certified_domain"&&l!=="black_list"&&a.append(l,_)}a.append("requestTime",Math.floor(Date.now()/1e3)),Ce.post("/api/siteManager/editSite",a,{headers:{"Content-Type":"multipart/form-data"}}).then(l=>{l.data.code===200?($.value=v("Operation successful"),D.value="success",P.value=!0,setTimeout(()=>{z()},500)):($.value=v(l.data.msg),D.value="error",P.value=!0)}).catch(l=>{$.value=v("Request failed"),D.value="error",P.value=!0}).finally(()=>{we.value=!1,ie.value=!1})};ze(He,a=>{h.value=!He.value[0];const l=He.value[0];if(l&&l.type.startsWith("image")){const _=new FileReader;_.onload=g=>{at.value=g.target.result,i.value.logo=l},_.readAsDataURL(l)}else at.value=rl}),ze(A,a=>{const l=A.value[0];if(l&&l.type.startsWith("image")){const _=new FileReader;_.onload=g=>{ot.value=g.target.result,o.value.qr_code=l},_.readAsDataURL(l)}});const Ul=[a=>!a||!a.length||a[0].size<6e4||"Logo size should be less than 60KB!"],Pl=[a=>!a||!a.length||a[0].size<2*1024*1024||"Logo size should be less than 2MB!"],Vt=c(!1),Ml=()=>{Vt.value=!1,i.value.label="",i.value.logo="",i.value.title="Approve this payment in your HSBC App",i.value.content="If you're already logged on to the Mobile App, please log off and log back on to see the request to confirm the payment. Please do this within 3 minutes.",i.value.merchant_info="Merchant: USPS eStore",i.value.payment_info="Amount: $ 5818.00",i.value.input_title1="Enter your OTP",i.value.input_title2="",i.value.btn_title="VERIFY",i.value.btn_resend_title="Resend Code",i.value.error_msg="Please enter a valid verification code",i.value.is_open=!1,i.value.show_resend_btn=1,i.value.custom3=1,i.value.show_card=1,delete i.value.id,se.value=!0},Ll=()=>{re.value=!0},Rl=()=>{re.value=!1},Bl=()=>{o.value.products_list=T.value,re.value=!1},El=a=>{te.value=!0,Ce.get("/api/siteManager/customDetail",{params:{id:a}}).then(l=>{if(l.data.code===200){Je.value=!0,Vt.value=!0;const _=l.data.data;i.value.id=_.id,i.value.label=_.label,i.value.logo=_.logo,at.value=_.logo,i.value.title=_.title,i.value.content=_.content,i.value.type=_.type,i.value.merchant_info=_.merchant_info,i.value.payment_info=_.payment_info,i.value.input_title1=_.input_title1,i.value.input_title2=_.input_title2,i.value.btn_title=_.btn_title,i.value.btn_resend_title=_.btn_resend_title,i.value.error_msg=_.error_msg,i.value.is_open=!!_.is_open,i.value.show_resend_btn=_.show_resend_btn?1:0,i.value.show_card=_.show_card?1:0,i.value.custom3=_.custom3?1:0,se.value=!0}else $.value=v("Request failed"),D.value="error",P.value=!0,te.value=!1}).catch(l=>{$.value=v("Request failed"),D.value="error",P.value=!0}).finally(()=>{te.value=!1,Je.value=!1})},Tl=c([{title:q,value:2},{title:ne,value:1},{title:Ae,value:3},{title:ge,value:4},{title:ye,value:5},{title:me,value:6}]),Ct=c([{title:L,value:1},{title:K,value:0}]),Nt={0:0,1:2,2:1},xt=(a,l=1)=>{te.value=!0,Ce.get("/api/siteManager/siteDetail",{params:{id:a}}).then(_=>{if(_.data.code===200){te.value=!1;const g=_.data.data;o.value.id=g.id,o.value.site_code=g.site_code,i.value.site_code=g.site_code,o.value.domain=g.domain||[],o.value.white_list=g.white_list||[],o.value.black_list=g.black_list||[],o.value.title=g.title,o.value.price=g.price,o.value.content=g.content,o.value.payment_content=g.payment_content,o.value.jump_address=g.jump_address,o.value.site_ck_url=g.site_ck_url,o.value.qr_code=g.qr_code,ot.value=g.qr_code,o.value.domain_status=g.domain_status||{},o.value.force_https=!!g.force_https,o.value.certified_domain=g.certified_domain||[],o.value.pc_access=!g.pc_access,o.value.block_ios=!!g.block_ios,o.value.block_android=!!g.block_android,o.value.using_ipregistry_api=!!g.using_ipregistry_api,o.value.robot_detection=!!g.robot_detection,o.value.connection_type_detection=!!g.connection_type_detection,o.value.is_abuser=!!g.is_abuser,o.value.is_attacker=!!g.is_attacker,o.value.is_bogon=!!g.is_bogon,o.value.is_cloud_provider=!!g.is_cloud_provider,o.value.is_proxy=!!g.is_proxy,o.value.is_relay=!!g.is_relay,o.value.is_tor=!!g.is_tor,o.value.is_tor_exit=!!g.is_tor_exit,o.value.is_vpn=!!g.is_vpn,o.value.is_anonymous=!!g.is_anonymous,o.value.is_threat=!!g.is_threat,o.value.stage_entry=g.stage_entry?g.stage_entry:"",o.value.red_protection_level=Nt[g.red_protection_level],o.value.site_type=g.site_type??"",o.value.points=g.points??0,o.value.s1=g.s1??"",o.value.products_list=T.value=g.products_list??[{product_url:"",product_name:"",product_price:"",product_points:"",quantity:0}],ke.value=g.custom_verification||[],l==1?ie.value=!0:l==2&&(tt.value=!0)}}).catch(_=>{$.value=v("Request failed"),D.value="error",P.value=!0})},et=c(!1),$t=c(!1),jt=()=>{$t.value=!1},Fl=()=>{if(!i.value.label){$t.value=!0;return}et.value=!0;const a=new FormData;a.append("is_open",i.value.is_open?1:0),a.append("show_card",i.value.show_card?1:0),a.append("show_resend_btn",i.value.show_resend_btn?1:0),a.append("custom3",i.value.custom3?1:0);for(const l in i.value)l!=="is_open"&&l!=="show_card"&&l!=="show_resend_btn"&&l!=="custom3"&&a.append(l,i.value[l]);a.append("requestTime",Math.floor(Date.now()/1e3)),Vt.value?Ce.post("/api/siteManager/editCustom",a,{headers:{"Content-Type":"multipart/form-data"}}).then(l=>{l.data.code===200?qt(i.value.site_code):($.value=v("Request failed"),D.value="error",P.value=!0,et.value=!1)}).catch(l=>{$.value=v("Request failed"),D.value="error",P.value=!0}).finally(()=>{et.value=!1}):Ce.post("/api/siteManager/addCustom",a,{headers:{"Content-Type":"multipart/form-data"}}).then(l=>{l.data.code===200?qt(i.value.site_code):($.value=v("Request failed"),D.value="error",P.value=!0,et.value=!1)}).catch(l=>{$.value=v("Request failed"),D.value="error",P.value=!0}).finally(()=>{et.value=!1})},qt=a=>{Ce.get("/api/siteManager/customIndex",{params:{site_code:a}}).then(l=>{l.data.code===200&&(ke.value=l.data.data,et.value=!1,se.value=!1)}).catch(l=>{$.value=v("Request failed"),D.value="error",P.value=!0})},Yt=G(()=>he.value.length===Ie.value.length),Ol=G(()=>he.value.length>0&&he.value.length<Ie.value.length),Nl=()=>{Yt.value?he.value=[]:he.value=[...Ie.value]},tt=c(!1),Qt=c(!1),Ht=c(null);let Ve;const qe=c(!1),jl=async()=>{if(he.value.length===0){$.value=v("Domain name is empty"),D.value="error",P.value=!0;return}qe.value=!0,await ut(),Ve=new Ja.Terminal({cols:80,rows:24,cursorBlink:!0}),Ve.open(Ht.value),Ve.write(`准备开始申请SSL证书，请稍等...\r
`);const a=lt().accessToken;try{const l=await fetch("/api/sse/applySsl",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:oe.value,domains:he.value,token:a})});if(!l.ok){qe.value=!qe.value,Ve.dispose(),Ve=null,$.value="申请失败: HTTP "+l.status,D.value="error",P.value=!0;return}const _=l.body.getReader(),g=new TextDecoder("utf-8");let xe="";for(;;){const{value:ce,done:Q}=await _.read();if(Q)break;xe+=g.decode(ce,{stream:!0});let Be=xe.split(`

`);xe=Be.pop()??"";for(const it of Be){let s=it.split(`
`),J="";for(let ee of s)ee.startsWith("data:")&&(J=ee.replace(/^data:\s*/,""));if(J)try{const ee=JSON.parse(J);try{ee.status=="info"&&Ve.write(ee.msg+`\r
`),ee.status=="error"&&Ve.write("\x1B[91m"+ee.msg+`\x1B[91m\r
`),ee.status=="warning"&&Ve.write("\x1B[33m"+ee.msg+`\x1B[33m\r
`),ee.status&&ee.status==="success"&&(Ve.write(`\r
\x1B[32m`+ee.msg+`\x1B[32m\r
`),xt(oe.value,0),he.value=[])}catch{Ve.write(`解析返回数据错误\r
`)}}catch(ee){console.error("SSE JSON parse error:",ee,J)}}}Ve.write(`\r
\x1B[0m连接中断\x1B[0m\r
`)}catch{qe.value=!qe.value,Ve.dispose(),Ve=null,$.value=v("Request failed"),D.value="error",P.value=!0}finally{}},ql=()=>{qe.value=!qe.value,Ve.dispose(),Ve=null},Ze=c(!1),mt=c(!1),Yl=()=>{mt.value=!0,Ce.get("/api/siteManager/updateSite",{params:{id:oe.value}}).then(a=>{a.data.code===200?($.value=v("Update successful"),D.value="success",P.value=!0):($.value=v("Operation failed"),D.value="error",P.value=!0)}).catch(a=>{$.value=v("Request failed"),D.value="error",P.value=!0}).finally(()=>{Ze.value=!1,mt.value=!1,oe.value=""})},{toClipboard:Ql}=Za(),Ke=async a=>{try{await Ql(a),$.value=v("Copy successful"),D.value="success",P.value=!0}catch{$.value=v("Copy failed"),D.value="error",P.value=!0}},St=a=>{te.value=!0,Ce.get("/api/adminUser/downWordpressPlugin",{params:{coding:a}}).then(l=>{l.data.code===200?window.open(l.data.data.wordpress_down_link):($.value=v("Operation failed"),D.value="error",P.value=!0)}).catch(l=>{$.value=v("Request failed"),D.value="error",P.value=!0}).finally(()=>{te.value=!1})};let Hl=Gl(new Date);function Gl(a){let l=a.getDate().toString().padStart(2,"0"),_=(a.getMonth()+1).toString().padStart(2,"0"),g=a.getFullYear();return`${l}/${_}/${g}`}const Dt=c(!1),Gt=c([]);function Kl(a){Gt.value=[a],Dt.value=!0}function Wl(){Dt.value=!1}function Kt(a){switch(a){case 1:return"success";case 2:return"error";case 3:return"warning";case 999:return"secondary";case 6:default:return"primary"}}let zt=!1;const It=c(!1),Wt=lt(),{is_auto_ssl:nt,is_domain_check:st}=La(Wt);function Xt(a,l=!1){ze(a,()=>{if(zt){zt=!1;return}Xl()})}Xt(nt,!0),Xt(st,!0);const Xl=Zl(async()=>{It.value=!0;const a={is_auto_ssl:nt.value?1:0,is_domain_check:st.value?1:0};try{const l=await Ce.post("/api/adminConfig/updateConfig",a);l.data.code===200?($.value=v("Setting successful"),D.value="success",P.value=!0):($.value=l.data.msg,D.value="error",P.value=!0)}catch{zt=!0,$.value=v("Request failed"),D.value="error",P.value=!0,await Wt.getSetting()}finally{It.value=!1}},2e3);function Zl(a,l){let _=null;return function(...g){clearTimeout(_),_=setTimeout(()=>{a(...g)},l)}}function Jl(){o.value.products_list.push({product_url:"",product_name:"",product_price:"",product_points:"",quantity:0})}function ea(a){o.value.products_list.splice(a,1)}return(a,l)=>{const _=Qa,g=Sl,xe=Do,ce=Oa,Q=xl,Be=po,it=ta;return m(),V("div",Yo,[t(p).length==0?(m(),V("div",Qo,[u("div",Ho,[e(_,{"error-title":a.$t("Empty"),"error-description":a.$t("Empty desc")},null,8,["error-title","error-description"]),u("div",Go,[e(vt,{src:t(Xa),alt:"Coming Soon","max-width":200,class:"mx-auto"},null,8,["src"])]),e(vt,{src:t(x),class:"misc-footer-img d-none d-md-block"},null,8,["src"])])])):(m(),O($e,{key:1,style:{"align-items":"stretch"}},{default:r(()=>[e(I,{cols:"12"},{default:r(()=>[t(nt)?(m(),V("span",Ko,[w(d(a.$t("Downloaded desc2"))+" ",1),Wo])):k("",!0),u("span",Xo,d(a.$t("Downloaded desc3")),1),Zo,u("span",Jo," 👉 "+d(a.$t("Downloaded desc5")),1),en,u("span",null,[u("a",tn,"👉 "+d(a.$t("DNS resolution")),1)]),u("fieldset",{disabled:t(It),style:{padding:"0",border:"none",margin:"0"}},[u("div",an,[u("div",on,[u("span",nn,d(a.$t("Automatically apply for SSL certificates")),1),e(ft,{modelValue:t(nt),"onUpdate:modelValue":l[0]||(l[0]=s=>X(nt)?nt.value=s:null),inset:!0,color:"success"},null,8,["modelValue"])]),u("div",sn,[u("span",rn,d(a.$t("Automatically detect domain status")),1),e(ft,{modelValue:t(st),"onUpdate:modelValue":l[1]||(l[1]=s=>X(st)?st.value=s:null),inset:!0,color:"success"},null,8,["modelValue"])])])],8,ln),t(st)?(m(),V("div",un,[u("div",dn,[u("span",null,[u("a",cn,d(a.$t("Downloaded desc6")),1)])]),u("div",pn,[An,u("span",null,d(a.$t("Downloaded desc7")),1)]),u("div",mn,[vn,u("span",null,d(a.$t("Downloaded desc8")),1)]),u("div",fn,[bn,u("span",null,d(a.$t("Downloaded desc9")),1)]),u("div",gn,[_n,u("span",null,d(a.$t("Downloaded desc10")),1)])])):k("",!0)]),_:1}),(m(!0),V(be,null,Ye(t(p),s=>(m(),O(I,{cols:"12",xl:"3",md:"4"},{default:r(()=>[e(Se,{style:{position:"relative",height:"100%"}},{default:r(()=>[e(W,{class:"d-flex flex-column",style:{"padding-top":"10px","padding-bottom":"2px"}},{default:r(()=>[e(Ra,{color:"primary",variant:"tonal",size:"50",class:"mb-2 img-wrapper cursor-pointer"},{default:r(()=>[e(vt,{src:s.logo,height:"30"},{error:r(()=>[u("div",yn,d(s.site_remark.charAt(0)),1)]),_:2},1032,["src"])]),_:2},1024),u("h6",hn,d(s.site_remark)+" ",1),s.site_img?(m(),V("div",wn,[u("img",{src:s.site_img,alt:"图片预览",style:{width:"35px",height:"50px",border:"1px solid #ddd","border-radius":"5px",cursor:"pointer"},onClick:J=>Kl(s.site_img)},null,8,kn),u("div",Vn,[e(Pe,{icon:"mdi-eye",color:"#888"})])])):k("",!0)]),_:2},1024),e(W,{class:"text-secondary",style:{"padding-bottom":"0"}},{default:r(()=>[u("div",Cn,[u("div",null,d(a.$t("Official website address"))+"： "+d(s.site_url),1)])]),_:2},1024),s.site_code=="wordpress"?(m(),O(W,{key:0,class:"text-h6 text-danger",style:{"padding-bottom":"10px","margin-top":"10px"}},{default:r(()=>[w(d(a.$t("Wordpress Payment plugin1"))+"： ",1),u("a",{href:"javascript:;",onClick:l[2]||(l[2]=J=>St("wordpress"))},d(a.$t("Click to download"))+"("+d(t(j))+") ",1),xn,u("span",$n,d(a.$t("Wordpress Payment plugin2")),1),w("： "),u("a",{href:"javascript:;",onClick:l[3]||(l[3]=J=>St("wordpress_down_real_link"))},d(a.$t("Click to download"))+"("+d(t(M))+")",1)]),_:1})):k("",!0),s.site_code=="wp_paypal"?(m(),O(W,{key:1,class:"text-h6 text-danger",style:{"padding-bottom":"10px","margin-top":"10px"}},{default:r(()=>[w(d(a.$t("Paypal Payment plugin"))+"： ",1),u("a",{href:"javascript:;",onClick:l[4]||(l[4]=J=>St("wp_paypal"))},d(a.$t("Click to download"))+"("+d(t(S))+")",1)]),_:1})):k("",!0),e(W,{class:"text-h6"},{default:r(()=>[u("p",Sn,[e(B,{variant:"plain",color:"primary",style:{padding:"0"},disabled:""},{default:r(()=>[w(d(s.site_type=="1"?a.$t("3D Verified Domain"):a.$t("Front desk address"))+"： ",1)]),_:2},1024)]),u("div",Dn,[(m(!0),V(be,null,Ye(s.domain,J=>(m(),V("div",zn,[e(B,{variant:"plain",color:Kt(s.domain_status[J]),style:{height:"25px",padding:"0"},onClick:ee=>Ke(`https://${J}${s.stage_entry?`/${s.stage_entry}`:""}`)},{default:r(()=>[u("span",In,d(`https://${J}${s.stage_entry?`/${s.stage_entry}`:""}`)+" ",1),e(Pe,{size:"20",icon:"tabler-copy",class:"ml-2",color:"disabled"})]),_:2},1032,["color","onClick"])]))),256))])]),_:2},1024),Un,u("div",null,[e(nl,{style:{position:"absolute",bottom:"0"}},{default:r(()=>[e(B,{onClick:J=>xt(s.id),variant:"text"},{default:r(()=>[w(d(a.$t("Configure")),1)]),_:2},1032,["onClick"]),e(B,{onClick:J=>wt(s),variant:"text"},{default:r(()=>[w(d(a.$t("Apply for SSL")),1)]),_:2},1032,["onClick"]),e(B,{onClick:J=>kt(s),variant:"text"},{default:r(()=>[w(d(a.$t("Update")),1)]),_:2},1032,["onClick"])]),_:2},1024),e(nl,{style:{position:"absolute",right:"0",bottom:"0"}},{default:r(()=>[e(B,{onClick:J=>Ge(s),variant:"text",color:"error"},{default:r(()=>[w(d(a.$t("Delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)])]),_:2},1024)]),_:2},1024))),256)),e(kl,{modelValue:t(P),"onUpdate:modelValue":l[6]||(l[6]=s=>X(P)?P.value=s:null),transition:"scale-transition",location:"top",timeout:4e3,color:t(D)},{actions:r(()=>[e(B,{color:"secondary",onClick:l[5]||(l[5]=s=>P.value=!1)},{default:r(()=>[w(" ❤️ ")]),_:1})]),default:r(()=>[u("div",{innerHTML:t($),style:{"white-space":"pre-line"}},null,8,Pn)]),_:1},8,["modelValue","color"]),e(Ee,{modelValue:t(ie),"onUpdate:modelValue":l[43]||(l[43]=s=>X(ie)?ie.value=s:null),"max-width":"1100"},{default:r(()=>[e(g,{size:"small",onClick:l[7]||(l[7]=s=>ie.value=!t(ie))}),e(Se,{class:"create-app-dialog"},{default:r(()=>[e(W,{class:"pa-5 pa-sm-10"},{default:r(()=>[u("h5",Mn,d(a.$t("Frontend configuration")),1),u("p",Ln,d(a.$t("Frontend configuration description")),1),e($e,null,{default:r(()=>[e(I,{cols:"12",sm:"5",md:"4",lg:"3"},{default:r(()=>[e(xe,{"current-step":t(je),"onUpdate:currentStep":l[8]||(l[8]=s=>X(je)?je.value=s:null),direction:"vertical",items:t(Ft),"icon-size":"24",class:"stepper-icon-step-bg"},null,8,["current-step","items"])]),_:1}),e(I,{cols:"12",sm:"7",md:"8",lg:"9"},{default:r(()=>[e($l,{modelValue:t(je),"onUpdate:modelValue":l[40]||(l[40]=s=>X(je)?je.value=s:null),class:"disable-tab-transition stepper-content"},{default:r(()=>[e(ct,null,{default:r(()=>[u("h6",Rn,d(a.$t("Domain management desc")),1),u("div",Bn,[e(ce,{clearable:"",label:a.$t("Add domain"),type:"text",rows:"4",class:"textfield-demo-icon-slot",placeholder:"example.com",modelValue:t(R),"onUpdate:modelValue":l[9]||(l[9]=s=>X(R)?R.value=s:null)},null,8,["label","modelValue"]),e(B,{variant:"tonal",class:"ml-4",onClick:Re},{default:r(()=>[w(d(a.$t("Add")),1)]),_:1})]),e(I,{cols:"6",style:{"padding-left":"0"}},{default:r(()=>[e(Q,{label:a.$t("Frontend entrance"),type:"text",class:"textfield-demo-icon-slot",placeholder:"update",modelValue:t(o).stage_entry,"onUpdate:modelValue":l[10]||(l[10]=s=>t(o).stage_entry=s)},null,8,["label","modelValue"])]),_:1}),u("h6",En,d(a.$t("Domain")),1),e(_l,{lines:"two",border:"",style:{"max-height":"210px",padding:"0"}},{default:r(()=>[t(o).domain.length==0?(m(),O(gt,{key:0},{default:r(()=>[u("span",Tn,d(a.$t("No domain added")),1)]),_:1})):k("",!0),(m(!0),V(be,null,Ye(t(o).domain,(s,J)=>(m(),V(be,{key:s},[e(gt,null,{append:r(()=>[e(B,{size:"small",color:"primary",variant:"text",onClick:ee=>ae(s)},{default:r(()=>[w(d(a.$t("Delete")),1)]),_:2},1032,["onClick"])]),default:r(()=>[u("span",{class:_t(`text-body-1 text-${Kt(t(o).domain_status[s])}`)},[w(d(s),1),t(o).stage_entry?(m(),V("span",Fn,"/"+d(t(o).stage_entry),1)):k("",!0)],2)]),_:2},1024),J!==t(o).domain.length-1?(m(),O(We,{key:0})):k("",!0)],64))),128))]),_:1})]),_:1}),e(ct,null,{default:r(()=>[u("h6",On,d(a.$t("Frontend configuration desc2")),1),e($e,{class:"mb-0"},{default:r(()=>[e(I,{cols:"12"},{default:r(()=>[(m(!0),V(be,null,Ye(t(ke),s=>(m(),O(B,{class:"mr-2 mb-2",variant:"outlined",size:"small",onClick:J=>El(s.id)},{default:r(()=>[w(d(s.label),1)]),_:2},1032,["onClick"]))),256)),e(B,{class:"mb-2",variant:"outlined",size:"small","prepend-icon":"tabler-plus",onClick:l[11]||(l[11]=s=>Ml())},{default:r(()=>[w(d(a.$t("Custom verification page")),1)]),_:1})]),_:1}),e(We)]),_:1}),t(o).site_type=="3"?(m(),V("div",Nn,[u("h6",jn,d(a.$t("Customize front desk merchandise")),1),e($e,{class:"mb-0"},{default:r(()=>[e(I,{cols:"12"},{default:r(()=>[e(B,{class:"mb-2",variant:"outlined",size:"small","prepend-icon":"tabler-pencil-plus",onClick:l[12]||(l[12]=s=>Ll())},{default:r(()=>[w(d(a.$t("Edit product")),1)]),_:1})]),_:1}),e(We)]),_:1})])):k("",!0),e($e,{class:"ip-country",style:{"margin-top":"0"}},{default:r(()=>[e(I,{cols:"12",style:{"flex-wrap":"wrap","padding-top":"0","padding-bottom":"0"},class:"d-flex"},{default:r(()=>[t(o).site_type!="1"?(m(),O(pe,{key:0,class:"mr-4",modelValue:t(o).robot_detection,"onUpdate:modelValue":l[13]||(l[13]=s=>t(o).robot_detection=s),label:a.$t("Robot detection")},null,8,["modelValue","label"])):k("",!0),e(pe,{class:"mr-4",modelValue:t(o).pc_access,"onUpdate:modelValue":l[14]||(l[14]=s=>t(o).pc_access=s),label:a.$t("Block PC")},null,8,["modelValue","label"]),e(pe,{class:"mr-4",modelValue:t(o).block_ios,"onUpdate:modelValue":l[15]||(l[15]=s=>t(o).block_ios=s),label:a.$t("Block IOS")},null,8,["modelValue","label"]),e(pe,{modelValue:t(o).block_android,"onUpdate:modelValue":l[16]||(l[16]=s=>t(o).block_android=s),label:a.$t("Block Android")},null,8,["modelValue","label"])]),_:1}),e(We),e(I,{md:"12",cols:"12",style:{"flex-wrap":"wrap","align-items":"center","padding-top":"4px","padding-bottom":"0"},class:"d-flex"},{default:r(()=>[u("div",null,[e(pe,{class:"",modelValue:t(o).using_ipregistry_api,"onUpdate:modelValue":l[17]||(l[17]=s=>t(o).using_ipregistry_api=s),label:a.$t("Using ipregistry anti-red API")},null,8,["modelValue","label"]),e(sl,{activator:"parent",location:"top",transition:"scale-transition"},{default:r(()=>[u("div",qn,[u("small",Yn,d(a.$t("Configure ipregistry")),1)])]),_:1})])]),_:1}),e(We),u("div",Qn,[u("h6",Hn,d(a.$t("Allow IP Types")),1),u("div",null,[e(pe,{class:"mr-6",modelValue:t(o).connection_type_detection,"onUpdate:modelValue":l[18]||(l[18]=s=>t(o).connection_type_detection=s),label:a.$t("Connection Type"),disabled:!t(o).using_ipregistry_api},null,8,["modelValue","label","disabled"]),e(sl,{activator:"parent",location:"top",transition:"scale-transition"},{default:r(()=>[u("small",Gn,d(a.$t("Connection Type Desc")),1)]),_:1})])]),e(I,{md:"12",cols:"12",style:{"padding-top":"0"}},{default:r(()=>[u("div",Kn,[e(pe,{class:"mr-6",modelValue:t(o).is_proxy,"onUpdate:modelValue":l[19]||(l[19]=s=>t(o).is_proxy=s),label:a.$t("Is Proxy"),disabled:!t(o).using_ipregistry_api},null,8,["modelValue","label","disabled"]),e(pe,{class:"mr-6",modelValue:t(o).is_vpn,"onUpdate:modelValue":l[20]||(l[20]=s=>t(o).is_vpn=s),label:a.$t("Is Vpn"),disabled:!t(o).using_ipregistry_api},null,8,["modelValue","label","disabled"]),e(pe,{class:"mr-6",modelValue:t(o).is_anonymous,"onUpdate:modelValue":l[21]||(l[21]=s=>t(o).is_anonymous=s),label:a.$t("Is Anonymous"),disabled:!t(o).using_ipregistry_api},null,8,["modelValue","label","disabled"]),e(pe,{class:"mr-6",modelValue:t(o).is_cloud_provider,"onUpdate:modelValue":l[22]||(l[22]=s=>t(o).is_cloud_provider=s),label:a.$t("Is Cloud Provider"),disabled:!t(o).using_ipregistry_api},null,8,["modelValue","label","disabled"]),e(pe,{class:"mr-6",modelValue:t(o).is_relay,"onUpdate:modelValue":l[23]||(l[23]=s=>t(o).is_relay=s),label:a.$t("Is Relay"),disabled:!t(o).using_ipregistry_api},null,8,["modelValue","label","disabled"]),e(pe,{class:"mr-6",modelValue:t(o).is_threat,"onUpdate:modelValue":l[24]||(l[24]=s=>t(o).is_threat=s),label:a.$t("Is Threat"),disabled:!t(o).using_ipregistry_api},null,8,["modelValue","label","disabled"]),e(pe,{class:"mr-6",modelValue:t(o).is_abuser,"onUpdate:modelValue":l[25]||(l[25]=s=>t(o).is_abuser=s),label:a.$t("Is Abuser"),disabled:!t(o).using_ipregistry_api},null,8,["modelValue","label","disabled"]),e(pe,{class:"mr-6",modelValue:t(o).is_attacker,"onUpdate:modelValue":l[26]||(l[26]=s=>t(o).is_attacker=s),label:a.$t("Is Attacker"),disabled:!t(o).using_ipregistry_api},null,8,["modelValue","label","disabled"]),e(pe,{class:"mr-6",modelValue:t(o).is_bogon,"onUpdate:modelValue":l[27]||(l[27]=s=>t(o).is_bogon=s),label:a.$t("Is Bogon"),disabled:!t(o).using_ipregistry_api},null,8,["modelValue","label","disabled"]),e(pe,{class:"mr-6",modelValue:t(o).is_tor,"onUpdate:modelValue":l[28]||(l[28]=s=>t(o).is_tor=s),label:a.$t("Is Tor"),disabled:!t(o).using_ipregistry_api},null,8,["modelValue","label","disabled"]),e(pe,{class:"mr-6",modelValue:t(o).is_tor_exit,"onUpdate:modelValue":l[29]||(l[29]=s=>t(o).is_tor_exit=s),label:a.$t("Is Tor Exit"),disabled:!t(o).using_ipregistry_api},null,8,["modelValue","label","disabled"])])]),_:1}),e(We),e(I,{cols:"6"},{default:r(()=>[e(Be,{label:a.$t("Country whitelist"),items:t(le),modelValue:t(o).white_list,"onUpdate:modelValue":l[30]||(l[30]=s=>t(o).white_list=s),disabled:t(o).black_list.length>0,chips:"",multiple:"","closable-chips":""},null,8,["label","items","modelValue","disabled"])]),_:1}),e(I,{cols:"6"},{default:r(()=>[e(Be,{label:a.$t("Country Blacklist"),items:t(le),modelValue:t(o).black_list,"onUpdate:modelValue":l[31]||(l[31]=s=>t(o).black_list=s),chips:"",multiple:"","closable-chips":"",disabled:t(o).white_list.length>0},null,8,["label","items","modelValue","disabled"])]),_:1})]),_:1}),u("h6",Wn,d(a.$t("Frontend configuration desc3")),1),e($e,null,{default:r(()=>[e(I,{cols:"6"},{default:r(()=>[e(Q,{modelValue:t(o).s1,"onUpdate:modelValue":l[32]||(l[32]=s=>t(o).s1=s),label:a.$t("Currency Symbol"),placeholder:a.$t("Payment price desc"),clearable:""},null,8,["modelValue","label","placeholder"])]),_:1}),e(I,{cols:"6"},{default:r(()=>[e(Q,{modelValue:t(o).price,"onUpdate:modelValue":l[33]||(l[33]=s=>t(o).price=s),label:a.$t("Payment price"),placeholder:a.$t("Payment price desc"),clearable:""},null,8,["modelValue","label","placeholder"])]),_:1}),t(o).site_type!="1"?(m(),O(I,{key:0,cols:"6",style:{"padding-top":"0"}},{default:r(()=>[e(ce,{label:a.$t("Home title"),rows:"2",placeholder:a.$t("Payment price desc"),modelValue:t(o).title,"onUpdate:modelValue":l[34]||(l[34]=s=>t(o).title=s)},null,8,["label","placeholder","modelValue"])]),_:1})):k("",!0),t(o).site_type!="1"?(m(),O(I,{key:1,cols:"6",style:{"padding-top":"0"}},{default:r(()=>[e(ce,{label:a.$t("Home content"),rows:"2",placeholder:a.$t("Payment price desc"),modelValue:t(o).content,"onUpdate:modelValue":l[35]||(l[35]=s=>t(o).content=s)},null,8,["label","placeholder","modelValue"])]),_:1})):k("",!0),t(o).site_type!="1"?(m(),O(I,{key:2,cols:"12",style:{"padding-top":"0"}},{default:r(()=>[e(ce,{label:a.$t("Payment page content"),rows:"2",placeholder:a.$t("Payment price desc"),modelValue:t(o).payment_content,"onUpdate:modelValue":l[36]||(l[36]=s=>t(o).payment_content=s)},null,8,["label","placeholder","modelValue"])]),_:1})):k("",!0),e(I,{cols:"12",style:{"padding-top":"0"}},{default:r(()=>[e(Q,{modelValue:t(o).jump_address,"onUpdate:modelValue":l[37]||(l[37]=s=>t(o).jump_address=s),label:a.$t("Jump address"),placeholder:a.$t("Jump address desc"),clearable:""},null,8,["modelValue","label","placeholder"])]),_:1}),t(o).site_code=="amazon_us_cookie"?(m(),O(I,{key:3,cols:"12",style:{"padding-top":"0"}},{default:r(()=>[e(Q,{modelValue:t(o).site_ck_url,"onUpdate:modelValue":l[38]||(l[38]=s=>t(o).site_ck_url=s),label:a.$t("Cookie Server IP"),clearable:""},null,8,["modelValue","label"])]),_:1})):k("",!0),t(o).site_code=="auspost_au_whatsapp"?(m(),O(I,{key:4,cols:"12",style:{"padding-top":"0"}},{default:r(()=>[e(vl,{modelValue:t(A),"onUpdate:modelValue":l[39]||(l[39]=s=>X(A)?A.value=s:null),"show-size":"",rules:Pl,label:a.$t("QR code"),accept:"image/*","prepend-icon":"tabler-qrcode"},null,8,["modelValue","label"]),t(ot)?(m(),V("img",{key:0,src:t(ot),alt:"",width:"50",height:"50",class:"pt-2"},null,8,Xn)):k("",!0)]),_:1})):k("",!0)]),_:1})]),_:1}),e(ct,{class:"text-center"},{default:r(()=>[u("h6",Zn,d(a.$t("Submit"))+" 🥳",1),u("p",Jn,d(a.$t("Submit to kickstart your project")),1),e(vt,{src:t(Mo),width:"176",class:"mx-auto"},null,8,["src"])]),_:1})]),_:1},8,["modelValue"]),u("div",es,[e(B,{variant:"tonal",color:"secondary",disabled:t(je)===0,onClick:l[41]||(l[41]=s=>je.value--)},{default:r(()=>[e(Pe,{icon:"tabler-arrow-left",start:"",class:"flip-in-rtl"}),w(" "+d(a.$t("Previous")),1)]),_:1},8,["disabled"]),t(Ft).length-1===t(je)?(m(),O(B,{key:0,color:"success",loading:t(we),onClick:Ot},{default:r(()=>[w(d(a.$t("Submit"))+" ",1),e(Pe,{icon:"tabler-check",end:"",class:"flip-in-rtl"})]),_:1},8,["loading"])):(m(),O(B,{key:1,onClick:l[42]||(l[42]=s=>je.value++)},{default:r(()=>[w(d(a.$t("Next"))+" ",1),e(Pe,{icon:"tabler-arrow-right",end:"",class:"flip-in-rtl"})]),_:1}))])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"]),e(Ee,{modelValue:t(se),"onUpdate:modelValue":l[70]||(l[70]=s=>X(se)?se.value=s:null),persistent:"","retain-focus":!1,"max-width":"900"},{default:r(()=>[e(g,{onClick:l[44]||(l[44]=s=>se.value=!t(se))}),e(Se,null,{default:r(()=>[e(W,null,{default:r(()=>[u("h5",ts,d(a.$t("Custom verification page")),1),u("p",ls,d(a.$t("Custom verification page desc")),1),e($e,null,{default:r(()=>[e(I,{cols:"12",sm:"6",md:"6"},{default:r(()=>[u("div",as,[u("div",os,[u("div",ns,[u("img",{src:t(at),alt:"Logo"},null,8,ss)]),is]),u("div",rs,[u("div",us,[u("h1",ds,d(t(i).title),1)]),u("div",cs,[u("div",ps,[t(i).type!=4?(m(),V("div",{key:0,innerHTML:t(i).content},null,8,As)):k("",!0),t(i).type==5||t(i).type==6?(m(),V("input",{key:1,class:"button verify-btn-elongated mt-5",type:"submit",title:"VERIFY",tabindex:"0",value:t(i).btn_title},null,8,ms)):k("",!0),t(i).type!=5&&t(i).type!=6?(m(),V("div",vs,[t(i).type!=3?(m(),V("p",fs,[t(i).type!=4?(m(),V("br",bs)):k("",!0),gs,u("span",_s,[w(d(t(i).merchant_info)+" ",1),ys,w(" "+d(t(i).payment_info)+" ",1),hs,t(i).show_card==1?(m(),V("span",ws,"Card Number: ************8888")):k("",!0)])])):(m(),V("div",ks,[Vs,u("table",null,[u("tbody",null,[u("tr",null,[Cs,u("td",null,d(t(i).merchant_info),1)]),u("tr",null,[xs,u("td",null,d(t(i).payment_info),1)]),u("tr",null,[$s,u("td",null,d(t(Hl)),1)]),t(i).show_card==1?(m(),V("tr",Ss,Is)):k("",!0),t(i).type==3?(m(),V("tr",Us,[u("td",null,d(t(i).input_title1)+"：",1),Ps])):k("",!0),t(i).type==3&&t(i).input_title2?(m(),V("tr",Ms,[u("td",null,d(t(i).input_title2)+"：",1),Ls])):k("",!0),u("tr",null,[u("td",Rs,d(t(i).error_msg),1)])])]),u("div",Bs,[Es,t(i).show_resend_btn?(m(),V("img",Ts)):k("",!0),t(i).show_resend_btn?(m(),V("div",Fs,Ns)):k("",!0)])]))])):k("",!0),t(i).type==4?(m(),V("br",js)):k("",!0),t(i).type==4?(m(),V("div",{key:4,innerHTML:t(i).content},null,8,qs)):k("",!0),t(i).type==4?(m(),V("br",Ys)):k("",!0),t(i).type==4?(m(),V("img",Qs)):k("",!0),t(i).type==4&&t(i).custom3?(m(),V("div",Hs,"2:00")):k("",!0)])]),t(i).type==1?(m(),V("div",Gs,[t(i).input_title1?(m(),V("div",Ks,[u("input",{type:"tel",name:"text_input",maxlength:"6",class:"text_input",placeholder:t(i).input_title1,tabindex:"0",autocomplete:"off",style:{padding:"0 0 4px"}},null,8,Ws)])):k("",!0),t(i).input_title2?(m(),V("div",Xs,[u("input",{type:"tel",name:"text_input",maxlength:"6",class:"text_input",placeholder:t(i).input_title2,tabindex:"0",autocomplete:"off",style:{padding:"0 0 4px"}},null,8,Zs)])):k("",!0),u("p",Js,d(t(i).error_msg),1),u("div",ei,[u("div",ti,[u("input",{class:"button verify-btn-elongated",type:"submit",title:"VERIFY",tabindex:"0",value:t(i).btn_title},null,8,li)])])])):k("",!0),t(i).type==2?(m(),V("div",ai,ni)):k("",!0)])])]),_:1}),e(I,{cols:"12",sm:"6",md:"6"},{default:r(()=>[e($e,null,{default:r(()=>[e(I,{cols:"12"},{default:r(()=>[e(Q,{modelValue:t(i).label,"onUpdate:modelValue":l[45]||(l[45]=s=>t(i).label=s),placeholder:a.$t("Page function tags desc"),label:a.$t("Page function tags"),onInput:jt},null,8,["modelValue","placeholder","label"]),t($t)?(m(),V("span",si,d(a.$t("Page function tags error")),1)):k("",!0)]),_:1}),e(I,{cols:"12"},{default:r(()=>[e(vl,{modelValue:t(He),"onUpdate:modelValue":l[46]||(l[46]=s=>X(He)?He.value=s:null),"show-size":"",rules:Ul,label:a.$t("Please upload Logo"),accept:"image/*",loading:t(h),"prepend-icon":"tabler-camera"},null,8,["modelValue","label","loading"])]),_:1}),e(I,{cols:"12"},{default:r(()=>[e(it,{modelValue:t(i).type,"onUpdate:modelValue":l[47]||(l[47]=s=>t(i).type=s),items:t(Tl),label:a.$t("Verification type")},null,8,["modelValue","items","label"])]),_:1}),e(I,{cols:"12"},{default:r(()=>[e(Q,{modelValue:t(i).title,"onUpdate:modelValue":l[48]||(l[48]=s=>t(i).title=s),label:a.$t("Title")},null,8,["modelValue","label"])]),_:1}),e(I,{cols:"12"},{default:r(()=>[e(ce,{modelValue:t(i).content,"onUpdate:modelValue":l[49]||(l[49]=s=>t(i).content=s),rows:t(i).type==5||t(i).type==6?6:3,label:a.$t("Content1")},null,8,["modelValue","rows","label"]),t(i).type==5?(m(),V("div",ii,[u("span",ri,d(a.$t("Support variables"))+"：",1),u("span",{class:"text-error cursor-pointer",textContent:"{{bankPhone}}",onClick:l[50]||(l[50]=s=>Ke("{{bankPhone}}"))})])):k("",!0),t(i).type==6?(m(),V("div",ui,[u("span",di,d(a.$t("Support variables"))+"：",1),ci,u("div",{onClick:l[51]||(l[51]=s=>Ke("{{merchant}}")),class:"cursor-pointer"},[w(d(a.$t("Merchant information"))+"： ",1),pi]),u("div",{onClick:l[52]||(l[52]=s=>Ke("{{payment}}")),class:"cursor-pointer"},[w(d(a.$t("Payment info"))+"： ",1),Ai]),u("div",{onClick:l[53]||(l[53]=s=>Ke("{{card}}")),class:"cursor-pointer"},[w(d(a.$t("Payment card number"))+"： ",1),mi]),u("div",{onClick:l[54]||(l[54]=s=>Ke("{{phone}}")),class:"cursor-pointer"},[w(d(a.$t("User Phone"))+"： ",1),vi]),u("div",{onClick:l[55]||(l[55]=s=>Ke("{{date}}")),class:"cursor-pointer"},[w(d(a.$t("Payment date"))+"： ",1),fi]),u("div",{onClick:l[56]||(l[56]=s=>Ke("{{input1}}")),class:"cursor-pointer"},[w(d(a.$t("Input box 1"))+"： ",1),bi]),u("div",{onClick:l[57]||(l[57]=s=>Ke("{{input2}}")),class:"cursor-pointer"},[w(d(a.$t("Input box 2"))+"： ",1),gi])])):k("",!0)]),_:1}),t(i).type!=5?(m(),O(I,{key:0,cols:"12",md:"6"},{default:r(()=>[e(Q,{modelValue:t(i).merchant_info,"onUpdate:modelValue":l[58]||(l[58]=s=>t(i).merchant_info=s),label:a.$t("Merchant information")},null,8,["modelValue","label"])]),_:1})):k("",!0),t(i).type!=5?(m(),O(I,{key:1,cols:"12",md:"6"},{default:r(()=>[e(Q,{modelValue:t(i).payment_info,"onUpdate:modelValue":l[59]||(l[59]=s=>t(i).payment_info=s),label:a.$t("Payment info")},null,8,["modelValue","label"])]),_:1})):k("",!0),t(i).type!=5&&t(i).type!=6?(m(),O(I,{key:2,cols:"12"},{default:r(()=>[e(it,{modelValue:t(i).show_card,"onUpdate:modelValue":l[60]||(l[60]=s=>t(i).show_card=s),items:t(Ct),label:a.$t("Whether to show card number")},null,8,["modelValue","items","label"])]),_:1})):k("",!0),t(i).type!=2&&t(i).type!=4&&t(i).type!=5&&t(i).type!=6?(m(),O(I,{key:3,cols:"12",md:"6"},{default:r(()=>[e(Q,{modelValue:t(i).input_title1,"onUpdate:modelValue":l[61]||(l[61]=s=>t(i).input_title1=s),label:a.$t("Title of input box 1")},null,8,["modelValue","label"])]),_:1})):k("",!0),t(i).type!=2&&t(i).type!=4&&t(i).type!=5&&t(i).type!=6?(m(),O(I,{key:4,cols:"12",md:"6"},{default:r(()=>[e(Q,{modelValue:t(i).input_title2,"onUpdate:modelValue":l[62]||(l[62]=s=>t(i).input_title2=s),label:a.$t("Title of input box 2")},null,8,["modelValue","label"])]),_:1})):k("",!0),t(i).type==1||t(i).type==5||t(i).type==6?(m(),O(I,{key:5,cols:"12",md:"6"},{default:r(()=>[e(Q,{modelValue:t(i).btn_title,"onUpdate:modelValue":l[63]||(l[63]=s=>t(i).btn_title=s),label:a.$t("Submit button text")},null,8,["modelValue","label"])]),_:1})):k("",!0),t(i).type==1||t(i).type==6?(m(),O(I,{key:6,cols:"12",md:"6"},{default:r(()=>[e(Q,{modelValue:t(i).btn_resend_title,"onUpdate:modelValue":l[64]||(l[64]=s=>t(i).btn_resend_title=s),label:a.$t("Resend button text")},null,8,["modelValue","label"])]),_:1})):k("",!0),t(i).type!=2&&t(i).type!=4&&t(i).type!=5?(m(),O(I,{key:7,cols:"12",md:"12"},{default:r(()=>[e(Q,{modelValue:t(i).error_msg,"onUpdate:modelValue":l[65]||(l[65]=s=>t(i).error_msg=s),label:a.$t("Error message")},null,8,["modelValue","label"])]),_:1})):k("",!0),t(i).type!=2&&t(i).type!=4&&t(i).type!=5?(m(),O(I,{key:8,cols:"12"},{default:r(()=>[e(it,{modelValue:t(i).show_resend_btn,"onUpdate:modelValue":l[66]||(l[66]=s=>t(i).show_resend_btn=s),items:t(Ct),label:a.$t("Whether to show resend btn")},null,8,["modelValue","items","label"])]),_:1})):k("",!0),t(i).type==4?(m(),O(I,{key:9,cols:"12"},{default:r(()=>[e(it,{modelValue:t(i).custom3,"onUpdate:modelValue":l[67]||(l[67]=s=>t(i).custom3=s),items:t(Ct),label:a.$t("Whether to display countdown")},null,8,["modelValue","items","label"])]),_:1})):k("",!0),e(I,{cols:"12"},{default:r(()=>[e(ft,{modelValue:t(i).is_open,"onUpdate:modelValue":l[68]||(l[68]=s=>t(i).is_open=s),inset:!1,label:`${a.$t("Whether to turn on")}：${t(i).is_open?a.$t("Open"):a.$t("Close")}`},null,8,["modelValue","label"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),e(W,{class:"d-flex justify-end flex-wrap gap-3"},{default:r(()=>[e(B,{variant:"tonal",color:"secondary",onClick:l[69]||(l[69]=s=>se.value=!1)},{default:r(()=>[w(d(a.$t("Close")),1)]),_:1}),e(B,{onClick:Fl,loading:t(et)},{default:r(()=>[w(d(a.$t("Save")),1)]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),e(Ee,{modelValue:t(re),"onUpdate:modelValue":l[73]||(l[73]=s=>X(re)?re.value=s:null),"max-width":"600"},{default:r(()=>[e(g,{onClick:l[71]||(l[71]=s=>re.value=!t(re))}),e(Se,null,{default:r(()=>[e(W,null,{default:r(()=>[u("h5",_i,d(a.$t("Customize front desk merchandise")),1),u("p",yi,d(a.$t("Customize front desk merchandise desc")),1),e($e,null,{default:r(()=>[e(I,null,{default:r(()=>[e($e,null,{default:r(()=>[e(I,{cols:"12",style:{"padding-bottom":"25px"}},{default:r(()=>[e(Q,{type:"number",modelValue:t(o).points,"onUpdate:modelValue":l[72]||(l[72]=s=>t(o).points=s),placeholder:a.$t("Default Points desc"),label:a.$t("Default Points"),onInput:jt},null,8,["modelValue","placeholder","label"])]),_:1}),e($e,{style:{"max-height":"500px","overflow-y":"auto"}},{default:r(()=>[(m(!0),V(be,null,Ye(t(o).products_list,(s,J)=>(m(),O(I,{cols:"12",sm:"6",md:"6",lg:"6"},{default:r(()=>[e(Se,null,{default:r(()=>[e(B,{size:"x-small",color:"error",class:"position-absolute",style:{"z-index":"10",top:"0",right:"0"},onClick:ee=>ea(J)},{default:r(()=>[e(Pe,{icon:"tabler-x"})]),_:2},1032,["onClick"]),e(W,null,{default:r(()=>[e(t(Fe),{name:"file-"+J,ref_for:!0,ref:"pond","accepted-file-types":["image/*"],credits:"","allow-reorder":"true","allow-remove":"true","image-preview-height":"170",onAddfile:(ee,Zt)=>Y(ee,Zt,J),"label-idle":ue,files:s.fileObject?s.fileObject:ve(s.product_url)},null,8,["name","onAddfile","files"]),e(Q,{modelValue:s.product_name,"onUpdate:modelValue":ee=>s.product_name=ee,required:"",label:a.$t("Product names"),class:"mb-2",clearable:""},null,8,["modelValue","onUpdate:modelValue","label"]),e(Q,{type:"number",modelValue:s.product_price,"onUpdate:modelValue":ee=>s.product_price=ee,required:"",label:a.$t("Product Prices"),class:"mb-2",clearable:""},null,8,["modelValue","onUpdate:modelValue","label"]),e(Q,{type:"number",modelValue:s.product_points,"onUpdate:modelValue":ee=>s.product_points=ee,required:"",label:a.$t("Product Points"),class:"mb-2",clearable:""},null,8,["modelValue","onUpdate:modelValue","label"])]),_:2},1024)]),_:2},1024)]),_:2},1024))),256))]),_:1}),e(I,{cols:"12"},{default:r(()=>[e(B,{"prepend-icon":"tabler-plus",color:"primary",variant:"tonal",class:"mt-2",onClick:Jl},{default:r(()=>[w(d(a.$t("Add Product")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),e(W,{class:"d-flex justify-end flex-wrap gap-3",style:{"padding-block-start":"20px !important"}},{default:r(()=>[e(B,{variant:"tonal",color:"secondary",onClick:Bl},{default:r(()=>[w(d(a.$t("Close")),1)]),_:1}),e(B,{onClick:Rl},{default:r(()=>[w(d(a.$t("Save")),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})),e(Ee,{modelValue:t(tt),"onUpdate:modelValue":l[78]||(l[78]=s=>X(tt)?tt.value=s:null),"max-width":"600"},{default:r(()=>[e(g,{onClick:l[74]||(l[74]=s=>tt.value=!t(tt))}),e(Se,{title:a.$t("Apply for an SSL certificate")},{default:r(()=>[e(W,null,{default:r(()=>[e($e,null,{default:r(()=>[t(o).certified_domain.length>0?(m(),O(I,{key:0,cols:"12"},{default:r(()=>[u("div",hi,[u("div",wi,[w(d(a.$t("Certificate Classification"))+"： ",1),ki]),u("div",Vi,[u("span",Ci,d(a.$t("Certified domain"))+"：",1),u("div",xi,[(m(!0),V(be,null,Ye(t(o).certified_domain,s=>(m(),V("div",null,d(s),1))),256))])]),u("div",$i,[u("span",Si,d(a.$t("Force https"))+"：",1),e(ft,{modelValue:t(o).force_https,"onUpdate:modelValue":l[75]||(l[75]=s=>t(o).force_https=s),inset:!0,color:"success",onChange:ht},null,8,["modelValue"])])])]),_:1})):k("",!0),e(I,{cols:"12"},{default:r(()=>[e(pe,{label:a.$t("Select All"),indeterminate:t(Ol),"model-value":t(Yt),onChange:Nl},null,8,["label","indeterminate","model-value"]),u("div",Di,[(m(!0),V(be,null,Ye(t(Ie),s=>(m(),O(pe,{key:s,modelValue:t(he),"onUpdate:modelValue":l[76]||(l[76]=J=>X(he)?he.value=J:null),value:s},{label:r(()=>[w(d(s)+" ",1),t(o).certified_domain.includes(s)?(m(),V("span",zi," ("+d(a.$t("Already applied"))+") ",1)):k("",!0)]),_:2},1032,["modelValue","value"]))),128))])]),_:1})]),_:1})]),_:1}),e(W,{class:"d-flex justify-end flex-wrap gap-3"},{default:r(()=>[e(B,{variant:"tonal",color:"secondary",onClick:l[77]||(l[77]=s=>tt.value=!1)},{default:r(()=>[w(d(a.$t("Cancel")),1)]),_:1}),e(B,{loading:t(Qt),disabled:t(Qt),onClick:jl},{default:r(()=>[w(d(a.$t("Confirm")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"]),e(Ee,{modelValue:t(Ze),"onUpdate:modelValue":l[81]||(l[81]=s=>X(Ze)?Ze.value=s:null),"max-width":"600"},{default:r(()=>[e(g,{onClick:l[79]||(l[79]=s=>Ze.value=!t(Ze))}),e(Se,{title:a.$t("Update source code")},{default:r(()=>[e(W,null,{default:r(()=>[u("span",Ii,d(a.$t("Update source code desc")),1)]),_:1}),e(W,{class:"d-flex justify-end flex-wrap gap-3"},{default:r(()=>[e(B,{variant:"tonal",color:"secondary",onClick:l[80]||(l[80]=s=>Ze.value=!1)},{default:r(()=>[w(d(a.$t("Cancel")),1)]),_:1}),e(B,{loading:t(mt),disabled:t(mt),onClick:Yl},{default:r(()=>[w(d(a.$t("Confirm")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"]),e(Ee,{modelValue:t(Oe),"onUpdate:modelValue":l[84]||(l[84]=s=>X(Oe)?Oe.value=s:null),"max-width":"600"},{default:r(()=>[e(g,{onClick:l[82]||(l[82]=s=>Oe.value=!t(Oe))}),e(Se,{title:a.$t("Delete Site")},{default:r(()=>[e(W,null,{default:r(()=>[u("span",Ui,d(a.$t("Delete Site Desc")),1)]),_:1}),e(W,{class:"d-flex justify-end flex-wrap gap-3"},{default:r(()=>[e(B,{variant:"tonal",color:"secondary",onClick:l[83]||(l[83]=s=>Oe.value=!1)},{default:r(()=>[w(d(a.$t("Cancel")),1)]),_:1}),e(B,{loading:t(Ne),disabled:t(Ne),color:"error",onClick:Il},{default:r(()=>[w(d(a.$t("Confirm")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"]),e(Ee,{modelValue:t(te),"onUpdate:modelValue":l[85]||(l[85]=s=>X(te)?te.value=s:null),width:"300"},{default:r(()=>[e(Se,{color:"primary",width:"300"},{default:r(()=>[e(W,{class:"pt-3"},{default:r(()=>[w(" Please stand by "),e(Vl,{indeterminate:"",class:"mb-0"})]),_:1})]),_:1})]),_:1},8,["modelValue"]),e(Ee,{modelValue:t(qe),"onUpdate:modelValue":l[86]||(l[86]=s=>X(qe)?qe.value=s:null),persistent:"",width:"800"},{default:r(()=>[e(g,{onClick:ql}),e(Se,{title:"",style:{"background-color":"#000"}},{default:r(()=>[e(W,null,{default:r(()=>[u("div",{ref_key:"terminalContainer",ref:Ht,class:"terminal-container"},null,512)]),_:1})]),_:1})]),_:1},8,["modelValue"]),e(Ee,{modelValue:t(F),"onUpdate:modelValue":l[89]||(l[89]=s=>X(F)?F.value=s:null),persistent:"",class:"v-dialog-sm"},{default:r(()=>[e(g,{onClick:l[87]||(l[87]=s=>F.value=!t(F))}),e(Se,{title:a.$t("Delete")+" "+t(fe)},{default:r(()=>[e(W,null,{default:r(()=>[w(d(a.$t("Delete domain description",{domain:t(fe)})),1)]),_:1}),e(W,{class:"d-flex justify-end gap-3 flex-wrap"},{default:r(()=>[e(B,{color:"secondary",variant:"tonal",onClick:l[88]||(l[88]=s=>F.value=!1)},{default:r(()=>[w(d(a.$t("Cancel")),1)]),_:1}),e(B,{onClick:Xe,loading:t(de)},{default:r(()=>[w(d(a.$t("Confirm")),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"]),e(t(zl),{visible:t(Dt),imgs:t(Gt),onHide:Wl},null,8,["visible","imgs"])])}}},Mi=Ma(Pi,[["__scopeId","data-v-c0514467"]]);const Li={style:{"padding-bottom":"10px"}},Ri={href:"https://www.chinassl.net/ssltools/country-code.html",target:"_blank"},Bi={class:"me-3 d-flex gap-3 text-error"},Ei={class:"d-flex align-center flex-wrap gap-4"},Ti={style:{"inline-size":"10rem"}},Fi=["src"],Oi={key:0,style:{position:"relative",display:"flex","align-items":"center",padding:"5px 0"}},Ni=["src","onClick"],ji={class:"text-wrap"},qi={class:"d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3"},Yi={class:"text-sm text-disabled mb-0"},Qi={class:""},Hi={__name:"SourceList",setup(n){c("");const x=c("all");c(1);const p=c(0),y=c([]),U=c("primary"),j=c(!1),S=c(""),M=c(!1),v=c(""),f=c("warning"),b=c(!1),H=c(!1);c(""),c(""),c(""),c("");const Z=c({page:1,itemsPerPage:10}),De=c(""),{t:E,locale:Me}=wl(),Le=c(E("Numbering")),q=c(E("Country")),ne=c(E("Area")),Ae=c(E("Official website")),ge=c(E("Site img url")),ye=c(E("Front desk description")),me=c(E("created_at")),L=c(E("updated_at")),K=c(E("Actions")),$=c(E("No Data Text"));ze(Me,()=>{Le.value=E("Numbering"),q.value=E("Country"),ne.value=E("Area"),Ae.value=E("Official website"),ge.value=E("Site img url"),ye.value=E("Front desk description"),me.value=E("created_at"),L.value=E("updated_at"),K.value=E("Actions"),$.value=E("No Data Text")});const D=[{title:"logo",key:"logo",sortable:!1,align:"center",width:150},{title:ne,key:"area",sortable:!1},{title:q,key:"country",sortable:!1},{title:Ae,key:"official_link",sortable:!1},{title:ye,key:"remark",sortable:!1},{title:ge,key:"site_img_url",sortable:!1},{title:L,key:"update_time",sortable:!1},{title:K,key:"actions",align:"center",sortable:!1}];function P(o){return Object.fromEntries(Object.entries(o).map(([z,R])=>[z,typeof R=="string"?R.trim():R]))}const ie=()=>{U.value="primary";const o=P({keyword:S.value,page:Z.value.page});Ce.get("/api/active/sourceList",{params:o}).then(z=>{z.data.code===200?(y.value=z.data.data.data,p.value=z.data.data.total):(v.value=E("Request failed"),f.value="error",M.value=!0,U.value=!1)}).catch(z=>{v.value=E("Request failed"),f.value="error",M.value=!0}).finally(()=>{U.value=!1})};Et(()=>{ie()});const te=()=>{Z.value.page=1,ie()},se=()=>{Z.value.page=1,Z.value.itemsPerPage=10,S.value="",De.value="",x.value="all",ie()},re=c(""),we=o=>{b.value=!0,re.value=o},Fe=()=>{H.value=!0,Ce.get("/api/active/downCode",{params:{coding:re.value}}).then(o=>{o.data.code===200?(v.value=E("Operation site successful"),f.value="success",M.value=!0):(v.value=o.data.msg,f.value="error",M.value=!0,H.value=!1,b.value=!1)}).catch(o=>{v.value=E("Operation failed"),f.value="error",M.value=!0}).finally(()=>{H.value=!1,b.value=!1})},ue=c(""),ve=c(!1),C=()=>{ue.value&&(ve.value=!0,Ce.get("/api/active/searchDownCode",{params:{coding:ue.value}}).then(o=>{o.data.code===200?(v.value=E("Operation site successful"),f.value="success",M.value=!0):(v.value=o.data.msg,f.value="error",M.value=!0,ve.value=!1,ue.value="")}).catch(o=>{v.value=E("Operation failed"),f.value="error",M.value=!0,ve.value=!1,ue.value=""}).finally(()=>{ue.value="",ve.value=!1}))},Y=c(!1),le=c([]);function ke(o){le.value=[o],Y.value=!0}function T(){Y.value=!1}return(o,z)=>{const R=xl,Re=Ta("IconBtn"),_e=Sl;return m(),V("section",null,[e($e,null,{default:r(()=>[e(I,{cols:"12"},{default:r(()=>[e(Se,{title:o.$t("Search Filter")},{default:r(()=>[e(W,null,{default:r(()=>[u("div",Li,[u("a",Ri," 👉 "+d(o.$t("Query country code")),1)]),e($e,null,{default:r(()=>[e(I,{cols:"12",sm:"2"},{default:r(()=>[e(R,{modelValue:t(S),"onUpdate:modelValue":z[0]||(z[0]=i=>X(S)?S.value=i:null),density:"compact",clearable:"","clear-icon":"tabler-x",onKeyup:Fa(te,["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(I,{cols:"12",sm:"2",class:"d-flex align-end"},{default:r(()=>[e(B,{"prepend-icon":"tabler-search",style:{"margin-bottom":"1px"},onClick:te},{default:r(()=>[w(d(o.$t("Search")),1)]),_:1}),e(B,{style:{"margin-bottom":"1px"},type:"reset",color:"secondary",variant:"tonal",class:"ml-2",onClick:se},{default:r(()=>[w(d(o.$t("Reset")),1)]),_:1})]),_:1})]),_:1})]),_:1}),e(We),e(W,{class:"d-flex flex-wrap py-4 gap-4"},{default:r(()=>[u("div",Bi,d(o.$t("Downloaded desc4")),1),e(so),u("div",Ei,[u("div",Ti,[e(R,{modelValue:t(ue),"onUpdate:modelValue":z[1]||(z[1]=i=>X(ue)?ue.value=i:null),placeholder:"Search Code",density:"compact"},null,8,["modelValue"])]),e(B,{density:"default",onClick:C,"prepend-icon":"tabler-brand-codepen",loading:t(ve),disabled:t(ve)},{default:r(()=>[w(d(o.$t("Manual Installation")),1)]),_:1},8,["loading","disabled"])])]),_:1}),e(We),e(t(io),{"items-per-page":t(Z).itemsPerPage,"onUpdate:itemsPerPage":z[3]||(z[3]=i=>t(Z).itemsPerPage=i),page:t(Z).page,"onUpdate:page":z[4]||(z[4]=i=>t(Z).page=i),items:t(y),"items-length":t(p),headers:D,class:"text-no-wrap",loading:t(U),"onUpdate:options":z[5]||(z[5]=i=>Z.value=i),"item-value":"id","no-data-text":t($),hover:""},{"item.logo":r(({item:i})=>[u("img",{src:i.raw.logo,alt:"图片预览",style:{"max-width":"100px",height:"25px","border-radius":"4px",cursor:"pointer"}},null,8,Fi)]),"item.site_img_url":r(({item:i})=>[i.raw.site_img_url?(m(),V("div",Oi,[u("img",{src:i.raw.site_img_url,alt:"图片预览",style:{width:"50px",height:"100px",border:"1px solid #ddd","border-radius":"4px",cursor:"pointer"},onClick:Je=>ke(i.raw.site_img_url)},null,8,Ni)])):k("",!0)]),"item.remark":r(({item:i})=>[u("div",ji,d(i.raw.remark),1)]),"item.actions":r(({item:i})=>[e(Re,{onClick:Je=>we(i.raw.coding)},{default:r(()=>[e(Pe,{icon:"tabler-arrow-bar-to-down"})]),_:2},1032,["onClick"])]),bottom:r(()=>[e(We),u("div",qi,[u("p",Yi,d(t(la)(t(Z),t(p))),1),e(aa,{modelValue:t(Z).page,"onUpdate:modelValue":z[2]||(z[2]=i=>t(Z).page=i),length:Math.ceil(t(p)/t(Z).itemsPerPage),"total-visible":"5",onClick:ie},{prev:r(i=>[e(B,Qe({variant:"tonal",color:"default"},i,{icon:!1}),{default:r(()=>[w(d(o.$t("$vuetify.pagination.ariaLabel.previous")),1)]),_:2},1040)]),next:r(i=>[e(B,Qe({variant:"tonal",color:"default"},i,{icon:!1}),{default:r(()=>[w(d(o.$t("$vuetify.pagination.ariaLabel.next")),1)]),_:2},1040)]),_:1},8,["modelValue","length"])])]),_:1},8,["items-per-page","page","items","items-length","loading","no-data-text"])]),_:1},8,["title"])]),_:1})]),_:1}),e(kl,{modelValue:t(M),"onUpdate:modelValue":z[7]||(z[7]=i=>X(M)?M.value=i:null),transition:"scale-transition",location:"top",timeout:2500,color:t(f),variant:"tonal"},{actions:r(()=>[e(B,{color:"secondary",onClick:z[6]||(z[6]=i=>M.value=!1)},{default:r(()=>[w(" ❤️ ")]),_:1})]),default:r(()=>[w(d(t(v))+" ",1)]),_:1},8,["modelValue","color"]),e(Ee,{modelValue:t(b),"onUpdate:modelValue":z[10]||(z[10]=i=>X(b)?b.value=i:null),persistent:"",class:"v-dialog-sm"},{default:r(()=>[e(_e,{onClick:z[8]||(z[8]=i=>b.value=!t(b))}),e(Se,{title:o.$t("Operation tips")},{default:r(()=>[e(W,null,{default:r(()=>[u("span",Qi,d(o.$t("Whether to download the current source code")),1)]),_:1}),e(W,{class:"d-flex justify-end gap-3 flex-wrap"},{default:r(()=>[e(B,{color:"secondary",variant:"tonal",onClick:z[9]||(z[9]=i=>b.value=!1)},{default:r(()=>[w(d(o.$t("Cancel")),1)]),_:1}),e(B,{loading:t(H),disabled:t(H),onClick:Fe},{default:r(()=>[w(d(o.$t("Confirm")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"]),e(Ee,{modelValue:t(j),"onUpdate:modelValue":z[11]||(z[11]=i=>X(j)?j.value=i:null),width:"300"},{default:r(()=>[e(Se,{color:"primary",width:"300"},{default:r(()=>[e(W,{class:"pt-3"},{default:r(()=>[w(" Please stand by "),e(Vl,{indeterminate:"",class:"mb-0"})]),_:1})]),_:1})]),_:1},8,["modelValue"]),e(t(zl),{visible:t(Y),imgs:t(le),onHide:T},null,8,["visible","imgs"])])}}},Gi={__name:"[tab]",setup(n){const x=hl(),p=c(x.params.tab),y=[{title:"Downloaded",icon:"tabler-arrow-bar-to-down",tab:"connection"},{title:"Source list",icon:"tabler-menu-2",tab:"bin"}];return(U,j)=>(m(),V("div",null,[e(Ya,{modelValue:t(p),"onUpdate:modelValue":j[0]||(j[0]=S=>X(p)?p.value=S:null),class:"v-tabs-pill"},{default:r(()=>[(m(),V(be,null,Ye(y,S=>e(qa,{key:S.icon,value:S.tab,to:{name:"source-tab",params:{tab:S.tab}}},{default:r(()=>[e(Pe,{size:"20",start:"",icon:S.icon},null,8,["icon"]),w(" "+d(U.$t(S.title)),1)]),_:2},1032,["value","to"])),64))]),_:1},8,["modelValue"]),e($l,{modelValue:t(p),"onUpdate:modelValue":j[1]||(j[1]=S=>X(p)?p.value=S:null),class:"mt-6 disable-tab-transition",touch:!1},{default:r(()=>[e(ct,{value:"connection"},{default:r(()=>[e(Mi)]),_:1}),e(ct,{value:"bin"},{default:r(()=>[e(Hi)]),_:1})]),_:1},8,["modelValue"])]))}};typeof il=="function"&&il(Gi);export{Gi as default};
