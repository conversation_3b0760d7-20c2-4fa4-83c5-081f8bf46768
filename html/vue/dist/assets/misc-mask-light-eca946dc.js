import{v as i,aa as u,B as o}from"./index-9a5dc664.js";const{skin:r}=i(),c=(s,t,m,n,e=!1)=>{const{global:a}=u();return o(()=>{if(a.name.value==="light")return r.value==="bordered"&&e?m:s;if(a.name.value==="dark")return r.value==="bordered"&&e?n:t})},f=""+new URL("misc-mask-dark-4a870cd1.webp",import.meta.url).href,k=""+new URL("misc-mask-light-51806448.webp",import.meta.url).href;export{k as a,f as m,c as u};
