import{_ as i}from"./ErrorHeader-d0ed5b30.js";import{u as n,m as c,a as m}from"./misc-mask-light-eca946dc.js";import{p as _}from"./401-043fe89a.js";import{b as t}from"./route-block-83d24a4e.js";import{o as d,c as l,q as e,w as u,aj as p,ah as h,n as f,s as a,aF as o}from"./index-9a5dc664.js";const g={class:"misc-wrapper"},k={class:"misc-avatar w-100 text-center"},v={__name:"not-authorized",setup(x){const s=n(m,c);return(w,V)=>{const r=i;return d(),l("div",g,[e(r,{"error-title":"You are not authorized! 🔐","error-description":`You do not have permission to view this page using the credentials that you have provided while login.
Please contact your site administrator.`}),e(h,{to:"/",class:"mb-12"},{default:u(()=>[p(" Back to Home ")]),_:1}),f("div",k,[e(o,{src:a(_),alt:"Coming Soon","max-width":170,class:"mx-auto"},null,8,["src"])]),e(o,{src:a(s),class:"misc-footer-img d-none d-md-block"},null,8,["src"])])}}};typeof t=="function"&&t(v);export{v as default};
