import{_ as u}from"./AppTextField-8c148b8f.js";import{l as d,o as p,c as _,n as s,q as e,w as a,s as n,ak as f,ah as g,aj as h,aG as w,aF as i}from"./index-9a5dc664.js";import{u as V,m as x,a as b}from"./misc-mask-light-eca946dc.js";import{b as m}from"./route-block-83d24a4e.js";import{V as k}from"./VForm-c6ce9b98.js";import"./VTextField-3e2d458d.js";const v=""+new URL("misc-coming-soon-e33407f1.webp",import.meta.url).href;const y={class:"misc-wrapper"},B=s("div",{class:"text-center mb-4"},[s("h4",{class:"text-h4 font-weight-medium mb-1"}," We are launching soon 🚀 "),s("p",null,"Our website is opening soon. Please register to get notified when it's ready!")],-1),M={class:"misc-avatar w-100 mt-10"},N={__name:"coming-soon",setup(S){const t=d(""),r=V(b,x);return(C,o)=>{const l=u;return p(),_("div",y,[s("div",null,[B,e(k,{onSubmit:o[1]||(o[1]=w(()=>{},["prevent"]))},{default:a(()=>[e(l,{modelValue:n(t),"onUpdate:modelValue":o[0]||(o[0]=c=>f(t)?t.value=c:null),autofocus:"",placeholder:"Enter your email",class:"misc-email-input"},{append:a(()=>[e(g,{type:"submit",class:"mt-n2"},{default:a(()=>[h(" Notify ")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),s("div",M,[e(i,{src:n(v),alt:"Coming Soon","max-width":263,class:"mx-auto"},null,8,["src"])]),e(i,{src:n(r),class:"misc-footer-img d-none d-md-block"},null,8,["src"])])}}};typeof m=="function"&&m(N);export{N as default};
