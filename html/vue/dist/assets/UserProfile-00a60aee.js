import{a as L,V as m}from"./VBadge-6aed7f04.js";import{ad as V,l as g,o as i,b as y,w as e,q as a,aF as d,s as o,ar as U,az as k,aA as c,aE as v,aB as p,aj as u,y as l,aP as R,c as f,z as I,ag as b,n as B,ah as S}from"./index-9a5dc664.js";import{V as x}from"./VListItemAction-68e43985.js";const _=""+new URL("avatar-1-6ac797f6.webp",import.meta.url).href,z=""+new URL("avatar-2-bdb907ae.webp",import.meta.url).href,N=""+new URL("avatar-3-4ee4c3a5.webp",import.meta.url).href,D=""+new URL("avatar-4-7d6e0b17.webp",import.meta.url).href,$=""+new URL("avatar-5-d36b77a8.webp",import.meta.url).href,A=""+new URL("avatar-6-aaec94fe.webp",import.meta.url).href,P=""+new URL("avatar-7-6ba92caa.webp",import.meta.url).href,C=""+new URL("avatar-8-8567b1d2.webp",import.meta.url).href,E={key:0},T={key:1},j={key:2},q={class:"py-2"},O={__name:"UserProfile",setup(F){const s=V().userInfo,t=g(_),h=s.name?s.name:"NULL",w=s.role?s.role:"NULL",r=s.role_id?s.role_id:999;return r==1?t.value=_:r==2?t.value=z:r==3?t.value=N:r==4?t.value=D:r==5?t.value=$:r==6?t.value=A:r==7?t.value=P:r==8?t.value=C:t.value=L,(n,G)=>(i(),y(m,{dot:"",location:"bottom right","offset-x":"3","offset-y":"3",bordered:"",color:"success"},{default:e(()=>[a(v,{class:"cursor-pointer",color:"primary",variant:"tonal"},{default:e(()=>[a(d,{src:o(t)},null,8,["src"]),a(U,{activator:"parent",width:"230",location:"bottom end",offset:"14px"},{default:e(()=>[a(k,null,{default:e(()=>[a(c,null,{prepend:e(()=>[a(x,{start:""},{default:e(()=>[a(m,{dot:"",location:"bottom right","offset-x":"3","offset-y":"3",color:"success"},{default:e(()=>[a(v,{color:"primary",variant:"tonal"},{default:e(()=>[a(d,{src:o(t)},null,8,["src"])]),_:1})]),_:1})]),_:1})]),default:e(()=>[a(p,{class:"font-weight-semibold text-button mb-1"},{default:e(()=>[u(l(o(h)),1)]),_:1}),a(R,null,{default:e(()=>[o(r)==1?(i(),f("span",E,l(n.$t("Superadmin")),1)):o(r)==999?(i(),f("span",T,l(n.$t("Visitor")),1)):(i(),f("span",j,l(o(w)),1))]),_:1})]),_:1}),a(I,{class:"my-2"}),a(c,{to:{name:"security"}},{prepend:e(()=>[a(b,{class:"me-2",icon:"tabler-lock",size:"22"})]),default:e(()=>[a(p,null,{default:e(()=>[u(l(n.$t("Change Password")),1)]),_:1})]),_:1}),a(c,{to:"/login"},{default:e(()=>[B("div",q,[a(S,{color:"error",style:{height:"30px","background-color":"rgb(255, 76, 81) !important"},class:"v-btn v-btn--block v-btn--elevated v-theme--light bg-error v-btn--density-default v-btn--size-small v-btn--variant-elevated"},{default:e(()=>[u(l(n.$t("Logout"))+" ",1),a(b,{class:"me-2 ml-2",icon:"tabler-logout",size:"18"})]),_:1})])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}))}};export{O as default};
