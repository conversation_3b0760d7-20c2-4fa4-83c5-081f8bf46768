import{I as V,aQ as B,K as N,aR as h,N as P,O as x,P as T,aS as R,U as w,W as d,Z as L,aT as _,aU as I,aa as U,aV as X,a7 as $,aW as A,q as t,aX as D,aC as W,aD as Y,a8 as u,ag as q}from"./index-9a5dc664.js";const M=""+new URL("avatar-10-b701178c.webp",import.meta.url).href;const O=V()({name:"VBadge",inheritAttrs:!1,props:{bordered:Boolean,color:String,content:[Number,String],dot:Boolean,floating:Boolean,icon:B,inline:Boolean,label:{type:String,default:"$vuetify.badge"},max:[Number,String],modelValue:{type:Boolean,default:!0},offsetX:[Number,String],offsetY:[Number,String],textColor:String,...N(),...h({location:"top end"}),...P(),...x(),...T(),...R({transition:"scale-rotate-transition"})},setup(a,o){const{backgroundColorClasses:c,backgroundColorStyles:g}=w(d(a,"color")),{roundedClasses:b}=L(a),{t:m}=_(),{textColorClasses:f,textColorStyles:v}=I(d(a,"textColor")),{themeClasses:C}=U(),{locationStyles:S}=X(a,!0,e=>(a.floating?a.dot?2:4:a.dot?8:12)+(["top","bottom"].includes(e)?+(a.offsetY??0):["left","right"].includes(e)?+(a.offsetX??0):0));return $(()=>{const e=Number(a.content),n=!a.max||isNaN(e)?a.content:e<=+a.max?e:`${a.max}+`,[k,y]=A(o.attrs,["aria-atomic","aria-label","aria-live","role","title"]);return t(a.tag,u({class:["v-badge",{"v-badge--bordered":a.bordered,"v-badge--dot":a.dot,"v-badge--floating":a.floating,"v-badge--inline":a.inline},a.class]},y,{style:a.style}),{default:()=>{var l,s;return[t("div",{class:"v-badge__wrapper"},[(s=(l=o.slots).default)==null?void 0:s.call(l),t(D,{transition:a.transition},{default:()=>{var i,r;return[W(t("span",u({class:["v-badge__badge",C.value,c.value,b.value,f.value],style:[g.value,v.value,a.inline?{}:S.value],"aria-atomic":"true","aria-label":m(a.label,e),"aria-live":"polite",role:"status"},k),[a.dot?void 0:o.slots.badge?(r=(i=o.slots).badge)==null?void 0:r.call(i):a.icon?t(q,{icon:a.icon},null):n]),[[Y,a.modelValue]])]}})])]}})}),{}}});export{O as V,M as a};
