import{aJ as I,d as A,v as x,l as c,k as B,aK as P,aL as V,o as d,c as m,n as u,q as i,w as N,ag as T,s as a,A as D,a8 as L,ak as b,F as R,ap as q,r as w,aM as E,aN as O,aO as U}from"./index-9a5dc664.js";import{S as g}from"./shepherd.esm-7d7e64e6.js";const h=l=>(O("data-v-4c11d9d0"),l=l(),U(),l),$=h(()=>u("span",{class:"me-3"},"Search",-1)),z=h(()=>u("span",{class:"meta-key"},"⌘K",-1)),F=[$,z],K=A({inheritAttrs:!1}),M=Object.assign(K,{__name:"NavSearchBar",setup(l){const{appContentLayoutNav:_}=x(),t=c(!1),v=[{title:"Popular Searches",content:[{icon:"tabler-chart-donut",title:"Analytics",url:{name:"dashboards-analytics"}},{icon:"tabler-chart-bubble",title:"CRM",url:{name:"dashboards-crm"}},{icon:"tabler-file",title:"Invoice List",url:{name:"apps-invoice-list"}},{icon:"tabler-users",title:"User List",url:{name:"apps-user-list"}}]},{title:"Apps & Pages",content:[{icon:"tabler-calendar",title:"Calendar",url:{name:"apps-calendar"}},{icon:"tabler-file-plus",title:"Invoice Add",url:{name:"apps-invoice-add"}},{icon:"tabler-currency-dollar",title:"Pricing",url:{name:"pages-pricing"}},{icon:"tabler-user",title:"Account Settings",url:{name:"pages-account-settings-tab",params:{tab:"account"}}}]},{title:"User Interface",content:[{icon:"tabler-letter-a",title:"Typography",url:{name:"pages-typography"}},{icon:"tabler-square",title:"Tabs",url:{name:"components-tabs"}},{icon:"tabler-hand-click",title:"Buttons",url:{name:"components-button"}},{icon:"tabler-keyboard",title:"Statistics",url:{name:"pages-cards-card-statistics"}}]},{title:"Popular Searches",content:[{icon:"tabler-list",title:"Select",url:{name:"forms-select"}},{icon:"tabler-space",title:"Combobox",url:{name:"forms-combobox"}},{icon:"tabler-calendar",title:"Date & Time Picker",url:{name:"forms-date-time-picker"}},{icon:"tabler-hexagon",title:"Rating",url:{name:"forms-rating"}}]}],f=[{title:"Analytics Dashboard",icon:"tabler-shopping-cart",url:{name:"dashboards-analytics"}},{title:"Account Settings",icon:"tabler-user",url:{name:"pages-account-settings-tab",params:{tab:"account"}}},{title:"Pricing Page",icon:"tabler-cash",url:{name:"pages-pricing"}}],n=c(""),p=c([]),y=B();P(()=>{q.get("/app-bar/search",{params:{q:n.value}}).then(r=>{p.value=r.data.data})});const S=r=>{y.push(r.url),t.value=!1,n.value=""},k=V(()=>E(()=>import("./AppBarSearch-d8f7b5f8.js"),["./AppBarSearch-d8f7b5f8.js","./index-9a5dc664.js","./index-9a43a361.css","./VTextField-3e2d458d.js","./VTextField-9f112b2b.css","./VDialog-0870f7b8.js","./VDialog-f7015bc7.css","./AppBarSearch-57517d38.css"],import.meta.url));return(r,e)=>{const C=w("IconBtn");return d(),m(R,null,[u("div",L({class:"d-flex align-center cursor-pointer"},r.$attrs,{style:{"user-select":"none"},onClick:e[2]||(e[2]=s=>t.value=!a(t))}),[i(C,{class:"me-1",onClick:e[0]||(e[0]=s=>{var o;return(o=a(g).activeTour)==null?void 0:o.cancel()})},{default:N(()=>[i(T,{size:"26",icon:"tabler-search"})]),_:1}),a(_)==="vertical"?(d(),m("span",{key:0,class:"d-none d-md-flex align-center text-disabled",onClick:e[1]||(e[1]=s=>{var o;return(o=a(g).activeTour)==null?void 0:o.cancel()})},F)):D("",!0)],16),i(a(k),{isDialogVisible:a(t),"onUpdate:isDialogVisible":e[3]||(e[3]=s=>b(t)?t.value=s:null),"search-query":a(n),"onUpdate:searchQuery":e[4]||(e[4]=s=>b(n)?n.value=s:null),"search-results":a(p),suggestions:v,"no-data-suggestion":f,onItemSelected:S},null,8,["isDialogVisible","search-query","search-results"])],64)}}}),G=I(M,[["__scopeId","data-v-4c11d9d0"]]);export{G as default};
