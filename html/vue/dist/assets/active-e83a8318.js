import{_ as z}from"./AppTextField-8c148b8f.js";import{aJ as B,ad as F,a9 as L,l as r,o as A,b as T,w as a,q as e,am as g,as as $,av as v,al as C,s as o,ak as V,n as U,y as _,az as q,c as N,F as P,a as E,aA as M,t as j,ag as G,ah as w,aj as u,aG as J,ao as O,b0 as H,ap as K}from"./index-9a5dc664.js";import{V as Q}from"./VForm-c6ce9b98.js";import{V as W}from"./VDialog-0870f7b8.js";import"./VTextField-3e2d458d.js";const X={class:"text-base font-weight-medium mb-3"},Y={__name:"active",setup(Z){const b=F(),{t:n,locale:ee}=L(),d=r(!1),c=r(b.cloud.code),x=b.cloud.expire_date,y=b.cloud.type!=5?x||n("Inactivated"):n("Permanently valid"),i=r(!1),p=r(""),m=r("warning"),f=r(!0),S=r(!1),D=[n("Activation instructions 4"),n("Activation instructions 5"),n("Activation instructions 3")],I=()=>{d.value=!0,K.post("/api/active/doStatus",{code:c.value}).then(s=>{s.data.code===200?(p.value=n("Activation successful"),m.value="success",i.value=!0,window.location.reload()):(p.value=s.data.msg,m.value="error",i.value=!0)}).catch(s=>{p.value=n("Request failed"),m.value="error",i.value=!0}).finally(()=>{d.value=!1})},R=()=>{};return(s,t)=>{const k=z;return A(),T(C,null,{default:a(()=>[e(g,{cols:"12"},{default:a(()=>[e($,{title:s.$t("Activate & Renew")},{default:a(()=>[e(Q,null,{default:a(()=>[e(v,{class:"pt-0"},{default:a(()=>[e(C,null,{default:a(()=>[e(g,{cols:"12",md:"6"},{default:a(()=>[e(k,{modelValue:o(y),"onUpdate:modelValue":t[0]||(t[0]=l=>V(y)?y.value=l:null),label:s.$t("Expire Date"),disabled:""},null,8,["modelValue","label"])]),_:1})]),_:1}),e(C,null,{default:a(()=>[e(g,{cols:"12",md:"6"},{default:a(()=>[e(k,{modelValue:o(c),"onUpdate:modelValue":t[1]||(t[1]=l=>V(c)?c.value=l:null),maxlength:"36",type:o(f)?"text":"password","append-inner-icon":o(f)?"tabler-eye-off":"tabler-eye",label:s.$t("Activation Code"),"onClick:appendInner":t[2]||(t[2]=l=>f.value=!o(f)),disabled:o(S)},null,8,["modelValue","type","append-inner-icon","label","disabled"])]),_:1})]),_:1})]),_:1}),e(v,null,{default:a(()=>[U("h6",X,_(s.$t("Activation Requirements"))+" : ",1),e(q,{class:"card-list"},{default:a(()=>[(A(),N(P,null,E(D,(l,h)=>e(M,{key:l,title:l,class:j(["text-medium-emphasis",{"text-error":h===0}])},{prepend:a(()=>[e(G,{size:"8",icon:"tabler-circle",class:"me-3"})]),_:2},1032,["title","class"])),64))]),_:1})]),_:1}),e(v,{class:"d-flex flex-wrap gap-4"},{default:a(()=>[e(w,{onClick:I},{default:a(()=>[u(_(s.$t("Save changes")),1)]),_:1}),e(w,{color:"secondary",variant:"tonal",type:"reset",onClick:J(R,["prevent"])},{default:a(()=>[u(_(s.$t("Reset")),1)]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1},8,["title"])]),_:1}),e(O,{modelValue:o(i),"onUpdate:modelValue":t[4]||(t[4]=l=>V(i)?i.value=l:null),transition:"scale-transition",location:"top",timeout:2500,color:o(m),variant:"tonal"},{actions:a(()=>[e(w,{color:"secondary",onClick:t[3]||(t[3]=l=>i.value=!1)},{default:a(()=>[u(" ❤️ ")]),_:1})]),default:a(()=>[u(_(o(p))+" ",1)]),_:1},8,["modelValue","color"]),e(W,{modelValue:o(d),"onUpdate:modelValue":t[5]||(t[5]=l=>V(d)?d.value=l:null),width:"300"},{default:a(()=>[e($,{color:"primary",width:"300"},{default:a(()=>[e(v,{class:"pt-3"},{default:a(()=>[u(" Please stand by "),e(H,{indeterminate:"",class:"mb-0"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})}}},ie=B(Y,[["__scopeId","data-v-5fd160bd"]]);export{ie as default};
