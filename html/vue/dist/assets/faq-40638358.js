import{_ as X}from"./AppTextField-8c148b8f.js";import{d as Z,o as m,b as E,w as s,q as a,av as R,n as c,y as _,a8 as J,ag as b,t as W,aY as M,s as r,as as G,I as A,K as B,b9 as ee,O as H,P as ae,ba as te,R as le,B as h,a6 as ne,W as $,a7 as L,bb as se,aQ as U,bc as oe,bd as Q,U as O,aC as q,be as ie,bf as Y,bg as ce,aD as z,bh as ue,L as re,bi as de,N as pe,bj as ve,X as me,Z as fe,bk as xe,l as k,E as F,c as g,ak as P,am as C,F as I,a as T,aj as j,aF as _e,aE as D,al as N}from"./index-9a5dc664.js";import{V as be,a as he,b as ye,c as ge}from"./VWindowItem-09058fe5.js";import{V as Ve}from"./VChip-a30ee730.js";import"./VTextField-3e2d458d.js";const Se=""+new URL("app-search-header-bg-0770a3bc.webp",import.meta.url).href;const we={class:"text-h3 font-weight-medium"},ke={class:"mb-0"},Pe=Z({inheritAttrs:!1}),Ce=Object.assign(Pe,{__name:"AppSearchHeader",props:{title:{type:String,required:!1},subtitle:{type:String,required:!1},customClass:{type:String,required:!1}},setup(e){const i=e;return(l,t)=>{const o=X;return m(),E(G,{flat:"",class:W(["text-center search-header",i.customClass]),style:M(`background: url(${r(Se)});`)},{default:s(()=>[a(R,null,{default:s(()=>[c("h5",we,_(i.title),1),a(o,J(l.$attrs,{placeholder:"Search a question...",class:"search-header-input mx-auto my-3",density:"comfortable"}),{"prepend-inner":s(()=>[a(b,{icon:"tabler-search",size:"23"})]),_:1},16),c("p",ke,_(i.subtitle),1)]),_:1})]),_:1},8,["class","style"])}}}),Ie=""+new URL("sitting-girl-with-laptop-33d53b10.webp",import.meta.url).href;const V=Symbol.for("vuetify:v-expansion-panel"),Te=["default","accordion","inset","popout"],Ee=A()({name:"VExpansionPanels",props:{color:String,variant:{type:String,default:"default",validator:e=>Te.includes(e)},readonly:Boolean,...B(),...ee(),...H(),...ae()},emits:{"update:modelValue":e=>!0},setup(e,i){let{slots:l}=i;te(e,V);const{themeClasses:t}=le(e),o=h(()=>e.variant&&`v-expansion-panels--variant-${e.variant}`);return ne({VExpansionPanel:{color:$(e,"color")},VExpansionPanelTitle:{readonly:$(e,"readonly")}}),L(()=>a(e.tag,{class:["v-expansion-panels",t.value,o.value,e.class],style:e.style},l)),{}}}),K=se({color:String,expandIcon:{type:U,default:"$expand"},collapseIcon:{type:U,default:"$collapse"},hideActions:Boolean,ripple:{type:[Boolean,Object],default:!1},readonly:Boolean},"v-expansion-panel-title"),qe=A()({name:"VExpansionPanelTitle",directives:{Ripple:oe},props:{...B(),...K()},setup(e,i){let{slots:l}=i;const t=Q(V);if(!t)throw new Error("[Vuetify] v-expansion-panel-title needs to be placed inside v-expansion-panel");const{backgroundColorClasses:o,backgroundColorStyles:d}=O(e,"color"),v=h(()=>({collapseIcon:e.collapseIcon,disabled:t.disabled.value,expanded:t.isSelected.value,expandIcon:e.expandIcon,readonly:e.readonly}));return L(()=>{var y;return q(a("button",{class:["v-expansion-panel-title",{"v-expansion-panel-title--active":t.isSelected.value},o.value,e.class],style:[d.value,e.style],type:"button",tabindex:t.disabled.value?-1:void 0,disabled:t.disabled.value,"aria-expanded":t.isSelected.value,onClick:e.readonly?void 0:t.toggle},[a("span",{class:"v-expansion-panel-title__overlay"},null),(y=l.default)==null?void 0:y.call(l,v.value),!e.hideActions&&a("span",{class:"v-expansion-panel-title__icon"},[l.actions?l.actions(v.value):a(b,{icon:t.isSelected.value?e.collapseIcon:e.expandIcon},null)])]),[[ie("ripple"),e.ripple]])}),{}}}),Ae=A()({name:"VExpansionPanelText",props:{...B(),...Y()},setup(e,i){let{slots:l}=i;const t=Q(V);if(!t)throw new Error("[Vuetify] v-expansion-panel-text needs to be placed inside v-expansion-panel");const{hasContent:o,onAfterLeave:d}=ce(e,t.isSelected);return L(()=>a(ue,{onAfterLeave:d},{default:()=>{var v;return[q(a("div",{class:["v-expansion-panel-text",e.class],style:e.style},[l.default&&o.value&&a("div",{class:"v-expansion-panel-text__wrapper"},[(v=l.default)==null?void 0:v.call(l)])]),[[z,t.isSelected.value]])]}})),{}}}),Be=A()({name:"VExpansionPanel",props:{title:String,text:String,bgColor:String,...B(),...re(),...de(),...Y(),...pe(),...H(),...K()},emits:{"group:selected":e=>!0},setup(e,i){let{slots:l}=i;const t=ve(e,V),{backgroundColorClasses:o,backgroundColorStyles:d}=O(e,"bgColor"),{elevationClasses:v}=me(e),{roundedClasses:y}=fe(e),p=h(()=>(t==null?void 0:t.disabled.value)||e.disabled),S=h(()=>t.group.items.value.reduce((f,u,w)=>(t.group.selected.value.includes(u.id)&&f.push(w),f),[])),n=h(()=>{const f=t.group.items.value.findIndex(u=>u.id===t.id);return!t.isSelected.value&&S.value.some(u=>u-f===1)}),x=h(()=>{const f=t.group.items.value.findIndex(u=>u.id===t.id);return!t.isSelected.value&&S.value.some(u=>u-f===-1)});return xe(V,t),L(()=>{const f=!!(l.text||e.text),u=!!(l.title||e.title);return a(e.tag,{class:["v-expansion-panel",{"v-expansion-panel--active":t.isSelected.value,"v-expansion-panel--before-active":n.value,"v-expansion-panel--after-active":x.value,"v-expansion-panel--disabled":p.value},y.value,o.value,e.class],style:[d.value,e.style],"aria-expanded":t.isSelected.value},{default:()=>{var w;return[a("div",{class:["v-expansion-panel__shadow",...v.value]},null),u&&a(qe,{key:"title",collapseIcon:e.collapseIcon,color:e.color,expandIcon:e.expandIcon,hideActions:e.hideActions,ripple:e.ripple},{default:()=>[l.title?l.title():e.title]}),f&&a(Ae,{key:"text",eager:e.eager},{default:()=>[l.text?l.text():e.text]}),(w=l.default)==null?void 0:w.call(l)]}})}),{}}}),Le={class:"d-flex align-center mb-6"},Re={class:"text-h6"},ze={class:"text-sm"},$e=c("span",{class:"text-base font-weight-medium"}," No Results Found!! ",-1),Ue={class:"text-center pt-15"},Fe=c("h5",{class:"text-h5 mb-2"}," You still have a question? ",-1),je=c("p",null," If you can't find question in our FAQ, you can contact us. We'll answer you shortly! ",-1),De={class:"text-h6 mb-2"},Oe={__name:"faq",setup(e){const i=k(""),l=k([]),t=()=>{},o=k("Payment"),d=k(0);F(o,()=>d.value=0),F(i,t,{immediate:!0});const v=[{icon:"tabler-phone",via:"+ (888) 8888 8888",tagLine:"We are always happy to help!"},{icon:"tabler-mail",via:"<EMAIL>",tagLine:"Best way to get answer faster!"}];return(y,p)=>{const S=Ce;return m(),g("section",null,[a(S,{modelValue:r(i),"onUpdate:modelValue":p[0]||(p[0]=n=>P(i)?i.value=n:null),title:"Hello, how can we help?",subtitle:"or select a category to quickly find the help you require","custom-class":"mb-7"},null,8,["modelValue"]),a(N,null,{default:s(()=>[q(a(C,{cols:"12",sm:"4",lg:"3",class:"position-relative"},{default:s(()=>[a(be,{modelValue:r(o),"onUpdate:modelValue":p[1]||(p[1]=n=>P(o)?o.value=n:null),direction:"vertical",class:"v-tabs-pill",grow:""},{default:s(()=>[(m(!0),g(I,null,T(r(l),n=>(m(),E(he,{key:n.faqTitle,value:n.faqTitle,class:"text-high-emphasis"},{default:s(()=>[a(b,{icon:n.faqIcon,size:20,start:""},null,8,["icon"]),j(" "+_(n.faqTitle),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"]),a(_e,{width:245,src:r(Ie),class:"d-none d-sm-block mt-10 mx-auto"},null,8,["src"])]),_:1},512),[[z,r(l).length]]),a(C,{cols:"12",sm:"8",lg:"9"},{default:s(()=>[a(ye,{modelValue:r(o),"onUpdate:modelValue":p[3]||(p[3]=n=>P(o)?o.value=n:null),class:"faq-v-window disable-tab-transition"},{default:s(()=>[(m(!0),g(I,null,T(r(l),n=>(m(),E(ge,{key:n.faqTitle,value:n.faqTitle},{default:s(()=>[c("div",Le,[a(D,{rounded:"",color:"primary",variant:"tonal",class:"me-3",size:"large"},{default:s(()=>[a(b,{size:32,icon:n.faqIcon},null,8,["icon"])]),_:2},1024),c("div",null,[c("h6",Re,_(n.faqTitle),1),c("span",ze,_(n.faqSubtitle),1)])]),a(Ee,{modelValue:r(d),"onUpdate:modelValue":p[2]||(p[2]=x=>P(d)?d.value=x:null),multiple:""},{default:s(()=>[(m(!0),g(I,null,T(n.faqs,x=>(m(),E(Be,{key:x.question,title:x.question,text:x.answer},null,8,["title","text"]))),128))]),_:2},1032,["modelValue"])]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),q(a(C,{cols:"12",class:W(r(l).length?"":"d-flex justify-center align-center")},{default:s(()=>[a(b,{icon:"tabler-help",start:"",size:"20"}),$e]),_:1},8,["class"]),[[z,!r(l).length]])]),_:1}),c("div",Ue,[a(Ve,{label:"",color:"primary",size:"small",class:"mb-2"},{default:s(()=>[j(" QUESTION? ")]),_:1}),Fe,je,a(N,{class:"mt-4"},{default:s(()=>[(m(),g(I,null,T(v,n=>a(C,{key:n.icon,sm:"6",cols:"12"},{default:s(()=>[a(G,{flat:"",class:"bg-var-theme-background"},{default:s(()=>[a(R,null,{default:s(()=>[a(D,{rounded:"",color:"primary",variant:"tonal",class:"me-3"},{default:s(()=>[a(b,{icon:n.icon},null,8,["icon"])]),_:2},1024)]),_:2},1024),a(R,null,{default:s(()=>[c("h6",De,_(n.via),1),c("span",null,_(n.tagLine),1)]),_:2},1024)]),_:2},1024)]),_:2},1024)),64))]),_:1})])])}}};export{Oe as default};
