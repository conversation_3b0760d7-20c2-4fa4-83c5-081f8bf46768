import{_ as je}from"./DialogCloseBtn-e6f97e88.js";import{_ as $e}from"./AppDateTimePicker-6623c419.js";import{p as Fe,V as Ge,_ as He}from"./VPagination-964310e8.js";import{b8 as Ye,g as We,aJ as Ze,ad as Xe,l as v,a9 as Ke,E as Ne,D as Je,r as Qe,o as Me,c as De,q as s,w as r,am as ue,as as Ve,av as ce,al as Ce,s as f,ak as be,ah as M,aj as V,y,z as Pe,n as q,A as ea,ag as ke,ar as aa,az as ia,aA as Be,aB as Ie,a8 as ze,ao as ta,ap as Te}from"./index-9a5dc664.js";import{V as oa}from"./VSpacer-d7832670.js";import{V as sa}from"./VDataTableServer-26da2b3a.js";import{V as la}from"./VChip-a30ee730.js";import{V as qe}from"./VDialog-0870f7b8.js";import"./VTextField-3e2d458d.js";import"./VDataTable-d690b508.js";import"./VSelectionControl-182ca2ca.js";var Ee={exports:{}};(function(_e,xe){(function(E,_){var ye="1.0.37",Z="",L="?",X="function",D="undefined",K="object",S="string",C="major",i="model",t="name",a="type",e="vendor",o="version",l="architecture",j="console",p="mobile",w="tablet",A="smarttv",B="wearable",J="embedded",Q=500,$="Amazon",F="Apple",se="ASUS",le="BlackBerry",U="Browser",G="Chrome",pe="Edge",H="Firefox",ee="Google",we="Huawei",P="LG",ae="Microsoft",me="Motorola",ie="Opera",te="Samsung",N="Sharp",Y="Sony",re="Xiaomi",ne="Zebra",de="Facebook",ve="Chromium OS",c="Mac OS",b=function(m,h){var u={};for(var k in m)h[k]&&h[k].length%2===0?u[k]=h[k].concat(m[k]):u[k]=m[k];return u},x=function(m){for(var h={},u=0;u<m.length;u++)h[m[u].toUpperCase()]=m[u];return h},fe=function(m,h){return typeof m===S?W(h).indexOf(W(m))!==-1:!1},W=function(m){return m.toLowerCase()},ge=function(m){return typeof m===S?m.replace(/[^\d\.]/g,Z).split(".")[0]:_},n=function(m,h){if(typeof m===S)return m=m.replace(/^\s\s*/,Z),typeof h===D?m:m.substring(0,Q)},I=function(m,h){for(var u=0,k,z,O,g,d,R;u<h.length&&!d;){var Ae=h[u],Ue=h[u+1];for(k=z=0;k<Ae.length&&!d&&Ae[k];)if(d=Ae[k++].exec(m),d)for(O=0;O<Ue.length;O++)R=d[++z],g=Ue[O],typeof g===K&&g.length>0?g.length===2?typeof g[1]==X?this[g[0]]=g[1].call(this,R):this[g[0]]=g[1]:g.length===3?typeof g[1]===X&&!(g[1].exec&&g[1].test)?this[g[0]]=R?g[1].call(this,R,g[2]):_:this[g[0]]=R?R.replace(g[1],g[2]):_:g.length===4&&(this[g[0]]=R?g[3].call(this,R.replace(g[1],g[2])):_):this[g]=R||_;u+=2}},Se=function(m,h){for(var u in h)if(typeof h[u]===K&&h[u].length>0){for(var k=0;k<h[u].length;k++)if(fe(h[u][k],m))return u===L?_:u}else if(fe(h[u],m))return u===L?_:u;return m},Le={"1.0":"/8","1.2":"/1","1.3":"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"},Oe={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2","8.1":"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Re={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[o,[t,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[o,[t,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[t,o],[/opios[\/ ]+([\w\.]+)/i],[o,[t,ie+" Mini"]],[/\bopr\/([\w\.]+)/i],[o,[t,ie]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[o,[t,"Baidu"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim)\s?(?:browser)?[\/ ]?([\w\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[t,o],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[o,[t,"UC"+U]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[o,[t,"WeChat"]],[/konqueror\/([\w\.]+)/i],[o,[t,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[o,[t,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[o,[t,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[o,[t,"Smart Lenovo "+U]],[/(avast|avg)\/([\w\.]+)/i],[[t,/(.+)/,"$1 Secure "+U],o],[/\bfocus\/([\w\.]+)/i],[o,[t,H+" Focus"]],[/\bopt\/([\w\.]+)/i],[o,[t,ie+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[o,[t,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[o,[t,"Dolphin"]],[/coast\/([\w\.]+)/i],[o,[t,ie+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[o,[t,"MIUI "+U]],[/fxios\/([-\w\.]+)/i],[o,[t,H]],[/\bqihu|(qi?ho?o?|360)browser/i],[[t,"360 "+U]],[/(oculus|sailfish|huawei|vivo)browser\/([\w\.]+)/i],[[t,/(.+)/,"$1 "+U],o],[/samsungbrowser\/([\w\.]+)/i],[o,[t,te+" Internet"]],[/(comodo_dragon)\/([\w\.]+)/i],[[t,/_/g," "],o],[/metasr[\/ ]?([\d\.]+)/i],[o,[t,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[t,"Sogou Mobile"],o],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345Explorer)[\/ ]?([\w\.]+)/i],[t,o],[/(lbbrowser)/i,/\[(linkedin)app\]/i],[t],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[t,de],o],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i],[t,o],[/\bgsa\/([\w\.]+) .*safari\//i],[o,[t,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[o,[t,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[o,[t,G+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[t,G+" WebView"],o],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[o,[t,"Android "+U]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[t,o],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[o,[t,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[o,t],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[t,[o,Se,Le]],[/(webkit|khtml)\/([\w\.]+)/i],[t,o],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[t,"Netscape"],o],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[o,[t,H+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[t,o],[/(cobalt)\/([\w\.]+)/i],[t,[o,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[l,"amd64"]],[/(ia32(?=;))/i],[[l,W]],[/((?:i[346]|x)86)[;\)]/i],[[l,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[l,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[l,"armhf"]],[/windows (ce|mobile); ppc;/i],[[l,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[l,/ower/,Z,W]],[/(sun4\w)[;\)]/i],[[l,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[l,W]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[i,[e,te],[a,w]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[i,[e,te],[a,p]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[i,[e,F],[a,p]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[i,[e,F],[a,w]],[/(macintosh);/i],[i,[e,F]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[i,[e,N],[a,p]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[i,[e,we],[a,w]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[i,[e,we],[a,p]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[i,/_/g," "],[e,re],[a,p]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[i,/_/g," "],[e,re],[a,w]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[i,[e,"OPPO"],[a,p]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[i,[e,"Vivo"],[a,p]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[i,[e,"Realme"],[a,p]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[i,[e,me],[a,p]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[i,[e,me],[a,w]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[i,[e,P],[a,w]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[i,[e,P],[a,p]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[i,[e,"Lenovo"],[a,w]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[i,/_/g," "],[e,"Nokia"],[a,p]],[/(pixel c)\b/i],[i,[e,ee],[a,w]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[i,[e,ee],[a,p]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[i,[e,Y],[a,p]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[i,"Xperia Tablet"],[e,Y],[a,w]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[i,[e,"OnePlus"],[a,p]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[i,[e,$],[a,w]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[i,/(.+)/g,"Fire Phone $1"],[e,$],[a,p]],[/(playbook);[-\w\),; ]+(rim)/i],[i,e,[a,w]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[i,[e,le],[a,p]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[i,[e,se],[a,w]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[i,[e,se],[a,p]],[/(nexus 9)/i],[i,[e,"HTC"],[a,w]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[e,[i,/_/g," "],[a,p]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[i,[e,"Acer"],[a,w]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[i,[e,"Meizu"],[a,p]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[i,[e,"Ulefone"],[a,p]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[e,i,[a,p]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[e,i,[a,w]],[/(surface duo)/i],[i,[e,ae],[a,w]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[i,[e,"Fairphone"],[a,p]],[/(u304aa)/i],[i,[e,"AT&T"],[a,p]],[/\bsie-(\w*)/i],[i,[e,"Siemens"],[a,p]],[/\b(rct\w+) b/i],[i,[e,"RCA"],[a,w]],[/\b(venue[\d ]{2,7}) b/i],[i,[e,"Dell"],[a,w]],[/\b(q(?:mv|ta)\w+) b/i],[i,[e,"Verizon"],[a,w]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[i,[e,"Barnes & Noble"],[a,w]],[/\b(tm\d{3}\w+) b/i],[i,[e,"NuVision"],[a,w]],[/\b(k88) b/i],[i,[e,"ZTE"],[a,w]],[/\b(nx\d{3}j) b/i],[i,[e,"ZTE"],[a,p]],[/\b(gen\d{3}) b.+49h/i],[i,[e,"Swiss"],[a,p]],[/\b(zur\d{3}) b/i],[i,[e,"Swiss"],[a,w]],[/\b((zeki)?tb.*\b) b/i],[i,[e,"Zeki"],[a,w]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[e,"Dragon Touch"],i,[a,w]],[/\b(ns-?\w{0,9}) b/i],[i,[e,"Insignia"],[a,w]],[/\b((nxa|next)-?\w{0,9}) b/i],[i,[e,"NextBook"],[a,w]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[e,"Voice"],i,[a,p]],[/\b(lvtel\-)?(v1[12]) b/i],[[e,"LvTel"],i,[a,p]],[/\b(ph-1) /i],[i,[e,"Essential"],[a,p]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[i,[e,"Envizen"],[a,w]],[/\b(trio[-\w\. ]+) b/i],[i,[e,"MachSpeed"],[a,w]],[/\btu_(1491) b/i],[i,[e,"Rotor"],[a,w]],[/(shield[\w ]+) b/i],[i,[e,"Nvidia"],[a,w]],[/(sprint) (\w+)/i],[e,i,[a,p]],[/(kin\.[onetw]{3})/i],[[i,/\./g," "],[e,ae],[a,p]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[i,[e,ne],[a,w]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[i,[e,ne],[a,p]],[/smart-tv.+(samsung)/i],[e,[a,A]],[/hbbtv.+maple;(\d+)/i],[[i,/^/,"SmartTV"],[e,te],[a,A]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[e,P],[a,A]],[/(apple) ?tv/i],[e,[i,F+" TV"],[a,A]],[/crkey/i],[[i,G+"cast"],[e,ee],[a,A]],[/droid.+aft(\w+)( bui|\))/i],[i,[e,$],[a,A]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[i,[e,N],[a,A]],[/(bravia[\w ]+)( bui|\))/i],[i,[e,Y],[a,A]],[/(mitv-\w{5}) bui/i],[i,[e,re],[a,A]],[/Hbbtv.*(technisat) (.*);/i],[e,i,[a,A]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[e,n],[i,n],[a,A]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[a,A]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[e,i,[a,j]],[/droid.+; (shield) bui/i],[i,[e,"Nvidia"],[a,j]],[/(playstation [345portablevi]+)/i],[i,[e,Y],[a,j]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[i,[e,ae],[a,j]],[/((pebble))app/i],[e,i,[a,B]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[i,[e,F],[a,B]],[/droid.+; (glass) \d/i],[i,[e,ee],[a,B]],[/droid.+; (wt63?0{2,3})\)/i],[i,[e,ne],[a,B]],[/(quest( 2| pro)?)/i],[i,[e,de],[a,B]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[e,[a,J]],[/(aeobc)\b/i],[i,[e,$],[a,J]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[i,[a,p]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[i,[a,w]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[a,w]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[a,p]],[/(android[-\w\. ]{0,9});.+buil/i],[i,[e,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[o,[t,pe+"HTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[o,[t,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[t,o],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[o,t]],os:[[/microsoft (windows) (vista|xp)/i],[t,o],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[t,[o,Se,Oe]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[o,Se,Oe],[t,"Windows"]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[o,/_/g,"."],[t,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[t,c],[o,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[o,t],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[t,o],[/\(bb(10);/i],[o,[t,le]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[o,[t,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[o,[t,H+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[o,[t,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[o,[t,"watchOS"]],[/crkey\/([\d\.]+)/i],[o,[t,G+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[t,ve],o],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[t,o],[/(sunos) ?([\w\.\d]*)/i],[[t,"Solaris"],o],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[t,o]]},T=function(m,h){if(typeof m===K&&(h=m,m=_),!(this instanceof T))return new T(m,h).getResult();var u=typeof E!==D&&E.navigator?E.navigator:_,k=m||(u&&u.userAgent?u.userAgent:Z),z=u&&u.userAgentData?u.userAgentData:_,O=h?b(Re,h):Re,g=u&&u.userAgent==k;return this.getBrowser=function(){var d={};return d[t]=_,d[o]=_,I.call(d,k,O.browser),d[C]=ge(d[o]),g&&u&&u.brave&&typeof u.brave.isBrave==X&&(d[t]="Brave"),d},this.getCPU=function(){var d={};return d[l]=_,I.call(d,k,O.cpu),d},this.getDevice=function(){var d={};return d[e]=_,d[i]=_,d[a]=_,I.call(d,k,O.device),g&&!d[a]&&z&&z.mobile&&(d[a]=p),g&&d[i]=="Macintosh"&&u&&typeof u.standalone!==D&&u.maxTouchPoints&&u.maxTouchPoints>2&&(d[i]="iPad",d[a]=w),d},this.getEngine=function(){var d={};return d[t]=_,d[o]=_,I.call(d,k,O.engine),d},this.getOS=function(){var d={};return d[t]=_,d[o]=_,I.call(d,k,O.os),g&&!d[t]&&z&&z.platform!="Unknown"&&(d[t]=z.platform.replace(/chrome os/i,ve).replace(/macos/i,c)),d},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return k},this.setUA=function(d){return k=typeof d===S&&d.length>Q?n(d,Q):d,this},this.setUA(k),this};T.VERSION=ye,T.BROWSER=x([t,o,C]),T.CPU=x([l]),T.DEVICE=x([i,e,a,j,p,A,w,B,J]),T.ENGINE=T.OS=x([t,o]),_e.exports&&(xe=_e.exports=T),xe.UAParser=T;var oe=typeof E!==D&&(E.jQuery||E.Zepto);if(oe&&!oe.ua){var he=new T;oe.ua=he.getResult(),oe.ua.get=function(){return he.getUA()},oe.ua.set=function(m){he.setUA(m);var h=he.getResult();for(var u in h)oe.ua[u]=h[u]}}})(typeof window=="object"?window:Ye)})(Ee,Ee.exports);var ra=Ee.exports;const na=We(ra);const da={class:"me-3 d-flex gap-3"},ua={key:0,class:"app-user-search-filter d-flex align-center flex-wrap gap-4",style:{"justify-content":"flex-end"}},ca={class:"text-wrap1"},ba={class:"text-wrap mt-2 mb-2"},pa={class:""},wa={class:"text-wrap1"},ma={class:"d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3"},va={class:"text-sm text-disabled mb-0"},fa={class:""},ga={__name:"index",setup(_e){const E=Xe().permissions,_=v(""),ye=v("all");v("");const Z=v(1),L=v(0),X=v([]),D=v("primary");v([]);const K=v(""),S=v(!1),C=v(""),i=v("warning"),t=v(!1),a=v(!1),e=v({page:1,itemsPerPage:10}),o=v(""),{t:l,locale:j}=Ke(),p=v(l("Domain")),w=v(l("Action")),A=v(l("Method")),B=v(l("Params")),J=v(l("UserAgent")),Q=v(l("Browser")),$=v(l("OS")),F=v(l("Remark")),se=v(l("Status")),le=v(l("created_at")),U=v(l("updated_at")),G=v(l("Country")),pe=v(l("Actions")),H=v(l("No Data Text"));Ne(j,()=>{p.value=l("Domain"),w.value=l("Action"),A.value=l("Method"),B.value=l("Params"),J.value=l("UserAgent"),Q.value=l("Browser"),$.value=l("OS"),F.value=l("Remark"),se.value=l("Status"),le.value=l("created_at"),U.value=l("updated_at"),pe.value=l("Actions"),G.value=l("Country"),H.value=l("No Data Text")});const ee=[{title:p,key:"data.referer",sortable:!1,width:100},{title:Q,key:"browser.name",sortable:!1,align:"center"},{title:$,key:"os.name",sortable:!1,align:"center"},{title:J,key:"ua",sortable:!1,width:250},{title:w,key:"data.action",sortable:!1,align:"center"},{title:se,key:"data.remark",sortable:!1,align:"center"},{title:"IP",key:"data.ip",sortable:!1},{title:G,key:"data.tag",sortable:!1},{title:le,key:"data.created_at",sortable:!1},{title:pe,key:"actions",align:"center",sortable:!1}],we=[{title:l("All"),value:""},{title:l("Success"),value:"1"},{title:l("Interception"),value:"0"}],P=async()=>{D.value="primary";let c="",b="";o.value&&o.value.indexOf("to")!=-1&&(c=o.value.split("to")[0].trim()),o.value&&o.value.indexOf("to")!=-1&&(b=o.value.split("to")[1].trim()),E.includes(17)&&await Te.get("/api/operationLog/viewIndex",{params:{action:_.value,createdName:K.value,page:e.value.page,pagesize:e.value.itemsPerPage,startTime:c,endTime:b,type:2}}).then(x=>{x.data.code===200&&(X.value=x.data.data.data,Z.value=x.data.data.last_page,L.value=x.data.data.total,e.value.page=x.data.data.current_page,me())}).catch(x=>{C.value=l("Request failed"),i.value="error",S.value=!0}).finally(()=>{D.value=!1})},ae=v([]),me=()=>{ae.value=X.value.map(c=>{const b=new na(c.ua);return{data:c,...b.getResult()}})};Je(async()=>{await P()});const ie=()=>{P()},te=()=>{e.value.page=1,e.value.itemsPerPage=10,K.value="",o.value="",ye.value="all",P()},N=v(!1),Y=v(""),re=c=>{N.value=!N.value,Y.value=c};Ne(()=>e.value.itemsPerPage,(c,b)=>{e.value.page=1,P()});const ne=()=>{a.value=!0,E.includes(18)&&Te.post("/api/operationLog/doViewEmpty",{ids:"",type:2}).then(c=>{c.data.code===200?(C.value=l("Operation successful"),i.value="success",S.value=!0,P()):(C.value=l("Operation failed"),i.value="error",S.value=!0)}).catch(c=>{C.value=l("Request failed"),i.value="error",S.value=!0}).finally(()=>{a.value=!1,t.value=!1})},de=(c,b)=>{Te.post("/api/user/blacklisting",{ids:"",uuid:"",referer:c.data.referer,country:c.data.tag,ip:c.data.ip,ips:c.data.ip,actionType:b}).then(x=>{x.data.code===200?(C.value=l("Operation successful"),i.value="success",S.value=!0,P()):(C.value=l("Operation failed"),i.value="error",S.value=!0)}).catch(x=>{C.value=l("Request failed"),i.value="error",S.value=!0}).finally(()=>{})};function ve(c){return c==="1"?"success":c==="0"?"error":"default"}return(c,b)=>{const x=He,fe=$e,W=Qe("IconBtn"),ge=je;return Me(),De("section",null,[s(Ce,null,{default:r(()=>[s(ue,{cols:"12"},{default:r(()=>[s(Ve,{title:c.$t("Search Filter")},{default:r(()=>[s(ce,null,{default:r(()=>[s(Ce,null,{default:r(()=>[s(ue,{cols:"12",sm:"2"},{default:r(()=>[s(x,{modelValue:f(_),"onUpdate:modelValue":b[0]||(b[0]=n=>be(_)?_.value=n:null),label:c.$t("Action"),items:we,clearable:"","clear-icon":"tabler-x"},null,8,["modelValue","label"])]),_:1}),s(ue,{cols:"12",sm:"2"},{default:r(()=>[s(fe,{modelValue:f(o),"onUpdate:modelValue":b[1]||(b[1]=n=>be(o)?o.value=n:null),label:c.$t("Select Date"),config:{mode:"range"},clearable:"","clear-icon":"tabler-x"},null,8,["modelValue","label"])]),_:1}),s(ue,{cols:"12",sm:"2",class:"d-flex align-end"},{default:r(()=>[s(M,{"prepend-icon":"tabler-search",style:{"margin-bottom":"1px"},onClick:ie},{default:r(()=>[V(y(c.$t("Search")),1)]),_:1}),s(M,{style:{"margin-bottom":"1px"},type:"reset",color:"secondary",variant:"tonal",class:"ml-2",onClick:te},{default:r(()=>[V(y(c.$t("Reset")),1)]),_:1})]),_:1})]),_:1})]),_:1}),s(Pe),s(ce,{class:"d-flex flex-wrap py-4 gap-4"},{default:r(()=>[q("div",da,[s(x,{"model-value":f(e).itemsPerPage,items:[{value:10,title:"10"},{value:25,title:"25"},{value:50,title:"50"},{value:100,title:"100"},{value:500,title:"500"}],style:{width:"6.25rem"},"onUpdate:modelValue":b[2]||(b[2]=n=>f(e).itemsPerPage=parseInt(n,10))},null,8,["model-value"])]),s(oa),f(E).includes(18)?(Me(),De("div",ua,[s(M,{variant:"tonal",color:"error","prepend-icon":"tabler-trash",onClick:b[3]||(b[3]=n=>t.value=!f(t))},{default:r(()=>[V(y(c.$t("One-click clearing")),1)]),_:1})])):ea("",!0)]),_:1}),s(Pe),s(f(sa),{"items-per-page":f(e).itemsPerPage,"onUpdate:itemsPerPage":b[5]||(b[5]=n=>f(e).itemsPerPage=n),page:f(e).page,"onUpdate:page":b[6]||(b[6]=n=>f(e).page=n),items:f(ae),"items-length":f(L),headers:ee,class:"text-no-wrap",loading:f(D),"onUpdate:options":b[7]||(b[7]=n=>e.value=n),"item-value":"id","no-data-text":f(H),hover:""},{"item.data.referer":r(({item:n})=>[q("div",ca,y(n.raw.data.referer),1)]),"item.ua":r(({item:n})=>[q("div",ba,y(n.raw.ua),1)]),"item.data.action":r(({item:n})=>[q("div",pa,[s(la,{label:"",color:ve(n.raw.data.action)},{default:r(()=>[V(y(n.raw.data.action==1?c.$t("Connected"):c.$t("Interception")),1)]),_:2},1032,["color"])])]),"item.data.remark":r(({item:n})=>[q("div",wa,y(c.$t(n.raw.data.remark)),1)]),"item.actions":r(({item:n})=>[s(W,{onClick:I=>re(n.raw)},{default:r(()=>[s(ke,{icon:"tabler-eye"})]),_:2},1032,["onClick"]),s(M,{icon:"",variant:"text",size:"small",color:"medium-emphasis"},{default:r(()=>[s(ke,{size:"24",icon:"tabler-dots-vertical"}),s(aa,{activator:"parent"},{default:r(()=>[s(ia,null,{default:r(()=>[s(Be,{onClick:I=>de(n.raw,1)},{prepend:r(()=>[s(ke,{icon:"tabler-accessible-off"})]),default:r(()=>[s(Ie,null,{default:r(()=>[V(y(c.$t("Blacklisted")),1)]),_:1})]),_:2},1032,["onClick"]),s(Be,{onClick:I=>de(n.raw,2)},{prepend:r(()=>[s(ke,{icon:"tabler-accessible"})]),default:r(()=>[s(Ie,null,{default:r(()=>[V(y(c.$t("Cancel blacklist")),1)]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1024)]),bottom:r(()=>[s(Pe),q("div",ma,[q("p",va,y(f(Fe)(f(e),f(L))),1),s(Ge,{modelValue:f(e).page,"onUpdate:modelValue":b[4]||(b[4]=n=>f(e).page=n),length:Math.ceil(f(L)/f(e).itemsPerPage),"total-visible":"5",onClick:P},{prev:r(n=>[s(M,ze({variant:"tonal",color:"default"},n,{icon:!1}),{default:r(()=>[V(y(c.$t("$vuetify.pagination.ariaLabel.previous")),1)]),_:2},1040)]),next:r(n=>[s(M,ze({variant:"tonal",color:"default"},n,{icon:!1}),{default:r(()=>[V(y(c.$t("$vuetify.pagination.ariaLabel.next")),1)]),_:2},1040)]),_:1},8,["modelValue","length"])])]),_:1},8,["items-per-page","page","items","items-length","loading","no-data-text"])]),_:1},8,["title"])]),_:1})]),_:1}),s(qe,{modelValue:f(N),"onUpdate:modelValue":b[9]||(b[9]=n=>be(N)?N.value=n:null),class:"v-dialog-sm","min-width":"880","retain-focus":!1},{default:r(()=>[s(ge,{onClick:b[8]||(b[8]=n=>N.value=!1)}),s(Ve,{title:c.$t("Params")},{default:r(()=>[s(ce,null,{default:r(()=>[s(Ce,null,{default:r(()=>[s(ue,{cols:"12",md:"12"},{default:r(()=>[V(y(f(Y).data.headers),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"]),s(ta,{modelValue:f(S),"onUpdate:modelValue":b[11]||(b[11]=n=>be(S)?S.value=n:null),transition:"scale-transition",location:"top",timeout:2500,color:f(i),variant:"tonal"},{actions:r(()=>[s(M,{color:"secondary",onClick:b[10]||(b[10]=n=>S.value=!1)},{default:r(()=>[V(" ❤️ ")]),_:1})]),default:r(()=>[V(y(f(C))+" ",1)]),_:1},8,["modelValue","color"]),s(qe,{modelValue:f(t),"onUpdate:modelValue":b[14]||(b[14]=n=>be(t)?t.value=n:null),persistent:"",class:"v-dialog-sm"},{default:r(()=>[s(ge,{onClick:b[12]||(b[12]=n=>t.value=!f(t))}),s(Ve,{title:c.$t("Operation tips")},{default:r(()=>[s(ce,null,{default:r(()=>[q("span",fa,y(c.$t("One-click clearing notice")),1)]),_:1}),s(ce,{class:"d-flex justify-end gap-3 flex-wrap"},{default:r(()=>[s(M,{color:"secondary",variant:"tonal",onClick:b[13]||(b[13]=n=>t.value=!1)},{default:r(()=>[V(y(c.$t("Cancel")),1)]),_:1}),s(M,{loading:f(a),disabled:f(a),onClick:ne},{default:r(()=>[V(y(c.$t("Confirm")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"])])}}},Ea=Ze(ga,[["__scopeId","data-v-2360cbef"]]);export{Ea as default};
