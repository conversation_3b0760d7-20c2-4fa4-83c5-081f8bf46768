import{I as S,K as k,bo as N,O as P,P as j,R as U,bp as E,a6 as q,W as y,B as V,a7 as D,q as t,C as _,aQ as w,N as L,bq as R,L as $,br as M,U as I,Z as O,X as F,ag as H,bs as K,bt as Q,bu as W,l as m,E as X,a9 as Z,ad as A,o as g,b as x,w as s,av as C,c as G,F as J,a as Y,s as v,as as b,n as r,y as f,z as p,ao as ee,ak as B,ah as te,aj as h,b0 as ie}from"./index-9a5dc664.js";import{V as le}from"./VDialog-0870f7b8.js";const ne=S()({name:"VTimeline",props:{align:{type:String,default:"center",validator:e=>["center","start"].includes(e)},direction:{type:String,default:"vertical",validator:e=>["vertical","horizontal"].includes(e)},justify:{type:String,default:"auto",validator:e=>["auto","center"].includes(e)},side:{type:String,validator:e=>e==null||["start","end"].includes(e)},lineInset:{type:[String,Number],default:0},lineThickness:{type:[String,Number],default:2},lineColor:String,truncateLine:{type:String,validator:e=>["start","end","both"].includes(e)},...k(),...N(),...P(),...j()},setup(e,d){let{slots:l}=d;const{themeClasses:u}=U(e),{densityClasses:a}=E(e);q({VTimelineDivider:{lineColor:y(e,"lineColor")},VTimelineItem:{density:y(e,"density"),lineInset:y(e,"lineInset")}});const c=V(()=>{const i=e.side?e.side:e.density!=="default"?"end":null;return i&&`v-timeline--side-${i}`}),o=V(()=>{const i=["v-timeline--truncate-line-start","v-timeline--truncate-line-end"];switch(e.truncateLine){case"both":return i;case"start":return i[0];case"end":return i[1];default:return null}});return D(()=>t(e.tag,{class:["v-timeline",`v-timeline--${e.direction}`,`v-timeline--align-${e.align}`,`v-timeline--justify-${e.justify}`,o.value,{"v-timeline--inset-line":!!e.lineInset},u.value,a.value,c.value,e.class],style:[{"--v-timeline-line-thickness":_(e.lineThickness)},e.style]},l)),{}}}),se=S()({name:"VTimelineDivider",props:{dotColor:String,fillDot:Boolean,hideDot:Boolean,icon:w,iconColor:String,lineColor:String,...k(),...L(),...R(),...$()},setup(e,d){let{slots:l}=d;const{sizeClasses:u,sizeStyles:a}=M(e,"v-timeline-divider__dot"),{backgroundColorStyles:c,backgroundColorClasses:o}=I(y(e,"dotColor")),{roundedClasses:i}=O(e,"v-timeline-divider__dot"),{elevationClasses:n}=F(e),{backgroundColorClasses:T,backgroundColorStyles:z}=I(y(e,"lineColor"));return D(()=>t("div",{class:["v-timeline-divider",{"v-timeline-divider--fill-dot":e.fillDot},e.class],style:e.style},[t("div",{class:["v-timeline-divider__before",T.value],style:z.value},null),!e.hideDot&&t("div",{key:"dot",class:["v-timeline-divider__dot",n.value,i.value,u.value],style:a.value},[t("div",{class:["v-timeline-divider__inner-dot",o.value,i.value],style:c.value},[l.default?t(K,{key:"icon-defaults",disabled:!e.icon,defaults:{VIcon:{color:e.iconColor,icon:e.icon,size:e.size}}},l.default):t(H,{key:"icon",color:e.iconColor,icon:e.icon,size:e.size},null)])]),t("div",{class:["v-timeline-divider__after",T.value],style:z.value},null)])),{}}}),ae=S()({name:"VTimelineItem",props:{density:String,dotColor:String,fillDot:Boolean,hideDot:Boolean,hideOpposite:{type:Boolean,default:void 0},icon:w,iconColor:String,lineInset:[Number,String],...k(),...Q(),...$(),...L(),...R(),...P()},setup(e,d){let{slots:l}=d;const{dimensionStyles:u}=W(e),a=m(0),c=m();return X(c,o=>{var i;o&&(a.value=((i=o.$el.querySelector(".v-timeline-divider__dot"))==null?void 0:i.getBoundingClientRect().width)??0)},{flush:"post"}),D(()=>{var o,i;return t("div",{class:["v-timeline-item",{"v-timeline-item--fill-dot":e.fillDot},e.class],style:[{"--v-timeline-dot-size":_(a.value),"--v-timeline-line-inset":e.lineInset?`calc(var(--v-timeline-dot-size) / 2 + ${_(e.lineInset)})`:_(0)},e.style]},[t("div",{class:"v-timeline-item__body",style:u.value},[(o=l.default)==null?void 0:o.call(l)]),t(se,{ref:c,hideDot:e.hideDot,icon:e.icon,iconColor:e.iconColor,size:e.size,elevation:e.elevation,dotColor:e.dotColor,fillDot:e.fillDot,rounded:e.rounded},{default:l.icon}),e.density!=="compact"&&t("div",{class:"v-timeline-item__opposite"},[!e.hideOpposite&&((i=l.opposite)==null?void 0:i.call(l))])])}),{}}}),oe={class:"d-flex justify-space-between align-center mb-1"},de={class:"app-timeline-title",style:{"font-size":"16px"}},re={class:"app-timeline-meta",style:{"font-size":"13px"}},ce={class:"app-timeline-text mb-0 text-error text-h6"},ue={class:"app-timeline-text mb-0"},me={class:"d-flex justify-space-between align-center"},ve={class:"d-flex align-center"},fe=["innerHTML"],ge={__name:"tour",setup(e){Z();const d=m(!1),l=m(""),u=m("warning"),a=m(!1),c=V(()=>A().updateInfo);return(o,i)=>(g(),x(b,{title:"后台更新记录"},{default:s(()=>[t(C,null,{default:s(()=>[t(ne,{side:"end",align:"start","line-inset":"8","truncate-line":"both",density:"compact"},{default:s(()=>[(g(!0),G(J,null,Y(v(c),n=>(g(),x(ae,{"dot-color":"success",size:"x-small"},{default:s(()=>[t(b,{class:"bg-light-primary",variant:"text"},{default:s(()=>[t(C,null,{default:s(()=>[r("div",oe,[r("span",de,f(n.version),1),r("span",re,f(n.time),1)]),r("p",ce,f(n.updateTitle),1),r("p",ue,f(n.updateDesc),1),t(p,{class:"my-4"}),r("div",me,[r("span",ve,[r("div",null,[r("span",{style:{color:"rgba(var(--v-theme-on-background), var(--v-high-emphasis-opacity))","font-size":"13px"},innerHTML:n.updateInfo},null,8,fe)])])])]),_:2},1024)]),_:2},1024)]),_:2},1024))),256))]),_:1})]),_:1}),t(ee,{modelValue:v(a),"onUpdate:modelValue":i[1]||(i[1]=n=>B(a)?a.value=n:null),transition:"scale-transition",location:"top",timeout:2500,color:v(u)},{actions:s(()=>[t(te,{color:"secondary",onClick:i[0]||(i[0]=n=>a.value=!1)},{default:s(()=>[h(" ❤️ ")]),_:1})]),default:s(()=>[h(f(v(l))+" ",1)]),_:1},8,["modelValue","color"]),t(le,{modelValue:v(d),"onUpdate:modelValue":i[2]||(i[2]=n=>B(d)?d.value=n:null),width:"300"},{default:s(()=>[t(b,{color:"primary",width:"300"},{default:s(()=>[t(C,{class:"pt-3"},{default:s(()=>[h(" Please stand by "),t(ie,{indeterminate:"",class:"mb-0"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}))}};export{ge as default};
