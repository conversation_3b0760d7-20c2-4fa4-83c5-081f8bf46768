import{ad as Pe,a9 as Ie,l as t,E as je,b2 as Me,aK as Ge,D as De,o as J,c as ke,q as e,w as a,am as I,as as x,av as w,n as g,y as u,s as l,al as Z,ak as C,b as de,A as Te,az as Ye,F as Ae,a as qe,aA as We,ah as h,aj as v,aG as xe,ao as Ne,b0 as Re,ap as ae,cq as va,z as Qe,aY as fa,ag as Oe,a8 as Xe,ar as pa,aF as _a,aE as ea,aq as ga,a3 as ba,bd as ya,bv as ka,bk as Va,cr as Ca}from"./index-9a5dc664.js";import{_ as ze}from"./AppTextField-8c148b8f.js";import{_ as la,p as ha,V as Sa}from"./VPagination-964310e8.js";import{V as we}from"./VForm-c6ce9b98.js";import{V as aa}from"./VListItemAction-68e43985.js";import{V as ta}from"./VSwitch-0179a869.js";import{V as oe}from"./VDialog-0870f7b8.js";import{_ as na}from"./DialogCloseBtn-e6f97e88.js";import{_ as oa}from"./AppTextarea-36c843b3.js";import{r as sa}from"./style-7dcccb91.js";import{V as $a}from"./VDataTableServer-26da2b3a.js";import{x as Ba}from"./xterm-8ae096f4.js";import{u as wa}from"./index-9465fde1.js";import{b as ia}from"./route-block-83d24a4e.js";import{a as Ia,V as Da,c as Ue,b as Na}from"./VWindowItem-09058fe5.js";import"./VTextField-3e2d458d.js";import"./VDataTable-d690b508.js";import"./VSelectionControl-182ca2ca.js";import"./VChip-a30ee730.js";import"./tinycolor-ea5bcbb6.js";const Ua={class:"mt-n4 mb-6 text-body-1"},Pa={class:"mt-n4 mb-6 text-body-1"},Ta={class:"d-flex flex-wrap gap-4 mt-4"},Aa={class:"mt-n4 mb-6 text-body-1"},Ra={class:"mt-n4 mb-6 text-body-1"},La={class:"mt-n4 mb-6 text-body-1"},Oa={__name:"BackstageSettings",props:{BackstageSettingsData:Object},setup(se){const{BackstageSettingsData:f}=se,b=Pe(),{t:n,locale:i}=Ie(),L=t(n("Open")),R=t(n("Close")),T=t(n("Allowed")),S=t(n("Forbidden")),U=t(n("Enable 3D verification")),O=t(n("Unattended1")),k=t(n("Filter by credit card")),$=t(n("Filter by debit card")),c=t(n("Payment Successful")),D=t(n("Payment Fail")),N=t(n("OTP Verification")),Q=t(n("Email Verification")),o=t(n("APP Authorization")),m=t(n("PIN Verification")),p=t(n("Pay by changing card")),_=t(n("CVV verification on the back of Amex card")),B=t(n("Custom OTP verification")),j=t(n("Custom rejection copywriting")),ne=t(n("VPASS verification (only available in JP countries)")),E=t(n("OB verification")),be=t(n("Custom APP authorization verification")),Ve=t(n("Balance Inquiry")),Ce=t(n("Default")),pe=t(n("Account password and card information")),$e=t(n("Account password")),_e=t(n("Online Banking"));je(i,()=>{L.value=n("Open"),R.value=n("Close"),T.value=n("Allowed"),S.value=n("Forbidden"),U.value=n("Enable 3D verification"),O.value=n("Unattended1"),k.value=n("Filter by credit card"),$.value=n("Filter by debit card"),c.value=n("Payment Successful"),D.value=n("Payment Fail"),N.value=n("OTP Verification"),Q.value=n("Email Verification"),o.value=n("APP Authorization"),m.value=n("PIN Verification"),p.value=n("Pay by changing card"),_.value=n("CVV verification on the back of Amex card"),B.value=n("Custom OTP verification"),j.value=n("Custom rejection copywriting"),ne.value=n("VPASS verification (only available in JP countries)"),E.value=n("OB verification"),be.value=n("Custom APP authorization verification"),Ve.value=n("Balance Inquiry"),Ce.value=n("Default"),pe.value=n("Account password and card information"),$e.value=n("Account password"),_e.value=n("Online Banking")});const ie=t(!1),Y=t(!1),ue=t(""),X=t("warning"),z=t(1),re=t(0),ce=t(0),ge=t(1),M=t(1),V=t(0),he=t([{title:Ce,value:0},{title:pe,value:2},{title:$e,value:1}]),W=t([{title:L,value:1},{title:R,value:0}]),te=t([{title:T,value:1},{title:S,value:0}]),Ee=t([{title:U,value:1},{title:O,value:0}]),Fe=t([{title:R,value:0},{title:"PIN",value:1},{title:_e,value:2}]),Be=t([{title:R,value:0},{title:k,value:1},{title:$,value:2}]),ye=t(1),me=t([{title:c,value:1},{title:D,value:0}]);let y=t(3);const P=t([]),ve=t([]),ee=t([{id:1,name:N,subtitle:"",title:"is_otp",connected:!1},{id:2,name:Q,subtitle:"",title:"is_email",connected:!1},{id:3,name:o,subtitle:"",title:"is_app",connected:!1},{id:4,name:m,subtitle:"",title:"is_pin",connected:!1},{id:5,name:p,subtitle:"",title:"is_change_card",connected:!1},{id:6,name:_,subtitle:"",title:"is_express_cvv",connected:!1},{id:7,name:B,subtitle:"",title:"is_custom_otp",connected:!1},{id:8,name:j,subtitle:"",title:"is_custom_reject",connected:!1},{id:9,name:ne,subtitle:"",title:"is_jp_3ds",connected:!1},{id:10,name:E,subtitle:"",title:"is_ob",connected:!1},{id:11,name:be,subtitle:"",title:"is_custom_app",connected:!1},{id:12,name:Ve,subtitle:"",title:"balance_inquiry",connected:!1}]);let A=Me({Unattended:n("Unattended"),PaymentStatus:n("Payment status"),paymentFailuresCount:n("Number of failures"),BackgroundVoiceReminder:n("Background voice reminder"),TelegramPush:n("Telegram push")});Ge(()=>{A.Unattended=n("Unattended"),A.PaymentStatus=n("Payment status"),A.paymentFailuresCount=n("Number of failures"),A.BackgroundVoiceReminder=n("Background voice reminder"),A.TelegramPush=n("Telegram push"),A.PaymentJump=n("Get after successful payment")}),De(()=>{f&&(z.value=f.synchronous_control,ye.value=f.payment_status,y.value=f.payment_failures_count,re.value=f.filter_by_card_type,ce.value=f.ob_control,ge.value=f.duplicate_card_submitted,P.value=f.synchronous_control_function||[1,2,3,8],M.value=f.data_backup,V.value=f.ac1,He())});const He=()=>{ee.value.forEach(H=>{P.value.includes(H.id)&&(H.connected=!0)})},Ke=()=>{z.value=1,re.value=0,ge.value=1},Je=()=>{ie.value=!0,ve.value=ee.value.filter(H=>H.connected).map(H=>H.id),ae.post("/api/adminConfig/updateConfig",{synchronous_control:z.value,payment_status:ye.value,payment_failures_count:y.value,filter_by_card_type:re.value,duplicate_card_submitted:ge.value,synchronous_control_function:ve.value,data_backup:M.value,ob_control:ce.value,ac1:V.value}).then(H=>{H.data.code===200?(ue.value=n("Setting successful"),X.value="success",Y.value=!0,b.setSettings(ve.value),b.setOBControl(ce.value)):(ue.value=n("Setting failed"),X.value="error",Y.value=!0)}).catch(H=>{ue.value=n("Request failed"),X.value="error",Y.value=!0}).finally(()=>{ie.value=!1})};return(H,G)=>{const Se=la,Le=ze;return J(),ke(Ae,null,[e(Z,null,{default:a(()=>[e(I,{cols:"12",md:"6"},{default:a(()=>[e(x,{title:H.$t("Synchronous control")},{default:a(()=>[e(w,{class:"pt-2"},{default:a(()=>[g("p",Ua,u(l(n)("Synchronous control desc")),1),e(we,{class:"mt-6"},{default:a(()=>[e(Z,null,{default:a(()=>[e(I,{cols:"12"},{default:a(()=>[e(Se,{modelValue:l(z),"onUpdate:modelValue":G[0]||(G[0]=F=>C(z)?z.value=F:null),items:l(Ee),label:l(A).Unattended},null,8,["modelValue","items","label"])]),_:1}),l(z)==0?(J(),de(I,{key:0,cols:"12"},{default:a(()=>[e(Se,{modelValue:l(ye),"onUpdate:modelValue":G[1]||(G[1]=F=>C(ye)?ye.value=F:null),items:l(me),label:l(A).PaymentStatus},null,8,["modelValue","items","label"])]),_:1})):Te("",!0),l(ye)==0&&l(z)==0?(J(),de(I,{key:1,cols:"12"},{default:a(()=>[e(Le,{modelValue:l(y),"onUpdate:modelValue":G[2]||(G[2]=F=>C(y)?y.value=F:y=F),type:"number",label:l(A).paymentFailuresCount},null,8,["modelValue","label"])]),_:1})):Te("",!0),l(z)==0?(J(),de(I,{key:2,cols:"12"},{default:a(()=>[e(Se,{modelValue:l(V),"onUpdate:modelValue":G[3]||(G[3]=F=>C(V)?V.value=F:null),items:l(Fe),label:l(A).PaymentJump},null,8,["modelValue","items","label"])]),_:1})):Te("",!0)]),_:1})]),_:1})]),_:1})]),_:1},8,["title"]),e(x,{title:H.$t("Synchronous control function"),class:"mt-5"},{default:a(()=>[e(w,null,{default:a(()=>[g("p",Pa,u(l(n)("Synchronous control function desc")),1),e(Ye,{class:"card-list"},{default:a(()=>[(J(!0),ke(Ae,null,qe(l(ee),F=>(J(),de(We,{key:F.name,title:F.name},{append:a(()=>[e(aa,null,{default:a(()=>[e(ta,{modelValue:F.connected,"onUpdate:modelValue":Ze=>F.connected=Ze,density:"compact",class:"me-1"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1032,["title"]))),128))]),_:1})]),_:1})]),_:1},8,["title"]),e(w,null,{default:a(()=>[g("div",Ta,[e(h,{type:"submit",onClick:Je},{default:a(()=>[v(u(H.$t("Save changes")),1)]),_:1}),e(h,{color:"secondary",variant:"tonal",type:"reset",onClick:xe(Ke,["prevent"])},{default:a(()=>[v(u(H.$t("Reset")),1)]),_:1},8,["onClick"])])]),_:1})]),_:1}),e(I,{cols:"12",md:"6"},{default:a(()=>[e(x,{title:H.$t("Access control interface data display")},{default:a(()=>[e(w,{class:"pt-2"},{default:a(()=>[g("p",Aa,u(l(n)("Access control interface data display desc")),1),e(we,{class:""},{default:a(()=>[e(Z,null,{default:a(()=>[e(I,{cols:"12"},{default:a(()=>[e(Se,{modelValue:l(ce),"onUpdate:modelValue":G[4]||(G[4]=F=>C(ce)?ce.value=F:null),items:l(he),label:H.$t("OB control")},null,8,["modelValue","items","label"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["title"]),e(x,{title:H.$t("Backstage functions"),class:"mt-5"},{default:a(()=>[e(w,{class:"pt-2"},{default:a(()=>[g("p",Ra,u(l(n)("Backstage functions desc")),1),e(we,{class:""},{default:a(()=>[e(Z,null,{default:a(()=>[e(I,{cols:"12"},{default:a(()=>[e(Se,{modelValue:l(re),"onUpdate:modelValue":G[5]||(G[5]=F=>C(re)?re.value=F:null),items:l(Be),label:H.$t("Filter by card type")},null,8,["modelValue","items","label"])]),_:1}),e(I,{cols:"12"},{default:a(()=>[e(Se,{modelValue:l(ge),"onUpdate:modelValue":G[6]||(G[6]=F=>C(ge)?ge.value=F:null),items:l(te),label:H.$t("Duplicate card submitted")},null,8,["modelValue","items","label"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["title"]),e(x,{title:H.$t("Data backup"),class:"mt-5"},{default:a(()=>[e(w,{class:"pt-2"},{default:a(()=>[g("p",La,u(l(n)("Data backup desc")),1),e(we,{class:"mt-6"},{default:a(()=>[e(Z,null,{default:a(()=>[e(I,{cols:"12"},{default:a(()=>[e(Se,{modelValue:l(M),"onUpdate:modelValue":G[7]||(G[7]=F=>C(M)?M.value=F:null),items:l(W),label:H.$t("Data backup")},null,8,["modelValue","items","label"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["title"])]),_:1})]),_:1}),e(Ne,{modelValue:l(Y),"onUpdate:modelValue":G[9]||(G[9]=F=>C(Y)?Y.value=F:null),transition:"scale-transition",location:"top",timeout:2500,color:l(X),variant:"tonal"},{actions:a(()=>[e(h,{color:"secondary",onClick:G[8]||(G[8]=F=>Y.value=!1)},{default:a(()=>[v(" ❤️ ")]),_:1})]),default:a(()=>[v(u(l(ue))+" ",1)]),_:1},8,["modelValue","color"]),e(oe,{modelValue:l(ie),"onUpdate:modelValue":G[10]||(G[10]=F=>C(ie)?ie.value=F:null),width:"300"},{default:a(()=>[e(x,{color:"primary",width:"300"},{default:a(()=>[e(w,{class:"pt-3"},{default:a(()=>[v(" Please stand by "),e(Re,{indeterminate:"",class:"mb-0"})]),_:1})]),_:1})]),_:1},8,["modelValue"])],64)}}};const ja={class:"mt-n4 mb-6 text-body-1"},xa={class:"d-flex align-center gap-4 flex-wrap"},Ea={class:"d-flex"},Fa={class:"ml-1"},Ma={class:"d-flex align-center justify-space-between flex-wrap gap-3 pa-5 pt-3"},Ga={class:"text-sm text-medium-emphasis mb-0"},qa={class:"text-error"},za=g("br",null,null,-1),Ha={key:0,class:"v-input__details text-error"},Ka={class:"text-error"},Ja=g("br",null,null,-1),Za=["innerHTML"],Ya=["innerHTML"],Wa=["innerHTML"],Qa={class:"d-flex align-center",style:{"justify-content":"space-between"}},Xa=g("div",null,[v(" 440393|US|Android|#000000"),g("br"),v(" 440393|Android|#000000"),g("br")],-1),et={class:"text-center"},at={class:"mt-1"},tt={class:"mr-2"},lt={class:""},nt=g("span",{class:"ml-2"},"?",-1),ot={class:""},st={__name:"BINSettings",props:{BINSettingsData:Object},setup(se){const{BINSettingsData:f}=se,b=Pe().userInfo;Pe().cloud.coding;const n=b.role_id?b.role_id:999,{t:i,locale:L}=Ie(),R=t(i("Write to database")),T=t(i("Not writing to database")),S=t(i("BIN")),U=t(i("Remark")),O=t(i("Country")),k=t(i("Color")),$=t(i("created_at")),c=t(i("updated_at")),D=t(i("Actions")),N=t(i("No Data Text"));je(L,()=>{R.value=i("Write to database"),T.value=i("Not writing to database"),S.value=i("BIN"),U.value=i("Remark"),O.value=i("Country"),k.value=i("Color"),$.value=i("created_at"),c.value=i("updated_at"),D.value=i("Actions"),N.value=i("No Data Text")});const Q=t(!1),o=t(!1),m=t(""),p=t("warning"),_=t(1),B=t([{title:R,value:1},{title:T,value:0}]);let j=t(""),ne=t(""),E=t(""),be=t(""),Ve=Me({RejectBIN:i("Reject BIN"),RejectionOfDuplicateCardNumbers:i("Rejection of duplicate card numbers")});const Ce=t([]),pe=t(0),$e=t(1),_e=t(""),ie=t("primary"),Y=t(!1),ue=t(!1),X=t(!1),z=t(!1),re=t(!1),ce=t(!1),ge=[{title:S,key:"bin",sortable:!1},{title:U,key:"remark",sortable:!1},{title:O,key:"country",sortable:!1},{title:k,key:"color",sortable:!1},{title:$,key:"created_at",sortable:!1},{title:c,key:"updated_at",sortable:!1},{title:D,key:"actions",sortable:!1,align:"center"}],M=t({page:1,itemsPerPage:10}),V=t({id:null,bin:"",country:"",remark:"",color:""}),he=t(!1),W=t(!1),te=()=>{ie.value="primary",ae.get("/api/cardRemark/remarkIndex",{params:{bin:_e.value,remark:"",country:"",page:M.value.page,pagesize:M.value.itemsPerPage}}).then(s=>{s.data.code===200&&(Ce.value=s.data.data.data,$e.value=s.data.data.last_page,pe.value=s.data.data.total,M.value.page=s.data.data.current_page)}).catch(s=>{m.value=i("Request failed"),p.value="error",o.value=!0}).finally(()=>{ie.value=!1})};De(()=>{f&&(j.value=f.reject_bin,ne.value=f.highlight_bin,E.value=f.highlight_bin2,be.value=f.highlight_bin3,_.value=f.reject_bin_is_written_db),te()}),Ge(()=>{Ve.RejectBIN=i("Reject BIN"),Ve.RejectionOfDuplicateCardNumbers=i("Rejection of duplicate card numbers")}),je(()=>M.value.itemsPerPage,(s,r)=>{M.value.page=1,te()});const Ee=()=>{V.value.id=null,V.value.bin="",V.value.country="",V.value.remark="",V.value.color="",he.value=!1,X.value=!0},Fe=()=>{ce.value=!0,ae.get("/api/cardRemark/remarkIndex",{params:{bin:"",remark:"",country:"",page:1,pagesize:999999999}}).then(s=>{if(s.data.code===200){const K=s.data.data.data.map(d=>`${d.bin}|${d.country}|${d.remark}|${d.color}`).join(`
`),le=new Blob([K],{type:"text/plain"}),fe=URL.createObjectURL(le),q=document.createElement("a");q.href=fe,q.download="bin-config.txt",document.body.appendChild(q),q.click(),document.body.removeChild(q),URL.revokeObjectURL(fe)}}).catch(s=>{m.value=i("Request failed"),p.value="error",o.value=!0}).finally(()=>{ce.value=!1})};va();const Be=t("");function ye(s){Be.value=s;const K=me.value.split(/\r?\n/).map(le=>le.trim()).filter(le=>le!=="").map(le=>{const q=le.replace(/\|+$/,"").split("|");switch(q.length){case 4:return q[3]=s,q.join("|");case 3:return/^#/.test(q[2])?q[2]=s:q.push(s),q.join("|");case 2:return q.push(s),q.join("|");default:return null}}).filter(Boolean);me.value=K.join(`
`)}const me=t(""),y=t(!1),P=t(!1),ve=()=>{me.value="",y.value=!0},ee=()=>{if(!me.value)return m.value=i("Fill information"),p.value="warning",o.value=!0,!1;const r=me.value.split(/\r?\n/).map(K=>K.trim()).filter(K=>K!=="").map(K=>{const le=K.split("|");if(le.length===4)return K;if(le.length===3){const[fe,q,d]=le;return/^#/.test(d)?[fe,"",q,d].join("|"):K}if(le.length===2){const[fe,q]=le;return[fe,"",q].join("|")}return null}).filter(K=>K);if(!r.length)return m.value=i("No valid BIN entries"),p.value="warning",o.value=!0,!1;P.value=!0,ae.post("/api/cardRemark/batchBin",{binData:r}).then(K=>{K.data.code===200?(m.value=i("Operation successful"),p.value="success",o.value=!0,te()):(m.value=i("Operation failed"),p.value="error",o.value=!0)}).catch(K=>{m.value=i("Request failed"),p.value="error",o.value=!0}).finally(()=>{P.value=!1,y.value=!1})},A=t(""),He=s=>{A.value=s.id,V.value.bin=s.bin,V.value.country=s.country,V.value.remark=s.remark,V.value.color=s.color,he.value=!0,X.value=!0},Ke=s=>{A.value=s.id,V.value.bin=s.bin,V.value.country=s.country,V.value.remark=s.remark,V.value.color=s.color,Y.value=!0},Je=()=>{ue.value=!0,ae.get("/api/cardRemark/delRemark",{params:{id:A.value}}).then(s=>{s.data.code===200?(m.value=i("Operation successful"),p.value="success",o.value=!0,te()):(m.value=i("Operation failed"),p.value="error",o.value=!0)}).catch(s=>{m.value=i("Request failed"),p.value="error",o.value=!0}).finally(()=>{Y.value=!1,ue.value=!1})},H=()=>{he.value?G():Se()},G=()=>{if(V.value.bin.length<6||!V.value.remark)return m.value=i("Fill information"),p.value="warning",o.value=!0,!1;W.value=!0,ae.post("/api/cardRemark/editRemark",{id:A.value,bin:V.value.bin,remark:V.value.remark,country:V.value.country.toUpperCase(),color:V.value.color}).then(s=>{s.data.code===200?(m.value=i("Operation successful"),p.value="success",o.value=!0,te()):(m.value=s.data.msg,p.value="error",o.value=!0)}).catch(s=>{m.value=i("Request failed"),p.value="error",o.value=!0}).finally(()=>{W.value=!1,X.value=!1})},Se=()=>{if(V.value.bin.length<6||!V.value.remark)return m.value=i("Fill information"),p.value="warning",o.value=!0,!1;W.value=!0,ae.post("/api/cardRemark/addRemark",{bin:V.value.bin,remark:V.value.remark,country:V.value.country.toUpperCase(),color:V.value.color}).then(s=>{s.data.code===200?(m.value=i("Operation successful"),p.value="success",o.value=!0,te()):(m.value=s.data.msg,p.value="error",o.value=!0)}).catch(s=>{m.value=i("Request failed"),p.value="error",o.value=!0}).finally(()=>{W.value=!1,X.value=!1})},Le=t(!1),F=s=>{let r=s.target.value.replace(/\D/g,"");V.value.bin=r,Le.value=r.length<6},Ze=()=>{j.value="",ne.value="",E.value="",be.value="",_.value=1},ua=()=>{Q.value=!0,ae.post("/api/adminConfig/updateConfig",{reject_bin:j.value.replace(/\s/g,""),highlight_bin:ne.value,highlight_bin2:E.value,highlight_bin3:be.value,reject_bin_is_written_db:_.value}).then(s=>{s.data.code===200?(m.value=i("Operation successful"),p.value="success",o.value=!0):(m.value=s.data.msg,p.value="error",o.value=!0)}).catch(s=>{m.value=i("Request failed"),p.value="error",o.value=!0}).finally(()=>{Q.value=!1})},ra=()=>{re.value=!0,n==1&&ae.get("/api/cardRemark/clearRemark").then(s=>{s.data.code===200?(m.value=i("Operation successful"),p.value="success",o.value=!0,te()):(m.value=i("Operation failed"),p.value="error",o.value=!0)}).catch(s=>{m.value=i("Request failed"),p.value="error",o.value=!0}).finally(()=>{re.value=!1,z.value=!1})},da=ca(()=>{te()},500);function ca(s,r){let K=null;return function(...le){clearTimeout(K),K=setTimeout(()=>{s(...le)},r)}}return(s,r)=>{const K=oa,le=la,fe=ze,q=na;return J(),de(Z,null,{default:a(()=>[e(I,{cols:"12",md:"5",lg:"4"},{default:a(()=>[e(x,{title:s.$t("Reject BIN")},{default:a(()=>[e(w,{class:"pt-2"},{default:a(()=>[g("p",ja,u(s.$t("BIN configuration desc",{pipe:"|"})),1),e(Z,null,{default:a(()=>[e(I,{cols:"12"},{default:a(()=>[e(K,{modelValue:l(j),"onUpdate:modelValue":r[0]||(r[0]=d=>C(j)?j.value=d:j=d),placeholder:s.$t("For example")+": 440393|413331|546714",color:"error",rows:"12"},null,8,["modelValue","placeholder"])]),_:1}),Te("",!0),e(I,{cols:"12",class:"d-flex flex-wrap gap-4"},{default:a(()=>[e(h,{onClick:ua},{default:a(()=>[v(u(s.$t("Save changes")),1)]),_:1}),e(h,{color:"secondary",variant:"tonal",type:"reset",onClick:xe(Ze,["prevent"])},{default:a(()=>[v(u(s.$t("Reset")),1)]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1})]),_:1},8,["title"])]),_:1}),e(I,{cols:"12",md:"7",lg:"8"},{default:a(()=>[e(x,{title:s.$t("BIN remarks")},{default:a(()=>[e(w,{class:"d-flex align-center justify-space-between flex-wrap gap-4"},{default:a(()=>[e(le,{"model-value":l(M).itemsPerPage,items:[{value:10,title:"10"}],style:{width:"5rem"},"onUpdate:modelValue":r[2]||(r[2]=d=>l(M).itemsPerPage=parseInt(d,10))},null,8,["model-value"]),g("div",xa,[e(fe,{modelValue:l(_e),"onUpdate:modelValue":r[3]||(r[3]=d=>C(_e)?_e.value=d:null),placeholder:"Search BIN",density:"compact",style:{width:"12.5rem"},onInput:l(da)},null,8,["modelValue","onInput"]),e(h,{density:"default",onClick:Ee,"prepend-icon":"tabler-plus"},{default:a(()=>[v(u(s.$t("Add BIN")),1)]),_:1}),e(h,{density:"default",onClick:ve,"prepend-icon":"tabler-paperclip"},{default:a(()=>[v(u(s.$t("Batch Import")),1)]),_:1}),e(h,{density:"default",onClick:Fe,"prepend-icon":"tabler-file-settings",loading:l(ce)},{default:a(()=>[v(u(s.$t("Export")),1)]),_:1},8,["loading"]),l(n)==1?(J(),de(h,{key:0,variant:"tonal",color:"error","prepend-icon":"tabler-trash",onClick:r[4]||(r[4]=d=>z.value=!l(z))},{default:a(()=>[v(u(s.$t("One-click clearing")),1)]),_:1})):Te("",!0)])]),_:1}),e(Qe),e(l($a),{"items-per-page":l(M).itemsPerPage,"onUpdate:itemsPerPage":r[7]||(r[7]=d=>l(M).itemsPerPage=d),page:l(M).page,"onUpdate:page":r[8]||(r[8]=d=>l(M).page=d),"items-length":l(pe),headers:ge,items:l(Ce),loading:l(ie),class:"text-medium-emphasis text-no-wrap","no-data-text":l(N),"onUpdate:options":r[9]||(r[9]=d=>M.value=d)},{"item.bin":a(({item:d})=>[g("div",null,u(d.raw.bin),1)]),"item.color":a(({item:d})=>[g("div",Ea,[g("div",{style:fa([{background:d.raw.color},{width:"20px",height:"20px"}])},null,4),g("div",Fa,u(d.raw.color),1)])]),"item.actions":a(({item:d})=>[e(h,{icon:"",size:"small",color:"medium-emphasis",variant:"text",onClick:ma=>He(d.raw)},{default:a(()=>[e(Oe,{size:"22",icon:"tabler-edit"})]),_:2},1032,["onClick"]),e(h,{icon:"",size:"small",variant:"text",color:"medium-emphasis",onClick:ma=>Ke(d.raw)},{default:a(()=>[e(Oe,{size:"22",icon:"tabler-trash"})]),_:2},1032,["onClick"])]),bottom:a(()=>[e(Qe),g("div",Ma,[g("p",Ga,u(l(ha)(l(M),l(pe))),1),e(Sa,{modelValue:l(M).page,"onUpdate:modelValue":r[5]||(r[5]=d=>l(M).page=d),length:Math.ceil(l(pe)/l(M).itemsPerPage),"total-visible":"5",onClick:r[6]||(r[6]=d=>te())},{prev:a(d=>[e(h,Xe({variant:"tonal",color:"default"},d,{icon:!1}),{default:a(()=>[v(u(s.$t("$vuetify.pagination.ariaLabel.previous")),1)]),_:2},1040)]),next:a(d=>[e(h,Xe({variant:"tonal",color:"default"},d,{icon:!1}),{default:a(()=>[v(u(s.$t("$vuetify.pagination.ariaLabel.next")),1)]),_:2},1040)]),_:1},8,["modelValue","length"])])]),_:1},8,["items-per-page","page","items-length","items","loading","no-data-text"])]),_:1},8,["title"])]),_:1}),e(Ne,{modelValue:l(o),"onUpdate:modelValue":r[11]||(r[11]=d=>C(o)?o.value=d:null),transition:"scale-transition",location:"top",timeout:2500,color:l(p)},{actions:a(()=>[e(h,{color:"secondary",onClick:r[10]||(r[10]=d=>o.value=!1)},{default:a(()=>[v(" ❤️ ")]),_:1})]),default:a(()=>[v(u(l(m))+" ",1)]),_:1},8,["modelValue","color"]),e(oe,{modelValue:l(X),"onUpdate:modelValue":r[19]||(r[19]=d=>C(X)?X.value=d:null),"max-width":"600",persistent:""},{default:a(()=>[e(q,{onClick:r[12]||(r[12]=d=>X.value=!l(X))}),e(x,{title:l(he)?s.$t("Edit BIN remarks"):s.$t("Add BIN remarks")},{default:a(()=>[e(w,null,{default:a(()=>[g("span",qa,[v(u(s.$t("BIN Adding Rules1")),1),za])]),_:1}),e(w,null,{default:a(()=>[e(Z,null,{default:a(()=>[e(I,{cols:"12"},{default:a(()=>[e(fe,{modelValue:l(V).bin,"onUpdate:modelValue":r[13]||(r[13]=d=>l(V).bin=d),type:"number",placeholder:s.$t("BIN Length Desc"),label:s.$t("BIN"),onInput:F},null,8,["modelValue","placeholder","label"]),l(Le)?(J(),ke("div",Ha,u(s.$t("BIN Length Desc")),1)):Te("",!0)]),_:1}),e(I,{cols:"12"},{default:a(()=>[e(K,{rows:"2",modelValue:l(V).remark,"onUpdate:modelValue":r[14]||(r[14]=d=>l(V).remark=d),label:s.$t("Remark")},null,8,["modelValue","label"])]),_:1}),e(I,{cols:"12"},{default:a(()=>[e(fe,{modelValue:l(V).country,"onUpdate:modelValue":r[15]||(r[15]=d=>l(V).country=d),label:s.$t("Country"),placeholder:s.$t("No need to fill in")},null,8,["modelValue","label","placeholder"])]),_:1}),e(I,{cols:"12"},{default:a(()=>[e(fe,{modelValue:l(V).color,"onUpdate:modelValue":r[17]||(r[17]=d=>l(V).color=d),label:s.$t("Color")},{default:a(()=>[e(l(sa),{pureColor:l(V).color,"onUpdate:pureColor":r[16]||(r[16]=d=>l(V).color=d),format:"hex",shape:"square","round-history":""},null,8,["pureColor"])]),_:1},8,["modelValue","label"])]),_:1})]),_:1})]),_:1}),e(w,{class:"d-flex justify-end flex-wrap gap-3"},{default:a(()=>[e(h,{variant:"tonal",color:"secondary",onClick:r[18]||(r[18]=d=>X.value=!1)},{default:a(()=>[v(u(s.$t("Cancel")),1)]),_:1}),e(h,{loading:l(W),disabled:l(W)||l(Le),onClick:H},{default:a(()=>[v(u(s.$t("Confirm")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"]),e(oe,{modelValue:l(y),"onUpdate:modelValue":r[24]||(r[24]=d=>C(y)?y.value=d:null),"max-width":"650",persistent:""},{default:a(()=>[e(q,{onClick:r[20]||(r[20]=d=>y.value=!l(y))}),e(x,{title:s.$t("Batch BIN remarks")},{default:a(()=>[e(w,null,{default:a(()=>[g("span",Ka,[v(u(s.$t("BIN Adding Rules1")),1),Ja])]),_:1}),e(w,null,{default:a(()=>[e(Z,null,{default:a(()=>[e(I,{cols:"12"},{default:a(()=>[g("div",{innerHTML:s.$t("Batch BIN remarks desc1")},null,8,Za),g("div",{innerHTML:s.$t("Batch BIN remarks desc2")},null,8,Ya),g("div",{style:{"padding-top":"3px"},innerHTML:s.$t("Batch BIN remarks desc3")},null,8,Wa),g("div",Qa,[Xa,g("div",et,[e(l(sa),{pureColor:l(Be),"onUpdate:pureColor":[r[21]||(r[21]=d=>C(Be)?Be.value=d:null),ye],format:"hex",shape:"circle","round-history":""},null,8,["pureColor"]),g("div",at,u(s.$t("Color wheel")),1)])])]),_:1}),e(I,{cols:"12"},{default:a(()=>[e(K,{rows:"10",modelValue:l(me),"onUpdate:modelValue":r[22]||(r[22]=d=>C(me)?me.value=d:null)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(w,{class:"d-flex justify-end flex-wrap gap-3"},{default:a(()=>[e(h,{variant:"tonal",color:"secondary",onClick:r[23]||(r[23]=d=>y.value=!1)},{default:a(()=>[v(u(s.$t("Cancel")),1)]),_:1}),e(h,{loading:l(P),disabled:l(P)||l(Le),onClick:ee},{default:a(()=>[v(u(s.$t("Confirm")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"]),e(oe,{modelValue:l(Y),"onUpdate:modelValue":r[27]||(r[27]=d=>C(Y)?Y.value=d:null),persistent:"",class:"v-dialog-sm"},{default:a(()=>[e(q,{onClick:r[25]||(r[25]=d=>Y.value=!l(Y))}),e(x,{title:s.$t("Operation tips")},{default:a(()=>[e(w,null,{default:a(()=>[g("span",tt,u(s.$t("Delete BIN Msg"))+": ",1),g("span",lt,u(l(V).bin),1),nt]),_:1}),e(w,{class:"d-flex justify-end gap-3 flex-wrap"},{default:a(()=>[e(h,{color:"secondary",variant:"tonal",onClick:r[26]||(r[26]=d=>Y.value=!1)},{default:a(()=>[v(u(s.$t("Cancel")),1)]),_:1}),e(h,{loading:l(ue),disabled:l(ue),onClick:Je},{default:a(()=>[v(u(s.$t("Confirm")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"]),e(oe,{modelValue:l(z),"onUpdate:modelValue":r[30]||(r[30]=d=>C(z)?z.value=d:null),persistent:"",class:"v-dialog-sm"},{default:a(()=>[e(q,{onClick:r[28]||(r[28]=d=>z.value=!l(z))}),e(x,{title:s.$t("Operation tips")},{default:a(()=>[e(w,null,{default:a(()=>[g("span",ot,u(s.$t("One-click clearing remark")),1)]),_:1}),e(w,{class:"d-flex justify-end gap-3 flex-wrap"},{default:a(()=>[e(h,{color:"secondary",variant:"tonal",onClick:r[29]||(r[29]=d=>z.value=!1)},{default:a(()=>[v(u(s.$t("Cancel")),1)]),_:1}),e(h,{loading:l(re),disabled:l(re),onClick:ra},{default:a(()=>[v(u(s.$t("Confirm")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"]),e(oe,{modelValue:l(Q),"onUpdate:modelValue":r[31]||(r[31]=d=>C(Q)?Q.value=d:null),width:"300"},{default:a(()=>[e(x,{color:"primary",width:"300"},{default:a(()=>[e(w,{class:"pt-3"},{default:a(()=>[v(" Please stand by "),e(Re,{indeterminate:"",class:"mb-0"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})}}},it=""+new URL("ipregistrytip-cf3a1c93.webp",import.meta.url).href,ut={class:"mt-n4 mb-6 text-body-1"},rt=g("a",{target:"_blank",href:"https://dashboard.ipregistry.co/signup"},"👉 https://dashboard.ipregistry.co/signup",-1),dt={class:"mt-n4 mb-6 text-body-1"},ct=g("br",null,null,-1),mt=g("br",null,null,-1),vt={class:"text-error"},ft={class:"mt-0 ml-3 cursor-pointer text-body-1 text-primary"},pt={class:"d-flex flex-wrap gap-4 mt-4"},_t=["innerHTML"],gt={__name:"ConnectionsSettings",props:{ConnectionsSettingsData:Object},setup(se){const{ConnectionsSettingsData:f}=se,{t:b,locale:n}=Ie(),i=t(b("Checking")),L=t(b("Not Checking")),R=t(b("Allowed")),T=t(b("Forbidden"));je(n,()=>{i.value=b("Checking"),L.value=b("Not Checking"),R.value=b("Allowed"),T.value=b("Forbidden")});const S=t(!1),U=t(!1),O=t(!1),k=t(!1),$=t(""),c=t("warning"),D=t(!1);t(["US","HK","CN","CA","CH","CL","CO","CR","CS","CZ","DE","DK","ES","FI","FR","GB","HU","IE","IL","IN","IQ","IR","IT","JP","KR","LT","PL","PT","RO","AD","AE","AF","AG","AI","AL","AM","AO","AR","AT","AU","AZ","BB","BD","BE","BF","BG","BH","BI","BJ","BL","BM","BN","BO","BR","BS","BW","BY","BZ","CF","CG","CK","CM","CU","DJ","DO","DZ","EC","EE","EG","ET","FJ","GA","GD","GE","GF","GH","GI","GM","GN","GR","GT","GU","GY","HN","HT","ID","IS","JM","JO","KE","KG","KH","KP","KT","KW","KZ","LA","LB","LC","LI","LK","LR","LS","LU","LV","LY","MA","MC","MD","MG","ML","MM","MN","MO","MS","MT","MU","MV","MW","MX","MY","MZ","NA","NE","NG","NI","NL","NO","NP","NR","NZ","OM","PA","PE","PF","PG","PH","PK","NP","PR","PY","PY","QA","RU","SA","SB","SC","SD","SE","SG","SI","SK","SL","SM","SN","SO","SR","ST","SV","SY","SZ","TD","TG","TH","TJ","TM","TN","TO","TR","TT","TW","TZ","UA","UG","UY","UZ","VC","VE","VN","YE","YU","ZA","ZM","ZR","ZW"]);const N=t(""),Q=t(""),o=t(window.location.origin+window.location.pathname),m=t(""),p=t("CN"),_=t(""),B=t(0),j=t(0),ne=t(0),E=t(0),be=t(!1),Ve=t(!1),Ce=t(!1),pe=t(!1),$e=t(!1),_e=t(!1),ie=t(!1),Y=t(!1),ue=t(!1),X=t(!1),z=t(!1);t([{title:R,value:1},{title:T,value:0}]),t([{title:i,value:1},{title:L,value:0}]),De(()=>{f&&(m.value=f.api_keys,p.value=f.block_country_access,_.value=f.blacklist_address_jump,B.value=f.pc_access,j.value=f.ip_purity_detection,be.value=!!f.is_abuser,Ve.value=!!f.is_attacker,Ce.value=!!f.is_bogon,pe.value=!!f.is_cloud_provider,$e.value=!!f.is_proxy,_e.value=!!f.is_relay,ie.value=!!f.is_tor,Y.value=!!f.is_tor_exit,ue.value=!!f.is_vpn,X.value=!!f.is_anonymous,z.value=!!f.is_threat,ne.value=f.connection_type_detection,E.value=f.connection_type_detection,N.value=f.domain,Q.value=f.domain,N.value?o.value="https://"+N.value+window.location.pathname:o.value=window.location.origin+window.location.pathname,m.value&&ge())});const re=()=>{p.value=["CN"],_.value="",m.value="",B.value=0,j.value=0,ne.value=0,N.value="",be.value=!0,Ve.value=!0,Ce.value=!0,pe.value=!0,$e.value=!0,_e.value=!0,ie.value=!0,Y.value=!0,ue.value=!0,X.value=!0,z.value=!0},ce=()=>{O.value=!0,ae.post("/api/adminConfig/updateConfig",{api_keys:m.value,block_country_access:p.value,blacklist_address_jump:_.value,pc_access:B.value,ip_purity_detection:j.value,connection_type_detection:ne.value,domain:N.value,is_abuser:be.value?1:0,is_attacker:Ve.value?1:0,is_bogon:Ce.value?1:0,is_cloud_provider:pe.value?1:0,is_proxy:$e.value?1:0,is_relay:_e.value?1:0,is_tor:ie.value?1:0,is_tor_exit:Y.value?1:0,is_vpn:ue.value?1:0,is_anonymous:X.value?1:0,is_threat:z.value?1:0}).then(y=>{y.data.code===200?($.value=b("Setting successful"),c.value="success",k.value=!0,m.value?setTimeout(()=>{ge()},1e3):E.value=0):($.value=b("Setting failed"),c.value="error",k.value=!0)}).catch(y=>{$.value=b("Request failed"),c.value="error",k.value=!0}).finally(()=>{O.value=!1})},ge=()=>{fetch("https://api.ipregistry.co/?key="+m.value).then(y=>(y.ok?E.value=y.headers.get("ipregistry-credits-remaining"):E.value=0,y.json())).then(y=>{y.code=="INSUFFICIENT_CREDITS"?($.value=b("INSUFFICIENT_CREDITS"),c.value="error",k.value=!0):y.code=="INVALID_API_KEY"&&($.value=b("INVALID_API_KEY"),c.value="error",k.value=!0)})},M=()=>{N.value?o.value="https://"+N.value+window.location.pathname:o.value=window.location.origin+window.location.pathname},V=()=>{if(!N.value){$.value=b("Domain name is empty"),c.value="error",k.value=!0;return}S.value=!0},he=t(null);let W;const te=t(!1),Ee=Pe(),Fe=async()=>{S.value=!1,te.value=!0,await ba(),W=new Ba.Terminal({cols:80,rows:24,cursorBlink:!0}),W.open(he.value),W.write(`准备开始申请SSL证书，请稍等...\r
`);const y=Ee.accessToken,P=new EventSource(`/api/sse/applyBackstageSsl?token=${encodeURIComponent(y)}`);P.onmessage=ve=>{try{const ee=JSON.parse(ve.data);ee.status=="info"&&W.write(ee.msg+`\r
`),ee.status=="error"&&W.write("\x1B[91m"+ee.msg+`\x1B[91m\r
`),ee.status=="warning"&&W.write("\x1B[33m"+ee.msg+`\x1B[33m\r
`),ee.status&&ee.status==="success"&&(W.write("\x1B[32mSSL "+ee.msg+`\x1B[32m\r
`),P.close())}catch{W.write(`解析返回数据错误\r
`)}},P.onerror=ve=>{W.write(`\r
\x1B[0m连接中断\x1B[0m\r
`),P.close()}},Be=()=>{te.value=!te.value,W.dispose(),W=null},{toClipboard:ye}=wa(),me=async y=>{try{await ye(y),$.value=b("Copy successful")+"："+y,c.value="success",k.value=!0}catch{$.value=b("Copy failed"),c.value="error",k.value=!0}};return(y,P)=>{const ve=ze,ee=na;return J(),ke(Ae,null,[e(Z,null,{default:a(()=>[e(I,{cols:"12",md:"6"},{default:a(()=>[e(x,{title:y.$t("Connected ipregistry")},{default:a(()=>[e(w,null,{default:a(()=>[g("p",ut,[v(u(y.$t("Ipregistry goTo"))+" ",1),rt,v(" "+u(y.$t("Ipregistry apply"))+" ",1),e(pa,{modelValue:l(D),"onUpdate:modelValue":P[0]||(P[0]=A=>C(D)?D.value=A:null),location:"top"},{activator:a(({props:A})=>[g("span",Xe({class:"cursor-pointer text-primary"},A),[v(" (👉"+u(y.$t("View steps"))+" ",1),e(Oe,{size:"18",icon:"tabler-help-circle",class:""}),v(") ")],16)]),default:a(()=>[e(x,{"max-width":"500"},{default:a(()=>[e(_a,{src:l(it),height:"248",width:"460"},null,8,["src"])]),_:1})]),_:1},8,["modelValue"])]),e(we,{class:"mt-6"},{default:a(()=>[e(Z,null,{default:a(()=>[e(I,{md:"12",cols:"12"},{default:a(()=>[e(ve,{modelValue:l(m),"onUpdate:modelValue":P[1]||(P[1]=A=>C(m)?m.value=A:null),label:"API Keys （"+y.$t("IpregistryCreditsRemaining")+": "+l(E)+"）"},null,8,["modelValue","label"])]),_:1}),e(I,{md:"12",cols:"12"},{default:a(()=>[e(ve,{modelValue:l(_),"onUpdate:modelValue":P[2]||(P[2]=A=>C(_)?_.value=A:null),label:y.$t("Blacklist address jump"),placeholder:y.$t("Blacklist address jump desc")},null,8,["modelValue","label","placeholder"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["title"])]),_:1}),e(I,{cols:"12",md:"6"},{default:a(()=>[e(x,{title:y.$t("Configure backend domain name")},{default:a(()=>[e(w,null,{default:a(()=>[g("p",dt,[v(u(y.$t("Configure backend domain name desc")),1),ct,v(" "+u(y.$t("Configure backend domain name desc2")),1),mt,g("span",vt,u(y.$t("Configure backend domain name desc3")),1)]),e(we,{class:"mt-6"},{default:a(()=>[e(Z,null,{default:a(()=>[e(I,{md:"11",cols:"11"},{default:a(()=>[e(ve,{modelValue:l(N),"onUpdate:modelValue":P[3]||(P[3]=A=>C(N)?N.value=A:null),label:y.$t("Add backend domain name"),onInput:M},null,8,["modelValue","label"])]),_:1}),e(I,{md:"1",cols:"1",class:"d-flex align-center justify-center mt-6 cursor-pointer"},{default:a(()=>[e(ea,{color:"primary",variant:"tonal",size:"30",onClick:V},{default:a(()=>[e(Oe,{size:"1.5rem",icon:"tabler-certificate"})]),_:1}),e(ga,{activator:"parent",location:"top"},{default:a(()=>[v(u(y.$t("Apply for an SSL certificate")),1)]),_:1})]),_:1}),g("p",ft,[v(u(y.$t("Current background address"))+"： "+u(l(o))+" ",1),e(h,{icon:"",color:"disabled",variant:"text",size:"x-small",onClick:P[4]||(P[4]=A=>me(l(o)))},{default:a(()=>[e(Oe,{size:"20",icon:"tabler-copy"})]),_:1})])]),_:1})]),_:1})]),_:1})]),_:1},8,["title"])]),_:1})]),_:1}),e(w,null,{default:a(()=>[g("div",pt,[e(h,{type:"submit",onClick:ce},{default:a(()=>[v(u(y.$t("Save changes")),1)]),_:1}),e(h,{color:"secondary",variant:"tonal",type:"reset",onClick:xe(re,["prevent"])},{default:a(()=>[v(u(y.$t("Reset")),1)]),_:1},8,["onClick"])])]),_:1}),e(Ne,{modelValue:l(k),"onUpdate:modelValue":P[6]||(P[6]=A=>C(k)?k.value=A:null),transition:"scale-transition",location:"top",timeout:3e3,color:l(c),variant:"tonal"},{actions:a(()=>[e(h,{color:"secondary",onClick:P[5]||(P[5]=A=>k.value=!1)},{default:a(()=>[v(" ❤️ ")]),_:1})]),default:a(()=>[g("div",{innerHTML:l($),style:{"white-space":"pre-line"}},null,8,_t)]),_:1},8,["modelValue","color"]),e(oe,{modelValue:l(S),"onUpdate:modelValue":P[9]||(P[9]=A=>C(S)?S.value=A:null),"max-width":"600"},{default:a(()=>[e(ee,{onClick:P[7]||(P[7]=A=>S.value=!l(S))}),e(x,{title:y.$t("Apply for an SSL certificate")},{default:a(()=>[e(w,null,{default:a(()=>[e(Z,null,{default:a(()=>[e(I,{cols:"12"},{default:a(()=>[v(u(y.$t("Configure backend domain name desc2")),1)]),_:1})]),_:1})]),_:1}),e(w,{class:"d-flex justify-end flex-wrap gap-3"},{default:a(()=>[e(h,{variant:"tonal",color:"secondary",onClick:P[8]||(P[8]=A=>S.value=!1)},{default:a(()=>[v(u(y.$t("Cancel")),1)]),_:1}),e(h,{loading:l(U),disabled:l(U),onClick:Fe},{default:a(()=>[v(u(y.$t("Confirm")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"]),e(oe,{modelValue:l(O),"onUpdate:modelValue":P[10]||(P[10]=A=>C(O)?O.value=A:null),width:"300"},{default:a(()=>[e(x,{color:"primary",width:"300"},{default:a(()=>[e(w,{class:"pt-3"},{default:a(()=>[v(" Please stand by "),e(Re,{indeterminate:"",class:"mb-0"})]),_:1})]),_:1})]),_:1},8,["modelValue"]),e(oe,{modelValue:l(te),"onUpdate:modelValue":P[11]||(P[11]=A=>C(te)?te.value=A:null),persistent:"",width:"800"},{default:a(()=>[e(ee,{onClick:Be}),e(x,{title:"",style:{"background-color":"#000"}},{default:a(()=>[e(w,null,{default:a(()=>[g("div",{ref_key:"terminalContainer",ref:he,class:"terminal-container"},null,512)]),_:1})]),_:1})]),_:1},8,["modelValue"])],64)}}},bt={class:"mt-n4 mb-6 text-body-1"},yt={class:"mr-2"},kt={__name:"ImportExport",props:{BINSettingsData:Object},setup(se){const{t:f,locale:b}=Ie(),n=t(!1),i=t(!1),L=t(""),R=t("warning"),T=t(!1),S=t(!1),U=t(null),O=t(null),k=t(null),$=async()=>{await ae.get("/api/cardRemark/remarkIndex",{params:{bin:"",remark:"",country:"",page:1,pagesize:999999999}}).then(_=>{_.data.code===200&&(O.value=_.data.data.data)}).catch(_=>{L.value=f("Request failed"),R.value="error",i.value=!0})},c=async()=>{await ae.get("/api/adminConfig/infoIndex").then(_=>{_.data.code===200&&(k.value=_.data.data,delete k.value.created_at,delete k.value.updated_at,delete k.value.domain,delete k.value.tg_userid,delete k.value.tg_robot_token,delete k.value.id)}).catch(_=>{L.value=f("Request failed"),R.value="error",i.value=!0})};De(async()=>{n.value=!0,await $(),await c();const _=O.value.map(E=>`${E.bin}|${E.country}|${E.remark}|${E.color}`),B={settingData:k.value,binData:_},j=JSON.stringify(B,null,2),ne=btoa(encodeURIComponent(j));U.value=ne,n.value=!1});const D=()=>{T.value=!0},N=async()=>{S.value=!0;let _=atob(U.value),B=decodeURIComponent(_);await o(JSON.parse(B).settingData),await m(JSON.parse(B).binData),S.value=!1,T.value=!1,Q()},Q=ya("updateConfigSetting"),o=async _=>{await ae.post("/api/adminConfig/updateConfig",_).then(B=>{B.data.code===200||(L.value=f("Operation failed"),R.value="error",i.value=!0)}).catch(B=>{L.value=f("Request failed"),R.value="error",i.value=!0})},m=async _=>{await ae.post("/api/cardRemark/batchBin",{binData:_}).then(B=>{B.data.code===200?(L.value=f("Operation successful Login"),R.value="success",i.value=!0):(L.value=f("Operation failed"),R.value="error",i.value=!0)}).catch(B=>{L.value=f("Request failed"),R.value="error",i.value=!0})};function p(){const _=new Blob([U.value],{type:"text/plain"}),B=URL.createObjectURL(_),j=document.createElement("a");j.href=B,j.download="LighthouseConfig.txt",document.body.appendChild(j),j.click(),document.body.removeChild(j),URL.revokeObjectURL(B)}return(_,B)=>{const j=oa,ne=na;return J(),de(Z,null,{default:a(()=>[e(I,{cols:"12"},{default:a(()=>[e(x,{title:_.$t("Import And Export")},{default:a(()=>[e(w,{class:"pt-2"},{default:a(()=>[g("p",bt,u(_.$t("Import And Export Desc")),1),e(Z,null,{default:a(()=>[e(I,{cols:"12"},{default:a(()=>[e(j,{modelValue:l(U),"onUpdate:modelValue":B[0]||(B[0]=E=>C(U)?U.value=E:null),placeholder:_.$t("Import And Export Input")},null,8,["modelValue","placeholder"])]),_:1}),e(I,{cols:"12",class:"d-flex flex-wrap gap-4"},{default:a(()=>[e(h,{onClick:D},{default:a(()=>[v(u(_.$t("Save changes")),1)]),_:1}),e(h,{color:"warning",variant:"tonal",onClick:p},{default:a(()=>[v(u(_.$t("Export")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["title"])]),_:1}),e(Ne,{modelValue:l(i),"onUpdate:modelValue":B[2]||(B[2]=E=>C(i)?i.value=E:null),transition:"scale-transition",location:"top",timeout:2500,color:l(R),variant:"tonal"},{actions:a(()=>[e(h,{color:"secondary",onClick:B[1]||(B[1]=E=>i.value=!1)},{default:a(()=>[v(" ❤️ ")]),_:1})]),default:a(()=>[v(u(l(L))+" ",1)]),_:1},8,["modelValue","color"]),e(oe,{modelValue:l(T),"onUpdate:modelValue":B[5]||(B[5]=E=>C(T)?T.value=E:null),persistent:"",class:"v-dialog-sm"},{default:a(()=>[e(ne,{onClick:B[3]||(B[3]=E=>T.value=!l(T))}),e(x,{title:_.$t("Operation tips")},{default:a(()=>[e(w,null,{default:a(()=>[g("span",yt,u(_.$t("Update local configuration desc")),1)]),_:1}),e(w,{class:"d-flex justify-end gap-3 flex-wrap"},{default:a(()=>[e(h,{color:"secondary",variant:"tonal",onClick:B[4]||(B[4]=E=>T.value=!1)},{default:a(()=>[v(u(_.$t("Cancel")),1)]),_:1}),e(h,{loading:l(S),disabled:l(S),onClick:N},{default:a(()=>[v(u(_.$t("Confirm")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"]),e(oe,{modelValue:l(n),"onUpdate:modelValue":B[6]||(B[6]=E=>C(n)?n.value=E:null),width:"300"},{default:a(()=>[e(x,{color:"primary",width:"300"},{default:a(()=>[e(w,{class:"pt-3"},{default:a(()=>[v(" Please stand by "),e(Re,{indeterminate:"",class:"mb-0"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})}}},Vt={class:"mt-n4 mb-6 text-body-1"},Ct={class:"mt-n4 mb-6 text-body-1"},ht={class:"d-flex flex-wrap gap-4 mt-4"},St={__name:"Notification",props:{NotificationData:Object},setup(se){const{NotificationData:f}=se,{t:b,locale:n}=Ie(),i=t(b("User enters homepage")),L=t(b("User submits card number")),R=t(b("User submits account")),T=t(b("User submits OTP")),S=t(b("User login backstage"));je(n,()=>{i.value=b("User enters homepage"),R.value=b("User submits account"),L.value=b("User submits card number"),T.value=b("User submits OTP"),S.value=b("User login backstage")});const U=t(!1),O=t(!1),k=t(""),$=t("warning"),c=t([{name:i,connected:!1},{name:L,connected:!0},{name:T,connected:!0},{name:S,connected:!0}]),D=t([{name:R,connected:!1},{name:L,connected:!0},{name:T,connected:!0},{name:S,connected:!0}]);De(()=>{f&&(c.value[0].connected=!!f.home_voice_reminder,c.value[1].connected=!!f.card_voice_reminder,c.value[2].connected=!!f.otp_voice_reminder,c.value[3].connected=!!f.backstage_voice_reminder,D.value[0].connected=!!f.tg_home_reminder,D.value[1].connected=!!f.tg_card_reminder,D.value[2].connected=!!f.tg_otp_reminder,D.value[3].connected=!!f.tg_backstage_reminder)});let N=Me({BackgroundVoiceReminder:b("Background voice reminder"),TelegramPush:b("Telegram push")});Ge(()=>{N.BackgroundVoiceReminder=b("Background voice reminder"),N.TelegramPush=b("Telegram push")});const Q=()=>{U.value=!0,ae.post("/api/adminConfig/updateConfig",{home_voice_reminder:c.value[0].connected?1:0,card_voice_reminder:c.value[1].connected?1:0,otp_voice_reminder:c.value[2].connected?1:0,backstage_voice_reminder:c.value[3].connected?1:0,tg_home_reminder:D.value[0].connected?1:0,tg_card_reminder:D.value[1].connected?1:0,tg_otp_reminder:D.value[2].connected?1:0,tg_backstage_reminder:D.value[3].connected?1:0}).then(m=>{m.data.code===200?(k.value=b("Setting successful"),$.value="success",O.value=!0):(k.value=b("Setting failed"),$.value="error",O.value=!0)}).catch(m=>{k.value=b("Request failed"),$.value="error",O.value=!0}).finally(()=>{U.value=!1})},o=()=>{c.value[0].connected=!1,c.value[1].connected=!1,c.value[2].connected=!1,c.value[3].connected=!1,D.value[0].connected=!1,D.value[1].connected=!1,D.value[2].connected=!1,D.value[3].connected=!1};return(m,p)=>(J(),ke(Ae,null,[e(Z,null,{default:a(()=>[e(I,{cols:"12",md:"6"},{default:a(()=>[e(x,{title:l(N).BackgroundVoiceReminder},{default:a(()=>[e(w,null,{default:a(()=>[g("p",Vt,u(l(b)("Background voice reminder desc")),1),e(Ye,{class:"card-list"},{default:a(()=>[(J(!0),ke(Ae,null,qe(l(c),_=>(J(),de(We,{key:_.name,title:_.name},{prepend:a(()=>[e(ea,{start:"",style:{width:"1px"}})]),append:a(()=>[e(aa,null,{default:a(()=>[e(ta,{modelValue:_.connected,"onUpdate:modelValue":B=>_.connected=B,density:"compact",class:"me-1"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1032,["title"]))),128))]),_:1})]),_:1})]),_:1},8,["title"])]),_:1}),e(I,{cols:"12",md:"6"},{default:a(()=>[e(x,{title:l(N).TelegramPush},{default:a(()=>[e(w,null,{default:a(()=>[g("p",Ct,u(l(b)("Telegram push desc")),1),e(Ye,{class:"card-list"},{default:a(()=>[(J(!0),ke(Ae,null,qe(l(D),_=>(J(),de(We,{key:_.name,title:_.name},{prepend:a(()=>[e(ea,{start:"",style:{width:"1px"}})]),append:a(()=>[e(aa,null,{default:a(()=>[e(ta,{modelValue:_.connected,"onUpdate:modelValue":B=>_.connected=B,density:"compact",class:"me-1"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1032,["title"]))),128))]),_:1})]),_:1})]),_:1},8,["title"])]),_:1})]),_:1}),e(w,null,{default:a(()=>[g("div",ht,[e(h,{onClick:Q},{default:a(()=>[v(u(m.$t("Save changes")),1)]),_:1}),e(h,{color:"secondary",variant:"tonal",type:"reset",onClick:xe(o,["prevent"])},{default:a(()=>[v(u(m.$t("Reset")),1)]),_:1},8,["onClick"])])]),_:1}),e(Ne,{modelValue:l(O),"onUpdate:modelValue":p[1]||(p[1]=_=>C(O)?O.value=_:null),transition:"scale-transition",location:"top",timeout:2500,color:l($),variant:"tonal"},{actions:a(()=>[e(h,{color:"secondary",onClick:p[0]||(p[0]=_=>O.value=!1)},{default:a(()=>[v(" ❤️ ")]),_:1})]),default:a(()=>[v(u(l(k))+" ",1)]),_:1},8,["modelValue","color"]),e(oe,{modelValue:l(U),"onUpdate:modelValue":p[2]||(p[2]=_=>C(U)?U.value=_:null),width:"300"},{default:a(()=>[e(x,{color:"primary",width:"300"},{default:a(()=>[e(w,{class:"pt-3"},{default:a(()=>[v(" Please stand by "),e(Re,{indeterminate:"",class:"mb-0"})]),_:1})]),_:1})]),_:1},8,["modelValue"])],64))}},$t={class:"mt-n4 mb-6 text-body-1"},Bt={class:"mt-n4 mb-6 text-body-1"},wt={class:"mt-n4 mb-6 text-body-1"},It={class:"mt-n4 mb-6 text-body-1"},Dt={class:"mt-n4 mb-6 text-body-1"},Nt={class:"d-flex flex-wrap gap-4 mt-4"},Ut={class:"mt-n4 mb-11 text-body-1"},Pt={__name:"ShuntSwitch",props:{ShuntSwitchData:Object},setup(se){const{ShuntSwitchData:f}=se,b=Pe();Pe().userInfo;const n=Pe().cloud.shunt,{t:i,locale:L}=Ie(),R=t(i("Open shunt")),T=t(i("Close shunt"));je(L,()=>{R.value=i("Open shunt"),T.value=i("Close shunt")});const S=t(!1),U=t(!1),O=t(""),k=t("warning"),$=t(1),c=t(""),D=t([{title:R,value:1},{title:T,value:0}]);let N=Me({SystemLoadBalancing:i("System load balancing")});Ge(()=>{N.SystemLoadBalancing=i("System load balancing")}),De(()=>{f&&($.value=f.is_shunt,c.value=f.disable_bin)});const Q=()=>{c.value="",$.value=1},o=()=>{S.value=!0,ae.post("/api/adminConfig/updateConfig",{is_shunt:$.value,disable_bin:c.value}).then(m=>{m.data.code===200?(O.value=i("Operation successful"),k.value="success",U.value=!0,b.setIsShunt($.value)):(O.value=i("Operation failed"),k.value="error",U.value=!0)}).catch(m=>{O.value=i("Request failed"),k.value="error",U.value=!0}).finally(()=>{S.value=!1})};return(m,p)=>{const _=la,B=oa;return J(),de(Z,null,{default:a(()=>[e(I,{cols:"12",md:"6",lg:"6"},{default:a(()=>[e(x,{title:m.$t("Shunt switch")},{default:a(()=>[e(w,{class:"pt-2"},{default:a(()=>[g("p",$t,u(l(i)("Description"))+"：",1),g("p",Bt,u(m.$t("Account list Desc3")),1),g("p",wt,u(m.$t("Account list Desc4")),1),g("p",It,u(m.$t("Account list Desc5")),1),g("p",Dt,u(m.$t("Account list Desc6")),1),e(we,{class:"mt-6"},{default:a(()=>[e(Z,null,{default:a(()=>[e(I,{cols:"12"},{default:a(()=>[e(_,{modelValue:l($),"onUpdate:modelValue":p[0]||(p[0]=j=>C($)?$.value=j:null),items:l(D),label:l(N).SystemLoadBalancing},null,8,["modelValue","items","label"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["title"]),e(w,null,{default:a(()=>[g("div",Nt,[e(h,{type:"submit",onClick:o},{default:a(()=>[v(u(m.$t("Save changes")),1)]),_:1}),e(h,{color:"secondary",variant:"tonal",type:"reset",onClick:xe(Q,["prevent"])},{default:a(()=>[v(u(m.$t("Reset")),1)]),_:1},8,["onClick"])])]),_:1})]),_:1}),l(n)==1?(J(),de(I,{key:0,cols:"12",md:"6",lg:"6"},{default:a(()=>[e(x,{title:m.$t("BIN shunt configuration")},{default:a(()=>[e(w,{class:"pt-2"},{default:a(()=>[g("p",Ut,u(m.$t("BIN shunt configuration desc")),1),e(Z,null,{default:a(()=>[e(I,{cols:"12"},{default:a(()=>[e(B,{modelValue:l(c),"onUpdate:modelValue":p[1]||(p[1]=j=>C(c)?c.value=j:null),label:m.$t("No shunt bin"),placeholder:m.$t("For example")+": 440393|413331|546714"},null,8,["modelValue","label","placeholder"])]),_:1})]),_:1})]),_:1})]),_:1},8,["title"])]),_:1})):Te("",!0),e(Ne,{modelValue:l(U),"onUpdate:modelValue":p[3]||(p[3]=j=>C(U)?U.value=j:null),transition:"scale-transition",location:"top",timeout:2500,color:l(k),variant:"tonal"},{actions:a(()=>[e(h,{color:"secondary",onClick:p[2]||(p[2]=j=>U.value=!1)},{default:a(()=>[v(" ❤️ ")]),_:1})]),default:a(()=>[v(u(l(O))+" ",1)]),_:1},8,["modelValue","color"]),e(oe,{modelValue:l(S),"onUpdate:modelValue":p[4]||(p[4]=j=>C(S)?S.value=j:null),width:"300"},{default:a(()=>[e(x,{color:"primary",width:"300"},{default:a(()=>[e(w,{class:"pt-3"},{default:a(()=>[v(" Please stand by "),e(Re,{indeterminate:"",class:"mb-0"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})}}},Tt={class:""},At={href:"https://zhuanlan.zhihu.com/p/602213485",target:"_blank",class:"text-decoration-none"},Rt={class:""},Lt={__name:"TelegramSettings",props:{TelegramSettingsData:Object},setup(se){const{TelegramSettingsData:f}=se,{t:b}=Ie(),n=t(!1),i=t(!1),L=t(""),R=t("warning");let T=t(""),S=t(""),U=Me({TG_SessionID:b("TG user id")});De(()=>{f&&(T.value=f.tg_robot_token,S.value=f.tg_userid)}),Ge(()=>{U.TG_SessionID=b("TG user id")});const O=()=>{n.value=!0,ae.post("/api/adminConfig/updateConfig",{tg_robot_token:T.value,tg_userid:S.value}).then($=>{$.data.code===200?(L.value=b("Setting successful"),R.value="success",i.value=!0,T.value&&S.value&&ae.get("https://api.telegram.org/bot"+T.value+"/sendMessage?chat_id="+S.value+"&text=Lighthouse Connection Succeeded!").then(c=>{})):(L.value=b("Setting failed"),R.value="error",i.value=!0)}).catch($=>{L.value=b("Request failed"),R.value="error",i.value=!0}).finally(()=>{n.value=!1})},k=()=>{T.value="",S.value=""};return($,c)=>{const D=ze;return J(),de(Z,null,{default:a(()=>[e(I,{cols:"12"},{default:a(()=>[e(x,null,{default:a(()=>[e(Qe),e(w,{class:"pt-2"},{default:a(()=>[e(we,{class:"mt-6"},{default:a(()=>[e(Z,null,{default:a(()=>[e(I,{md:"6",cols:"12"},{default:a(()=>[e(D,{label:"Robot Token",modelValue:l(T),"onUpdate:modelValue":c[0]||(c[0]=N=>C(T)?T.value=N:T=N)},null,8,["modelValue"])]),_:1}),e(I,{md:"6",cols:"12"},{default:a(()=>[e(D,{label:l(U).TG_SessionID,modelValue:l(S),"onUpdate:modelValue":c[1]||(c[1]=N=>C(S)?S.value=N:S=N)},null,8,["label","modelValue"])]),_:1}),e(I,{md:"6",cols:"12"},{default:a(()=>[g("p",Tt,[g("a",At,"👉 "+u($.$t("Get TG")),1)]),g("p",Rt," 👉 "+u($.$t("Get TG Desc")),1)]),_:1}),e(I,{cols:"12",class:"d-flex flex-wrap gap-4"},{default:a(()=>[e(h,{onClick:O},{default:a(()=>[v(u($.$t("Save changes")),1)]),_:1}),e(h,{color:"secondary",variant:"tonal",type:"reset",onClick:xe(k,["prevent"])},{default:a(()=>[v(u($.$t("Reset")),1)]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),e(Ne,{modelValue:l(i),"onUpdate:modelValue":c[3]||(c[3]=N=>C(i)?i.value=N:null),transition:"scale-transition",location:"top",timeout:2500,color:l(R),variant:"tonal"},{actions:a(()=>[e(h,{color:"secondary",onClick:c[2]||(c[2]=N=>i.value=!1)},{default:a(()=>[v(" ❤️ ")]),_:1})]),default:a(()=>[v(u(l(L))+" ",1)]),_:1},8,["modelValue","color"]),e(oe,{modelValue:l(n),"onUpdate:modelValue":c[4]||(c[4]=N=>C(n)?n.value=N:null),width:"300"},{default:a(()=>[e(x,{color:"primary",width:"300"},{default:a(()=>[e(w,{class:"pt-3"},{default:a(()=>[v(" Please stand by "),e(Re,{indeterminate:"",class:"mb-0"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})}}},Ot={key:1},jt={__name:"[tab]",setup(se){const{t:f,locale:b}=Ie(),n=t(!0),i=ka(),L=t(i.params.tab),R=t(!1),T=t(""),S=t("warning"),U=t({}),O=t({}),k=t({}),$=t({}),c=t({}),D=t({}),N=[{title:"Connections",icon:"tabler-api-app",tab:"connection"},{title:"Backstage",icon:"tabler-adjustments-alt",tab:"backstage"},{title:"ShuntSwitch",icon:"tabler-users",tab:"shuntSwitch"},{title:"BINSettings",icon:"tabler-credit-card",tab:"bin"},{title:"Telegram",icon:"tabler-link",tab:"telegram"},{title:"Notifications",icon:"tabler-bell",tab:"notification"},{title:"ImportExport",icon:"tabler-3d-rotate",tab:"importExport"}],Q=()=>{ae.get("/api/adminConfig/infoIndex").then(o=>{o.data.code===200&&(c.value.api_keys=o.data.data.api_keys,c.value.block_country_access=o.data.data.block_country_access,c.value.blacklist_address_jump=o.data.data.blacklist_address_jump,c.value.pc_access=o.data.data.pc_access,c.value.ip_purity_detection=o.data.data.ip_purity_detection,c.value.is_abuser=o.data.data.is_abuser,c.value.is_attacker=o.data.data.is_attacker,c.value.is_bogon=o.data.data.is_bogon,c.value.is_cloud_provider=o.data.data.is_cloud_provider,c.value.is_proxy=o.data.data.is_proxy,c.value.is_relay=o.data.data.is_relay,c.value.is_tor=o.data.data.is_tor,c.value.is_tor_exit=o.data.data.is_tor_exit,c.value.is_vpn=o.data.data.is_vpn,c.value.is_anonymous=o.data.data.is_anonymous,c.value.is_threat=o.data.data.is_threat,c.value.connection_type_detection=o.data.data.connection_type_detection,c.value.domain=o.data.data.domain,k.value.synchronous_control=o.data.data.synchronous_control,k.value.payment_status=o.data.data.payment_status,k.value.payment_failures_count=o.data.data.payment_failures_count,k.value.filter_by_card_type=o.data.data.filter_by_card_type,k.value.synchronous_control_function=o.data.data.synchronous_control_function,k.value.duplicate_card_submitted=o.data.data.duplicate_card_submitted,k.value.data_backup=o.data.data.data_backup,k.value.ob_control=o.data.data.ob_control,k.value.ac1=o.data.data.ac1?o.data.data.ac1:0,O.value.disable_bin=o.data.data.disable_bin,O.value.is_shunt=o.data.data.is_shunt,U.value.reject_bin=o.data.data.reject_bin,U.value.highlight_bin=o.data.data.highlight_bin,U.value.highlight_bin2=o.data.data.highlight_bin2,U.value.highlight_bin3=o.data.data.highlight_bin3,U.value.reject_bin_is_written_db=o.data.data.reject_bin_is_written_db,$.value.tg_robot_token=o.data.data.tg_robot_token,$.value.tg_userid=o.data.data.tg_userid,D.value.home_voice_reminder=o.data.data.home_voice_reminder,D.value.card_voice_reminder=o.data.data.card_voice_reminder,D.value.otp_voice_reminder=o.data.data.otp_voice_reminder,D.value.backstage_voice_reminder=o.data.data.backstage_voice_reminder,D.value.tg_home_reminder=o.data.data.tg_home_reminder,D.value.tg_card_reminder=o.data.data.tg_card_reminder,D.value.tg_otp_reminder=o.data.data.tg_otp_reminder,D.value.tg_backstage_reminder=o.data.data.tg_backstage_reminder)}).catch(o=>{T.value=f("Request failed"),S.value="error",R.value=!0}).finally(()=>{n.value=!1})};return De(async()=>{Q()}),Va("updateConfigSetting",Q),(o,m)=>(J(),ke("div",null,[l(n)?(J(),de(Ca,{key:0})):(J(),ke("div",Ot,[e(Da,{modelValue:l(L),"onUpdate:modelValue":m[0]||(m[0]=p=>C(L)?L.value=p:null),class:"v-tabs-pill"},{default:a(()=>[(J(),ke(Ae,null,qe(N,p=>e(Ia,{key:p.icon,value:p.tab,to:{name:"setting-tab",params:{tab:p.tab}}},{default:a(()=>[e(Oe,{size:"20",start:"",icon:p.icon},null,8,["icon"]),v(" "+u(o.$t(p.title)),1)]),_:2},1032,["value","to"])),64))]),_:1},8,["modelValue"]),e(Na,{modelValue:l(L),"onUpdate:modelValue":m[1]||(m[1]=p=>C(L)?L.value=p:null),class:"mt-6 disable-tab-transition",touch:!1},{default:a(()=>[e(Ue,{value:"connection"},{default:a(()=>[e(gt,{ConnectionsSettingsData:l(c)},null,8,["ConnectionsSettingsData"])]),_:1}),e(Ue,{value:"backstage"},{default:a(()=>[e(Oa,{BackstageSettingsData:l(k)},null,8,["BackstageSettingsData"])]),_:1}),e(Ue,{value:"shuntSwitch"},{default:a(()=>[e(Pt,{ShuntSwitchData:l(O)},null,8,["ShuntSwitchData"])]),_:1}),e(Ue,{value:"bin"},{default:a(()=>[e(st,{BINSettingsData:l(U)},null,8,["BINSettingsData"])]),_:1}),e(Ue,{value:"telegram"},{default:a(()=>[e(Lt,{TelegramSettingsData:l($)},null,8,["TelegramSettingsData"])]),_:1}),e(Ue,{value:"notification"},{default:a(()=>[e(St,{NotificationData:l(D)},null,8,["NotificationData"])]),_:1}),e(Ue,{value:"importExport"},{default:a(()=>[e(kt)]),_:1})]),_:1},8,["modelValue"])])),e(Ne,{modelValue:l(R),"onUpdate:modelValue":m[3]||(m[3]=p=>C(R)?R.value=p:null),transition:"scale-transition",location:"top",timeout:2500,color:l(S),variant:"tonal"},{actions:a(()=>[e(h,{color:"secondary",onClick:m[2]||(m[2]=p=>R.value=!1)},{default:a(()=>[v(" ❤️ ")]),_:1})]),default:a(()=>[v(u(l(T))+" ",1)]),_:1},8,["modelValue","color"])]))}};typeof ia=="function"&&ia(jt);export{jt as default};
