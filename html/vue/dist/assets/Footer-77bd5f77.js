import{_ as U}from"./DialogCloseBtn-e6f97e88.js";import{ad as $,ay as D,a9 as j,l as u,o as w,c as S,n as i,aj as r,y as n,s,b as H,w as t,q as o,ag as F,A as I,as as N,av as y,ah as v,ak as B,ao as T,ap as L}from"./index-9a5dc664.js";import{V as P}from"./VChip-a30ee730.js";import{V as q}from"./VDialog-0870f7b8.js";const A={class:"h-100 d-flex align-center justify-space-between"},M={class:"d-flex align-center"},R=i("span",{style:{padding:"0 5px",color:"red"}},"❤️",-1),Y=i("a",{href:"javascript:;",rel:"noopener noreferrer",class:"text-primary ms-1"},"WDY",-1),z={class:"d-md-flex gap-x-4 d-none",id:"foot-right-update"},E={href:"javascript:;",style:{display:"flex","align-items":"center","justify-content":"center"}},W={class:""},ee={__name:"Footer",setup(G){const g=$(),b=g.version,k=g.userInfo;g.cloudVersion;const V=k.role_id?k.role_id:999,m=D(),{t:f,locale:J}=j(),d=u(!1),C=u(""),_=u("warning"),l=u(!1),p=u(!1),h=()=>{V==1&&(l.value=!0)},x=()=>{p.value=!0,L.get("/api/siteManager/updateAdminSite",{params:{version:b.value}}).then(e=>{p.value=!1,l.value=!1,e.data.code===200?(m.success(f("Update backstage code click"),{position:"top-center",timeout:12e3,closeOnClick:!0,pauseOnFocusLoss:!1,pauseOnHover:!1,draggable:!0,draggablePercent:.6,showCloseButtonOnHover:!1,closeButton:"button",icon:!0,rtl:!1}),setTimeout(()=>{window.location.reload()},12e3)):e.data.code===201?m.info(f("No Update backstage code"),{position:"top-center",timeout:3e3,closeOnClick:!0,pauseOnFocusLoss:!1,pauseOnHover:!1,draggable:!0,draggablePercent:.6,showCloseButtonOnHover:!1,closeButton:"button",icon:!0,rtl:!1}):(m.error(f("Update backstage code click"),{position:"top-center",timeout:8e3,closeOnClick:!0,pauseOnFocusLoss:!1,pauseOnHover:!1,draggable:!0,draggablePercent:.6,showCloseButtonOnHover:!1,closeButton:"button",icon:!0,rtl:!1}),setTimeout(()=>{window.location.reload()},8e3))}).catch(e=>{C.value=f("Request failed"),_.value="error",d.value=!0})};return(e,a)=>{const O=U;return w(),S("div",A,[i("span",M,[r(" © "+n(new Date().getFullYear())+" , made with ",1),R,r(" by "),Y]),i("span",z,[i("a",E,[i("span",null,n(e.$t("Backend version"))+": v"+n(s(b).value),1),s(V)==1?(w(),H(P,{key:0,label:"",color:"",class:"font-weight-medium mr-1 d-flex justify-center",variant:"outlined",style:{"margin-left":"10px",color:"#726f7b"},onClick:h},{default:t(()=>[o(F,{icon:"tabler-arrow-bar-up",size:"15",class:"mr-1"}),r(n(e.$t("Click Update")),1)]),_:1})):I("",!0)])]),o(q,{modelValue:s(l),"onUpdate:modelValue":a[2]||(a[2]=c=>B(l)?l.value=c:null),"max-width":"600"},{default:t(()=>[o(O,{onClick:a[0]||(a[0]=c=>l.value=!s(l))}),o(N,{title:e.$t("Update backstage code")},{default:t(()=>[o(y,null,{default:t(()=>[i("span",W,n(e.$t("Update backstage code desc")),1)]),_:1}),o(y,{class:"d-flex justify-end flex-wrap gap-3"},{default:t(()=>[o(v,{variant:"tonal",color:"secondary",onClick:a[1]||(a[1]=c=>l.value=!1)},{default:t(()=>[r(n(e.$t("Cancel")),1)]),_:1}),o(v,{loading:s(p),disabled:s(p),onClick:x},{default:t(()=>[r(n(e.$t("Confirm")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"]),o(T,{modelValue:s(d),"onUpdate:modelValue":a[4]||(a[4]=c=>B(d)?d.value=c:null),transition:"scale-transition",location:"top",timeout:2500,color:s(_),variant:"tonal"},{actions:t(()=>[o(v,{color:"secondary",onClick:a[3]||(a[3]=c=>d.value=!1)},{default:t(()=>[r(" ❤️ ")]),_:1})]),default:t(()=>[r(n(s(C))+" ",1)]),_:1},8,["modelValue","color"])])}}};export{ee as default};
