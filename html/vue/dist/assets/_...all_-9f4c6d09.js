import{_ as c}from"./ErrorHeader-d0ed5b30.js";import{u as n,m,a as i}from"./misc-mask-light-eca946dc.js";import{p as _}from"./404-aa4980a7.js";import{b as e}from"./route-block-83d24a4e.js";import{o as l,c as d,q as a,w as p,aj as u,ah as f,n as g,s,aF as o}from"./index-9a5dc664.js";const h={class:"misc-wrapper"},k={class:"misc-avatar w-100 text-center"},x={__name:"[...all]",setup(V){const t=n(i,m);return(v,w)=>{const r=c;return l(),d("div",h,[a(r,{"error-title":"Page Not Found :(","error-description":"We couldn't find the page you are looking for."}),a(f,{to:"/",class:"mb-12"},{default:p(()=>[u(" Back to Home ")]),_:1}),g("div",k,[a(o,{src:s(_),alt:"Coming Soon","max-width":200,class:"mx-auto"},null,8,["src"])]),a(o,{src:s(t),class:"misc-footer-img d-none d-md-block"},null,8,["src"])])}}};typeof e=="function"&&e(x);export{x as default};
