import{m as Hs,u as Ws,a as jt}from"./VTextField-3e2d458d.js";import{B as Ys,C as Qt}from"./VDataTable-d690b508.js";import{I as qs,aZ as $s,a_ as zs,B as ks,a7 as Xs,a$ as js,q as Zt,a8 as Kt,h as Jt}from"./index-9a5dc664.js";const Ac=qs()({name:"VCheckbox",inheritAttrs:!1,props:{...Hs(),...$s(Ys(),["inline"])},emits:{"update:focused":e=>!0},setup(e,t){let{attrs:n,slots:s}=t;const{isFocused:r,focus:a,blur:i}=Ws(e),l=zs(),o=ks(()=>e.id||`checkbox-${l}`);return Xs(()=>{const[d,c]=js(n),[f,I]=jt.filterProps(e),[p,u]=Qt.filterProps(e);return Zt(jt,Kt({class:["v-checkbox",e.class]},d,f,{id:o.value,focused:r.value,style:e.style}),{...s,default:_=>{let{id:m,messagesId:g,isDisabled:E,isReadonly:T}=_;return Zt(Qt,Kt(p,{id:m.value,"aria-describedby":g.value,disabled:E.value,readonly:T.value},c,{onFocus:a,onBlur:i}),s)}})}),{}}});/*!
 * FilePond 4.31.3
 * Licensed under MIT, https://opensource.org/licenses/MIT/
 * Please visit https://pqina.nl/filepond/ for details.
 */const Qs=e=>e instanceof HTMLElement,Zs=(e,t=[],n=[])=>{const s={...e},r=[],a=[],i=()=>({...s}),l=()=>{const u=[...r];return r.length=0,u},o=()=>{const u=[...a];a.length=0,u.forEach(({type:_,data:m})=>{d(_,m)})},d=(u,_,m)=>{if(m&&!document.hidden){a.push({type:u,data:_});return}p[u]&&p[u](_),r.push({type:u,data:_})},c=(u,..._)=>I[u]?I[u](..._):null,f={getState:i,processActionQueue:l,processDispatchQueue:o,dispatch:d,query:c};let I={};t.forEach(u=>{I={...u(s),...I}});let p={};return n.forEach(u=>{p={...u(d,c,s),...p}}),f},Ks=(e,t,n)=>{if(typeof n=="function"){e[t]=n;return}Object.defineProperty(e,t,{...n})},H=(e,t)=>{for(const n in e)e.hasOwnProperty(n)&&t(n,e[n])},_e=e=>{const t={};return H(e,n=>{Ks(t,n,e[n])}),t},$=(e,t,n=null)=>{if(n===null)return e.getAttribute(t)||e.hasAttribute(t);e.setAttribute(t,n)},Js="http://www.w3.org/2000/svg",er=["svg","path"],en=e=>er.includes(e),st=(e,t,n={})=>{typeof t=="object"&&(n=t,t=null);const s=en(e)?document.createElementNS(Js,e):document.createElement(e);return t&&(en(e)?$(s,"class",t):s.className=t),H(n,(r,a)=>{$(s,r,a)}),s},tr=e=>(t,n)=>{typeof n<"u"&&e.children[n]?e.insertBefore(t,e.children[n]):e.appendChild(t)},nr=(e,t)=>(n,s)=>(typeof s<"u"?t.splice(s,0,n):t.push(n),n),sr=(e,t)=>n=>(t.splice(t.indexOf(n),1),n.element.parentNode&&e.removeChild(n.element),n),rr=(()=>typeof window<"u"&&typeof window.document<"u")(),Zn=()=>rr,ir=Zn()?st("svg"):{},ar="children"in ir?e=>e.children.length:e=>e.childNodes.length,Kn=(e,t,n,s)=>{const r=n[0]||e.left,a=n[1]||e.top,i=r+e.width,l=a+e.height*(s[1]||1),o={element:{...e},inner:{left:e.left,top:e.top,right:e.right,bottom:e.bottom},outer:{left:r,top:a,right:i,bottom:l}};return t.filter(d=>!d.isRectIgnored()).map(d=>d.rect).forEach(d=>{tn(o.inner,{...d.inner}),tn(o.outer,{...d.outer})}),nn(o.inner),o.outer.bottom+=o.element.marginBottom,o.outer.right+=o.element.marginRight,nn(o.outer),o},tn=(e,t)=>{t.top+=e.top,t.right+=e.left,t.bottom+=e.top,t.left+=e.left,t.bottom>e.bottom&&(e.bottom=t.bottom),t.right>e.right&&(e.right=t.right)},nn=e=>{e.width=e.right-e.left,e.height=e.bottom-e.top},ge=e=>typeof e=="number",or=(e,t,n,s=.001)=>Math.abs(e-t)<s&&Math.abs(n)<s,lr=({stiffness:e=.5,damping:t=.75,mass:n=10}={})=>{let s=null,r=null,a=0,i=!1;const d=_e({interpolate:(c,f)=>{if(i)return;if(!(ge(s)&&ge(r))){i=!0,a=0;return}const I=-(r-s)*e;a+=I/n,r+=a,a*=t,or(r,s,a)||f?(r=s,a=0,i=!0,d.onupdate(r),d.oncomplete(r)):d.onupdate(r)},target:{set:c=>{if(ge(c)&&!ge(r)&&(r=c),s===null&&(s=c,r=c),s=c,r===s||typeof s>"u"){i=!0,a=0,d.onupdate(r),d.oncomplete(r);return}i=!1},get:()=>s},resting:{get:()=>i},onupdate:c=>{},oncomplete:c=>{}});return d},cr=e=>e<.5?2*e*e:-1+(4-2*e)*e,dr=({duration:e=500,easing:t=cr,delay:n=0}={})=>{let s=null,r,a,i=!0,l=!1,o=null;const c=_e({interpolate:(f,I)=>{i||o===null||(s===null&&(s=f),!(f-s<n)&&(r=f-s-n,r>=e||I?(r=1,a=l?0:1,c.onupdate(a*o),c.oncomplete(a*o),i=!0):(a=r/e,c.onupdate((r>=0?t(l?1-a:a):0)*o))))},target:{get:()=>l?0:o,set:f=>{if(o===null){o=f,c.onupdate(f),c.oncomplete(f);return}f<o?(o=1,l=!0):(l=!1,o=f),i=!1,s=null}},resting:{get:()=>i},onupdate:f=>{},oncomplete:f=>{}});return c},sn={spring:lr,tween:dr},ur=(e,t,n)=>{const s=e[t]&&typeof e[t][n]=="object"?e[t][n]:e[t]||e,r=typeof s=="string"?s:s.type,a=typeof s=="object"?{...s}:{};return sn[r]?sn[r](a):null},Ft=(e,t,n,s=!1)=>{t=Array.isArray(t)?t:[t],t.forEach(r=>{e.forEach(a=>{let i=a,l=()=>n[a],o=d=>n[a]=d;typeof a=="object"&&(i=a.key,l=a.getter||l,o=a.setter||o),!(r[i]&&!s)&&(r[i]={get:l,set:o})})})},fr=({mixinConfig:e,viewProps:t,viewInternalAPI:n,viewExternalAPI:s})=>{const r={...t},a=[];return H(e,(i,l)=>{const o=ur(l);if(!o)return;o.onupdate=c=>{t[i]=c},o.target=r[i],Ft([{key:i,setter:c=>{o.target!==c&&(o.target=c)},getter:()=>t[i]}],[n,s],t,!0),a.push(o)}),{write:i=>{let l=document.hidden,o=!0;return a.forEach(d=>{d.resting||(o=!1),d.interpolate(i,l)}),o},destroy:()=>{}}},Er=e=>(t,n)=>{e.addEventListener(t,n)},pr=e=>(t,n)=>{e.removeEventListener(t,n)},Ir=({mixinConfig:e,viewProps:t,viewInternalAPI:n,viewExternalAPI:s,viewState:r,view:a})=>{const i=[],l=Er(a.element),o=pr(a.element);return s.on=(d,c)=>{i.push({type:d,fn:c}),l(d,c)},s.off=(d,c)=>{i.splice(i.findIndex(f=>f.type===d&&f.fn===c),1),o(d,c)},{write:()=>!0,destroy:()=>{i.forEach(d=>{o(d.type,d.fn)})}}},_r=({mixinConfig:e,viewProps:t,viewExternalAPI:n})=>{Ft(e,n,t)},j=e=>e!=null,Tr={opacity:1,scaleX:1,scaleY:1,translateX:0,translateY:0,rotateX:0,rotateY:0,rotateZ:0,originX:0,originY:0},mr=({mixinConfig:e,viewProps:t,viewInternalAPI:n,viewExternalAPI:s,view:r})=>{const a={...t},i={};Ft(e,[n,s],t);const l=()=>[t.translateX||0,t.translateY||0],o=()=>[t.scaleX||0,t.scaleY||0],d=()=>r.rect?Kn(r.rect,r.childViews,l(),o()):null;return n.rect={get:d},s.rect={get:d},e.forEach(c=>{t[c]=typeof a[c]>"u"?Tr[c]:a[c]}),{write:()=>{if(gr(i,t))return hr(r.element,t),Object.assign(i,{...t}),!0},destroy:()=>{}}},gr=(e,t)=>{if(Object.keys(e).length!==Object.keys(t).length)return!0;for(const n in t)if(t[n]!==e[n])return!0;return!1},hr=(e,{opacity:t,perspective:n,translateX:s,translateY:r,scaleX:a,scaleY:i,rotateX:l,rotateY:o,rotateZ:d,originX:c,originY:f,width:I,height:p})=>{let u="",_="";(j(c)||j(f))&&(_+=`transform-origin: ${c||0}px ${f||0}px;`),j(n)&&(u+=`perspective(${n}px) `),(j(s)||j(r))&&(u+=`translate3d(${s||0}px, ${r||0}px, 0) `),(j(a)||j(i))&&(u+=`scale3d(${j(a)?a:1}, ${j(i)?i:1}, 1) `),j(d)&&(u+=`rotateZ(${d}rad) `),j(l)&&(u+=`rotateX(${l}rad) `),j(o)&&(u+=`rotateY(${o}rad) `),u.length&&(_+=`transform:${u};`),j(t)&&(_+=`opacity:${t};`,t===0&&(_+="visibility:hidden;"),t<1&&(_+="pointer-events:none;")),j(p)&&(_+=`height:${p}px;`),j(I)&&(_+=`width:${I}px;`);const m=e.elementCurrentStyle||"";(_.length!==m.length||_!==m)&&(e.style.cssText=_,e.elementCurrentStyle=_)},Rr={styles:mr,listeners:Ir,animations:fr,apis:_r},rn=(e={},t={},n={})=>(t.layoutCalculated||(e.paddingTop=parseInt(n.paddingTop,10)||0,e.marginTop=parseInt(n.marginTop,10)||0,e.marginRight=parseInt(n.marginRight,10)||0,e.marginBottom=parseInt(n.marginBottom,10)||0,e.marginLeft=parseInt(n.marginLeft,10)||0,t.layoutCalculated=!0),e.left=t.offsetLeft||0,e.top=t.offsetTop||0,e.width=t.offsetWidth||0,e.height=t.offsetHeight||0,e.right=e.left+e.width,e.bottom=e.top+e.height,e.scrollTop=t.scrollTop,e.hidden=t.offsetParent===null,e),q=({tag:e="div",name:t=null,attributes:n={},read:s=()=>{},write:r=()=>{},create:a=()=>{},destroy:i=()=>{},filterFrameActionsForChild:l=(p,u)=>u,didCreateView:o=()=>{},didWriteView:d=()=>{},ignoreRect:c=!1,ignoreRectUpdate:f=!1,mixins:I=[]}={})=>(p,u={})=>{const _=st(e,`filepond--${t}`,n),m=window.getComputedStyle(_,null),g=rn();let E=null,T=!1;const R=[],O=[],L={},b={},D=[r],P=[s],v=[i],M=()=>_,w=()=>R.concat(),x=()=>L,y=U=>(X,fe)=>X(U,fe),F=()=>E||(E=Kn(g,R,[0,0],[1,1]),E),A=()=>m,S=()=>{E=null,R.forEach(fe=>fe._read()),!(f&&g.width&&g.height)&&rn(g,_,m);const X={root:ae,props:u,rect:g};P.forEach(fe=>fe(X))},C=(U,X,fe)=>{let Me=X.length===0;return D.forEach(K=>{K({props:u,root:ae,actions:X,timestamp:U,shouldOptimize:fe})===!1&&(Me=!1)}),O.forEach(K=>{K.write(U)===!1&&(Me=!1)}),R.filter(K=>!!K.element.parentNode).forEach(K=>{K._write(U,l(K,X),fe)||(Me=!1)}),R.forEach((K,qe)=>{K.element.parentNode||(ae.appendChild(K.element,qe),K._read(),K._write(U,l(K,X),fe),Me=!1)}),T=Me,d({props:u,root:ae,actions:X,timestamp:U}),Me},N=()=>{O.forEach(U=>U.destroy()),v.forEach(U=>{U({root:ae,props:u})}),R.forEach(U=>U._destroy())},B={element:{get:M},style:{get:A},childViews:{get:w}},V={...B,rect:{get:F},ref:{get:x},is:U=>t===U,appendChild:tr(_),createChildView:y(p),linkView:U=>(R.push(U),U),unlinkView:U=>{R.splice(R.indexOf(U),1)},appendChildView:nr(_,R),removeChildView:sr(_,R),registerWriter:U=>D.push(U),registerReader:U=>P.push(U),registerDestroyer:U=>v.push(U),invalidateLayout:()=>_.layoutCalculated=!1,dispatch:p.dispatch,query:p.query},Pe={element:{get:M},childViews:{get:w},rect:{get:F},resting:{get:()=>T},isRectIgnored:()=>c,_read:S,_write:C,_destroy:N},Ye={...B,rect:{get:()=>g}};Object.keys(I).sort((U,X)=>U==="styles"?1:X==="styles"?-1:0).forEach(U=>{const X=Rr[U]({mixinConfig:I[U],viewProps:u,viewState:b,viewInternalAPI:V,viewExternalAPI:Pe,view:_e(Ye)});X&&O.push(X)});const ae=_e(V);a({root:ae,props:u});const It=ar(_);return R.forEach((U,X)=>{ae.appendChild(U.element,It+X)}),o(ae),_e(Pe)},Ar=(e,t,n=60)=>{const s="__framePainter";if(window[s]){window[s].readers.push(e),window[s].writers.push(t);return}window[s]={readers:[e],writers:[t]};const r=window[s],a=1e3/n;let i=null,l=null,o=null,d=null;const c=()=>{document.hidden?(o=()=>window.setTimeout(()=>f(performance.now()),a),d=()=>window.clearTimeout(l)):(o=()=>window.requestAnimationFrame(f),d=()=>window.cancelAnimationFrame(l))};document.addEventListener("visibilitychange",()=>{d&&d(),c(),f(performance.now())});const f=I=>{l=o(f),i||(i=I);const p=I-i;p<=a||(i=I-p%a,r.readers.forEach(u=>u()),r.writers.forEach(u=>u(I)))};return c(),f(performance.now()),{pause:()=>{d(l)}}},Z=(e,t)=>({root:n,props:s,actions:r=[],timestamp:a,shouldOptimize:i})=>{r.filter(l=>e[l.type]).forEach(l=>e[l.type]({root:n,props:s,action:l.data,timestamp:a,shouldOptimize:i})),t&&t({root:n,props:s,actions:r,timestamp:a,shouldOptimize:i})},an=(e,t)=>t.parentNode.insertBefore(e,t),on=(e,t)=>t.parentNode.insertBefore(e,t.nextSibling),lt=e=>Array.isArray(e),Ee=e=>e==null,Or=e=>e.trim(),ct=e=>""+e,Dr=(e,t=",")=>Ee(e)?[]:lt(e)?e:ct(e).split(t).map(Or).filter(n=>n.length),Jn=e=>typeof e=="boolean",es=e=>Jn(e)?e:e==="true",Q=e=>typeof e=="string",ts=e=>ge(e)?e:Q(e)?ct(e).replace(/[a-z]+/gi,""):0,et=e=>parseInt(ts(e),10),ln=e=>parseFloat(ts(e)),Ge=e=>ge(e)&&isFinite(e)&&Math.floor(e)===e,cn=(e,t=1e3)=>{if(Ge(e))return e;let n=ct(e).trim();return/MB$/i.test(n)?(n=n.replace(/MB$i/,"").trim(),et(n)*t*t):/KB/i.test(n)?(n=n.replace(/KB$i/,"").trim(),et(n)*t):et(n)},he=e=>typeof e=="function",Sr=e=>{let t=self,n=e.split("."),s=null;for(;s=n.shift();)if(t=t[s],!t)return null;return t},dn={process:"POST",patch:"PATCH",revert:"DELETE",fetch:"GET",restore:"GET",load:"GET"},yr=e=>{const t={};return t.url=Q(e)?e:e.url||"",t.timeout=e.timeout?parseInt(e.timeout,10):0,t.headers=e.headers?e.headers:{},H(dn,n=>{t[n]=Lr(n,e[n],dn[n],t.timeout,t.headers)}),t.process=e.process||Q(e)||e.url?t.process:null,t.remove=e.remove||null,delete t.headers,t},Lr=(e,t,n,s,r)=>{if(t===null)return null;if(typeof t=="function")return t;const a={url:n==="GET"||n==="PATCH"?`?${e}=`:"",method:n,headers:r,withCredentials:!1,timeout:s,onload:null,ondata:null,onerror:null};if(Q(t))return a.url=t,a;if(Object.assign(a,t),Q(a.headers)){const i=a.headers.split(/:(.+)/);a.headers={header:i[0],value:i[1]}}return a.withCredentials=es(a.withCredentials),a},Pr=e=>yr(e),Mr=e=>e===null,k=e=>typeof e=="object"&&e!==null,wr=e=>k(e)&&Q(e.url)&&k(e.process)&&k(e.revert)&&k(e.restore)&&k(e.fetch),yt=e=>lt(e)?"array":Mr(e)?"null":Ge(e)?"int":/^[0-9]+ ?(?:GB|MB|KB)$/gi.test(e)?"bytes":wr(e)?"api":typeof e,Cr=e=>e.replace(/{\s*'/g,'{"').replace(/'\s*}/g,'"}').replace(/'\s*:/g,'":').replace(/:\s*'/g,':"').replace(/,\s*'/g,',"').replace(/'\s*,/g,'",'),br={array:Dr,boolean:es,int:e=>yt(e)==="bytes"?cn(e):et(e),number:ln,float:ln,bytes:cn,string:e=>he(e)?e:ct(e),function:e=>Sr(e),serverapi:Pr,object:e=>{try{return JSON.parse(Cr(e))}catch{return null}}},Nr=(e,t)=>br[t](e),ns=(e,t,n)=>{if(e===t)return e;let s=yt(e);if(s!==n){const r=Nr(e,n);if(s=yt(r),r===null)throw`Trying to assign value with incorrect type to "${option}", allowed type: "${n}"`;e=r}return e},vr=(e,t)=>{let n=e;return{enumerable:!0,get:()=>n,set:s=>{n=ns(s,e,t)}}},Gr=e=>{const t={};return H(e,n=>{const s=e[n];t[n]=vr(s[0],s[1])}),_e(t)},Fr=e=>({items:[],listUpdateTimeout:null,itemUpdateTimeout:null,processingQueue:[],options:Gr(e)}),dt=(e,t="-")=>e.split(/(?=[A-Z])/).map(n=>n.toLowerCase()).join(t),Br=(e,t)=>{const n={};return H(t,s=>{n[s]={get:()=>e.getState().options[s],set:r=>{e.dispatch(`SET_${dt(s,"_").toUpperCase()}`,{value:r})}}}),n},Ur=e=>(t,n,s)=>{const r={};return H(e,a=>{const i=dt(a,"_").toUpperCase();r[`SET_${i}`]=l=>{try{s.options[a]=l.value}catch{}t(`DID_SET_${i}`,{value:s.options[a]})}}),r},xr=e=>t=>{const n={};return H(e,s=>{n[`GET_${dt(s,"_").toUpperCase()}`]=r=>t.options[s]}),n},ie={API:1,DROP:2,BROWSE:3,PASTE:4,NONE:5},Bt=()=>Math.random().toString(36).substring(2,11),Ut=(e,t)=>e.splice(t,1),Vr=(e,t)=>{t?e():document.hidden?Promise.resolve(1).then(e):setTimeout(e,0)},ut=()=>{const e=[],t=(s,r)=>{Ut(e,e.findIndex(a=>a.event===s&&(a.cb===r||!r)))},n=(s,r,a)=>{e.filter(i=>i.event===s).map(i=>i.cb).forEach(i=>Vr(()=>i(...r),a))};return{fireSync:(s,...r)=>{n(s,r,!0)},fire:(s,...r)=>{n(s,r,!1)},on:(s,r)=>{e.push({event:s,cb:r})},onOnce:(s,r)=>{e.push({event:s,cb:(...a)=>{t(s,r),r(...a)}})},off:t}},ss=(e,t,n)=>{Object.getOwnPropertyNames(e).filter(s=>!n.includes(s)).forEach(s=>Object.defineProperty(t,s,Object.getOwnPropertyDescriptor(e,s)))},Hr=["fire","process","revert","load","on","off","onOnce","retryLoad","extend","archive","archived","release","released","requestProcessing","freeze"],J=e=>{const t={};return ss(e,t,Hr),t},Wr=e=>{e.forEach((t,n)=>{t.released&&Ut(e,n)})},G={INIT:1,IDLE:2,PROCESSING_QUEUED:9,PROCESSING:3,PROCESSING_COMPLETE:5,PROCESSING_ERROR:6,PROCESSING_REVERT_ERROR:10,LOADING:7,LOAD_ERROR:8},z={INPUT:1,LIMBO:2,LOCAL:3},rs=e=>/[^0-9]+/.exec(e),is=()=>rs(1.1.toLocaleString())[0],Yr=()=>{const e=is(),t=1e3.toLocaleString(),n=1e3.toString();return t!==n?rs(t)[0]:e==="."?",":"."},h={BOOLEAN:"boolean",INT:"int",NUMBER:"number",STRING:"string",ARRAY:"array",OBJECT:"object",FUNCTION:"function",ACTION:"action",SERVER_API:"serverapi",REGEX:"regex"},xt=[],oe=(e,t,n)=>new Promise((s,r)=>{const a=xt.filter(l=>l.key===e).map(l=>l.cb);if(a.length===0){s(t);return}const i=a.shift();a.reduce((l,o)=>l.then(d=>o(d,n)),i(t,n)).then(l=>s(l)).catch(l=>r(l))}),ye=(e,t,n)=>xt.filter(s=>s.key===e).map(s=>s.cb(t,n)),qr=(e,t)=>xt.push({key:e,cb:t}),$r=e=>Object.assign(we,e),rt=()=>({...we}),zr=e=>{H(e,(t,n)=>{we[t]&&(we[t][0]=ns(n,we[t][0],we[t][1]))})},we={id:[null,h.STRING],name:["filepond",h.STRING],disabled:[!1,h.BOOLEAN],className:[null,h.STRING],required:[!1,h.BOOLEAN],captureMethod:[null,h.STRING],allowSyncAcceptAttribute:[!0,h.BOOLEAN],allowDrop:[!0,h.BOOLEAN],allowBrowse:[!0,h.BOOLEAN],allowPaste:[!0,h.BOOLEAN],allowMultiple:[!1,h.BOOLEAN],allowReplace:[!0,h.BOOLEAN],allowRevert:[!0,h.BOOLEAN],allowRemove:[!0,h.BOOLEAN],allowProcess:[!0,h.BOOLEAN],allowReorder:[!1,h.BOOLEAN],allowDirectoriesOnly:[!1,h.BOOLEAN],storeAsFile:[!1,h.BOOLEAN],forceRevert:[!1,h.BOOLEAN],maxFiles:[null,h.INT],checkValidity:[!1,h.BOOLEAN],itemInsertLocationFreedom:[!0,h.BOOLEAN],itemInsertLocation:["before",h.STRING],itemInsertInterval:[75,h.INT],dropOnPage:[!1,h.BOOLEAN],dropOnElement:[!0,h.BOOLEAN],dropValidation:[!1,h.BOOLEAN],ignoredFiles:[[".ds_store","thumbs.db","desktop.ini"],h.ARRAY],instantUpload:[!0,h.BOOLEAN],maxParallelUploads:[2,h.INT],allowMinimumUploadDuration:[!0,h.BOOLEAN],chunkUploads:[!1,h.BOOLEAN],chunkForce:[!1,h.BOOLEAN],chunkSize:[5e6,h.INT],chunkRetryDelays:[[500,1e3,3e3],h.ARRAY],server:[null,h.SERVER_API],fileSizeBase:[1e3,h.INT],labelFileSizeBytes:["bytes",h.STRING],labelFileSizeKilobytes:["KB",h.STRING],labelFileSizeMegabytes:["MB",h.STRING],labelFileSizeGigabytes:["GB",h.STRING],labelDecimalSeparator:[is(),h.STRING],labelThousandsSeparator:[Yr(),h.STRING],labelIdle:['Drag & Drop your files or <span class="filepond--label-action">Browse</span>',h.STRING],labelInvalidField:["Field contains invalid files",h.STRING],labelFileWaitingForSize:["Waiting for size",h.STRING],labelFileSizeNotAvailable:["Size not available",h.STRING],labelFileCountSingular:["file in list",h.STRING],labelFileCountPlural:["files in list",h.STRING],labelFileLoading:["Loading",h.STRING],labelFileAdded:["Added",h.STRING],labelFileLoadError:["Error during load",h.STRING],labelFileRemoved:["Removed",h.STRING],labelFileRemoveError:["Error during remove",h.STRING],labelFileProcessing:["Uploading",h.STRING],labelFileProcessingComplete:["Upload complete",h.STRING],labelFileProcessingAborted:["Upload cancelled",h.STRING],labelFileProcessingError:["Error during upload",h.STRING],labelFileProcessingRevertError:["Error during revert",h.STRING],labelTapToCancel:["tap to cancel",h.STRING],labelTapToRetry:["tap to retry",h.STRING],labelTapToUndo:["tap to undo",h.STRING],labelButtonRemoveItem:["Remove",h.STRING],labelButtonAbortItemLoad:["Abort",h.STRING],labelButtonRetryItemLoad:["Retry",h.STRING],labelButtonAbortItemProcessing:["Cancel",h.STRING],labelButtonUndoItemProcessing:["Undo",h.STRING],labelButtonRetryItemProcessing:["Retry",h.STRING],labelButtonProcessItem:["Upload",h.STRING],iconRemove:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M11.586 13l-2.293 2.293a1 1 0 0 0 1.414 1.414L13 14.414l2.293 2.293a1 1 0 0 0 1.414-1.414L14.414 13l2.293-2.293a1 1 0 0 0-1.414-1.414L13 11.586l-2.293-2.293a1 1 0 0 0-1.414 1.414L11.586 13z" fill="currentColor" fill-rule="nonzero"/></svg>',h.STRING],iconProcess:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M14 10.414v3.585a1 1 0 0 1-2 0v-3.585l-1.293 1.293a1 1 0 0 1-1.414-1.415l3-3a1 1 0 0 1 1.414 0l3 3a1 1 0 0 1-1.414 1.415L14 10.414zM9 18a1 1 0 0 1 0-2h8a1 1 0 0 1 0 2H9z" fill="currentColor" fill-rule="evenodd"/></svg>',h.STRING],iconRetry:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M10.81 9.185l-.038.02A4.997 4.997 0 0 0 8 13.683a5 5 0 0 0 5 5 5 5 0 0 0 5-5 1 1 0 0 1 2 0A7 7 0 1 1 9.722 7.496l-.842-.21a.999.999 0 1 1 .484-1.94l3.23.806c.535.133.86.675.73 1.21l-.804 3.233a.997.997 0 0 1-1.21.73.997.997 0 0 1-.73-1.21l.23-.928v-.002z" fill="currentColor" fill-rule="nonzero"/></svg>',h.STRING],iconUndo:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M9.185 10.81l.02-.038A4.997 4.997 0 0 1 13.683 8a5 5 0 0 1 5 5 5 5 0 0 1-5 5 1 1 0 0 0 0 2A7 7 0 1 0 7.496 9.722l-.21-.842a.999.999 0 1 0-1.94.484l.806 3.23c.133.535.675.86 1.21.73l3.233-.803a.997.997 0 0 0 .73-1.21.997.997 0 0 0-1.21-.73l-.928.23-.002-.001z" fill="currentColor" fill-rule="nonzero"/></svg>',h.STRING],iconDone:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M18.293 9.293a1 1 0 0 1 1.414 1.414l-7.002 7a1 1 0 0 1-1.414 0l-3.998-4a1 1 0 1 1 1.414-1.414L12 15.586l6.294-6.293z" fill="currentColor" fill-rule="nonzero"/></svg>',h.STRING],oninit:[null,h.FUNCTION],onwarning:[null,h.FUNCTION],onerror:[null,h.FUNCTION],onactivatefile:[null,h.FUNCTION],oninitfile:[null,h.FUNCTION],onaddfilestart:[null,h.FUNCTION],onaddfileprogress:[null,h.FUNCTION],onaddfile:[null,h.FUNCTION],onprocessfilestart:[null,h.FUNCTION],onprocessfileprogress:[null,h.FUNCTION],onprocessfileabort:[null,h.FUNCTION],onprocessfilerevert:[null,h.FUNCTION],onprocessfile:[null,h.FUNCTION],onprocessfiles:[null,h.FUNCTION],onremovefile:[null,h.FUNCTION],onpreparefile:[null,h.FUNCTION],onupdatefiles:[null,h.FUNCTION],onreorderfiles:[null,h.FUNCTION],beforeDropFile:[null,h.FUNCTION],beforeAddFile:[null,h.FUNCTION],beforeRemoveFile:[null,h.FUNCTION],beforePrepareFile:[null,h.FUNCTION],stylePanelLayout:[null,h.STRING],stylePanelAspectRatio:[null,h.STRING],styleItemPanelAspectRatio:[null,h.STRING],styleButtonRemoveItemPosition:["left",h.STRING],styleButtonProcessItemPosition:["right",h.STRING],styleLoadIndicatorPosition:["right",h.STRING],styleProgressIndicatorPosition:["right",h.STRING],styleButtonRemoveItemAlign:[!1,h.BOOLEAN],files:[[],h.ARRAY],credits:[["https://pqina.nl/","Powered by PQINA"],h.ARRAY]},Re=(e,t)=>Ee(t)?e[0]||null:Ge(t)?e[t]||null:(typeof t=="object"&&(t=t.id),e.find(n=>n.id===t)||null),as=e=>{if(Ee(e))return e;if(/:/.test(e)){const t=e.split(":");return t[1]/t[0]}return parseFloat(e)},le=e=>e.filter(t=>!t.archived),kr={EMPTY:0,IDLE:1,ERROR:2,BUSY:3,READY:4};let $e=null;const Xr=()=>{if($e===null)try{const e=new DataTransfer;e.items.add(new File(["hello world"],"This_Works.txt"));const t=document.createElement("input");t.setAttribute("type","file"),t.files=e.files,$e=t.files.length===1}catch{$e=!1}return $e},jr=[G.LOAD_ERROR,G.PROCESSING_ERROR,G.PROCESSING_REVERT_ERROR],Qr=[G.LOADING,G.PROCESSING,G.PROCESSING_QUEUED,G.INIT],Zr=[G.PROCESSING_COMPLETE],Kr=e=>jr.includes(e.status),Jr=e=>Qr.includes(e.status),ei=e=>Zr.includes(e.status),un=e=>k(e.options.server)&&(k(e.options.server.process)||he(e.options.server.process)),ti=e=>({GET_STATUS:()=>{const t=le(e.items),{EMPTY:n,ERROR:s,BUSY:r,IDLE:a,READY:i}=kr;return t.length===0?n:t.some(Kr)?s:t.some(Jr)?r:t.some(ei)?i:a},GET_ITEM:t=>Re(e.items,t),GET_ACTIVE_ITEM:t=>Re(le(e.items),t),GET_ACTIVE_ITEMS:()=>le(e.items),GET_ITEMS:()=>e.items,GET_ITEM_NAME:t=>{const n=Re(e.items,t);return n?n.filename:null},GET_ITEM_SIZE:t=>{const n=Re(e.items,t);return n?n.fileSize:null},GET_STYLES:()=>Object.keys(e.options).filter(t=>/^style/.test(t)).map(t=>({name:t,value:e.options[t]})),GET_PANEL_ASPECT_RATIO:()=>/circle/.test(e.options.stylePanelLayout)?1:as(e.options.stylePanelAspectRatio),GET_ITEM_PANEL_ASPECT_RATIO:()=>e.options.styleItemPanelAspectRatio,GET_ITEMS_BY_STATUS:t=>le(e.items).filter(n=>n.status===t),GET_TOTAL_ITEMS:()=>le(e.items).length,SHOULD_UPDATE_FILE_INPUT:()=>e.options.storeAsFile&&Xr()&&!un(e),IS_ASYNC:()=>un(e),GET_FILE_SIZE_LABELS:t=>({labelBytes:t("GET_LABEL_FILE_SIZE_BYTES")||void 0,labelKilobytes:t("GET_LABEL_FILE_SIZE_KILOBYTES")||void 0,labelMegabytes:t("GET_LABEL_FILE_SIZE_MEGABYTES")||void 0,labelGigabytes:t("GET_LABEL_FILE_SIZE_GIGABYTES")||void 0})}),ni=e=>{const t=le(e.items).length;if(!e.options.allowMultiple)return t===0;const n=e.options.maxFiles;return n===null||t<n},os=(e,t,n)=>Math.max(Math.min(n,e),t),si=(e,t,n)=>e.splice(t,0,n),ri=(e,t,n)=>Ee(t)?null:typeof n>"u"?(e.push(t),t):(n=os(n,0,e.length),si(e,n,t),t),Lt=e=>/^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*)\s*$/i.test(e),We=e=>`${e}`.split("/").pop().split("?").shift(),ft=e=>e.split(".").pop(),ii=e=>{if(typeof e!="string")return"";const t=e.split("/").pop();return/svg/.test(t)?"svg":/zip|compressed/.test(t)?"zip":/plain/.test(t)?"txt":/msword/.test(t)?"doc":/[a-z]+/.test(t)?t==="jpeg"?"jpg":t:""},Fe=(e,t="")=>(t+e).slice(-t.length),ls=(e=new Date)=>`${e.getFullYear()}-${Fe(e.getMonth()+1,"00")}-${Fe(e.getDate(),"00")}_${Fe(e.getHours(),"00")}-${Fe(e.getMinutes(),"00")}-${Fe(e.getSeconds(),"00")}`,ve=(e,t,n=null,s=null)=>{const r=typeof n=="string"?e.slice(0,e.size,n):e.slice(0,e.size,e.type);return r.lastModifiedDate=new Date,e._relativePath&&(r._relativePath=e._relativePath),Q(t)||(t=ls()),t&&s===null&&ft(t)?r.name=t:(s=s||ii(r.type),r.name=t+(s?"."+s:"")),r},ai=()=>window.BlobBuilder=window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder,cs=(e,t)=>{const n=ai();if(n){const s=new n;return s.append(e),s.getBlob(t)}return new Blob([e],{type:t})},oi=(e,t)=>{const n=new ArrayBuffer(e.length),s=new Uint8Array(n);for(let r=0;r<e.length;r++)s[r]=e.charCodeAt(r);return cs(n,t)},ds=e=>(/^data:(.+);/.exec(e)||[])[1]||null,li=e=>e.split(",")[1].replace(/\s/g,""),ci=e=>atob(li(e)),di=e=>{const t=ds(e),n=ci(e);return oi(n,t)},ui=(e,t,n)=>ve(di(e),t,null,n),fi=e=>{if(!/^content-disposition:/i.test(e))return null;const t=e.split(/filename=|filename\*=.+''/).splice(1).map(n=>n.trim().replace(/^["']|[;"']{0,2}$/g,"")).filter(n=>n.length);return t.length?decodeURI(t[t.length-1]):null},Ei=e=>{if(/content-length:/i.test(e)){const t=e.match(/[0-9]+/)[0];return t?parseInt(t,10):null}return null},pi=e=>/x-content-transfer-id:/i.test(e)&&(e.split(":")[1]||"").trim()||null,Vt=e=>{const t={source:null,name:null,size:null},n=e.split(`
`);for(let s of n){const r=fi(s);if(r){t.name=r;continue}const a=Ei(s);if(a){t.size=a;continue}const i=pi(s);if(i){t.source=i;continue}}return t},Ii=e=>{const t={source:null,complete:!1,progress:0,size:null,timestamp:null,duration:0,request:null},n=()=>t.progress,s=()=>{t.request&&t.request.abort&&t.request.abort()},r=()=>{const l=t.source;i.fire("init",l),l instanceof File?i.fire("load",l):l instanceof Blob?i.fire("load",ve(l,l.name)):Lt(l)?i.fire("load",ui(l)):a(l)},a=l=>{if(!e){i.fire("error",{type:"error",body:"Can't load URL",code:400});return}t.timestamp=Date.now(),t.request=e(l,o=>{t.duration=Date.now()-t.timestamp,t.complete=!0,o instanceof Blob&&(o=ve(o,o.name||We(l))),i.fire("load",o instanceof Blob?o:o?o.body:null)},o=>{i.fire("error",typeof o=="string"?{type:"error",code:0,body:o}:o)},(o,d,c)=>{if(c&&(t.size=c),t.duration=Date.now()-t.timestamp,!o){t.progress=null;return}t.progress=d/c,i.fire("progress",t.progress)},()=>{i.fire("abort")},o=>{const d=Vt(typeof o=="string"?o:o.headers);i.fire("meta",{size:t.size||d.size,filename:d.name,source:d.source})})},i={...ut(),setSource:l=>t.source=l,getProgress:n,abort:s,load:r};return i},fn=e=>/GET|HEAD/.test(e),Ae=(e,t,n)=>{const s={onheaders:()=>{},onprogress:()=>{},onload:()=>{},ontimeout:()=>{},onerror:()=>{},onabort:()=>{},abort:()=>{r=!0,i.abort()}};let r=!1,a=!1;n={method:"POST",headers:{},withCredentials:!1,...n},t=encodeURI(t),fn(n.method)&&e&&(t=`${t}${encodeURIComponent(typeof e=="string"?e:JSON.stringify(e))}`);const i=new XMLHttpRequest,l=fn(n.method)?i:i.upload;return l.onprogress=o=>{r||s.onprogress(o.lengthComputable,o.loaded,o.total)},i.onreadystatechange=()=>{i.readyState<2||i.readyState===4&&i.status===0||a||(a=!0,s.onheaders(i))},i.onload=()=>{i.status>=200&&i.status<300?s.onload(i):s.onerror(i)},i.onerror=()=>s.onerror(i),i.onabort=()=>{r=!0,s.onabort()},i.ontimeout=()=>s.ontimeout(i),i.open(n.method,t,!0),Ge(n.timeout)&&(i.timeout=n.timeout),Object.keys(n.headers).forEach(o=>{const d=unescape(encodeURIComponent(n.headers[o]));i.setRequestHeader(o,d)}),n.responseType&&(i.responseType=n.responseType),n.withCredentials&&(i.withCredentials=!0),i.send(e),s},W=(e,t,n,s)=>({type:e,code:t,body:n,headers:s}),Oe=e=>t=>{e(W("error",0,"Timeout",t.getAllResponseHeaders()))},En=e=>/\?/.test(e),xe=(...e)=>{let t="";return e.forEach(n=>{t+=En(t)&&En(n)?n.replace(/\?/,"&"):n}),t},_t=(e="",t)=>{if(typeof t=="function")return t;if(!t||!Q(t.url))return null;const n=t.onload||(r=>r),s=t.onerror||(r=>null);return(r,a,i,l,o,d)=>{const c=Ae(r,xe(e,t.url),{...t,responseType:"blob"});return c.onload=f=>{const I=f.getAllResponseHeaders(),p=Vt(I).name||We(r);a(W("load",f.status,t.method==="HEAD"?null:ve(n(f.response),p),I))},c.onerror=f=>{i(W("error",f.status,s(f.response)||f.statusText,f.getAllResponseHeaders()))},c.onheaders=f=>{d(W("headers",f.status,null,f.getAllResponseHeaders()))},c.ontimeout=Oe(i),c.onprogress=l,c.onabort=o,c}},ne={QUEUED:0,COMPLETE:1,PROCESSING:2,ERROR:3,WAITING:4},_i=(e,t,n,s,r,a,i,l,o,d,c)=>{const f=[],{chunkTransferId:I,chunkServer:p,chunkSize:u,chunkRetryDelays:_}=c,m={serverId:I,aborted:!1},g=t.ondata||(y=>y),E=t.onload||((y,F)=>F==="HEAD"?y.getResponseHeader("Upload-Offset"):y.response),T=t.onerror||(y=>null),R=y=>{const F=new FormData;k(r)&&F.append(n,JSON.stringify(r));const A=typeof t.headers=="function"?t.headers(s,r):{...t.headers,"Upload-Length":s.size},S={...t,headers:A},C=Ae(g(F),xe(e,t.url),S);C.onload=N=>y(E(N,S.method)),C.onerror=N=>i(W("error",N.status,T(N.response)||N.statusText,N.getAllResponseHeaders())),C.ontimeout=Oe(i)},O=y=>{const F=xe(e,p.url,m.serverId),S={headers:typeof t.headers=="function"?t.headers(m.serverId):{...t.headers},method:"HEAD"},C=Ae(null,F,S);C.onload=N=>y(E(N,S.method)),C.onerror=N=>i(W("error",N.status,T(N.response)||N.statusText,N.getAllResponseHeaders())),C.ontimeout=Oe(i)},L=Math.floor(s.size/u);for(let y=0;y<=L;y++){const F=y*u,A=s.slice(F,F+u,"application/offset+octet-stream");f[y]={index:y,size:A.size,offset:F,data:A,file:s,progress:0,retries:[..._],status:ne.QUEUED,error:null,request:null,timeout:null}}const b=()=>a(m.serverId),D=y=>y.status===ne.QUEUED||y.status===ne.ERROR,P=y=>{if(m.aborted)return;if(y=y||f.find(D),!y){f.every(B=>B.status===ne.COMPLETE)&&b();return}y.status=ne.PROCESSING,y.progress=null;const F=p.ondata||(B=>B),A=p.onerror||(B=>null),S=xe(e,p.url,m.serverId),C=typeof p.headers=="function"?p.headers(y):{...p.headers,"Content-Type":"application/offset+octet-stream","Upload-Offset":y.offset,"Upload-Length":s.size,"Upload-Name":s.name},N=y.request=Ae(F(y.data),S,{...p,headers:C});N.onload=()=>{y.status=ne.COMPLETE,y.request=null,w()},N.onprogress=(B,V,Pe)=>{y.progress=B?V:null,M()},N.onerror=B=>{y.status=ne.ERROR,y.request=null,y.error=A(B.response)||B.statusText,v(y)||i(W("error",B.status,A(B.response)||B.statusText,B.getAllResponseHeaders()))},N.ontimeout=B=>{y.status=ne.ERROR,y.request=null,v(y)||Oe(i)(B)},N.onabort=()=>{y.status=ne.QUEUED,y.request=null,o()}},v=y=>y.retries.length===0?!1:(y.status=ne.WAITING,clearTimeout(y.timeout),y.timeout=setTimeout(()=>{P(y)},y.retries.shift()),!0),M=()=>{const y=f.reduce((A,S)=>A===null||S.progress===null?null:A+S.progress,0);if(y===null)return l(!1,0,0);const F=f.reduce((A,S)=>A+S.size,0);l(!0,y,F)},w=()=>{f.filter(F=>F.status===ne.PROCESSING).length>=1||P()},x=()=>{f.forEach(y=>{clearTimeout(y.timeout),y.request&&y.request.abort()})};return m.serverId?O(y=>{m.aborted||(f.filter(F=>F.offset<y).forEach(F=>{F.status=ne.COMPLETE,F.progress=F.size}),w())}):R(y=>{m.aborted||(d(y),m.serverId=y,w())}),{abort:()=>{m.aborted=!0,x()}}},Ti=(e,t,n,s)=>(r,a,i,l,o,d,c)=>{if(!r)return;const f=s.chunkUploads,I=f&&r.size>s.chunkSize,p=f&&(I||s.chunkForce);if(r instanceof Blob&&p)return _i(e,t,n,r,a,i,l,o,d,c,s);const u=t.ondata||(O=>O),_=t.onload||(O=>O),m=t.onerror||(O=>null),g=typeof t.headers=="function"?t.headers(r,a)||{}:{...t.headers},E={...t,headers:g};var T=new FormData;k(a)&&T.append(n,JSON.stringify(a)),(r instanceof Blob?[{name:null,file:r}]:r).forEach(O=>{T.append(n,O.file,O.name===null?O.file.name:`${O.name}${O.file.name}`)});const R=Ae(u(T),xe(e,t.url),E);return R.onload=O=>{i(W("load",O.status,_(O.response),O.getAllResponseHeaders()))},R.onerror=O=>{l(W("error",O.status,m(O.response)||O.statusText,O.getAllResponseHeaders()))},R.ontimeout=Oe(l),R.onprogress=o,R.onabort=d,R},mi=(e="",t,n,s)=>typeof t=="function"?(...r)=>t(n,...r,s):!t||!Q(t.url)?null:Ti(e,t,n,s),Be=(e="",t)=>{if(typeof t=="function")return t;if(!t||!Q(t.url))return(r,a)=>a();const n=t.onload||(r=>r),s=t.onerror||(r=>null);return(r,a,i)=>{const l=Ae(r,e+t.url,t);return l.onload=o=>{a(W("load",o.status,n(o.response),o.getAllResponseHeaders()))},l.onerror=o=>{i(W("error",o.status,s(o.response)||o.statusText,o.getAllResponseHeaders()))},l.ontimeout=Oe(i),l}},us=(e=0,t=1)=>e+Math.random()*(t-e),gi=(e,t=1e3,n=0,s=25,r=250)=>{let a=null;const i=Date.now(),l=()=>{let o=Date.now()-i,d=us(s,r);o+d>t&&(d=o+d-t);let c=o/t;if(c>=1||document.hidden){e(1);return}e(c),a=setTimeout(l,d)};return t>0&&l(),{clear:()=>{clearTimeout(a)}}},hi=(e,t)=>{const n={complete:!1,perceivedProgress:0,perceivedPerformanceUpdater:null,progress:null,timestamp:null,perceivedDuration:0,duration:0,request:null,response:null},{allowMinimumUploadDuration:s}=t,r=(c,f)=>{const I=()=>{n.duration===0||n.progress===null||d.fire("progress",d.getProgress())},p=()=>{n.complete=!0,d.fire("load-perceived",n.response.body)};d.fire("start"),n.timestamp=Date.now(),n.perceivedPerformanceUpdater=gi(u=>{n.perceivedProgress=u,n.perceivedDuration=Date.now()-n.timestamp,I(),n.response&&n.perceivedProgress===1&&!n.complete&&p()},s?us(750,1500):0),n.request=e(c,f,u=>{n.response=k(u)?u:{type:"load",code:200,body:`${u}`,headers:{}},n.duration=Date.now()-n.timestamp,n.progress=1,d.fire("load",n.response.body),(!s||s&&n.perceivedProgress===1)&&p()},u=>{n.perceivedPerformanceUpdater.clear(),d.fire("error",k(u)?u:{type:"error",code:0,body:`${u}`})},(u,_,m)=>{n.duration=Date.now()-n.timestamp,n.progress=u?_/m:null,I()},()=>{n.perceivedPerformanceUpdater.clear(),d.fire("abort",n.response?n.response.body:null)},u=>{d.fire("transfer",u)})},a=()=>{n.request&&(n.perceivedPerformanceUpdater.clear(),n.request.abort&&n.request.abort(),n.complete=!0)},i=()=>{a(),n.complete=!1,n.perceivedProgress=0,n.progress=0,n.timestamp=null,n.perceivedDuration=0,n.duration=0,n.request=null,n.response=null},l=s?()=>n.progress?Math.min(n.progress,n.perceivedProgress):null:()=>n.progress||null,o=s?()=>Math.min(n.duration,n.perceivedDuration):()=>n.duration,d={...ut(),process:r,abort:a,getProgress:l,getDuration:o,reset:i};return d},fs=e=>e.substring(0,e.lastIndexOf("."))||e,Ri=e=>{let t=[e.name,e.size,e.type];return e instanceof Blob||Lt(e)?t[0]=e.name||ls():Lt(e)?(t[1]=e.length,t[2]=ds(e)):Q(e)&&(t[0]=We(e),t[1]=0,t[2]="application/octet-stream"),{name:t[0],size:t[1],type:t[2]}},De=e=>!!(e instanceof File||e instanceof Blob&&e.name),Es=e=>{if(!k(e))return e;const t=lt(e)?[]:{};for(const n in e){if(!e.hasOwnProperty(n))continue;const s=e[n];t[n]=s&&k(s)?Es(s):s}return t},Ai=(e=null,t=null,n=null)=>{const s=Bt(),r={archived:!1,frozen:!1,released:!1,source:null,file:n,serverFileReference:t,transferId:null,processingAborted:!1,status:t?G.PROCESSING_COMPLETE:G.INIT,activeLoader:null,activeProcessor:null};let a=null;const i={},l=D=>r.status=D,o=(D,...P)=>{r.released||r.frozen||L.fire(D,...P)},d=()=>ft(r.file.name),c=()=>r.file.type,f=()=>r.file.size,I=()=>r.file,p=(D,P,v)=>{if(r.source=D,L.fireSync("init"),r.file){L.fireSync("load-skip");return}r.file=Ri(D),P.on("init",()=>{o("load-init")}),P.on("meta",M=>{r.file.size=M.size,r.file.filename=M.filename,M.source&&(e=z.LIMBO,r.serverFileReference=M.source,r.status=G.PROCESSING_COMPLETE),o("load-meta")}),P.on("progress",M=>{l(G.LOADING),o("load-progress",M)}),P.on("error",M=>{l(G.LOAD_ERROR),o("load-request-error",M)}),P.on("abort",()=>{l(G.INIT),o("load-abort")}),P.on("load",M=>{r.activeLoader=null;const w=y=>{r.file=De(y)?y:r.file,e===z.LIMBO&&r.serverFileReference?l(G.PROCESSING_COMPLETE):l(G.IDLE),o("load")},x=y=>{r.file=M,o("load-meta"),l(G.LOAD_ERROR),o("load-file-error",y)};if(r.serverFileReference){w(M);return}v(M,w,x)}),P.setSource(D),r.activeLoader=P,P.load()},u=()=>{r.activeLoader&&r.activeLoader.load()},_=()=>{if(r.activeLoader){r.activeLoader.abort();return}l(G.INIT),o("load-abort")},m=(D,P)=>{if(r.processingAborted){r.processingAborted=!1;return}if(l(G.PROCESSING),a=null,!(r.file instanceof Blob)){L.on("load",()=>{m(D,P)});return}D.on("load",w=>{r.transferId=null,r.serverFileReference=w}),D.on("transfer",w=>{r.transferId=w}),D.on("load-perceived",w=>{r.activeProcessor=null,r.transferId=null,r.serverFileReference=w,l(G.PROCESSING_COMPLETE),o("process-complete",w)}),D.on("start",()=>{o("process-start")}),D.on("error",w=>{r.activeProcessor=null,l(G.PROCESSING_ERROR),o("process-error",w)}),D.on("abort",w=>{r.activeProcessor=null,r.serverFileReference=w,l(G.IDLE),o("process-abort"),a&&a()}),D.on("progress",w=>{o("process-progress",w)});const v=w=>{r.archived||D.process(w,{...i})},M=console.error;P(r.file,v,M),r.activeProcessor=D},g=()=>{r.processingAborted=!1,l(G.PROCESSING_QUEUED)},E=()=>new Promise(D=>{if(!r.activeProcessor){r.processingAborted=!0,l(G.IDLE),o("process-abort"),D();return}a=()=>{D()},r.activeProcessor.abort()}),T=(D,P)=>new Promise((v,M)=>{const w=r.serverFileReference!==null?r.serverFileReference:r.transferId;if(w===null){v();return}D(w,()=>{r.serverFileReference=null,r.transferId=null,v()},x=>{if(!P){v();return}l(G.PROCESSING_REVERT_ERROR),o("process-revert-error"),M(x)}),l(G.IDLE),o("process-revert")}),R=(D,P,v)=>{const M=D.split("."),w=M[0],x=M.pop();let y=i;M.forEach(F=>y=y[F]),JSON.stringify(y[x])!==JSON.stringify(P)&&(y[x]=P,o("metadata-update",{key:w,value:i[w],silent:v}))},L={id:{get:()=>s},origin:{get:()=>e,set:D=>e=D},serverId:{get:()=>r.serverFileReference},transferId:{get:()=>r.transferId},status:{get:()=>r.status},filename:{get:()=>r.file.name},filenameWithoutExtension:{get:()=>fs(r.file.name)},fileExtension:{get:d},fileType:{get:c},fileSize:{get:f},file:{get:I},relativePath:{get:()=>r.file._relativePath},source:{get:()=>r.source},getMetadata:D=>Es(D?i[D]:i),setMetadata:(D,P,v)=>{if(k(D)){const M=D;return Object.keys(M).forEach(w=>{R(w,M[w],P)}),D}return R(D,P,v),P},extend:(D,P)=>b[D]=P,abortLoad:_,retryLoad:u,requestProcessing:g,abortProcessing:E,load:p,process:m,revert:T,...ut(),freeze:()=>r.frozen=!0,release:()=>r.released=!0,released:{get:()=>r.released},archive:()=>r.archived=!0,archived:{get:()=>r.archived},setFile:D=>r.file=D},b=_e(L);return b},Oi=(e,t)=>Ee(t)?0:Q(t)?e.findIndex(n=>n.id===t):-1,pn=(e,t)=>{const n=Oi(e,t);if(!(n<0))return e[n]||null},In=(e,t,n,s,r,a)=>{const i=Ae(null,e,{method:"GET",responseType:"blob"});return i.onload=l=>{const o=l.getAllResponseHeaders(),d=Vt(o).name||We(e);t(W("load",l.status,ve(l.response,d),o))},i.onerror=l=>{n(W("error",l.status,l.statusText,l.getAllResponseHeaders()))},i.onheaders=l=>{a(W("headers",l.status,null,l.getAllResponseHeaders()))},i.ontimeout=Oe(n),i.onprogress=s,i.onabort=r,i},_n=e=>(e.indexOf("//")===0&&(e=location.protocol+e),e.toLowerCase().replace("blob:","").replace(/([a-z])?:\/\//,"$1").split("/")[0]),Di=e=>(e.indexOf(":")>-1||e.indexOf("//")>-1)&&_n(location.href)!==_n(e),ze=e=>(...t)=>he(e)?e(...t):e,Si=e=>!De(e.file),Tt=(e,t)=>{clearTimeout(t.listUpdateTimeout),t.listUpdateTimeout=setTimeout(()=>{e("DID_UPDATE_ITEMS",{items:le(t.items)})},0)},Tn=(e,...t)=>new Promise(n=>{if(!e)return n(!0);const s=e(...t);if(s==null)return n(!0);if(typeof s=="boolean")return n(s);typeof s.then=="function"&&s.then(n)}),mt=(e,t)=>{e.items.sort((n,s)=>t(J(n),J(s)))},se=(e,t)=>({query:n,success:s=()=>{},failure:r=()=>{},...a}={})=>{const i=Re(e.items,n);if(!i){r({error:W("error",0,"Item not found"),file:null});return}t(i,s,r,a||{})},yi=(e,t,n)=>({ABORT_ALL:()=>{le(n.items).forEach(s=>{s.freeze(),s.abortLoad(),s.abortProcessing()})},DID_SET_FILES:({value:s=[]})=>{const r=s.map(i=>({source:i.source?i.source:i,options:i.options}));let a=le(n.items);a.forEach(i=>{r.find(l=>l.source===i.source||l.source===i.file)||e("REMOVE_ITEM",{query:i,remove:!1})}),a=le(n.items),r.forEach((i,l)=>{a.find(o=>o.source===i.source||o.file===i.source)||e("ADD_ITEM",{...i,interactionMethod:ie.NONE,index:l})})},DID_UPDATE_ITEM_METADATA:({id:s,action:r,change:a})=>{a.silent||(clearTimeout(n.itemUpdateTimeout),n.itemUpdateTimeout=setTimeout(()=>{const i=pn(n.items,s);if(!t("IS_ASYNC")){oe("SHOULD_PREPARE_OUTPUT",!1,{item:i,query:t,action:r,change:a}).then(c=>{const f=t("GET_BEFORE_PREPARE_FILE");f&&(c=f(i,c)),c&&e("REQUEST_PREPARE_OUTPUT",{query:s,item:i,success:I=>{e("DID_PREPARE_OUTPUT",{id:s,file:I})}},!0)});return}i.origin===z.LOCAL&&e("DID_LOAD_ITEM",{id:i.id,error:null,serverFileReference:i.source});const l=()=>{setTimeout(()=>{e("REQUEST_ITEM_PROCESSING",{query:s})},32)},o=c=>{i.revert(Be(n.options.server.url,n.options.server.revert),t("GET_FORCE_REVERT")).then(c?l:()=>{}).catch(()=>{})},d=c=>{i.abortProcessing().then(c?l:()=>{})};if(i.status===G.PROCESSING_COMPLETE)return o(n.options.instantUpload);if(i.status===G.PROCESSING)return d(n.options.instantUpload);n.options.instantUpload&&l()},0))},MOVE_ITEM:({query:s,index:r})=>{const a=Re(n.items,s);if(!a)return;const i=n.items.indexOf(a);r=os(r,0,n.items.length-1),i!==r&&n.items.splice(r,0,n.items.splice(i,1)[0])},SORT:({compare:s})=>{mt(n,s),e("DID_SORT_ITEMS",{items:t("GET_ACTIVE_ITEMS")})},ADD_ITEMS:({items:s,index:r,interactionMethod:a,success:i=()=>{},failure:l=()=>{}})=>{let o=r;if(r===-1||typeof r>"u"){const p=t("GET_ITEM_INSERT_LOCATION"),u=t("GET_TOTAL_ITEMS");o=p==="before"?0:u}const d=t("GET_IGNORED_FILES"),c=p=>De(p)?!d.includes(p.name.toLowerCase()):!Ee(p),I=s.filter(c).map(p=>new Promise((u,_)=>{e("ADD_ITEM",{interactionMethod:a,source:p.source||p,success:u,failure:_,index:o++,options:p.options||{}})}));Promise.all(I).then(i).catch(l)},ADD_ITEM:({source:s,index:r=-1,interactionMethod:a,success:i=()=>{},failure:l=()=>{},options:o={}})=>{if(Ee(s)){l({error:W("error",0,"No source"),file:null});return}if(De(s)&&n.options.ignoredFiles.includes(s.name.toLowerCase()))return;if(!ni(n)){if(n.options.allowMultiple||!n.options.allowMultiple&&!n.options.allowReplace){const E=W("warning",0,"Max files");e("DID_THROW_MAX_FILES",{source:s,error:E}),l({error:E,file:null});return}const g=le(n.items)[0];if(g.status===G.PROCESSING_COMPLETE||g.status===G.PROCESSING_REVERT_ERROR){const E=t("GET_FORCE_REVERT");if(g.revert(Be(n.options.server.url,n.options.server.revert),E).then(()=>{E&&e("ADD_ITEM",{source:s,index:r,interactionMethod:a,success:i,failure:l,options:o})}).catch(()=>{}),E)return}e("REMOVE_ITEM",{query:g.id})}const d=o.type==="local"?z.LOCAL:o.type==="limbo"?z.LIMBO:z.INPUT,c=Ai(d,d===z.INPUT?null:s,o.file);Object.keys(o.metadata||{}).forEach(g=>{c.setMetadata(g,o.metadata[g])}),ye("DID_CREATE_ITEM",c,{query:t,dispatch:e});const f=t("GET_ITEM_INSERT_LOCATION");n.options.itemInsertLocationFreedom||(r=f==="before"?-1:n.items.length),ri(n.items,c,r),he(f)&&s&&mt(n,f);const I=c.id;c.on("init",()=>{e("DID_INIT_ITEM",{id:I})}),c.on("load-init",()=>{e("DID_START_ITEM_LOAD",{id:I})}),c.on("load-meta",()=>{e("DID_UPDATE_ITEM_META",{id:I})}),c.on("load-progress",g=>{e("DID_UPDATE_ITEM_LOAD_PROGRESS",{id:I,progress:g})}),c.on("load-request-error",g=>{const E=ze(n.options.labelFileLoadError)(g);if(g.code>=400&&g.code<500){e("DID_THROW_ITEM_INVALID",{id:I,error:g,status:{main:E,sub:`${g.code} (${g.body})`}}),l({error:g,file:J(c)});return}e("DID_THROW_ITEM_LOAD_ERROR",{id:I,error:g,status:{main:E,sub:n.options.labelTapToRetry}})}),c.on("load-file-error",g=>{e("DID_THROW_ITEM_INVALID",{id:I,error:g.status,status:g.status}),l({error:g.status,file:J(c)})}),c.on("load-abort",()=>{e("REMOVE_ITEM",{query:I})}),c.on("load-skip",()=>{c.on("metadata-update",g=>{De(c.file)&&e("DID_UPDATE_ITEM_METADATA",{id:I,change:g})}),e("COMPLETE_LOAD_ITEM",{query:I,item:c,data:{source:s,success:i}})}),c.on("load",()=>{const g=E=>{if(!E){e("REMOVE_ITEM",{query:I});return}c.on("metadata-update",T=>{e("DID_UPDATE_ITEM_METADATA",{id:I,change:T})}),oe("SHOULD_PREPARE_OUTPUT",!1,{item:c,query:t}).then(T=>{const R=t("GET_BEFORE_PREPARE_FILE");R&&(T=R(c,T));const O=()=>{e("COMPLETE_LOAD_ITEM",{query:I,item:c,data:{source:s,success:i}}),Tt(e,n)};if(T){e("REQUEST_PREPARE_OUTPUT",{query:I,item:c,success:L=>{e("DID_PREPARE_OUTPUT",{id:I,file:L}),O()}},!0);return}O()})};oe("DID_LOAD_ITEM",c,{query:t,dispatch:e}).then(()=>{Tn(t("GET_BEFORE_ADD_FILE"),J(c)).then(g)}).catch(E=>{if(!E||!E.error||!E.status)return g(!1);e("DID_THROW_ITEM_INVALID",{id:I,error:E.error,status:E.status})})}),c.on("process-start",()=>{e("DID_START_ITEM_PROCESSING",{id:I})}),c.on("process-progress",g=>{e("DID_UPDATE_ITEM_PROCESS_PROGRESS",{id:I,progress:g})}),c.on("process-error",g=>{e("DID_THROW_ITEM_PROCESSING_ERROR",{id:I,error:g,status:{main:ze(n.options.labelFileProcessingError)(g),sub:n.options.labelTapToRetry}})}),c.on("process-revert-error",g=>{e("DID_THROW_ITEM_PROCESSING_REVERT_ERROR",{id:I,error:g,status:{main:ze(n.options.labelFileProcessingRevertError)(g),sub:n.options.labelTapToRetry}})}),c.on("process-complete",g=>{e("DID_COMPLETE_ITEM_PROCESSING",{id:I,error:null,serverFileReference:g}),e("DID_DEFINE_VALUE",{id:I,value:g})}),c.on("process-abort",()=>{e("DID_ABORT_ITEM_PROCESSING",{id:I})}),c.on("process-revert",()=>{e("DID_REVERT_ITEM_PROCESSING",{id:I}),e("DID_DEFINE_VALUE",{id:I,value:null})}),e("DID_ADD_ITEM",{id:I,index:r,interactionMethod:a}),Tt(e,n);const{url:p,load:u,restore:_,fetch:m}=n.options.server||{};c.load(s,Ii(d===z.INPUT?Q(s)&&Di(s)&&m?_t(p,m):In:d===z.LIMBO?_t(p,_):_t(p,u)),(g,E,T)=>{oe("LOAD_FILE",g,{query:t}).then(E).catch(T)})},REQUEST_PREPARE_OUTPUT:({item:s,success:r,failure:a=()=>{}})=>{const i={error:W("error",0,"Item not found"),file:null};if(s.archived)return a(i);oe("PREPARE_OUTPUT",s.file,{query:t,item:s}).then(l=>{oe("COMPLETE_PREPARE_OUTPUT",l,{query:t,item:s}).then(o=>{if(s.archived)return a(i);r(o)})})},COMPLETE_LOAD_ITEM:({item:s,data:r})=>{const{success:a,source:i}=r,l=t("GET_ITEM_INSERT_LOCATION");if(he(l)&&i&&mt(n,l),e("DID_LOAD_ITEM",{id:s.id,error:null,serverFileReference:s.origin===z.INPUT?null:i}),a(J(s)),s.origin===z.LOCAL){e("DID_LOAD_LOCAL_ITEM",{id:s.id});return}if(s.origin===z.LIMBO){e("DID_COMPLETE_ITEM_PROCESSING",{id:s.id,error:null,serverFileReference:i}),e("DID_DEFINE_VALUE",{id:s.id,value:s.serverId||i});return}t("IS_ASYNC")&&n.options.instantUpload&&e("REQUEST_ITEM_PROCESSING",{query:s.id})},RETRY_ITEM_LOAD:se(n,s=>{s.retryLoad()}),REQUEST_ITEM_PREPARE:se(n,(s,r,a)=>{e("REQUEST_PREPARE_OUTPUT",{query:s.id,item:s,success:i=>{e("DID_PREPARE_OUTPUT",{id:s.id,file:i}),r({file:s,output:i})},failure:a},!0)}),REQUEST_ITEM_PROCESSING:se(n,(s,r,a)=>{if(!(s.status===G.IDLE||s.status===G.PROCESSING_ERROR)){const l=()=>e("REQUEST_ITEM_PROCESSING",{query:s,success:r,failure:a}),o=()=>document.hidden?l():setTimeout(l,32);s.status===G.PROCESSING_COMPLETE||s.status===G.PROCESSING_REVERT_ERROR?s.revert(Be(n.options.server.url,n.options.server.revert),t("GET_FORCE_REVERT")).then(o).catch(()=>{}):s.status===G.PROCESSING&&s.abortProcessing().then(o);return}s.status!==G.PROCESSING_QUEUED&&(s.requestProcessing(),e("DID_REQUEST_ITEM_PROCESSING",{id:s.id}),e("PROCESS_ITEM",{query:s,success:r,failure:a},!0))}),PROCESS_ITEM:se(n,(s,r,a)=>{const i=t("GET_MAX_PARALLEL_UPLOADS");if(t("GET_ITEMS_BY_STATUS",G.PROCESSING).length===i){n.processingQueue.push({id:s.id,success:r,failure:a});return}if(s.status===G.PROCESSING)return;const o=()=>{const c=n.processingQueue.shift();if(!c)return;const{id:f,success:I,failure:p}=c,u=Re(n.items,f);if(!u||u.archived){o();return}e("PROCESS_ITEM",{query:f,success:I,failure:p},!0)};s.onOnce("process-complete",()=>{r(J(s)),o();const c=n.options.server;if(n.options.instantUpload&&s.origin===z.LOCAL&&he(c.remove)){const p=()=>{};s.origin=z.LIMBO,n.options.server.remove(s.source,p,p)}t("GET_ITEMS_BY_STATUS",G.PROCESSING_COMPLETE).length===n.items.length&&e("DID_COMPLETE_ITEM_PROCESSING_ALL")}),s.onOnce("process-error",c=>{a({error:c,file:J(s)}),o()});const d=n.options;s.process(hi(mi(d.server.url,d.server.process,d.name,{chunkTransferId:s.transferId,chunkServer:d.server.patch,chunkUploads:d.chunkUploads,chunkForce:d.chunkForce,chunkSize:d.chunkSize,chunkRetryDelays:d.chunkRetryDelays}),{allowMinimumUploadDuration:t("GET_ALLOW_MINIMUM_UPLOAD_DURATION")}),(c,f,I)=>{oe("PREPARE_OUTPUT",c,{query:t,item:s}).then(p=>{e("DID_PREPARE_OUTPUT",{id:s.id,file:p}),f(p)}).catch(I)})}),RETRY_ITEM_PROCESSING:se(n,s=>{e("REQUEST_ITEM_PROCESSING",{query:s})}),REQUEST_REMOVE_ITEM:se(n,s=>{Tn(t("GET_BEFORE_REMOVE_FILE"),J(s)).then(r=>{r&&e("REMOVE_ITEM",{query:s})})}),RELEASE_ITEM:se(n,s=>{s.release()}),REMOVE_ITEM:se(n,(s,r,a,i)=>{const l=()=>{const d=s.id;pn(n.items,d).archive(),e("DID_REMOVE_ITEM",{error:null,id:d,item:s}),Tt(e,n),r(J(s))},o=n.options.server;s.origin===z.LOCAL&&o&&he(o.remove)&&i.remove!==!1?(e("DID_START_ITEM_REMOVE",{id:s.id}),o.remove(s.source,()=>l(),d=>{e("DID_THROW_ITEM_REMOVE_ERROR",{id:s.id,error:W("error",0,d,null),status:{main:ze(n.options.labelFileRemoveError)(d),sub:n.options.labelTapToRetry}})})):((i.revert&&s.origin!==z.LOCAL&&s.serverId!==null||n.options.chunkUploads&&s.file.size>n.options.chunkSize||n.options.chunkUploads&&n.options.chunkForce)&&s.revert(Be(n.options.server.url,n.options.server.revert),t("GET_FORCE_REVERT")),l())}),ABORT_ITEM_LOAD:se(n,s=>{s.abortLoad()}),ABORT_ITEM_PROCESSING:se(n,s=>{if(s.serverId){e("REVERT_ITEM_PROCESSING",{id:s.id});return}s.abortProcessing().then(()=>{n.options.instantUpload&&e("REMOVE_ITEM",{query:s.id})})}),REQUEST_REVERT_ITEM_PROCESSING:se(n,s=>{if(!n.options.instantUpload){e("REVERT_ITEM_PROCESSING",{query:s});return}const r=l=>{l&&e("REVERT_ITEM_PROCESSING",{query:s})},a=t("GET_BEFORE_REMOVE_FILE");if(!a)return r(!0);const i=a(J(s));if(i==null)return r(!0);if(typeof i=="boolean")return r(i);typeof i.then=="function"&&i.then(r)}),REVERT_ITEM_PROCESSING:se(n,s=>{s.revert(Be(n.options.server.url,n.options.server.revert),t("GET_FORCE_REVERT")).then(()=>{(n.options.instantUpload||Si(s))&&e("REMOVE_ITEM",{query:s.id})}).catch(()=>{})}),SET_OPTIONS:({options:s})=>{const r=Object.keys(s),a=Li.filter(l=>r.includes(l));[...a,...Object.keys(s).filter(l=>!a.includes(l))].forEach(l=>{e(`SET_${dt(l,"_").toUpperCase()}`,{value:s[l]})})}}),Li=["server"],Ht=e=>e,pe=e=>document.createElement(e),Y=(e,t)=>{let n=e.childNodes[0];n?t!==n.nodeValue&&(n.nodeValue=t):(n=document.createTextNode(t),e.appendChild(n))},mn=(e,t,n,s)=>{const r=(s%360-90)*Math.PI/180;return{x:e+n*Math.cos(r),y:t+n*Math.sin(r)}},Pi=(e,t,n,s,r,a)=>{const i=mn(e,t,n,r),l=mn(e,t,n,s);return["M",i.x,i.y,"A",n,n,0,a,0,l.x,l.y].join(" ")},Mi=(e,t,n,s,r)=>{let a=1;return r>s&&r-s<=.5&&(a=0),s>r&&s-r>=.5&&(a=0),Pi(e,t,n,Math.min(.9999,s)*360,Math.min(.9999,r)*360,a)},wi=({root:e,props:t})=>{t.spin=!1,t.progress=0,t.opacity=0;const n=st("svg");e.ref.path=st("path",{"stroke-width":2,"stroke-linecap":"round"}),n.appendChild(e.ref.path),e.ref.svg=n,e.appendChild(n)},Ci=({root:e,props:t})=>{if(t.opacity===0)return;t.align&&(e.element.dataset.align=t.align);const n=parseInt($(e.ref.path,"stroke-width"),10),s=e.rect.element.width*.5;let r=0,a=0;t.spin?(r=0,a=.5):(r=0,a=t.progress);const i=Mi(s,s,s-n,r,a);$(e.ref.path,"d",i),$(e.ref.path,"stroke-opacity",t.spin||t.progress>0?1:0)},gn=q({tag:"div",name:"progress-indicator",ignoreRectUpdate:!0,ignoreRect:!0,create:wi,write:Ci,mixins:{apis:["progress","spin","align"],styles:["opacity"],animations:{opacity:{type:"tween",duration:500},progress:{type:"spring",stiffness:.95,damping:.65,mass:10}}}}),bi=({root:e,props:t})=>{e.element.innerHTML=(t.icon||"")+`<span>${t.label}</span>`,t.isDisabled=!1},Ni=({root:e,props:t})=>{const{isDisabled:n}=t,s=e.query("GET_DISABLED")||t.opacity===0;s&&!n?(t.isDisabled=!0,$(e.element,"disabled","disabled")):!s&&n&&(t.isDisabled=!1,e.element.removeAttribute("disabled"))},ps=q({tag:"button",attributes:{type:"button"},ignoreRect:!0,ignoreRectUpdate:!0,name:"file-action-button",mixins:{apis:["label"],styles:["translateX","translateY","scaleX","scaleY","opacity"],animations:{scaleX:"spring",scaleY:"spring",translateX:"spring",translateY:"spring",opacity:{type:"tween",duration:250}},listeners:!0},create:bi,write:Ni}),Is=(e,t=".",n=1e3,s={})=>{const{labelBytes:r="bytes",labelKilobytes:a="KB",labelMegabytes:i="MB",labelGigabytes:l="GB"}=s;e=Math.round(Math.abs(e));const o=n,d=n*n,c=n*n*n;return e<o?`${e} ${r}`:e<d?`${Math.floor(e/o)} ${a}`:e<c?`${hn(e/d,1,t)} ${i}`:`${hn(e/c,2,t)} ${l}`},hn=(e,t,n)=>e.toFixed(t).split(".").filter(s=>s!=="0").join(n),vi=({root:e,props:t})=>{const n=pe("span");n.className="filepond--file-info-main",$(n,"aria-hidden","true"),e.appendChild(n),e.ref.fileName=n;const s=pe("span");s.className="filepond--file-info-sub",e.appendChild(s),e.ref.fileSize=s,Y(s,e.query("GET_LABEL_FILE_WAITING_FOR_SIZE")),Y(n,Ht(e.query("GET_ITEM_NAME",t.id)))},Pt=({root:e,props:t})=>{Y(e.ref.fileSize,Is(e.query("GET_ITEM_SIZE",t.id),".",e.query("GET_FILE_SIZE_BASE"),e.query("GET_FILE_SIZE_LABELS",e.query))),Y(e.ref.fileName,Ht(e.query("GET_ITEM_NAME",t.id)))},Rn=({root:e,props:t})=>{if(Ge(e.query("GET_ITEM_SIZE",t.id))){Pt({root:e,props:t});return}Y(e.ref.fileSize,e.query("GET_LABEL_FILE_SIZE_NOT_AVAILABLE"))},Gi=q({name:"file-info",ignoreRect:!0,ignoreRectUpdate:!0,write:Z({DID_LOAD_ITEM:Pt,DID_UPDATE_ITEM_META:Pt,DID_THROW_ITEM_LOAD_ERROR:Rn,DID_THROW_ITEM_INVALID:Rn}),didCreateView:e=>{ye("CREATE_VIEW",{...e,view:e})},create:vi,mixins:{styles:["translateX","translateY"],animations:{translateX:"spring",translateY:"spring"}}}),_s=e=>Math.round(e*100),Fi=({root:e})=>{const t=pe("span");t.className="filepond--file-status-main",e.appendChild(t),e.ref.main=t;const n=pe("span");n.className="filepond--file-status-sub",e.appendChild(n),e.ref.sub=n,Ts({root:e,action:{progress:null}})},Ts=({root:e,action:t})=>{const n=t.progress===null?e.query("GET_LABEL_FILE_LOADING"):`${e.query("GET_LABEL_FILE_LOADING")} ${_s(t.progress)}%`;Y(e.ref.main,n),Y(e.ref.sub,e.query("GET_LABEL_TAP_TO_CANCEL"))},Bi=({root:e,action:t})=>{const n=t.progress===null?e.query("GET_LABEL_FILE_PROCESSING"):`${e.query("GET_LABEL_FILE_PROCESSING")} ${_s(t.progress)}%`;Y(e.ref.main,n),Y(e.ref.sub,e.query("GET_LABEL_TAP_TO_CANCEL"))},Ui=({root:e})=>{Y(e.ref.main,e.query("GET_LABEL_FILE_PROCESSING")),Y(e.ref.sub,e.query("GET_LABEL_TAP_TO_CANCEL"))},xi=({root:e})=>{Y(e.ref.main,e.query("GET_LABEL_FILE_PROCESSING_ABORTED")),Y(e.ref.sub,e.query("GET_LABEL_TAP_TO_RETRY"))},Vi=({root:e})=>{Y(e.ref.main,e.query("GET_LABEL_FILE_PROCESSING_COMPLETE")),Y(e.ref.sub,e.query("GET_LABEL_TAP_TO_UNDO"))},An=({root:e})=>{Y(e.ref.main,""),Y(e.ref.sub,"")},Ue=({root:e,action:t})=>{Y(e.ref.main,t.status.main),Y(e.ref.sub,t.status.sub)},Hi=q({name:"file-status",ignoreRect:!0,ignoreRectUpdate:!0,write:Z({DID_LOAD_ITEM:An,DID_REVERT_ITEM_PROCESSING:An,DID_REQUEST_ITEM_PROCESSING:Ui,DID_ABORT_ITEM_PROCESSING:xi,DID_COMPLETE_ITEM_PROCESSING:Vi,DID_UPDATE_ITEM_PROCESS_PROGRESS:Bi,DID_UPDATE_ITEM_LOAD_PROGRESS:Ts,DID_THROW_ITEM_LOAD_ERROR:Ue,DID_THROW_ITEM_INVALID:Ue,DID_THROW_ITEM_PROCESSING_ERROR:Ue,DID_THROW_ITEM_PROCESSING_REVERT_ERROR:Ue,DID_THROW_ITEM_REMOVE_ERROR:Ue}),didCreateView:e=>{ye("CREATE_VIEW",{...e,view:e})},create:Fi,mixins:{styles:["translateX","translateY","opacity"],animations:{opacity:{type:"tween",duration:250},translateX:"spring",translateY:"spring"}}}),Mt={AbortItemLoad:{label:"GET_LABEL_BUTTON_ABORT_ITEM_LOAD",action:"ABORT_ITEM_LOAD",className:"filepond--action-abort-item-load",align:"LOAD_INDICATOR_POSITION"},RetryItemLoad:{label:"GET_LABEL_BUTTON_RETRY_ITEM_LOAD",action:"RETRY_ITEM_LOAD",icon:"GET_ICON_RETRY",className:"filepond--action-retry-item-load",align:"BUTTON_PROCESS_ITEM_POSITION"},RemoveItem:{label:"GET_LABEL_BUTTON_REMOVE_ITEM",action:"REQUEST_REMOVE_ITEM",icon:"GET_ICON_REMOVE",className:"filepond--action-remove-item",align:"BUTTON_REMOVE_ITEM_POSITION"},ProcessItem:{label:"GET_LABEL_BUTTON_PROCESS_ITEM",action:"REQUEST_ITEM_PROCESSING",icon:"GET_ICON_PROCESS",className:"filepond--action-process-item",align:"BUTTON_PROCESS_ITEM_POSITION"},AbortItemProcessing:{label:"GET_LABEL_BUTTON_ABORT_ITEM_PROCESSING",action:"ABORT_ITEM_PROCESSING",className:"filepond--action-abort-item-processing",align:"BUTTON_PROCESS_ITEM_POSITION"},RetryItemProcessing:{label:"GET_LABEL_BUTTON_RETRY_ITEM_PROCESSING",action:"RETRY_ITEM_PROCESSING",icon:"GET_ICON_RETRY",className:"filepond--action-retry-item-processing",align:"BUTTON_PROCESS_ITEM_POSITION"},RevertItemProcessing:{label:"GET_LABEL_BUTTON_UNDO_ITEM_PROCESSING",action:"REQUEST_REVERT_ITEM_PROCESSING",icon:"GET_ICON_UNDO",className:"filepond--action-revert-item-processing",align:"BUTTON_PROCESS_ITEM_POSITION"}},wt=[];H(Mt,e=>{wt.push(e)});const te=e=>{if(Ct(e)==="right")return 0;const t=e.ref.buttonRemoveItem.rect.element;return t.hidden?null:t.width+t.left},Wi=e=>e.ref.buttonAbortItemLoad.rect.element.width,ke=e=>Math.floor(e.ref.buttonRemoveItem.rect.element.height/4),Yi=e=>Math.floor(e.ref.buttonRemoveItem.rect.element.left/2),qi=e=>e.query("GET_STYLE_LOAD_INDICATOR_POSITION"),$i=e=>e.query("GET_STYLE_PROGRESS_INDICATOR_POSITION"),Ct=e=>e.query("GET_STYLE_BUTTON_REMOVE_ITEM_POSITION"),zi={buttonAbortItemLoad:{opacity:0},buttonRetryItemLoad:{opacity:0},buttonRemoveItem:{opacity:0},buttonProcessItem:{opacity:0},buttonAbortItemProcessing:{opacity:0},buttonRetryItemProcessing:{opacity:0},buttonRevertItemProcessing:{opacity:0},loadProgressIndicator:{opacity:0,align:qi},processProgressIndicator:{opacity:0,align:$i},processingCompleteIndicator:{opacity:0,scaleX:.75,scaleY:.75},info:{translateX:0,translateY:0,opacity:0},status:{translateX:0,translateY:0,opacity:0}},On={buttonRemoveItem:{opacity:1},buttonProcessItem:{opacity:1},info:{translateX:te},status:{translateX:te}},gt={buttonAbortItemProcessing:{opacity:1},processProgressIndicator:{opacity:1},status:{opacity:1}},Ce={DID_THROW_ITEM_INVALID:{buttonRemoveItem:{opacity:1},info:{translateX:te},status:{translateX:te,opacity:1}},DID_START_ITEM_LOAD:{buttonAbortItemLoad:{opacity:1},loadProgressIndicator:{opacity:1},status:{opacity:1}},DID_THROW_ITEM_LOAD_ERROR:{buttonRetryItemLoad:{opacity:1},buttonRemoveItem:{opacity:1},info:{translateX:te},status:{opacity:1}},DID_START_ITEM_REMOVE:{processProgressIndicator:{opacity:1,align:Ct},info:{translateX:te},status:{opacity:0}},DID_THROW_ITEM_REMOVE_ERROR:{processProgressIndicator:{opacity:0,align:Ct},buttonRemoveItem:{opacity:1},info:{translateX:te},status:{opacity:1,translateX:te}},DID_LOAD_ITEM:On,DID_LOAD_LOCAL_ITEM:{buttonRemoveItem:{opacity:1},info:{translateX:te},status:{translateX:te}},DID_START_ITEM_PROCESSING:gt,DID_REQUEST_ITEM_PROCESSING:gt,DID_UPDATE_ITEM_PROCESS_PROGRESS:gt,DID_COMPLETE_ITEM_PROCESSING:{buttonRevertItemProcessing:{opacity:1},info:{opacity:1},status:{opacity:1}},DID_THROW_ITEM_PROCESSING_ERROR:{buttonRemoveItem:{opacity:1},buttonRetryItemProcessing:{opacity:1},status:{opacity:1},info:{translateX:te}},DID_THROW_ITEM_PROCESSING_REVERT_ERROR:{buttonRevertItemProcessing:{opacity:1},status:{opacity:1},info:{opacity:1}},DID_ABORT_ITEM_PROCESSING:{buttonRemoveItem:{opacity:1},buttonProcessItem:{opacity:1},info:{translateX:te},status:{opacity:1}},DID_REVERT_ITEM_PROCESSING:On},ki=q({create:({root:e})=>{e.element.innerHTML=e.query("GET_ICON_DONE")},name:"processing-complete-indicator",ignoreRect:!0,mixins:{styles:["scaleX","scaleY","opacity"],animations:{scaleX:"spring",scaleY:"spring",opacity:{type:"tween",duration:250}}}}),Xi=({root:e,props:t})=>{const n=Object.keys(Mt).reduce((u,_)=>(u[_]={...Mt[_]},u),{}),{id:s}=t,r=e.query("GET_ALLOW_REVERT"),a=e.query("GET_ALLOW_REMOVE"),i=e.query("GET_ALLOW_PROCESS"),l=e.query("GET_INSTANT_UPLOAD"),o=e.query("IS_ASYNC"),d=e.query("GET_STYLE_BUTTON_REMOVE_ITEM_ALIGN");let c;o?i&&!r?c=u=>!/RevertItemProcessing/.test(u):!i&&r?c=u=>!/ProcessItem|RetryItemProcessing|AbortItemProcessing/.test(u):!i&&!r&&(c=u=>!/Process/.test(u)):c=u=>!/Process/.test(u);const f=c?wt.filter(c):wt.concat();if(l&&r&&(n.RevertItemProcessing.label="GET_LABEL_BUTTON_REMOVE_ITEM",n.RevertItemProcessing.icon="GET_ICON_REMOVE"),o&&!r){const u=Ce.DID_COMPLETE_ITEM_PROCESSING;u.info.translateX=Yi,u.info.translateY=ke,u.status.translateY=ke,u.processingCompleteIndicator={opacity:1,scaleX:1,scaleY:1}}if(o&&!i&&(["DID_START_ITEM_PROCESSING","DID_REQUEST_ITEM_PROCESSING","DID_UPDATE_ITEM_PROCESS_PROGRESS","DID_THROW_ITEM_PROCESSING_ERROR"].forEach(u=>{Ce[u].status.translateY=ke}),Ce.DID_THROW_ITEM_PROCESSING_ERROR.status.translateX=Wi),d&&r){n.RevertItemProcessing.align="BUTTON_REMOVE_ITEM_POSITION";const u=Ce.DID_COMPLETE_ITEM_PROCESSING;u.info.translateX=te,u.status.translateY=ke,u.processingCompleteIndicator={opacity:1,scaleX:1,scaleY:1}}a||(n.RemoveItem.disabled=!0),H(n,(u,_)=>{const m=e.createChildView(ps,{label:e.query(_.label),icon:e.query(_.icon),opacity:0});f.includes(u)&&e.appendChildView(m),_.disabled&&(m.element.setAttribute("disabled","disabled"),m.element.setAttribute("hidden","hidden")),m.element.dataset.align=e.query(`GET_STYLE_${_.align}`),m.element.classList.add(_.className),m.on("click",g=>{g.stopPropagation(),!_.disabled&&e.dispatch(_.action,{query:s})}),e.ref[`button${u}`]=m}),e.ref.processingCompleteIndicator=e.appendChildView(e.createChildView(ki)),e.ref.processingCompleteIndicator.element.dataset.align=e.query("GET_STYLE_BUTTON_PROCESS_ITEM_POSITION"),e.ref.info=e.appendChildView(e.createChildView(Gi,{id:s})),e.ref.status=e.appendChildView(e.createChildView(Hi,{id:s}));const I=e.appendChildView(e.createChildView(gn,{opacity:0,align:e.query("GET_STYLE_LOAD_INDICATOR_POSITION")}));I.element.classList.add("filepond--load-indicator"),e.ref.loadProgressIndicator=I;const p=e.appendChildView(e.createChildView(gn,{opacity:0,align:e.query("GET_STYLE_PROGRESS_INDICATOR_POSITION")}));p.element.classList.add("filepond--process-indicator"),e.ref.processProgressIndicator=p,e.ref.activeStyles=[]},ji=({root:e,actions:t,props:n})=>{Qi({root:e,actions:t,props:n});let s=t.concat().filter(r=>/^DID_/.test(r.type)).reverse().find(r=>Ce[r.type]);if(s){e.ref.activeStyles=[];const r=Ce[s.type];H(zi,(a,i)=>{const l=e.ref[a];H(i,(o,d)=>{const c=r[a]&&typeof r[a][o]<"u"?r[a][o]:d;e.ref.activeStyles.push({control:l,key:o,value:c})})})}e.ref.activeStyles.forEach(({control:r,key:a,value:i})=>{r[a]=typeof i=="function"?i(e):i})},Qi=Z({DID_SET_LABEL_BUTTON_ABORT_ITEM_PROCESSING:({root:e,action:t})=>{e.ref.buttonAbortItemProcessing.label=t.value},DID_SET_LABEL_BUTTON_ABORT_ITEM_LOAD:({root:e,action:t})=>{e.ref.buttonAbortItemLoad.label=t.value},DID_SET_LABEL_BUTTON_ABORT_ITEM_REMOVAL:({root:e,action:t})=>{e.ref.buttonAbortItemRemoval.label=t.value},DID_REQUEST_ITEM_PROCESSING:({root:e})=>{e.ref.processProgressIndicator.spin=!0,e.ref.processProgressIndicator.progress=0},DID_START_ITEM_LOAD:({root:e})=>{e.ref.loadProgressIndicator.spin=!0,e.ref.loadProgressIndicator.progress=0},DID_START_ITEM_REMOVE:({root:e})=>{e.ref.processProgressIndicator.spin=!0,e.ref.processProgressIndicator.progress=0},DID_UPDATE_ITEM_LOAD_PROGRESS:({root:e,action:t})=>{e.ref.loadProgressIndicator.spin=!1,e.ref.loadProgressIndicator.progress=t.progress},DID_UPDATE_ITEM_PROCESS_PROGRESS:({root:e,action:t})=>{e.ref.processProgressIndicator.spin=!1,e.ref.processProgressIndicator.progress=t.progress}}),Zi=q({create:Xi,write:ji,didCreateView:e=>{ye("CREATE_VIEW",{...e,view:e})},name:"file"}),Ki=({root:e,props:t})=>{e.ref.fileName=pe("legend"),e.appendChild(e.ref.fileName),e.ref.file=e.appendChildView(e.createChildView(Zi,{id:t.id})),e.ref.data=!1},Ji=({root:e,props:t})=>{Y(e.ref.fileName,Ht(e.query("GET_ITEM_NAME",t.id)))},ea=q({create:Ki,ignoreRect:!0,write:Z({DID_LOAD_ITEM:Ji}),didCreateView:e=>{ye("CREATE_VIEW",{...e,view:e})},tag:"fieldset",name:"file-wrapper"}),Dn={type:"spring",damping:.6,mass:7},ta=({root:e,props:t})=>{[{name:"top"},{name:"center",props:{translateY:null,scaleY:null},mixins:{animations:{scaleY:Dn},styles:["translateY","scaleY"]}},{name:"bottom",props:{translateY:null},mixins:{animations:{translateY:Dn},styles:["translateY"]}}].forEach(n=>{na(e,n,t.name)}),e.element.classList.add(`filepond--${t.name}`),e.ref.scalable=null},na=(e,t,n)=>{const s=q({name:`panel-${t.name} filepond--${n}`,mixins:t.mixins,ignoreRectUpdate:!0}),r=e.createChildView(s,t.props);e.ref[t.name]=e.appendChildView(r)},sa=({root:e,props:t})=>{if((e.ref.scalable===null||t.scalable!==e.ref.scalable)&&(e.ref.scalable=Jn(t.scalable)?t.scalable:!0,e.element.dataset.scalable=e.ref.scalable),!t.height)return;const n=e.ref.top.rect.element,s=e.ref.bottom.rect.element,r=Math.max(n.height+s.height,t.height);e.ref.center.translateY=n.height,e.ref.center.scaleY=(r-n.height-s.height)/100,e.ref.bottom.translateY=r-s.height},ms=q({name:"panel",read:({root:e,props:t})=>t.heightCurrent=e.ref.bottom.translateY,write:sa,create:ta,ignoreRect:!0,mixins:{apis:["height","heightCurrent","scalable"]}}),ra=e=>{const t=e.map(s=>s.id);let n;return{setIndex:s=>{n=s},getIndex:()=>n,getItemIndex:s=>t.indexOf(s.id)}},Sn={type:"spring",stiffness:.75,damping:.45,mass:10},yn="spring",Ln={DID_START_ITEM_LOAD:"busy",DID_UPDATE_ITEM_LOAD_PROGRESS:"loading",DID_THROW_ITEM_INVALID:"load-invalid",DID_THROW_ITEM_LOAD_ERROR:"load-error",DID_LOAD_ITEM:"idle",DID_THROW_ITEM_REMOVE_ERROR:"remove-error",DID_START_ITEM_REMOVE:"busy",DID_START_ITEM_PROCESSING:"busy processing",DID_REQUEST_ITEM_PROCESSING:"busy processing",DID_UPDATE_ITEM_PROCESS_PROGRESS:"processing",DID_COMPLETE_ITEM_PROCESSING:"processing-complete",DID_THROW_ITEM_PROCESSING_ERROR:"processing-error",DID_THROW_ITEM_PROCESSING_REVERT_ERROR:"processing-revert-error",DID_ABORT_ITEM_PROCESSING:"cancelled",DID_REVERT_ITEM_PROCESSING:"idle"},ia=({root:e,props:t})=>{if(e.ref.handleClick=s=>e.dispatch("DID_ACTIVATE_ITEM",{id:t.id}),e.element.id=`filepond--item-${t.id}`,e.element.addEventListener("click",e.ref.handleClick),e.ref.container=e.appendChildView(e.createChildView(ea,{id:t.id})),e.ref.panel=e.appendChildView(e.createChildView(ms,{name:"item-panel"})),e.ref.panel.height=null,t.markedForRemoval=!1,!e.query("GET_ALLOW_REORDER"))return;e.element.dataset.dragState="idle";const n=s=>{if(!s.isPrimary)return;let r=!1;const a={x:s.pageX,y:s.pageY};t.dragOrigin={x:e.translateX,y:e.translateY},t.dragCenter={x:s.offsetX,y:s.offsetY};const i=ra(e.query("GET_ACTIVE_ITEMS"));e.dispatch("DID_GRAB_ITEM",{id:t.id,dragState:i});const l=f=>{if(!f.isPrimary)return;f.stopPropagation(),f.preventDefault(),t.dragOffset={x:f.pageX-a.x,y:f.pageY-a.y},t.dragOffset.x*t.dragOffset.x+t.dragOffset.y*t.dragOffset.y>16&&!r&&(r=!0,e.element.removeEventListener("click",e.ref.handleClick)),e.dispatch("DID_DRAG_ITEM",{id:t.id,dragState:i})},o=f=>{f.isPrimary&&(t.dragOffset={x:f.pageX-a.x,y:f.pageY-a.y},c())},d=()=>{c()},c=()=>{document.removeEventListener("pointercancel",d),document.removeEventListener("pointermove",l),document.removeEventListener("pointerup",o),e.dispatch("DID_DROP_ITEM",{id:t.id,dragState:i}),r&&setTimeout(()=>e.element.addEventListener("click",e.ref.handleClick),0)};document.addEventListener("pointercancel",d),document.addEventListener("pointermove",l),document.addEventListener("pointerup",o)};e.element.addEventListener("pointerdown",n)},aa=Z({DID_UPDATE_PANEL_HEIGHT:({root:e,action:t})=>{e.height=t.height}}),oa=Z({DID_GRAB_ITEM:({root:e,props:t})=>{t.dragOrigin={x:e.translateX,y:e.translateY}},DID_DRAG_ITEM:({root:e})=>{e.element.dataset.dragState="drag"},DID_DROP_ITEM:({root:e,props:t})=>{t.dragOffset=null,t.dragOrigin=null,e.element.dataset.dragState="drop"}},({root:e,actions:t,props:n,shouldOptimize:s})=>{e.element.dataset.dragState==="drop"&&e.scaleX<=1&&(e.element.dataset.dragState="idle");let r=t.concat().filter(i=>/^DID_/.test(i.type)).reverse().find(i=>Ln[i.type]);r&&r.type!==n.currentState&&(n.currentState=r.type,e.element.dataset.filepondItemState=Ln[n.currentState]||"");const a=e.query("GET_ITEM_PANEL_ASPECT_RATIO")||e.query("GET_PANEL_ASPECT_RATIO");a?s||(e.height=e.rect.element.width*a):(aa({root:e,actions:t,props:n}),!e.height&&e.ref.container.rect.element.height>0&&(e.height=e.ref.container.rect.element.height)),s&&(e.ref.panel.height=null),e.ref.panel.height=e.height}),la=q({create:ia,write:oa,destroy:({root:e,props:t})=>{e.element.removeEventListener("click",e.ref.handleClick),e.dispatch("RELEASE_ITEM",{query:t.id})},tag:"li",name:"item",mixins:{apis:["id","interactionMethod","markedForRemoval","spawnDate","dragCenter","dragOrigin","dragOffset"],styles:["translateX","translateY","scaleX","scaleY","opacity","height"],animations:{scaleX:yn,scaleY:yn,translateX:Sn,translateY:Sn,opacity:{type:"tween",duration:150}}}});var Wt=(e,t)=>Math.max(1,Math.floor((e+1)/t));const Yt=(e,t,n)=>{if(!n)return;const s=e.rect.element.width,r=t.length;let a=null;if(r===0||n.top<t[0].rect.element.top)return-1;const l=t[0].rect.element,o=l.marginLeft+l.marginRight,d=l.width+o,c=Wt(s,d);if(c===1){for(let p=0;p<r;p++){const u=t[p],_=u.rect.outer.top+u.rect.element.height*.5;if(n.top<_)return p}return r}const f=l.marginTop+l.marginBottom,I=l.height+f;for(let p=0;p<r;p++){const u=p%c,_=Math.floor(p/c),m=u*d,g=_*I,E=g-l.marginTop,T=m+d,R=g+I+l.marginBottom;if(n.top<R&&n.top>E){if(n.left<T)return p;p!==r-1?a=p:a=null}}return a!==null?a:r},Xe={height:0,width:0,get getHeight(){return this.height},set setHeight(e){(this.height===0||e===0)&&(this.height=e)},get getWidth(){return this.width},set setWidth(e){(this.width===0||e===0)&&(this.width=e)},setDimensions:function(e,t){(this.height===0||e===0)&&(this.height=e),(this.width===0||t===0)&&(this.width=t)}},ca=({root:e})=>{$(e.element,"role","list"),e.ref.lastItemSpanwDate=Date.now()},da=({root:e,action:t})=>{const{id:n,index:s,interactionMethod:r}=t;e.ref.addIndex=s;const a=Date.now();let i=a,l=1;if(r!==ie.NONE){l=0;const o=e.query("GET_ITEM_INSERT_INTERVAL"),d=a-e.ref.lastItemSpanwDate;i=d<o?a+(o-d):a}e.ref.lastItemSpanwDate=i,e.appendChildView(e.createChildView(la,{spawnDate:i,id:n,opacity:l,interactionMethod:r}),s)},Pn=(e,t,n,s=0,r=1)=>{e.dragOffset?(e.translateX=null,e.translateY=null,e.translateX=e.dragOrigin.x+e.dragOffset.x,e.translateY=e.dragOrigin.y+e.dragOffset.y,e.scaleX=1.025,e.scaleY=1.025):(e.translateX=t,e.translateY=n,Date.now()>e.spawnDate&&(e.opacity===0&&ua(e,t,n,s,r),e.scaleX=1,e.scaleY=1,e.opacity=1))},ua=(e,t,n,s,r)=>{e.interactionMethod===ie.NONE?(e.translateX=null,e.translateX=t,e.translateY=null,e.translateY=n):e.interactionMethod===ie.DROP?(e.translateX=null,e.translateX=t-s*20,e.translateY=null,e.translateY=n-r*10,e.scaleX=.8,e.scaleY=.8):e.interactionMethod===ie.BROWSE?(e.translateY=null,e.translateY=n-30):e.interactionMethod===ie.API&&(e.translateX=null,e.translateX=t-30,e.translateY=null)},fa=({root:e,action:t})=>{const{id:n}=t,s=e.childViews.find(r=>r.id===n);s&&(s.scaleX=.9,s.scaleY=.9,s.opacity=0,s.markedForRemoval=!0)},ht=e=>e.rect.element.height+e.rect.element.marginBottom*.5+e.rect.element.marginTop*.5,Ea=e=>e.rect.element.width+e.rect.element.marginLeft*.5+e.rect.element.marginRight*.5,pa=({root:e,action:t})=>{const{id:n,dragState:s}=t,r=e.query("GET_ITEM",{id:n}),a=e.childViews.find(m=>m.id===n),i=e.childViews.length,l=s.getItemIndex(r);if(!a)return;const o={x:a.dragOrigin.x+a.dragOffset.x+a.dragCenter.x,y:a.dragOrigin.y+a.dragOffset.y+a.dragCenter.y},d=ht(a),c=Ea(a);let f=Math.floor(e.rect.outer.width/c);f>i&&(f=i);const I=Math.floor(i/f+1);Xe.setHeight=d*I,Xe.setWidth=c*f;var p={y:Math.floor(o.y/d),x:Math.floor(o.x/c),getGridIndex:function(){return o.y>Xe.getHeight||o.y<0||o.x>Xe.getWidth||o.x<0?l:this.y*f+this.x},getColIndex:function(){const g=e.query("GET_ACTIVE_ITEMS"),E=e.childViews.filter(M=>M.rect.element.height),T=g.map(M=>E.find(w=>w.id===M.id)),R=T.findIndex(M=>M===a),O=ht(a),L=T.length;let b=L,D=0,P=0,v=0;for(let M=0;M<L;M++)if(D=ht(T[M]),v=P,P=v+D,o.y<P){if(R>M){if(o.y<v+O){b=M;break}continue}b=M;break}return b}};const u=f>1?p.getGridIndex():p.getColIndex();e.dispatch("MOVE_ITEM",{query:a,index:u});const _=s.getIndex();if(_===void 0||_!==u){if(s.setIndex(u),_===void 0)return;e.dispatch("DID_REORDER_ITEMS",{items:e.query("GET_ACTIVE_ITEMS"),origin:l,target:u})}},Ia=Z({DID_ADD_ITEM:da,DID_REMOVE_ITEM:fa,DID_DRAG_ITEM:pa}),_a=({root:e,props:t,actions:n,shouldOptimize:s})=>{Ia({root:e,props:t,actions:n});const{dragCoordinates:r}=t,a=e.rect.element.width,i=e.childViews.filter(T=>T.rect.element.height),l=e.query("GET_ACTIVE_ITEMS").map(T=>i.find(R=>R.id===T.id)).filter(T=>T),o=r?Yt(e,l,r):null,d=e.ref.addIndex||null;e.ref.addIndex=null;let c=0,f=0,I=0;if(l.length===0)return;const p=l[0].rect.element,u=p.marginTop+p.marginBottom,_=p.marginLeft+p.marginRight,m=p.width+_,g=p.height+u,E=Wt(a,m);if(E===1){let T=0,R=0;l.forEach((O,L)=>{if(o){let P=L-o;P===-2?R=-u*.25:P===-1?R=-u*.75:P===0?R=u*.75:P===1?R=u*.25:R=0}s&&(O.translateX=null,O.translateY=null),O.markedForRemoval||Pn(O,0,T+R);let D=(O.rect.element.height+u)*(O.markedForRemoval?O.opacity:1);T+=D})}else{let T=0,R=0;l.forEach((O,L)=>{L===o&&(c=1),L===d&&(I+=1),O.markedForRemoval&&O.opacity<.5&&(f-=1);const b=L+I+c+f,D=b%E,P=Math.floor(b/E),v=D*m,M=P*g,w=Math.sign(v-T),x=Math.sign(M-R);T=v,R=M,!O.markedForRemoval&&(s&&(O.translateX=null,O.translateY=null),Pn(O,v,M,w,x))})}},Ta=(e,t)=>t.filter(n=>n.data&&n.data.id?e.id===n.data.id:!0),ma=q({create:ca,write:_a,tag:"ul",name:"list",didWriteView:({root:e})=>{e.childViews.filter(t=>t.markedForRemoval&&t.opacity===0&&t.resting).forEach(t=>{t._destroy(),e.removeChildView(t)})},filterFrameActionsForChild:Ta,mixins:{apis:["dragCoordinates"]}}),ga=({root:e,props:t})=>{e.ref.list=e.appendChildView(e.createChildView(ma)),t.dragCoordinates=null,t.overflowing=!1},ha=({root:e,props:t,action:n})=>{e.query("GET_ITEM_INSERT_LOCATION_FREEDOM")&&(t.dragCoordinates={left:n.position.scopeLeft-e.ref.list.rect.element.left,top:n.position.scopeTop-(e.rect.outer.top+e.rect.element.marginTop+e.rect.element.scrollTop)})},Ra=({props:e})=>{e.dragCoordinates=null},Aa=Z({DID_DRAG:ha,DID_END_DRAG:Ra}),Oa=({root:e,props:t,actions:n})=>{if(Aa({root:e,props:t,actions:n}),e.ref.list.dragCoordinates=t.dragCoordinates,t.overflowing&&!t.overflow&&(t.overflowing=!1,e.element.dataset.state="",e.height=null),t.overflow){const s=Math.round(t.overflow);s!==e.height&&(t.overflowing=!0,e.element.dataset.state="overflow",e.height=s)}},Da=q({create:ga,write:Oa,name:"list-scroller",mixins:{apis:["overflow","dragCoordinates"],styles:["height","translateY"],animations:{translateY:"spring"}}}),ce=(e,t,n,s="")=>{n?$(e,t,s):e.removeAttribute(t)},Sa=e=>{if(!(!e||e.value==="")){try{e.value=""}catch{}if(e.value){const t=pe("form"),n=e.parentNode,s=e.nextSibling;t.appendChild(e),t.reset(),s?n.insertBefore(e,s):n.appendChild(e)}}},ya=({root:e,props:t})=>{e.element.id=`filepond--browser-${t.id}`,$(e.element,"name",e.query("GET_NAME")),$(e.element,"aria-controls",`filepond--assistant-${t.id}`),$(e.element,"aria-labelledby",`filepond--drop-label-${t.id}`),gs({root:e,action:{value:e.query("GET_ACCEPTED_FILE_TYPES")}}),hs({root:e,action:{value:e.query("GET_ALLOW_MULTIPLE")}}),Rs({root:e,action:{value:e.query("GET_ALLOW_DIRECTORIES_ONLY")}}),bt({root:e}),As({root:e,action:{value:e.query("GET_REQUIRED")}}),Os({root:e,action:{value:e.query("GET_CAPTURE_METHOD")}}),e.ref.handleChange=n=>{if(!e.element.value)return;const s=Array.from(e.element.files).map(r=>(r._relativePath=r.webkitRelativePath,r));setTimeout(()=>{t.onload(s),Sa(e.element)},250)},e.element.addEventListener("change",e.ref.handleChange)},gs=({root:e,action:t})=>{e.query("GET_ALLOW_SYNC_ACCEPT_ATTRIBUTE")&&ce(e.element,"accept",!!t.value,t.value?t.value.join(","):"")},hs=({root:e,action:t})=>{ce(e.element,"multiple",t.value)},Rs=({root:e,action:t})=>{ce(e.element,"webkitdirectory",t.value)},bt=({root:e})=>{const t=e.query("GET_DISABLED"),n=e.query("GET_ALLOW_BROWSE"),s=t||!n;ce(e.element,"disabled",s)},As=({root:e,action:t})=>{t.value?e.query("GET_TOTAL_ITEMS")===0&&ce(e.element,"required",!0):ce(e.element,"required",!1)},Os=({root:e,action:t})=>{ce(e.element,"capture",!!t.value,t.value===!0?"":t.value)},Mn=({root:e})=>{const{element:t}=e;e.query("GET_TOTAL_ITEMS")>0?(ce(t,"required",!1),ce(t,"name",!1)):(ce(t,"name",!0,e.query("GET_NAME")),e.query("GET_CHECK_VALIDITY")&&t.setCustomValidity(""),e.query("GET_REQUIRED")&&ce(t,"required",!0))},La=({root:e})=>{e.query("GET_CHECK_VALIDITY")&&e.element.setCustomValidity(e.query("GET_LABEL_INVALID_FIELD"))},Pa=q({tag:"input",name:"browser",ignoreRect:!0,ignoreRectUpdate:!0,attributes:{type:"file"},create:ya,destroy:({root:e})=>{e.element.removeEventListener("change",e.ref.handleChange)},write:Z({DID_LOAD_ITEM:Mn,DID_REMOVE_ITEM:Mn,DID_THROW_ITEM_INVALID:La,DID_SET_DISABLED:bt,DID_SET_ALLOW_BROWSE:bt,DID_SET_ALLOW_DIRECTORIES_ONLY:Rs,DID_SET_ALLOW_MULTIPLE:hs,DID_SET_ACCEPTED_FILE_TYPES:gs,DID_SET_CAPTURE_METHOD:Os,DID_SET_REQUIRED:As})}),wn={ENTER:13,SPACE:32},Ma=({root:e,props:t})=>{const n=pe("label");$(n,"for",`filepond--browser-${t.id}`),$(n,"id",`filepond--drop-label-${t.id}`),e.ref.handleKeyDown=s=>{(s.keyCode===wn.ENTER||s.keyCode===wn.SPACE)&&(s.preventDefault(),e.ref.label.click())},e.ref.handleClick=s=>{s.target===n||n.contains(s.target)||e.ref.label.click()},n.addEventListener("keydown",e.ref.handleKeyDown),e.element.addEventListener("click",e.ref.handleClick),Ds(n,t.caption),e.appendChild(n),e.ref.label=n},Ds=(e,t)=>{e.innerHTML=t;const n=e.querySelector(".filepond--label-action");return n&&$(n,"tabindex","0"),t},wa=q({name:"drop-label",ignoreRect:!0,create:Ma,destroy:({root:e})=>{e.ref.label.addEventListener("keydown",e.ref.handleKeyDown),e.element.removeEventListener("click",e.ref.handleClick)},write:Z({DID_SET_LABEL_IDLE:({root:e,action:t})=>{Ds(e.ref.label,t.value)}}),mixins:{styles:["opacity","translateX","translateY"],animations:{opacity:{type:"tween",duration:150},translateX:"spring",translateY:"spring"}}}),Ca=q({name:"drip-blob",ignoreRect:!0,mixins:{styles:["translateX","translateY","scaleX","scaleY","opacity"],animations:{scaleX:"spring",scaleY:"spring",translateX:"spring",translateY:"spring",opacity:{type:"tween",duration:250}}}}),ba=({root:e})=>{const t=e.rect.element.width*.5,n=e.rect.element.height*.5;e.ref.blob=e.appendChildView(e.createChildView(Ca,{opacity:0,scaleX:2.5,scaleY:2.5,translateX:t,translateY:n}))},Na=({root:e,action:t})=>{if(!e.ref.blob){ba({root:e});return}e.ref.blob.translateX=t.position.scopeLeft,e.ref.blob.translateY=t.position.scopeTop,e.ref.blob.scaleX=1,e.ref.blob.scaleY=1,e.ref.blob.opacity=1},va=({root:e})=>{e.ref.blob&&(e.ref.blob.opacity=0)},Ga=({root:e})=>{e.ref.blob&&(e.ref.blob.scaleX=2.5,e.ref.blob.scaleY=2.5,e.ref.blob.opacity=0)},Fa=({root:e,props:t,actions:n})=>{Ba({root:e,props:t,actions:n});const{blob:s}=e.ref;n.length===0&&s&&s.opacity===0&&(e.removeChildView(s),e.ref.blob=null)},Ba=Z({DID_DRAG:Na,DID_DROP:Ga,DID_END_DRAG:va}),Ua=q({ignoreRect:!0,ignoreRectUpdate:!0,name:"drip",write:Fa}),Ss=(e,t)=>{try{const n=new DataTransfer;t.forEach(s=>{s instanceof File?n.items.add(s):n.items.add(new File([s],s.name,{type:s.type}))}),e.files=n.files}catch{return!1}return!0},xa=({root:e})=>e.ref.fields={},Et=(e,t)=>e.ref.fields[t],qt=e=>{e.query("GET_ACTIVE_ITEMS").forEach(t=>{e.ref.fields[t.id]&&e.element.appendChild(e.ref.fields[t.id])})},Cn=({root:e})=>qt(e),Va=({root:e,action:t})=>{const r=!(e.query("GET_ITEM",t.id).origin===z.LOCAL)&&e.query("SHOULD_UPDATE_FILE_INPUT"),a=pe("input");a.type=r?"file":"hidden",a.name=e.query("GET_NAME"),e.ref.fields[t.id]=a,qt(e)},Ha=({root:e,action:t})=>{const n=Et(e,t.id);if(!n||(t.serverFileReference!==null&&(n.value=t.serverFileReference),!e.query("SHOULD_UPDATE_FILE_INPUT")))return;const s=e.query("GET_ITEM",t.id);Ss(n,[s.file])},Wa=({root:e,action:t})=>{e.query("SHOULD_UPDATE_FILE_INPUT")&&setTimeout(()=>{const n=Et(e,t.id);n&&Ss(n,[t.file])},0)},Ya=({root:e})=>{e.element.disabled=e.query("GET_DISABLED")},qa=({root:e,action:t})=>{const n=Et(e,t.id);n&&(n.parentNode&&n.parentNode.removeChild(n),delete e.ref.fields[t.id])},$a=({root:e,action:t})=>{const n=Et(e,t.id);n&&(t.value===null?n.removeAttribute("value"):n.type!="file"&&(n.value=t.value),qt(e))},za=Z({DID_SET_DISABLED:Ya,DID_ADD_ITEM:Va,DID_LOAD_ITEM:Ha,DID_REMOVE_ITEM:qa,DID_DEFINE_VALUE:$a,DID_PREPARE_OUTPUT:Wa,DID_REORDER_ITEMS:Cn,DID_SORT_ITEMS:Cn}),ka=q({tag:"fieldset",name:"data",create:xa,write:za,ignoreRect:!0}),Xa=e=>"getRootNode"in e?e.getRootNode():document,ja=["jpg","jpeg","png","gif","bmp","webp","svg","tiff"],Qa=["css","csv","html","txt"],Za={zip:"zip|compressed",epub:"application/epub+zip"},ys=(e="")=>(e=e.toLowerCase(),ja.includes(e)?"image/"+(e==="jpg"?"jpeg":e==="svg"?"svg+xml":e):Qa.includes(e)?"text/"+e:Za[e]||""),$t=e=>new Promise((t,n)=>{const s=io(e);if(s.length&&!Ka(e))return t(s);Ja(e).then(t)}),Ka=e=>e.files?e.files.length>0:!1,Ja=e=>new Promise((t,n)=>{const s=(e.items?Array.from(e.items):[]).filter(r=>eo(r)).map(r=>to(r));if(!s.length){t(e.files?Array.from(e.files):[]);return}Promise.all(s).then(r=>{const a=[];r.forEach(i=>{a.push.apply(a,i)}),t(a.filter(i=>i).map(i=>(i._relativePath||(i._relativePath=i.webkitRelativePath),i)))}).catch(console.error)}),eo=e=>{if(Ls(e)){const t=zt(e);if(t)return t.isFile||t.isDirectory}return e.kind==="file"},to=e=>new Promise((t,n)=>{if(ro(e)){no(zt(e)).then(t).catch(n);return}t([e.getAsFile()])}),no=e=>new Promise((t,n)=>{const s=[];let r=0,a=0;const i=()=>{a===0&&r===0&&t(s)},l=o=>{r++;const d=o.createReader(),c=()=>{d.readEntries(f=>{if(f.length===0){r--,i();return}f.forEach(I=>{I.isDirectory?l(I):(a++,I.file(p=>{const u=so(p);I.fullPath&&(u._relativePath=I.fullPath),s.push(u),a--,i()}))}),c()},n)};c()};l(e)}),so=e=>{if(e.type.length)return e;const t=e.lastModifiedDate,n=e.name,s=ys(ft(e.name));return s.length&&(e=e.slice(0,e.size,s),e.name=n,e.lastModifiedDate=t),e},ro=e=>Ls(e)&&(zt(e)||{}).isDirectory,Ls=e=>"webkitGetAsEntry"in e,zt=e=>e.webkitGetAsEntry(),io=e=>{let t=[];try{if(t=oo(e),t.length)return t;t=ao(e)}catch{}return t},ao=e=>{let t=e.getData("url");return typeof t=="string"&&t.length?[t]:[]},oo=e=>{let t=e.getData("text/html");if(typeof t=="string"&&t.length){const n=t.match(/src\s*=\s*"(.+?)"/);if(n)return[n[1]]}return[]},it=[],Se=e=>({pageLeft:e.pageX,pageTop:e.pageY,scopeLeft:e.offsetX||e.layerX,scopeTop:e.offsetY||e.layerY}),lo=(e,t,n)=>{const s=co(t),r={element:e,filterElement:n,state:null,ondrop:()=>{},onenter:()=>{},ondrag:()=>{},onexit:()=>{},onload:()=>{},allowdrop:()=>{}};return r.destroy=s.addListener(r),r},co=e=>{const t=it.find(s=>s.element===e);if(t)return t;const n=uo(e);return it.push(n),n},uo=e=>{const t=[],n={dragenter:Eo,dragover:po,dragleave:_o,drop:Io},s={};H(n,(a,i)=>{s[a]=i(e,t),e.addEventListener(a,s[a],!1)});const r={element:e,addListener:a=>(t.push(a),()=>{t.splice(t.indexOf(a),1),t.length===0&&(it.splice(it.indexOf(r),1),H(n,i=>{e.removeEventListener(i,s[i],!1)}))})};return r},fo=(e,t)=>("elementFromPoint"in e||(e=document),e.elementFromPoint(t.x,t.y)),kt=(e,t)=>{const n=Xa(t),s=fo(n,{x:e.pageX-window.pageXOffset,y:e.pageY-window.pageYOffset});return s===t||t.contains(s)};let Ps=null;const je=(e,t)=>{try{e.dropEffect=t}catch{}},Eo=(e,t)=>n=>{n.preventDefault(),Ps=n.target,t.forEach(s=>{const{element:r,onenter:a}=s;kt(n,r)&&(s.state="enter",a(Se(n)))})},po=(e,t)=>n=>{n.preventDefault();const s=n.dataTransfer;$t(s).then(r=>{let a=!1;t.some(i=>{const{filterElement:l,element:o,onenter:d,onexit:c,ondrag:f,allowdrop:I}=i;je(s,"copy");const p=I(r);if(!p){je(s,"none");return}if(kt(n,o)){if(a=!0,i.state===null){i.state="enter",d(Se(n));return}if(i.state="over",l&&!p){je(s,"none");return}f(Se(n))}else l&&!a&&je(s,"none"),i.state&&(i.state=null,c(Se(n)))})})},Io=(e,t)=>n=>{n.preventDefault();const s=n.dataTransfer;$t(s).then(r=>{t.forEach(a=>{const{filterElement:i,element:l,ondrop:o,onexit:d,allowdrop:c}=a;if(a.state=null,!(i&&!kt(n,l))){if(!c(r))return d(Se(n));o(Se(n),r)}})})},_o=(e,t)=>n=>{Ps===n.target&&t.forEach(s=>{const{onexit:r}=s;s.state=null,r(Se(n))})},To=(e,t,n)=>{e.classList.add("filepond--hopper");const{catchesDropsOnPage:s,requiresDropOnElement:r,filterItems:a=c=>c}=n,i=lo(e,s?document.documentElement:e,r);let l="",o="";i.allowdrop=c=>t(a(c)),i.ondrop=(c,f)=>{const I=a(f);if(!t(I)){d.ondragend(c);return}o="drag-drop",d.onload(I,c)},i.ondrag=c=>{d.ondrag(c)},i.onenter=c=>{o="drag-over",d.ondragstart(c)},i.onexit=c=>{o="drag-exit",d.ondragend(c)};const d={updateHopperState:()=>{l!==o&&(e.dataset.hopperState=o,l=o)},onload:()=>{},ondragstart:()=>{},ondrag:()=>{},ondragend:()=>{},destroy:()=>{i.destroy()}};return d};let Nt=!1;const Ne=[],Ms=e=>{const t=document.activeElement;if(t&&/textarea|input/i.test(t.nodeName)){let n=!1,s=t;for(;s!==document.body;){if(s.classList.contains("filepond--root")){n=!0;break}s=s.parentNode}if(!n)return}$t(e.clipboardData).then(n=>{n.length&&Ne.forEach(s=>s(n))})},mo=e=>{Ne.includes(e)||(Ne.push(e),!Nt&&(Nt=!0,document.addEventListener("paste",Ms)))},go=e=>{Ut(Ne,Ne.indexOf(e)),Ne.length===0&&(document.removeEventListener("paste",Ms),Nt=!1)},ho=()=>{const e=n=>{t.onload(n)},t={destroy:()=>{go(e)},onload:()=>{}};return mo(e),t},Ro=({root:e,props:t})=>{e.element.id=`filepond--assistant-${t.id}`,$(e.element,"role","status"),$(e.element,"aria-live","polite"),$(e.element,"aria-relevant","additions")};let bn=null,Nn=null;const Rt=[],pt=(e,t)=>{e.element.textContent=t},Ao=e=>{e.element.textContent=""},ws=(e,t,n)=>{const s=e.query("GET_TOTAL_ITEMS");pt(e,`${n} ${t}, ${s} ${s===1?e.query("GET_LABEL_FILE_COUNT_SINGULAR"):e.query("GET_LABEL_FILE_COUNT_PLURAL")}`),clearTimeout(Nn),Nn=setTimeout(()=>{Ao(e)},1500)},Cs=e=>e.element.parentNode.contains(document.activeElement),Oo=({root:e,action:t})=>{if(!Cs(e))return;e.element.textContent="";const n=e.query("GET_ITEM",t.id);Rt.push(n.filename),clearTimeout(bn),bn=setTimeout(()=>{ws(e,Rt.join(", "),e.query("GET_LABEL_FILE_ADDED")),Rt.length=0},750)},Do=({root:e,action:t})=>{if(!Cs(e))return;const n=t.item;ws(e,n.filename,e.query("GET_LABEL_FILE_REMOVED"))},So=({root:e,action:t})=>{const s=e.query("GET_ITEM",t.id).filename,r=e.query("GET_LABEL_FILE_PROCESSING_COMPLETE");pt(e,`${s} ${r}`)},vn=({root:e,action:t})=>{const s=e.query("GET_ITEM",t.id).filename,r=e.query("GET_LABEL_FILE_PROCESSING_ABORTED");pt(e,`${s} ${r}`)},Qe=({root:e,action:t})=>{const s=e.query("GET_ITEM",t.id).filename;pt(e,`${t.status.main} ${s} ${t.status.sub}`)},yo=q({create:Ro,ignoreRect:!0,ignoreRectUpdate:!0,write:Z({DID_LOAD_ITEM:Oo,DID_REMOVE_ITEM:Do,DID_COMPLETE_ITEM_PROCESSING:So,DID_ABORT_ITEM_PROCESSING:vn,DID_REVERT_ITEM_PROCESSING:vn,DID_THROW_ITEM_REMOVE_ERROR:Qe,DID_THROW_ITEM_LOAD_ERROR:Qe,DID_THROW_ITEM_INVALID:Qe,DID_THROW_ITEM_PROCESSING_ERROR:Qe}),tag:"span",name:"assistant"}),bs=(e,t="-")=>e.replace(new RegExp(`${t}.`,"g"),n=>n.charAt(1).toUpperCase()),Ns=(e,t=16,n=!0)=>{let s=Date.now(),r=null;return(...a)=>{clearTimeout(r);const i=Date.now()-s,l=()=>{s=Date.now(),e(...a)};i<t?n||(r=setTimeout(l,t-i)):l()}},Lo=1e6,at=e=>e.preventDefault(),Po=({root:e,props:t})=>{const n=e.query("GET_ID");n&&(e.element.id=n);const s=e.query("GET_CLASS_NAME");s&&s.split(" ").filter(o=>o.length).forEach(o=>{e.element.classList.add(o)}),e.ref.label=e.appendChildView(e.createChildView(wa,{...t,translateY:null,caption:e.query("GET_LABEL_IDLE")})),e.ref.list=e.appendChildView(e.createChildView(Da,{translateY:null})),e.ref.panel=e.appendChildView(e.createChildView(ms,{name:"panel-root"})),e.ref.assistant=e.appendChildView(e.createChildView(yo,{...t})),e.ref.data=e.appendChildView(e.createChildView(ka,{...t})),e.ref.measure=pe("div"),e.ref.measure.style.height="100%",e.element.appendChild(e.ref.measure),e.ref.bounds=null,e.query("GET_STYLES").filter(o=>!Ee(o.value)).map(({name:o,value:d})=>{e.element.dataset[o]=d}),e.ref.widthPrevious=null,e.ref.widthUpdated=Ns(()=>{e.ref.updateHistory=[],e.dispatch("DID_RESIZE_ROOT")},250),e.ref.previousAspectRatio=null,e.ref.updateHistory=[];const r=window.matchMedia("(pointer: fine) and (hover: hover)").matches,a="PointerEvent"in window;e.query("GET_ALLOW_REORDER")&&a&&!r&&(e.element.addEventListener("touchmove",at,{passive:!1}),e.element.addEventListener("gesturestart",at));const i=e.query("GET_CREDITS");if(i.length===2){const o=document.createElement("a");o.className="filepond--credits",o.href=i[0],o.tabIndex=-1,o.target="_blank",o.rel="noopener noreferrer",o.textContent=i[1],e.element.appendChild(o),e.ref.credits=o}},Mo=({root:e,props:t,actions:n})=>{if(vo({root:e,props:t,actions:n}),n.filter(L=>/^DID_SET_STYLE_/.test(L.type)).filter(L=>!Ee(L.data.value)).map(({type:L,data:b})=>{const D=bs(L.substring(8).toLowerCase(),"_");e.element.dataset[D]=b.value,e.invalidateLayout()}),e.rect.element.hidden)return;e.rect.element.width!==e.ref.widthPrevious&&(e.ref.widthPrevious=e.rect.element.width,e.ref.widthUpdated());let s=e.ref.bounds;s||(s=e.ref.bounds=bo(e),e.element.removeChild(e.ref.measure),e.ref.measure=null);const{hopper:r,label:a,list:i,panel:l}=e.ref;r&&r.updateHopperState();const o=e.query("GET_PANEL_ASPECT_RATIO"),d=e.query("GET_ALLOW_MULTIPLE"),c=e.query("GET_TOTAL_ITEMS"),f=d?e.query("GET_MAX_FILES")||Lo:1,I=c===f,p=n.find(L=>L.type==="DID_ADD_ITEM");if(I&&p){const L=p.data.interactionMethod;a.opacity=0,d?a.translateY=-40:L===ie.API?a.translateX=40:L===ie.BROWSE?a.translateY=40:a.translateY=30}else I||(a.opacity=1,a.translateX=0,a.translateY=0);const u=wo(e),_=Co(e),m=a.rect.element.height,g=!d||I?0:m,E=I?i.rect.element.marginTop:0,T=c===0?0:i.rect.element.marginBottom,R=g+E+_.visual+T,O=g+E+_.bounds+T;if(i.translateY=Math.max(0,g-i.rect.element.marginTop)-u.top,o){const L=e.rect.element.width,b=L*o;o!==e.ref.previousAspectRatio&&(e.ref.previousAspectRatio=o,e.ref.updateHistory=[]);const D=e.ref.updateHistory;D.push(L);const P=2;if(D.length>P*2){const M=D.length,w=M-10;let x=0;for(let y=M;y>=w;y--)if(D[y]===D[y-2]&&x++,x>=P)return}l.scalable=!1,l.height=b;const v=b-g-(T-u.bottom)-(I?E:0);_.visual>v?i.overflow=v:i.overflow=null,e.height=b}else if(s.fixedHeight){l.scalable=!1;const L=s.fixedHeight-g-(T-u.bottom)-(I?E:0);_.visual>L?i.overflow=L:i.overflow=null}else if(s.cappedHeight){const L=R>=s.cappedHeight,b=Math.min(s.cappedHeight,R);l.scalable=!0,l.height=L?b:b-u.top-u.bottom;const D=b-g-(T-u.bottom)-(I?E:0);R>s.cappedHeight&&_.visual>D?i.overflow=D:i.overflow=null,e.height=Math.min(s.cappedHeight,O-u.top-u.bottom)}else{const L=c>0?u.top+u.bottom:0;l.scalable=!0,l.height=Math.max(m,R-L),e.height=Math.max(m,O-L)}e.ref.credits&&l.heightCurrent&&(e.ref.credits.style.transform=`translateY(${l.heightCurrent}px)`)},wo=e=>{const t=e.ref.list.childViews[0].childViews[0];return t?{top:t.rect.element.marginTop,bottom:t.rect.element.marginBottom}:{top:0,bottom:0}},Co=e=>{let t=0,n=0;const s=e.ref.list,r=s.childViews[0],a=r.childViews.filter(E=>E.rect.element.height),i=e.query("GET_ACTIVE_ITEMS").map(E=>a.find(T=>T.id===E.id)).filter(E=>E);if(i.length===0)return{visual:t,bounds:n};const l=r.rect.element.width,o=Yt(r,i,s.dragCoordinates),d=i[0].rect.element,c=d.marginTop+d.marginBottom,f=d.marginLeft+d.marginRight,I=d.width+f,p=d.height+c,u=typeof o<"u"&&o>=0?1:0,_=i.find(E=>E.markedForRemoval&&E.opacity<.45)?-1:0,m=i.length+u+_,g=Wt(l,I);return g===1?i.forEach(E=>{const T=E.rect.element.height+c;n+=T,t+=T*E.opacity}):(n=Math.ceil(m/g)*p,t=n),{visual:t,bounds:n}},bo=e=>{const t=e.ref.measureHeight||null;return{cappedHeight:parseInt(e.style.maxHeight,10)||null,fixedHeight:t===0?null:t}},Xt=(e,t)=>{const n=e.query("GET_ALLOW_REPLACE"),s=e.query("GET_ALLOW_MULTIPLE"),r=e.query("GET_TOTAL_ITEMS");let a=e.query("GET_MAX_FILES");const i=t.length;return!s&&i>1?(e.dispatch("DID_THROW_MAX_FILES",{source:t,error:W("warning",0,"Max files")}),!0):(a=s?a:1,!s&&n?!1:Ge(a)&&r+i>a?(e.dispatch("DID_THROW_MAX_FILES",{source:t,error:W("warning",0,"Max files")}),!0):!1)},No=(e,t,n)=>{const s=e.childViews[0];return Yt(s,t,{left:n.scopeLeft-s.rect.element.left,top:n.scopeTop-(e.rect.outer.top+e.rect.element.marginTop+e.rect.element.scrollTop)})},Gn=e=>{const t=e.query("GET_ALLOW_DROP"),n=e.query("GET_DISABLED"),s=t&&!n;if(s&&!e.ref.hopper){const r=To(e.element,a=>{const i=e.query("GET_BEFORE_DROP_FILE")||(()=>!0);return e.query("GET_DROP_VALIDATION")?a.every(o=>ye("ALLOW_HOPPER_ITEM",o,{query:e.query}).every(d=>d===!0)&&i(o)):!0},{filterItems:a=>{const i=e.query("GET_IGNORED_FILES");return a.filter(l=>De(l)?!i.includes(l.name.toLowerCase()):!0)},catchesDropsOnPage:e.query("GET_DROP_ON_PAGE"),requiresDropOnElement:e.query("GET_DROP_ON_ELEMENT")});r.onload=(a,i)=>{const o=e.ref.list.childViews[0].childViews.filter(c=>c.rect.element.height),d=e.query("GET_ACTIVE_ITEMS").map(c=>o.find(f=>f.id===c.id)).filter(c=>c);oe("ADD_ITEMS",a,{dispatch:e.dispatch}).then(c=>{if(Xt(e,c))return!1;e.dispatch("ADD_ITEMS",{items:c,index:No(e.ref.list,d,i),interactionMethod:ie.DROP})}),e.dispatch("DID_DROP",{position:i}),e.dispatch("DID_END_DRAG",{position:i})},r.ondragstart=a=>{e.dispatch("DID_START_DRAG",{position:a})},r.ondrag=Ns(a=>{e.dispatch("DID_DRAG",{position:a})}),r.ondragend=a=>{e.dispatch("DID_END_DRAG",{position:a})},e.ref.hopper=r,e.ref.drip=e.appendChildView(e.createChildView(Ua))}else!s&&e.ref.hopper&&(e.ref.hopper.destroy(),e.ref.hopper=null,e.removeChildView(e.ref.drip))},Fn=(e,t)=>{const n=e.query("GET_ALLOW_BROWSE"),s=e.query("GET_DISABLED"),r=n&&!s;r&&!e.ref.browser?e.ref.browser=e.appendChildView(e.createChildView(Pa,{...t,onload:a=>{oe("ADD_ITEMS",a,{dispatch:e.dispatch}).then(i=>{if(Xt(e,i))return!1;e.dispatch("ADD_ITEMS",{items:i,index:-1,interactionMethod:ie.BROWSE})})}}),0):!r&&e.ref.browser&&(e.removeChildView(e.ref.browser),e.ref.browser=null)},Bn=e=>{const t=e.query("GET_ALLOW_PASTE"),n=e.query("GET_DISABLED"),s=t&&!n;s&&!e.ref.paster?(e.ref.paster=ho(),e.ref.paster.onload=r=>{oe("ADD_ITEMS",r,{dispatch:e.dispatch}).then(a=>{if(Xt(e,a))return!1;e.dispatch("ADD_ITEMS",{items:a,index:-1,interactionMethod:ie.PASTE})})}):!s&&e.ref.paster&&(e.ref.paster.destroy(),e.ref.paster=null)},vo=Z({DID_SET_ALLOW_BROWSE:({root:e,props:t})=>{Fn(e,t)},DID_SET_ALLOW_DROP:({root:e})=>{Gn(e)},DID_SET_ALLOW_PASTE:({root:e})=>{Bn(e)},DID_SET_DISABLED:({root:e,props:t})=>{Gn(e),Bn(e),Fn(e,t),e.query("GET_DISABLED")?e.element.dataset.disabled="disabled":e.element.removeAttribute("data-disabled")}}),Go=q({name:"root",read:({root:e})=>{e.ref.measure&&(e.ref.measureHeight=e.ref.measure.offsetHeight)},create:Po,write:Mo,destroy:({root:e})=>{e.ref.paster&&e.ref.paster.destroy(),e.ref.hopper&&e.ref.hopper.destroy(),e.element.removeEventListener("touchmove",at),e.element.removeEventListener("gesturestart",at)},mixins:{styles:["height"]}}),Fo=(e={})=>{let t=null;const n=rt(),s=Zs(Fr(n),[ti,xr(n)],[yi,Ur(n)]);s.dispatch("SET_OPTIONS",{options:e});const r=()=>{document.hidden||s.dispatch("KICK")};document.addEventListener("visibilitychange",r);let a=null,i=!1,l=!1,o=null,d=null;const c=()=>{i||(i=!0),clearTimeout(a),a=setTimeout(()=>{i=!1,o=null,d=null,l&&(l=!1,s.dispatch("DID_STOP_RESIZE"))},500)};window.addEventListener("resize",c);const f=Go(s,{id:Bt()});let I=!1,p=!1;const u={_read:()=>{i&&(d=window.innerWidth,o||(o=d),!l&&d!==o&&(s.dispatch("DID_START_RESIZE"),l=!0)),p&&I&&(I=f.element.offsetParent===null),!I&&(f._read(),p=f.rect.element.hidden)},_write:A=>{const S=s.processActionQueue().filter(C=>!/^SET_/.test(C.type));I&&!S.length||(E(S),I=f._write(A,S,l),Wr(s.query("GET_ITEMS")),I&&s.processDispatchQueue())}},_=A=>S=>{const C={type:A};if(!S)return C;if(S.hasOwnProperty("error")&&(C.error=S.error?{...S.error}:null),S.status&&(C.status={...S.status}),S.file&&(C.output=S.file),S.source)C.file=S.source;else if(S.item||S.id){const N=S.item?S.item:s.query("GET_ITEM",S.id);C.file=N?J(N):null}return S.items&&(C.items=S.items.map(J)),/progress/.test(A)&&(C.progress=S.progress),S.hasOwnProperty("origin")&&S.hasOwnProperty("target")&&(C.origin=S.origin,C.target=S.target),C},m={DID_DESTROY:_("destroy"),DID_INIT:_("init"),DID_THROW_MAX_FILES:_("warning"),DID_INIT_ITEM:_("initfile"),DID_START_ITEM_LOAD:_("addfilestart"),DID_UPDATE_ITEM_LOAD_PROGRESS:_("addfileprogress"),DID_LOAD_ITEM:_("addfile"),DID_THROW_ITEM_INVALID:[_("error"),_("addfile")],DID_THROW_ITEM_LOAD_ERROR:[_("error"),_("addfile")],DID_THROW_ITEM_REMOVE_ERROR:[_("error"),_("removefile")],DID_PREPARE_OUTPUT:_("preparefile"),DID_START_ITEM_PROCESSING:_("processfilestart"),DID_UPDATE_ITEM_PROCESS_PROGRESS:_("processfileprogress"),DID_ABORT_ITEM_PROCESSING:_("processfileabort"),DID_COMPLETE_ITEM_PROCESSING:_("processfile"),DID_COMPLETE_ITEM_PROCESSING_ALL:_("processfiles"),DID_REVERT_ITEM_PROCESSING:_("processfilerevert"),DID_THROW_ITEM_PROCESSING_ERROR:[_("error"),_("processfile")],DID_REMOVE_ITEM:_("removefile"),DID_UPDATE_ITEMS:_("updatefiles"),DID_ACTIVATE_ITEM:_("activatefile"),DID_REORDER_ITEMS:_("reorderfiles")},g=A=>{const S={pond:F,...A};delete S.type,f.element.dispatchEvent(new CustomEvent(`FilePond:${A.type}`,{detail:S,bubbles:!0,cancelable:!0,composed:!0}));const C=[];A.hasOwnProperty("error")&&C.push(A.error),A.hasOwnProperty("file")&&C.push(A.file);const N=["type","error","file"];Object.keys(A).filter(V=>!N.includes(V)).forEach(V=>C.push(A[V])),F.fire(A.type,...C);const B=s.query(`GET_ON${A.type.toUpperCase()}`);B&&B(...C)},E=A=>{A.length&&A.filter(S=>m[S.type]).forEach(S=>{const C=m[S.type];(Array.isArray(C)?C:[C]).forEach(N=>{S.type==="DID_INIT_ITEM"?g(N(S.data)):setTimeout(()=>{g(N(S.data))},0)})})},T=A=>s.dispatch("SET_OPTIONS",{options:A}),R=A=>s.query("GET_ACTIVE_ITEM",A),O=A=>new Promise((S,C)=>{s.dispatch("REQUEST_ITEM_PREPARE",{query:A,success:N=>{S(N)},failure:N=>{C(N)}})}),L=(A,S={})=>new Promise((C,N)=>{P([{source:A,options:S}],{index:S.index}).then(B=>C(B&&B[0])).catch(N)}),b=A=>A.file&&A.id,D=(A,S)=>(typeof A=="object"&&!b(A)&&!S&&(S=A,A=void 0),s.dispatch("REMOVE_ITEM",{...S,query:A}),s.query("GET_ACTIVE_ITEM",A)===null),P=(...A)=>new Promise((S,C)=>{const N=[],B={};if(lt(A[0]))N.push.apply(N,A[0]),Object.assign(B,A[1]||{});else{const V=A[A.length-1];typeof V=="object"&&!(V instanceof Blob)&&Object.assign(B,A.pop()),N.push(...A)}s.dispatch("ADD_ITEMS",{items:N,index:B.index,interactionMethod:ie.API,success:S,failure:C})}),v=()=>s.query("GET_ACTIVE_ITEMS"),M=A=>new Promise((S,C)=>{s.dispatch("REQUEST_ITEM_PROCESSING",{query:A,success:N=>{S(N)},failure:N=>{C(N)}})}),w=(...A)=>{const S=Array.isArray(A[0])?A[0]:A,C=S.length?S:v();return Promise.all(C.map(O))},x=(...A)=>{const S=Array.isArray(A[0])?A[0]:A;if(!S.length){const C=v().filter(N=>!(N.status===G.IDLE&&N.origin===z.LOCAL)&&N.status!==G.PROCESSING&&N.status!==G.PROCESSING_COMPLETE&&N.status!==G.PROCESSING_REVERT_ERROR);return Promise.all(C.map(M))}return Promise.all(S.map(M))},y=(...A)=>{const S=Array.isArray(A[0])?A[0]:A;let C;typeof S[S.length-1]=="object"?C=S.pop():Array.isArray(A[0])&&(C=A[1]);const N=v();return S.length?S.map(V=>ge(V)?N[V]?N[V].id:null:V).filter(V=>V).map(V=>D(V,C)):Promise.all(N.map(V=>D(V,C)))},F={...ut(),...u,...Br(s,n),setOptions:T,addFile:L,addFiles:P,getFile:R,processFile:M,prepareFile:O,removeFile:D,moveFile:(A,S)=>s.dispatch("MOVE_ITEM",{query:A,index:S}),getFiles:v,processFiles:x,removeFiles:y,prepareFiles:w,sort:A=>s.dispatch("SORT",{compare:A}),browse:()=>{var A=f.element.querySelector("input[type=file]");A&&A.click()},destroy:()=>{F.fire("destroy",f.element),s.dispatch("ABORT_ALL"),f._destroy(),window.removeEventListener("resize",c),document.removeEventListener("visibilitychange",r),s.dispatch("DID_DESTROY")},insertBefore:A=>an(f.element,A),insertAfter:A=>on(f.element,A),appendTo:A=>A.appendChild(f.element),replaceElement:A=>{an(f.element,A),A.parentNode.removeChild(A),t=A},restoreElement:()=>{t&&(on(t,f.element),f.element.parentNode.removeChild(f.element),t=null)},isAttachedTo:A=>f.element===A||t===A,element:{get:()=>f.element},status:{get:()=>s.query("GET_STATUS")}};return s.dispatch("DID_INIT"),_e(F)},vs=(e={})=>{const t={};return H(rt(),(s,r)=>{t[s]=r[0]}),Fo({...t,...e})},Bo=e=>e.charAt(0).toLowerCase()+e.slice(1),Uo=e=>bs(e.replace(/^data-/,"")),Gs=(e,t)=>{H(t,(n,s)=>{H(e,(r,a)=>{const i=new RegExp(n);if(!i.test(r)||(delete e[r],s===!1))return;if(Q(s)){e[s]=a;return}const o=s.group;k(s)&&!e[o]&&(e[o]={}),e[o][Bo(r.replace(i,""))]=a}),s.mapping&&Gs(e[s.group],s.mapping)})},xo=(e,t={})=>{const n=[];H(e.attributes,r=>{n.push(e.attributes[r])});const s=n.filter(r=>r.name).reduce((r,a)=>{const i=$(e,a.name);return r[Uo(a.name)]=i===a.name?!0:i,r},{});return Gs(s,t),s},Vo=(e,t={})=>{const n={"^class$":"className","^multiple$":"allowMultiple","^capture$":"captureMethod","^webkitdirectory$":"allowDirectoriesOnly","^server":{group:"server",mapping:{"^process":{group:"process"},"^revert":{group:"revert"},"^fetch":{group:"fetch"},"^restore":{group:"restore"},"^load":{group:"load"}}},"^type$":!1,"^files$":!1};ye("SET_ATTRIBUTE_TO_OPTION_MAP",n);const s={...t},r=xo(e.nodeName==="FIELDSET"?e.querySelector("input[type=file]"):e,n);Object.keys(r).forEach(i=>{k(r[i])?(k(s[i])||(s[i]={}),Object.assign(s[i],r[i])):s[i]=r[i]}),s.files=(t.files||[]).concat(Array.from(e.querySelectorAll("input:not([type=file])")).map(i=>({source:i.value,options:{type:i.dataset.type}})));const a=vs(s);return e.files&&Array.from(e.files).forEach(i=>{a.addFile(i)}),a.replaceElement(e),a},Ho=(...e)=>Qs(e[0])?Vo(...e):vs(...e),Wo=["fire","_read","_write"],Un=e=>{const t={};return ss(e,t,Wo),t},Yo=(e,t)=>e.replace(/(?:{([a-zA-Z]+)})/g,(n,s)=>t[s]),qo=e=>{const t=new Blob(["(",e.toString(),")()"],{type:"application/javascript"}),n=URL.createObjectURL(t),s=new Worker(n);return{transfer:(r,a)=>{},post:(r,a,i)=>{const l=Bt();s.onmessage=o=>{o.data.id===l&&a(o.data.message)},s.postMessage({id:l,message:r},i)},terminate:()=>{s.terminate(),URL.revokeObjectURL(n)}}},$o=e=>new Promise((t,n)=>{const s=new Image;s.onload=()=>{t(s)},s.onerror=r=>{n(r)},s.src=e}),Fs=(e,t)=>{const n=e.slice(0,e.size,e.type);return n.lastModifiedDate=e.lastModifiedDate,n.name=t,n},zo=e=>Fs(e,e.name),xn=[],ko=e=>{if(xn.includes(e))return;xn.push(e);const t=e({addFilter:qr,utils:{Type:h,forin:H,isString:Q,isFile:De,toNaturalFileSize:Is,replaceInString:Yo,getExtensionFromFilename:ft,getFilenameWithoutExtension:fs,guesstimateMimeType:ys,getFileFromBlob:ve,getFilenameFromURL:We,createRoute:Z,createWorker:qo,createView:q,createItemAPI:J,loadImage:$o,copyFile:zo,renameFile:Fs,createBlob:cs,applyFilterChain:oe,text:Y,getNumericAspectRatioFromString:as},views:{fileActionButton:ps}});$r(t.options)},Xo=()=>Object.prototype.toString.call(window.operamini)==="[object OperaMini]",jo=()=>"Promise"in window,Qo=()=>"slice"in Blob.prototype,Zo=()=>"URL"in window&&"createObjectURL"in window.URL,Ko=()=>"visibilityState"in document,Jo=()=>"performance"in window,el=()=>"supports"in(window.CSS||{}),tl=()=>/MSIE|Trident/.test(window.navigator.userAgent),vt=(()=>{const e=Zn()&&!Xo()&&Ko()&&jo()&&Qo()&&Zo()&&Jo()&&(el()||tl());return()=>e})(),Ie={apps:[]},nl="filepond",Le=()=>{};let ot={},tt=Le,At=Le,Vn=Le,Hn=Le,Gt=Le,Wn=Le,Yn=Le;if(vt()){Ar(()=>{Ie.apps.forEach(n=>n._read())},n=>{Ie.apps.forEach(s=>s._write(n))});const e=()=>{document.dispatchEvent(new CustomEvent("FilePond:loaded",{detail:{supported:vt,create:tt,destroy:At,parse:Vn,find:Hn,registerPlugin:Gt,setOptions:Yn}})),document.removeEventListener("DOMContentLoaded",e)};document.readyState!=="loading"?setTimeout(()=>e(),0):document.addEventListener("DOMContentLoaded",e);const t=()=>H(rt(),(n,s)=>{ot[n]=s[1]});ot={},t(),tt=(...n)=>{const s=Ho(...n);return s.on("destroy",At),Ie.apps.push(s),Un(s)},At=n=>{const s=Ie.apps.findIndex(r=>r.isAttachedTo(n));return s>=0?(Ie.apps.splice(s,1)[0].restoreElement(),!0):!1},Vn=n=>Array.from(n.querySelectorAll(`.${nl}`)).filter(a=>!Ie.apps.find(i=>i.isAttachedTo(a))).map(a=>tt(a)),Hn=n=>{const s=Ie.apps.find(r=>r.isAttachedTo(n));return s?Un(s):null},Gt=(...n)=>{n.forEach(ko),t()},Wn=()=>{const n={};return H(rt(),(s,r)=>{n[s]=r[0]}),n},Yn=n=>(k(n)&&(Ie.apps.forEach(s=>{s.setOptions(n)}),zr(n)),Wn())}/*!
 * vue-filepond v7.0.4
 * A handy FilePond adapter component for Vue
 * 
 * Copyright (c) 2023 PQINA
 * https://pqina.nl/filepond
 * 
 * Licensed under the MIT license.
 */const sl=["setOptions","on","off","onOnce","appendTo","insertAfter","insertBefore","isAttachedTo","replaceElement","restoreElement","destroy"],rl=vt(),il=e=>({string:String,boolean:Boolean,array:Array,function:Function,int:Number,serverapi:Object,object:Object})[e],Ze={},Ot=[],Dt=[];let al={};const Oc=(...e)=>{Gt(...e),Ot.length=0;for(const t in ot){if(/^on/.test(t)){Ot.push(t);continue}let n=[String,il(ot[t])];t=="labelFileProcessingError"&&n.push(Function),Ze[t]={type:n,default:void 0}}return{name:"FilePond",props:Ze,render(){const t=Object.entries({id:this.id,name:this.name,type:"file",class:this.className,required:this.required,multiple:this.allowMultiple,accept:this.acceptedFileTypes,capture:this.captureMethod}).reduce((n,[s,r])=>(r!==void 0&&(n[s]=r),n),{});return Jt("div",{class:{"filepond--wrapper":!0}},[Jt("input",t)])},created(){this.watchers=Object.keys(Ze).map(t=>this.$watch(t,n=>{this._pond&&(this._pond[t]=n)}))},mounted(){if(!rl)return;this._element=this.$el.querySelector("input");const t=Ot.reduce((s,r)=>(s[r]=(...a)=>{this.$emit("input",this._pond?this._pond.getFiles():[]),this.$emit(r.substr(2),...a)},s),{}),n={};Object.keys(Ze).forEach(s=>{this[s]!==void 0&&(n[s]=this[s])}),this._pond=tt(this._element,Object.assign({},al,t,n)),Object.keys(this._pond).filter(s=>!sl.includes(s)).forEach(s=>{this[s]=this._pond[s]}),Dt.push(this._pond)},beforeUnmount(){const{detached:t}=this.$options;if(!this.$el.offsetParent){t.call(this);return}const n=(r,a)=>{const l=((r[0]||{}).removedNodes||[])[0];!l||!l.contains(this.$el)||(a.disconnect(),t.call(this))};new MutationObserver(n).observe(document.documentElement,{childList:!0,subtree:!0})},detached(){if(this.watchers.forEach(n=>n()),!this._pond)return;this._pond.destroy();const t=Dt.indexOf(this._pond);t>=0&&Dt.splice(t,1),this._pond=null}}};/*!
 * FilePondPluginFileEncode 2.1.14
 * Licensed under MIT, https://opensource.org/licenses/MIT/
 * Please visit https://pqina.nl/filepond/ for details.
 */const ol=function(){self.onmessage=t=>{e(t.data.message,n=>{self.postMessage({id:t.data.id,message:n})})};const e=(t,n)=>{const{file:s}=t,r=new FileReader;r.onloadend=()=>{n(r.result.replace("data:","").replace(/^.+,/,""))},r.readAsDataURL(s)}},ll=({addFilter:e,utils:t})=>{const{Type:n,createWorker:s,createRoute:r,isFile:a}=t,i=({name:o,file:d})=>new Promise(c=>{const f=s(ol);f.post({file:d},I=>{c({name:o,data:I}),f.terminate()})}),l=[];return e("DID_CREATE_ITEM",(o,{query:d})=>{d("GET_ALLOW_FILE_ENCODE")&&(o.extend("getFileEncodeBase64String",()=>l[o.id]&&l[o.id].data),o.extend("getFileEncodeDataURL",()=>l[o.id]&&`data:${o.fileType};base64,${l[o.id].data}`))}),e("SHOULD_PREPARE_OUTPUT",(o,{query:d})=>new Promise(c=>{c(d("GET_ALLOW_FILE_ENCODE"))})),e("COMPLETE_PREPARE_OUTPUT",(o,{item:d,query:c})=>new Promise(f=>{if(!c("GET_ALLOW_FILE_ENCODE")||!a(o)&&!Array.isArray(o))return f(o);l[d.id]={metadata:d.getMetadata(),data:null},Promise.all((o instanceof Blob?[{name:null,file:o}]:o).map(i)).then(I=>{l[d.id].data=o instanceof Blob?I[0].data:I,f(o)})})),e("CREATE_VIEW",o=>{const{is:d,view:c,query:f}=o;!d("file-wrapper")||!f("GET_ALLOW_FILE_ENCODE")||c.registerWriter(r({DID_PREPARE_OUTPUT:({root:I,action:p})=>{if(f("IS_ASYNC"))return;const u=f("GET_ITEM",p.id);if(!u)return;const _=l[u.id],m=_.metadata,g=_.data,E=JSON.stringify({id:u.id,name:u.file.name,type:u.file.type,size:u.file.size,metadata:m,data:g});I.ref.data?I.ref.data.value=E:I.dispatch("DID_DEFINE_VALUE",{id:u.id,value:E})},DID_REMOVE_ITEM:({action:I})=>{const p=f("GET_ITEM",I.id);p&&delete l[p.id]}}))}),{options:{allowFileEncode:[!0,n.BOOLEAN]}}},cl=typeof window<"u"&&typeof window.document<"u";cl&&document.dispatchEvent(new CustomEvent("FilePond:pluginloaded",{detail:ll}));/*!
 * FilePondPluginFileValidateSize 2.2.8
 * Licensed under MIT, https://opensource.org/licenses/MIT/
 * Please visit https://pqina.nl/filepond/ for details.
 */const dl=({addFilter:e,utils:t})=>{const{Type:n,replaceInString:s,toNaturalFileSize:r}=t;return e("ALLOW_HOPPER_ITEM",(a,{query:i})=>{if(!i("GET_ALLOW_FILE_SIZE_VALIDATION"))return!0;const l=i("GET_MAX_FILE_SIZE");if(l!==null&&a.size>l)return!1;const o=i("GET_MIN_FILE_SIZE");return!(o!==null&&a.size<o)}),e("LOAD_FILE",(a,{query:i})=>new Promise((l,o)=>{if(!i("GET_ALLOW_FILE_SIZE_VALIDATION"))return l(a);const d=i("GET_FILE_VALIDATE_SIZE_FILTER");if(d&&!d(a))return l(a);const c=i("GET_MAX_FILE_SIZE");if(c!==null&&a.size>c){o({status:{main:i("GET_LABEL_MAX_FILE_SIZE_EXCEEDED"),sub:s(i("GET_LABEL_MAX_FILE_SIZE"),{filesize:r(c,".",i("GET_FILE_SIZE_BASE"),i("GET_FILE_SIZE_LABELS",i))})}});return}const f=i("GET_MIN_FILE_SIZE");if(f!==null&&a.size<f){o({status:{main:i("GET_LABEL_MIN_FILE_SIZE_EXCEEDED"),sub:s(i("GET_LABEL_MIN_FILE_SIZE"),{filesize:r(f,".",i("GET_FILE_SIZE_BASE"),i("GET_FILE_SIZE_LABELS",i))})}});return}const I=i("GET_MAX_TOTAL_FILE_SIZE");if(I!==null&&i("GET_ACTIVE_ITEMS").reduce((u,_)=>u+_.fileSize,0)>I){o({status:{main:i("GET_LABEL_MAX_TOTAL_FILE_SIZE_EXCEEDED"),sub:s(i("GET_LABEL_MAX_TOTAL_FILE_SIZE"),{filesize:r(I,".",i("GET_FILE_SIZE_BASE"),i("GET_FILE_SIZE_LABELS",i))})}});return}l(a)})),{options:{allowFileSizeValidation:[!0,n.BOOLEAN],maxFileSize:[null,n.INT],minFileSize:[null,n.INT],maxTotalFileSize:[null,n.INT],fileValidateSizeFilter:[null,n.FUNCTION],labelMinFileSizeExceeded:["File is too small",n.STRING],labelMinFileSize:["Minimum file size is {filesize}",n.STRING],labelMaxFileSizeExceeded:["File is too large",n.STRING],labelMaxFileSize:["Maximum file size is {filesize}",n.STRING],labelMaxTotalFileSizeExceeded:["Maximum total size exceeded",n.STRING],labelMaxTotalFileSize:["Maximum total file size is {filesize}",n.STRING]}}},ul=typeof window<"u"&&typeof window.document<"u";ul&&document.dispatchEvent(new CustomEvent("FilePond:pluginloaded",{detail:dl}));/*!
 * FilePondPluginFileValidateType 1.2.9
 * Licensed under MIT, https://opensource.org/licenses/MIT/
 * Please visit https://pqina.nl/filepond/ for details.
 */const fl=({addFilter:e,utils:t})=>{const{Type:n,isString:s,replaceInString:r,guesstimateMimeType:a,getExtensionFromFilename:i,getFilenameFromURL:l}=t,o=(p,u)=>{const _=(/^[^/]+/.exec(p)||[]).pop(),m=u.slice(0,-2);return _===m},d=(p,u)=>p.some(_=>/\*$/.test(_)?o(u,_):_===u),c=p=>{let u="";if(s(p)){const _=l(p),m=i(_);m&&(u=a(m))}else u=p.type;return u},f=(p,u,_)=>{if(u.length===0)return!0;const m=c(p);return _?new Promise((g,E)=>{_(p,m).then(T=>{d(u,T)?g():E()}).catch(E)}):d(u,m)},I=p=>u=>p[u]===null?!1:p[u]||u;return e("SET_ATTRIBUTE_TO_OPTION_MAP",p=>Object.assign(p,{accept:"acceptedFileTypes"})),e("ALLOW_HOPPER_ITEM",(p,{query:u})=>u("GET_ALLOW_FILE_TYPE_VALIDATION")?f(p,u("GET_ACCEPTED_FILE_TYPES")):!0),e("LOAD_FILE",(p,{query:u})=>new Promise((_,m)=>{if(!u("GET_ALLOW_FILE_TYPE_VALIDATION")){_(p);return}const g=u("GET_ACCEPTED_FILE_TYPES"),E=u("GET_FILE_VALIDATE_TYPE_DETECT_TYPE"),T=f(p,g,E),R=()=>{const O=g.map(I(u("GET_FILE_VALIDATE_TYPE_LABEL_EXPECTED_TYPES_MAP"))).filter(b=>b!==!1),L=O.filter((b,D)=>O.indexOf(b)===D);m({status:{main:u("GET_LABEL_FILE_TYPE_NOT_ALLOWED"),sub:r(u("GET_FILE_VALIDATE_TYPE_LABEL_EXPECTED_TYPES"),{allTypes:L.join(", "),allButLastType:L.slice(0,-1).join(", "),lastType:L[L.length-1]})}})};if(typeof T=="boolean")return T?_(p):R();T.then(()=>{_(p)}).catch(R)})),{options:{allowFileTypeValidation:[!0,n.BOOLEAN],acceptedFileTypes:[[],n.ARRAY],labelFileTypeNotAllowed:["File is of invalid type",n.STRING],fileValidateTypeLabelExpectedTypes:["Expects {allButLastType} or {lastType}",n.STRING],fileValidateTypeLabelExpectedTypesMap:[{},n.OBJECT],fileValidateTypeDetectType:[null,n.FUNCTION]}}},El=typeof window<"u"&&typeof window.document<"u";El&&document.dispatchEvent(new CustomEvent("FilePond:pluginloaded",{detail:fl}));/*!
 * FilePondPluginImageExifOrientation 1.0.11
 * Licensed under MIT, https://opensource.org/licenses/MIT/
 * Please visit https://pqina.nl/filepond/ for details.
 */const pl=e=>/^image\/jpeg/.test(e.type),Te={JPEG:65496,APP1:65505,EXIF:**********,TIFF:18761,Orientation:274,Unknown:65280},me=(e,t,n=!1)=>e.getUint16(t,n),qn=(e,t,n=!1)=>e.getUint32(t,n),Il=e=>new Promise((t,n)=>{const s=new FileReader;s.onload=function(r){const a=new DataView(r.target.result);if(me(a,0)!==Te.JPEG){t(-1);return}const i=a.byteLength;let l=2;for(;l<i;){const o=me(a,l);if(l+=2,o===Te.APP1){if(qn(a,l+=2)!==Te.EXIF)break;const d=me(a,l+=6)===Te.TIFF;l+=qn(a,l+4,d);const c=me(a,l,d);l+=2;for(let f=0;f<c;f++)if(me(a,l+f*12,d)===Te.Orientation){t(me(a,l+f*12+8,d));return}}else{if((o&Te.Unknown)!==Te.Unknown)break;l+=me(a,l)}}t(-1)},s.readAsArrayBuffer(e.slice(0,64*1024))}),_l=(()=>typeof window<"u"&&typeof window.document<"u")(),Tl=()=>_l,ml="data:image/jpg;base64,/9j/4AAQSkZJRgABAQEASABIAAD/4QA6RXhpZgAATU0AKgAAAAgAAwESAAMAAAABAAYAAAEoAAMAAAABAAIAAAITAAMAAAABAAEAAAAAAAD/2wBDAP//////////////////////////////////////////////////////////////////////////////////////wAALCAABAAIBASIA/8QAJgABAAAAAAAAAAAAAAAAAAAAAxABAAAAAAAAAAAAAAAAAAAAAP/aAAgBAQAAPwBH/9k=";let Bs;const nt=Tl()?new Image:{};nt.onload=()=>Bs=nt.naturalWidth>nt.naturalHeight;nt.src=ml;const gl=()=>Bs,hl=({addFilter:e,utils:t})=>{const{Type:n,isFile:s}=t;return e("DID_LOAD_ITEM",(r,{query:a})=>new Promise((i,l)=>{const o=r.file;if(!s(o)||!pl(o)||!a("GET_ALLOW_IMAGE_EXIF_ORIENTATION")||!gl())return i(r);Il(o).then(d=>{r.setMetadata("exif",{orientation:d}),i(r)})})),{options:{allowImageExifOrientation:[!0,n.BOOLEAN]}}},Rl=typeof window<"u"&&typeof window.document<"u";Rl&&document.dispatchEvent(new CustomEvent("FilePond:pluginloaded",{detail:hl}));/*!
 * FilePondPluginImagePreview 4.6.12
 * Licensed under MIT, https://opensource.org/licenses/MIT/
 * Please visit https://pqina.nl/filepond/ for details.
 */const Al=e=>/^image/.test(e.type),$n=(e,t)=>He(e.x*t,e.y*t),zn=(e,t)=>He(e.x+t.x,e.y+t.y),Ol=e=>{const t=Math.sqrt(e.x*e.x+e.y*e.y);return t===0?{x:0,y:0}:He(e.x/t,e.y/t)},Ke=(e,t,n)=>{const s=Math.cos(t),r=Math.sin(t),a=He(e.x-n.x,e.y-n.y);return He(n.x+s*a.x-r*a.y,n.y+r*a.x+s*a.y)},He=(e=0,t=0)=>({x:e,y:t}),ee=(e,t,n=1,s)=>{if(typeof e=="string")return parseFloat(e)*n;if(typeof e=="number")return e*(s?t[s]:Math.min(t.width,t.height))},Dl=(e,t,n)=>{const s=e.borderStyle||e.lineStyle||"solid",r=e.backgroundColor||e.fontColor||"transparent",a=e.borderColor||e.lineColor||"transparent",i=ee(e.borderWidth||e.lineWidth,t,n),l=e.lineCap||"round",o=e.lineJoin||"round",d=typeof s=="string"?"":s.map(f=>ee(f,t,n)).join(","),c=e.opacity||1;return{"stroke-linecap":l,"stroke-linejoin":o,"stroke-width":i||0,"stroke-dasharray":d,stroke:a,fill:r,opacity:c}},re=e=>e!=null,Sl=(e,t,n=1)=>{let s=ee(e.x,t,n,"width")||ee(e.left,t,n,"width"),r=ee(e.y,t,n,"height")||ee(e.top,t,n,"height"),a=ee(e.width,t,n,"width"),i=ee(e.height,t,n,"height"),l=ee(e.right,t,n,"width"),o=ee(e.bottom,t,n,"height");return re(r)||(re(i)&&re(o)?r=t.height-i-o:r=o),re(s)||(re(a)&&re(l)?s=t.width-a-l:s=l),re(a)||(re(s)&&re(l)?a=t.width-s-l:a=0),re(i)||(re(r)&&re(o)?i=t.height-r-o:i=0),{x:s||0,y:r||0,width:a||0,height:i||0}},yl=e=>e.map((t,n)=>`${n===0?"M":"L"} ${t.x} ${t.y}`).join(" "),ue=(e,t)=>Object.keys(t).forEach(n=>e.setAttribute(n,t[n])),Ll="http://www.w3.org/2000/svg",be=(e,t)=>{const n=document.createElementNS(Ll,e);return t&&ue(n,t),n},Pl=e=>ue(e,{...e.rect,...e.styles}),Ml=e=>{const t=e.rect.x+e.rect.width*.5,n=e.rect.y+e.rect.height*.5,s=e.rect.width*.5,r=e.rect.height*.5;return ue(e,{cx:t,cy:n,rx:s,ry:r,...e.styles})},wl={contain:"xMidYMid meet",cover:"xMidYMid slice"},Cl=(e,t)=>{ue(e,{...e.rect,...e.styles,preserveAspectRatio:wl[t.fit]||"none"})},bl={left:"start",center:"middle",right:"end"},Nl=(e,t,n,s)=>{const r=ee(t.fontSize,n,s),a=t.fontFamily||"sans-serif",i=t.fontWeight||"normal",l=bl[t.textAlign]||"start";ue(e,{...e.rect,...e.styles,"stroke-width":0,"font-weight":i,"font-size":r,"font-family":a,"text-anchor":l}),e.text!==t.text&&(e.text=t.text,e.textContent=t.text.length?t.text:" ")},vl=(e,t,n,s)=>{ue(e,{...e.rect,...e.styles,fill:"none"});const r=e.childNodes[0],a=e.childNodes[1],i=e.childNodes[2],l=e.rect,o={x:e.rect.x+e.rect.width,y:e.rect.y+e.rect.height};if(ue(r,{x1:l.x,y1:l.y,x2:o.x,y2:o.y}),!t.lineDecoration)return;a.style.display="none",i.style.display="none";const d=Ol({x:o.x-l.x,y:o.y-l.y}),c=ee(.05,n,s);if(t.lineDecoration.indexOf("arrow-begin")!==-1){const f=$n(d,c),I=zn(l,f),p=Ke(l,2,I),u=Ke(l,-2,I);ue(a,{style:"display:block;",d:`M${p.x},${p.y} L${l.x},${l.y} L${u.x},${u.y}`})}if(t.lineDecoration.indexOf("arrow-end")!==-1){const f=$n(d,-c),I=zn(o,f),p=Ke(o,2,I),u=Ke(o,-2,I);ue(i,{style:"display:block;",d:`M${p.x},${p.y} L${o.x},${o.y} L${u.x},${u.y}`})}},Gl=(e,t,n,s)=>{ue(e,{...e.styles,fill:"none",d:yl(t.points.map(r=>({x:ee(r.x,n,s,"width"),y:ee(r.y,n,s,"height")})))})},Je=e=>t=>be(e,{id:t.id}),Fl=e=>{const t=be("image",{id:e.id,"stroke-linecap":"round","stroke-linejoin":"round",opacity:"0"});return t.onload=()=>{t.setAttribute("opacity",e.opacity||1)},t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",e.src),t},Bl=e=>{const t=be("g",{id:e.id,"stroke-linecap":"round","stroke-linejoin":"round"}),n=be("line");t.appendChild(n);const s=be("path");t.appendChild(s);const r=be("path");return t.appendChild(r),t},Ul={image:Fl,rect:Je("rect"),ellipse:Je("ellipse"),text:Je("text"),path:Je("path"),line:Bl},xl={rect:Pl,ellipse:Ml,image:Cl,text:Nl,path:Gl,line:vl},Vl=(e,t)=>Ul[e](t),Hl=(e,t,n,s,r)=>{t!=="path"&&(e.rect=Sl(n,s,r)),e.styles=Dl(n,s,r),xl[t](e,n,s,r)},Wl=["x","y","left","top","right","bottom","width","height"],Yl=e=>typeof e=="string"&&/%/.test(e)?parseFloat(e)/100:e,ql=e=>{const[t,n]=e,s=n.points?{}:Wl.reduce((r,a)=>(r[a]=Yl(n[a]),r),{});return[t,{zIndex:0,...n,...s}]},$l=(e,t)=>e[1].zIndex>t[1].zIndex?1:e[1].zIndex<t[1].zIndex?-1:0,zl=e=>e.utils.createView({name:"image-preview-markup",tag:"svg",ignoreRect:!0,mixins:{apis:["width","height","crop","markup","resize","dirty"]},write:({root:t,props:n})=>{if(!n.dirty)return;const{crop:s,resize:r,markup:a}=n,i=n.width,l=n.height;let o=s.width,d=s.height;if(r){const{size:p}=r;let u=p&&p.width,_=p&&p.height;const m=r.mode,g=r.upscale;u&&!_&&(_=u),_&&!u&&(u=_);const E=o<u&&d<_;if(!E||E&&g){let T=u/o,R=_/d;if(m==="force")o=u,d=_;else{let O;m==="cover"?O=Math.max(T,R):m==="contain"&&(O=Math.min(T,R)),o=o*O,d=d*O}}}const c={width:i,height:l};t.element.setAttribute("width",c.width),t.element.setAttribute("height",c.height);const f=Math.min(i/o,l/d);t.element.innerHTML="";const I=t.query("GET_IMAGE_PREVIEW_MARKUP_FILTER");a.filter(I).map(ql).sort($l).forEach(p=>{const[u,_]=p,m=Vl(u,_);Hl(m,u,_,c,f),t.element.appendChild(m)})}}),Ve=(e,t)=>({x:e,y:t}),kl=(e,t)=>e.x*t.x+e.y*t.y,kn=(e,t)=>Ve(e.x-t.x,e.y-t.y),Xl=(e,t)=>kl(kn(e,t),kn(e,t)),Xn=(e,t)=>Math.sqrt(Xl(e,t)),jn=(e,t)=>{const n=e,s=1.5707963267948966,r=t,a=1.5707963267948966-t,i=Math.sin(s),l=Math.sin(r),o=Math.sin(a),d=Math.cos(a),c=n/i,f=c*l,I=c*o;return Ve(d*f,d*I)},jl=(e,t)=>{const n=e.width,s=e.height,r=jn(n,t),a=jn(s,t),i=Ve(e.x+Math.abs(r.x),e.y-Math.abs(r.y)),l=Ve(e.x+e.width+Math.abs(a.y),e.y+Math.abs(a.x)),o=Ve(e.x-Math.abs(a.y),e.y+e.height-Math.abs(a.x));return{width:Xn(i,l),height:Xn(i,o)}},Ql=(e,t,n=1)=>{const s=e.height/e.width;let r=1,a=t,i=1,l=s;l>a&&(l=a,i=l/s);const o=Math.max(r/i,a/l),d=e.width/(n*o*i),c=d*t;return{width:d,height:c}},Us=(e,t,n,s)=>{const r=s.x>.5?1-s.x:s.x,a=s.y>.5?1-s.y:s.y,i=r*2*e.width,l=a*2*e.height,o=jl(t,n);return Math.max(o.width/i,o.height/l)},xs=(e,t)=>{let n=e.width,s=n*t;s>e.height&&(s=e.height,n=s/t);const r=(e.width-n)*.5,a=(e.height-s)*.5;return{x:r,y:a,width:n,height:s}},Zl=(e,t={})=>{let{zoom:n,rotation:s,center:r,aspectRatio:a}=t;a||(a=e.height/e.width);const i=Ql(e,a,n),l={x:i.width*.5,y:i.height*.5},o={x:0,y:0,width:i.width,height:i.height,center:l},d=typeof t.scaleToFit>"u"||t.scaleToFit,c=Us(e,xs(o,a),s,d?r:{x:.5,y:.5}),f=n*c;return{widthFloat:i.width/f,heightFloat:i.height/f,width:Math.round(i.width/f),height:Math.round(i.height/f)}},de={type:"spring",stiffness:.5,damping:.45,mass:10},Kl=e=>e.utils.createView({name:"image-bitmap",ignoreRect:!0,mixins:{styles:["scaleX","scaleY"]},create:({root:t,props:n})=>{t.appendChild(n.image)}}),Jl=e=>e.utils.createView({name:"image-canvas-wrapper",tag:"div",ignoreRect:!0,mixins:{apis:["crop","width","height"],styles:["originX","originY","translateX","translateY","scaleX","scaleY","rotateZ"],animations:{originX:de,originY:de,scaleX:de,scaleY:de,translateX:de,translateY:de,rotateZ:de}},create:({root:t,props:n})=>{n.width=n.image.width,n.height=n.image.height,t.ref.bitmap=t.appendChildView(t.createChildView(Kl(e),{image:n.image}))},write:({root:t,props:n})=>{const{flip:s}=n.crop,{bitmap:r}=t.ref;r.scaleX=s.horizontal?-1:1,r.scaleY=s.vertical?-1:1}}),ec=e=>e.utils.createView({name:"image-clip",tag:"div",ignoreRect:!0,mixins:{apis:["crop","markup","resize","width","height","dirty","background"],styles:["width","height","opacity"],animations:{opacity:{type:"tween",duration:250}}},didWriteView:function({root:t,props:n}){n.background&&(t.element.style.backgroundColor=n.background)},create:({root:t,props:n})=>{t.ref.image=t.appendChildView(t.createChildView(Jl(e),Object.assign({},n))),t.ref.createMarkup=()=>{t.ref.markup||(t.ref.markup=t.appendChildView(t.createChildView(zl(e),Object.assign({},n))))},t.ref.destroyMarkup=()=>{t.ref.markup&&(t.removeChildView(t.ref.markup),t.ref.markup=null)};const s=t.query("GET_IMAGE_PREVIEW_TRANSPARENCY_INDICATOR");s!==null&&(s==="grid"?t.element.dataset.transparencyIndicator=s:t.element.dataset.transparencyIndicator="color")},write:({root:t,props:n,shouldOptimize:s})=>{const{crop:r,markup:a,resize:i,dirty:l,width:o,height:d}=n;t.ref.image.crop=r;const c={x:0,y:0,width:o,height:d,center:{x:o*.5,y:d*.5}},f={width:t.ref.image.width,height:t.ref.image.height},I={x:r.center.x*f.width,y:r.center.y*f.height},p={x:c.center.x-f.width*r.center.x,y:c.center.y-f.height*r.center.y},u=Math.PI*2+r.rotation%(Math.PI*2),_=r.aspectRatio||f.height/f.width,m=typeof r.scaleToFit>"u"||r.scaleToFit,g=Us(f,xs(c,_),u,m?r.center:{x:.5,y:.5}),E=r.zoom*g;a&&a.length?(t.ref.createMarkup(),t.ref.markup.width=o,t.ref.markup.height=d,t.ref.markup.resize=i,t.ref.markup.dirty=l,t.ref.markup.markup=a,t.ref.markup.crop=Zl(f,r)):t.ref.markup&&t.ref.destroyMarkup();const T=t.ref.image;if(s){T.originX=null,T.originY=null,T.translateX=null,T.translateY=null,T.rotateZ=null,T.scaleX=null,T.scaleY=null;return}T.originX=I.x,T.originY=I.y,T.translateX=p.x,T.translateY=p.y,T.rotateZ=u,T.scaleX=E,T.scaleY=E}}),tc=e=>e.utils.createView({name:"image-preview",tag:"div",ignoreRect:!0,mixins:{apis:["image","crop","markup","resize","dirty","background"],styles:["translateY","scaleX","scaleY","opacity"],animations:{scaleX:de,scaleY:de,translateY:de,opacity:{type:"tween",duration:400}}},create:({root:t,props:n})=>{t.ref.clip=t.appendChildView(t.createChildView(ec(e),{id:n.id,image:n.image,crop:n.crop,markup:n.markup,resize:n.resize,dirty:n.dirty,background:n.background}))},write:({root:t,props:n,shouldOptimize:s})=>{const{clip:r}=t.ref,{image:a,crop:i,markup:l,resize:o,dirty:d}=n;if(r.crop=i,r.markup=l,r.resize=o,r.dirty=d,r.opacity=s?0:1,s||t.rect.element.hidden)return;const c=a.height/a.width;let f=i.aspectRatio||c;const I=t.rect.inner.width,p=t.rect.inner.height;let u=t.query("GET_IMAGE_PREVIEW_HEIGHT");const _=t.query("GET_IMAGE_PREVIEW_MIN_HEIGHT"),m=t.query("GET_IMAGE_PREVIEW_MAX_HEIGHT"),g=t.query("GET_PANEL_ASPECT_RATIO"),E=t.query("GET_ALLOW_MULTIPLE");g&&!E&&(u=I*g,f=g);let T=u!==null?u:Math.max(_,Math.min(I*f,m)),R=T/f;R>I&&(R=I,T=R*f),T>p&&(T=p,R=p/f),r.width=R,r.height=T}});let nc=`<svg width="500" height="200" viewBox="0 0 500 200" preserveAspectRatio="none">
    <defs>
        <radialGradient id="gradient-__UID__" cx=".5" cy="1.25" r="1.15">
            <stop offset='50%' stop-color='#000000'/>
            <stop offset='56%' stop-color='#0a0a0a'/>
            <stop offset='63%' stop-color='#262626'/>
            <stop offset='69%' stop-color='#4f4f4f'/>
            <stop offset='75%' stop-color='#808080'/>
            <stop offset='81%' stop-color='#b1b1b1'/>
            <stop offset='88%' stop-color='#dadada'/>
            <stop offset='94%' stop-color='#f6f6f6'/>
            <stop offset='100%' stop-color='#ffffff'/>
        </radialGradient>
        <mask id="mask-__UID__">
            <rect x="0" y="0" width="500" height="200" fill="url(#gradient-__UID__)"></rect>
        </mask>
    </defs>
    <rect x="0" width="500" height="200" fill="currentColor" mask="url(#mask-__UID__)"></rect>
</svg>`,Qn=0;const sc=e=>e.utils.createView({name:"image-preview-overlay",tag:"div",ignoreRect:!0,create:({root:t,props:n})=>{let s=nc;if(document.querySelector("base")){const r=new URL(window.location.href.replace(window.location.hash,"")).href;s=s.replace(/url\(\#/g,"url("+r+"#")}Qn++,t.element.classList.add(`filepond--image-preview-overlay-${n.status}`),t.element.innerHTML=s.replace(/__UID__/g,Qn)},mixins:{styles:["opacity"],animations:{opacity:{type:"spring",mass:25}}}}),rc=function(){self.onmessage=e=>{createImageBitmap(e.data.message.file).then(t=>{self.postMessage({id:e.data.id,message:t},[t])})}},ic=function(){self.onmessage=e=>{const t=e.data.message.imageData,n=e.data.message.colorMatrix,s=t.data,r=s.length,a=n[0],i=n[1],l=n[2],o=n[3],d=n[4],c=n[5],f=n[6],I=n[7],p=n[8],u=n[9],_=n[10],m=n[11],g=n[12],E=n[13],T=n[14],R=n[15],O=n[16],L=n[17],b=n[18],D=n[19];let P=0,v=0,M=0,w=0,x=0;for(;P<r;P+=4)v=s[P]/255,M=s[P+1]/255,w=s[P+2]/255,x=s[P+3]/255,s[P]=Math.max(0,Math.min((v*a+M*i+w*l+x*o+d)*255,255)),s[P+1]=Math.max(0,Math.min((v*c+M*f+w*I+x*p+u)*255,255)),s[P+2]=Math.max(0,Math.min((v*_+M*m+w*g+x*E+T)*255,255)),s[P+3]=Math.max(0,Math.min((v*R+M*O+w*L+x*b+D)*255,255));self.postMessage({id:e.data.id,message:t},[t.data.buffer])}},ac=(e,t)=>{let n=new Image;n.onload=()=>{const s=n.naturalWidth,r=n.naturalHeight;n=null,t(s,r)},n.src=e},oc={1:()=>[1,0,0,1,0,0],2:e=>[-1,0,0,1,e,0],3:(e,t)=>[-1,0,0,-1,e,t],4:(e,t)=>[1,0,0,-1,0,t],5:()=>[0,1,1,0,0,0],6:(e,t)=>[0,1,-1,0,t,0],7:(e,t)=>[0,-1,-1,0,t,e],8:e=>[0,-1,1,0,0,e]},lc=(e,t,n,s)=>{s!==-1&&e.transform.apply(e,oc[s](t,n))},cc=(e,t,n,s)=>{t=Math.round(t),n=Math.round(n);const r=document.createElement("canvas");r.width=t,r.height=n;const a=r.getContext("2d");return s>=5&&s<=8&&([t,n]=[n,t]),lc(a,t,n,s),a.drawImage(e,0,0,t,n),r},Vs=e=>/^image/.test(e.type)&&!/svg/.test(e.type),dc=10,uc=10,fc=e=>{const t=Math.min(dc/e.width,uc/e.height),n=document.createElement("canvas"),s=n.getContext("2d"),r=n.width=Math.ceil(e.width*t),a=n.height=Math.ceil(e.height*t);s.drawImage(e,0,0,r,a);let i=null;try{i=s.getImageData(0,0,r,a).data}catch{return null}const l=i.length;let o=0,d=0,c=0,f=0;for(;f<l;f+=4)o+=i[f]*i[f],d+=i[f+1]*i[f+1],c+=i[f+2]*i[f+2];return o=St(o,l),d=St(d,l),c=St(c,l),{r:o,g:d,b:c}},St=(e,t)=>Math.floor(Math.sqrt(e/(t/4))),Ec=(e,t)=>(t=t||document.createElement("canvas"),t.width=e.width,t.height=e.height,t.getContext("2d").drawImage(e,0,0),t),pc=e=>{let t;try{t=new ImageData(e.width,e.height)}catch{t=document.createElement("canvas").getContext("2d").createImageData(e.width,e.height)}return t.data.set(new Uint8ClampedArray(e.data)),t},Ic=e=>new Promise((t,n)=>{const s=new Image;s.crossOrigin="Anonymous",s.onload=()=>{t(s)},s.onerror=r=>{n(r)},s.src=e}),_c=e=>{const t=sc(e),n=tc(e),{createWorker:s}=e.utils,r=(E,T,R)=>new Promise(O=>{E.ref.imageData||(E.ref.imageData=R.getContext("2d").getImageData(0,0,R.width,R.height));const L=pc(E.ref.imageData);if(!T||T.length!==20)return R.getContext("2d").putImageData(L,0,0),O();const b=s(ic);b.post({imageData:L,colorMatrix:T},D=>{R.getContext("2d").putImageData(D,0,0),b.terminate(),O()},[L.data.buffer])}),a=(E,T)=>{E.removeChildView(T),T.image.width=1,T.image.height=1,T._destroy()},i=({root:E})=>{const T=E.ref.images.shift();return T.opacity=0,T.translateY=-15,E.ref.imageViewBin.push(T),T},l=({root:E,props:T,image:R})=>{const O=T.id,L=E.query("GET_ITEM",{id:O});if(!L)return;const b=L.getMetadata("crop")||{center:{x:.5,y:.5},flip:{horizontal:!1,vertical:!1},zoom:1,rotation:0,aspectRatio:null},D=E.query("GET_IMAGE_TRANSFORM_CANVAS_BACKGROUND_COLOR");let P,v,M=!1;E.query("GET_IMAGE_PREVIEW_MARKUP_SHOW")&&(P=L.getMetadata("markup")||[],v=L.getMetadata("resize"),M=!0);const w=E.appendChildView(E.createChildView(n,{id:O,image:R,crop:b,resize:v,markup:P,dirty:M,background:D,opacity:0,scaleX:1.15,scaleY:1.15,translateY:15}),E.childViews.length);E.ref.images.push(w),w.opacity=1,w.scaleX=1,w.scaleY=1,w.translateY=0,setTimeout(()=>{E.dispatch("DID_IMAGE_PREVIEW_SHOW",{id:O})},250)},o=({root:E,props:T})=>{const R=E.query("GET_ITEM",{id:T.id});if(!R)return;const O=E.ref.images[E.ref.images.length-1];O.crop=R.getMetadata("crop"),O.background=E.query("GET_IMAGE_TRANSFORM_CANVAS_BACKGROUND_COLOR"),E.query("GET_IMAGE_PREVIEW_MARKUP_SHOW")&&(O.dirty=!0,O.resize=R.getMetadata("resize"),O.markup=R.getMetadata("markup"))},d=({root:E,props:T,action:R})=>{if(!/crop|filter|markup|resize/.test(R.change.key)||!E.ref.images.length)return;const O=E.query("GET_ITEM",{id:T.id});if(O){if(/filter/.test(R.change.key)){const L=E.ref.images[E.ref.images.length-1];r(E,R.change.value,L.image);return}if(/crop|markup|resize/.test(R.change.key)){const L=O.getMetadata("crop"),b=E.ref.images[E.ref.images.length-1];if(L&&L.aspectRatio&&b.crop&&b.crop.aspectRatio&&Math.abs(L.aspectRatio-b.crop.aspectRatio)>1e-5){const D=i({root:E});l({root:E,props:T,image:Ec(D.image)})}else o({root:E,props:T})}}},c=E=>{const R=window.navigator.userAgent.match(/Firefox\/([0-9]+)\./),O=R?parseInt(R[1]):null;return O!==null&&O<=58?!1:"createImageBitmap"in window&&Vs(E)},f=({root:E,props:T})=>{const{id:R}=T,O=E.query("GET_ITEM",R);if(!O)return;const L=URL.createObjectURL(O.file);ac(L,(b,D)=>{E.dispatch("DID_IMAGE_PREVIEW_CALCULATE_SIZE",{id:R,width:b,height:D})})},I=({root:E,props:T})=>{const{id:R}=T,O=E.query("GET_ITEM",R);if(!O)return;const L=URL.createObjectURL(O.file),b=()=>{Ic(L).then(D)},D=P=>{URL.revokeObjectURL(L);const M=(O.getMetadata("exif")||{}).orientation||-1;let{width:w,height:x}=P;if(!w||!x)return;M>=5&&M<=8&&([w,x]=[x,w]);const y=Math.max(1,window.devicePixelRatio*.75),A=E.query("GET_IMAGE_PREVIEW_ZOOM_FACTOR")*y,S=x/w,C=E.rect.element.width,N=E.rect.element.height;let B=C,V=B*S;S>1?(B=Math.min(w,C*A),V=B*S):(V=Math.min(x,N*A),B=V/S);const Pe=cc(P,B,V,M),Ye=()=>{const It=E.query("GET_IMAGE_PREVIEW_CALCULATE_AVERAGE_IMAGE_COLOR")?fc(data):null;O.setMetadata("color",It,!0),"close"in P&&P.close(),E.ref.overlayShadow.opacity=1,l({root:E,props:T,image:Pe})},ae=O.getMetadata("filter");ae?r(E,ae,Pe).then(Ye):Ye()};if(c(O.file)){const P=s(rc);P.post({file:O.file},v=>{if(P.terminate(),!v){b();return}D(v)})}else b()},p=({root:E})=>{const T=E.ref.images[E.ref.images.length-1];T.translateY=0,T.scaleX=1,T.scaleY=1,T.opacity=1},u=({root:E})=>{E.ref.overlayShadow.opacity=1,E.ref.overlayError.opacity=0,E.ref.overlaySuccess.opacity=0},_=({root:E})=>{E.ref.overlayShadow.opacity=.25,E.ref.overlayError.opacity=1},m=({root:E})=>{E.ref.overlayShadow.opacity=.25,E.ref.overlaySuccess.opacity=1},g=({root:E})=>{E.ref.images=[],E.ref.imageData=null,E.ref.imageViewBin=[],E.ref.overlayShadow=E.appendChildView(E.createChildView(t,{opacity:0,status:"idle"})),E.ref.overlaySuccess=E.appendChildView(E.createChildView(t,{opacity:0,status:"success"})),E.ref.overlayError=E.appendChildView(E.createChildView(t,{opacity:0,status:"failure"}))};return e.utils.createView({name:"image-preview-wrapper",create:g,styles:["height"],apis:["height"],destroy:({root:E})=>{E.ref.images.forEach(T=>{T.image.width=1,T.image.height=1})},didWriteView:({root:E})=>{E.ref.images.forEach(T=>{T.dirty=!1})},write:e.utils.createRoute({DID_IMAGE_PREVIEW_DRAW:p,DID_IMAGE_PREVIEW_CONTAINER_CREATE:f,DID_FINISH_CALCULATE_PREVIEWSIZE:I,DID_UPDATE_ITEM_METADATA:d,DID_THROW_ITEM_LOAD_ERROR:_,DID_THROW_ITEM_PROCESSING_ERROR:_,DID_THROW_ITEM_INVALID:_,DID_COMPLETE_ITEM_PROCESSING:m,DID_START_ITEM_PROCESSING:u,DID_REVERT_ITEM_PROCESSING:u},({root:E})=>{const T=E.ref.imageViewBin.filter(R=>R.opacity===0);E.ref.imageViewBin=E.ref.imageViewBin.filter(R=>R.opacity>0),T.forEach(R=>a(E,R)),T.length=0})})},Tc=e=>{const{addFilter:t,utils:n}=e,{Type:s,createRoute:r,isFile:a}=n,i=_c(e);return t("CREATE_VIEW",l=>{const{is:o,view:d,query:c}=l;if(!o("file")||!c("GET_ALLOW_IMAGE_PREVIEW"))return;const f=({root:m,props:g})=>{const{id:E}=g,T=c("GET_ITEM",E);if(!T||!a(T.file)||T.archived)return;const R=T.file;if(!Al(R)||!c("GET_IMAGE_PREVIEW_FILTER_ITEM")(T))return;const O="createImageBitmap"in(window||{}),L=c("GET_IMAGE_PREVIEW_MAX_FILE_SIZE");if(!O&&L&&R.size>L)return;m.ref.imagePreview=d.appendChildView(d.createChildView(i,{id:E}));const b=m.query("GET_IMAGE_PREVIEW_HEIGHT");b&&m.dispatch("DID_UPDATE_PANEL_HEIGHT",{id:T.id,height:b});const D=!O&&R.size>c("GET_IMAGE_PREVIEW_MAX_INSTANT_PREVIEW_FILE_SIZE");m.dispatch("DID_IMAGE_PREVIEW_CONTAINER_CREATE",{id:E},D)},I=(m,g)=>{if(!m.ref.imagePreview)return;let{id:E}=g;const T=m.query("GET_ITEM",{id:E});if(!T)return;const R=m.query("GET_PANEL_ASPECT_RATIO"),O=m.query("GET_ITEM_PANEL_ASPECT_RATIO"),L=m.query("GET_IMAGE_PREVIEW_HEIGHT");if(R||O||L)return;let{imageWidth:b,imageHeight:D}=m.ref;if(!b||!D)return;const P=m.query("GET_IMAGE_PREVIEW_MIN_HEIGHT"),v=m.query("GET_IMAGE_PREVIEW_MAX_HEIGHT"),w=(T.getMetadata("exif")||{}).orientation||-1;if(w>=5&&w<=8&&([b,D]=[D,b]),!Vs(T.file)||m.query("GET_IMAGE_PREVIEW_UPSCALE")){const C=2048/b;b*=C,D*=C}const x=D/b,y=(T.getMetadata("crop")||{}).aspectRatio||x;let F=Math.max(P,Math.min(D,v));const A=m.rect.element.width,S=Math.min(A*y,F);m.dispatch("DID_UPDATE_PANEL_HEIGHT",{id:T.id,height:S})},p=({root:m})=>{m.ref.shouldRescale=!0},u=({root:m,action:g})=>{g.change.key==="crop"&&(m.ref.shouldRescale=!0)},_=({root:m,action:g})=>{m.ref.imageWidth=g.width,m.ref.imageHeight=g.height,m.ref.shouldRescale=!0,m.ref.shouldDrawPreview=!0,m.dispatch("KICK")};d.registerWriter(r({DID_RESIZE_ROOT:p,DID_STOP_RESIZE:p,DID_LOAD_ITEM:f,DID_IMAGE_PREVIEW_CALCULATE_SIZE:_,DID_UPDATE_ITEM_METADATA:u},({root:m,props:g})=>{m.ref.imagePreview&&(m.rect.element.hidden||(m.ref.shouldRescale&&(I(m,g),m.ref.shouldRescale=!1),m.ref.shouldDrawPreview&&(requestAnimationFrame(()=>{requestAnimationFrame(()=>{m.dispatch("DID_FINISH_CALCULATE_PREVIEWSIZE",{id:g.id})})}),m.ref.shouldDrawPreview=!1)))}))}),{options:{allowImagePreview:[!0,s.BOOLEAN],imagePreviewFilterItem:[()=>!0,s.FUNCTION],imagePreviewHeight:[null,s.INT],imagePreviewMinHeight:[44,s.INT],imagePreviewMaxHeight:[256,s.INT],imagePreviewMaxFileSize:[null,s.INT],imagePreviewZoomFactor:[2,s.INT],imagePreviewUpscale:[!1,s.BOOLEAN],imagePreviewMaxInstantPreviewFileSize:[1e6,s.INT],imagePreviewTransparencyIndicator:[null,s.STRING],imagePreviewCalculateAverageImageColor:[!1,s.BOOLEAN],imagePreviewMarkupShow:[!0,s.BOOLEAN],imagePreviewMarkupFilter:[()=>!0,s.FUNCTION]}}},mc=typeof window<"u"&&typeof window.document<"u";mc&&document.dispatchEvent(new CustomEvent("FilePond:pluginloaded",{detail:Tc}));export{Ac as V,hl as a,dl as b,ll as c,fl as d,Tc as p,Oc as v};
