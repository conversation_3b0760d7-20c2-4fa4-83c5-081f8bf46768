import{_ as n}from"./ErrorHeader-d0ed5b30.js";import{p as c}from"./404-aa4980a7.js";import{u as m,m as i,a as _}from"./misc-mask-light-eca946dc.js";import{b as r}from"./route-block-83d24a4e.js";import{o as l,c as p,q as e,w as d,aj as u,ah as f,n as h,s,aF as a}from"./index-9a5dc664.js";const g={class:"misc-wrapper"},k={class:"misc-avatar w-100 text-center"},v={__name:"internal-server-error",setup(x){const t=m(_,i);return(w,V)=>{const o=n;return l(),p("div",g,[e(o,{"error-title":"Internal server error 👨🏻‍💻","error-description":"Oops, something went wrong!"}),e(f,{to:"/",class:"mb-12"},{default:d(()=>[u(" Back to Home ")]),_:1}),h("div",k,[e(a,{src:s(c),alt:"Coming Soon","max-width":200,class:"mx-auto"},null,8,["src"])]),e(a,{src:s(t),class:"misc-footer-img d-none d-md-block"},null,8,["src"])])}}};typeof r=="function"&&r(v);export{v as default};
