import{bv as u,bw as i,E as d,D as m,o as p,c as x,q as e,w as a,av as b,ah as f,s as n,aj as y,as as _}from"./index-9a5dc664.js";import{u as k}from"./entry-a444d4f4.js";import"./shepherd.esm-7d7e64e6.js";const w={__name:"list",setup(h){const l=u(),{ctrl_k:c,meta_k:r}=i();let t=null;return d([c,r,()=>l.path],()=>{t.isActive()&&t.cancel()}),m(()=>{const o=document.querySelector(".layout-navbar");t=k({useModalOverlay:!0,stepsContainer:document.querySelector(".layout-wrapper"),modelContainer:document.querySelector(".layout-wrapper"),defaultStepOptions:{cancelIcon:{enabled:!0},modalOverlayOpeningPadding:2,modalOverlayOpeningRadius:5}}),t.addSteps([{id:"welcome",title:"Welcome",arrow:!0,attachTo:{element:o,on:"bottom"},text:"Welcome to our tour page, Guide users to the key features of the product.",buttons:[{action:t.cancel,classes:"backBtnClass",text:"Back"},{action:t.next,text:"Next",classes:"nextBtnClass"}]},{id:"notification",title:"Notifications",arrow:!0,attachTo:{element:document.querySelector("#notification-btn"),on:"bottom"},text:"Manage your notifications and stay up-to-date with latest updates.",buttons:[{label:"Back",text:"Back",action:t.back,classes:"backBtnClass"},{label:"Next",text:"Next",action:t.next,classes:"nextBtnClass"}]},{id:"footer",title:"Footer",arrow:!0,attachTo:{element:document.querySelector(".layout-footer"),on:"bottom"},text:"Footer section of the page.",buttons:[{label:"Back",text:"Back",action:t.back,classes:"backBtnClass"},{label:"Finish",text:"Finish",action:t.complete,classes:"nextBtnClass"}]}])}),(o,s)=>(p(),x("div",null,[e(_,{title:"Tour"},{default:a(()=>[e(b,null,{default:a(()=>[e(f,{variant:"elevated",onClick:s[0]||(s[0]=()=>{n(t)&&n(t).start()})},{default:a(()=>[y(" Start Tour ")]),_:1})]),_:1})]),_:1})]))}};export{w as default};
