import{I as P,bG as y,bH as h,a0 as I,bI as S,l as x,bJ as B,E as v,a3 as D,B as w,a8 as f,a7 as R,bK as m,q as g,bs as T,bL as F,bM as L}from"./index-9a5dc664.js";const A=P()({name:"VDialog",props:{fullscreen:Boolean,retainFocus:{type:Boolean,default:!0},scrollable:Boolean,...y({origin:"center center",scrollStrategy:"block",transition:{component:h},zIndex:2400})},emits:{"update:modelValue":a=>!0},setup(a,b){let{slots:c}=b;const r=I(a,"modelValue"),{scopeId:p}=S(),t=x();function i(l){var n,s;const e=l.relatedTarget,o=l.target;if(e!==o&&((n=t.value)!=null&&n.contentEl)&&((s=t.value)!=null&&s.globalTop)&&![document,t.value.contentEl].includes(o)&&!t.value.contentEl.contains(o)){const u=L(t.value.contentEl);if(!u.length)return;const d=u[0],E=u[u.length-1];e===d?E.focus():d.focus()}}B&&v(()=>r.value&&a.retainFocus,l=>{l?document.addEventListener("focusin",i):document.removeEventListener("focusin",i)},{immediate:!0}),v(r,async l=>{var e,o;await D(),l?(e=t.value.contentEl)==null||e.focus({preventScroll:!0}):(o=t.value.activatorEl)==null||o.focus({preventScroll:!0})});const V=w(()=>f({"aria-haspopup":"dialog","aria-expanded":String(r.value)},a.activatorProps));return R(()=>{const[l]=m.filterProps(a);return g(m,f({ref:t,class:["v-dialog",{"v-dialog--fullscreen":a.fullscreen,"v-dialog--scrollable":a.scrollable},a.class],style:a.style},l,{modelValue:r.value,"onUpdate:modelValue":e=>r.value=e,"aria-modal":"true",activatorProps:V.value,role:"dialog"},p),{activator:c.activator,default:function(){for(var e=arguments.length,o=new Array(e),n=0;n<e;n++)o[n]=arguments[n];return g(T,{root:!0},{default:()=>{var s;return[(s=c.default)==null?void 0:s.call(c,...o)]}})}})}),F({},t)}});export{A as V};
