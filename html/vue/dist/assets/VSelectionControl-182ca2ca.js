import{bb as U,aQ as P,by as M,P as O,bo as j,I as _,K as D,a0 as T,a_ as w,B as u,bk as H,bS as K,a6 as N,W as o,a7 as G,q as f,bc as Q,l as g,a$ as W,aC as z,be as J,ag as X,a8 as x,bd as Y,bp as Z,c3 as A,aU as p,c8 as F,a3 as ee}from"./index-9a5dc664.js";import{V as le}from"./VTextField-3e2d458d.js";const $=Symbol.for("vuetify:selection-control-group"),R=U({color:String,disabled:Boolean,error:Boolean,id:String,inline:Boolean,falseIcon:P,trueIcon:P,ripple:{type:Boolean,default:!0},multiple:{type:Boolean,default:null},name:String,readonly:<PERSON>olean,modelValue:null,type:String,valueComparator:{type:Function,default:M},...O(),...j()},"v-selection-control-group"),ue=_()({name:"VSelectionControlGroup",props:{defaultsTarget:{type:String,default:"VSelectionControl"},...D(),...R()},emits:{"update:modelValue":e=>!0},setup(e,i){let{slots:s}=i;const l=T(e,"modelValue"),t=w(),m=u(()=>e.id||`v-selection-control-group-${t}`),d=u(()=>e.name||m.value),a=new Set;return H($,{modelValue:l,forceUpdate:()=>{a.forEach(n=>n())},onForceUpdate:n=>{a.add(n),K(()=>{a.delete(n)})}}),N({[e.defaultsTarget]:{color:o(e,"color"),disabled:o(e,"disabled"),density:o(e,"density"),error:o(e,"error"),inline:o(e,"inline"),modelValue:l,multiple:u(()=>!!e.multiple||e.multiple==null&&Array.isArray(l.value)),name:d,falseIcon:o(e,"falseIcon"),trueIcon:o(e,"trueIcon"),readonly:o(e,"readonly"),ripple:o(e,"ripple"),type:o(e,"type"),valueComparator:o(e,"valueComparator")}}),G(()=>{var n;return f("div",{class:["v-selection-control-group",{"v-selection-control-group--inline":e.inline},e.class],style:e.style,role:e.type==="radio"?"radiogroup":void 0},[(n=s.default)==null?void 0:n.call(s)])}),{}}}),ae=U({label:String,trueValue:null,falseValue:null,value:null,...D(),...R()},"v-selection-control");function te(e){const i=Y($,void 0),{densityClasses:s}=Z(e),l=T(e,"modelValue"),t=u(()=>e.trueValue!==void 0?e.trueValue:e.value!==void 0?e.value:!0),m=u(()=>e.falseValue!==void 0?e.falseValue:!1),d=u(()=>!!e.multiple||e.multiple==null&&Array.isArray(l.value)),a=u({get(){const v=i?i.modelValue.value:l.value;return d.value?v.some(r=>e.valueComparator(r,t.value)):e.valueComparator(v,t.value)},set(v){if(e.readonly)return;const r=v?t.value:m.value;let c=r;d.value&&(c=v?[...A(l.value),r]:A(l.value).filter(V=>!e.valueComparator(V,t.value))),i?i.modelValue.value=c:l.value=c}}),{textColorClasses:n,textColorStyles:b}=p(u(()=>a.value&&!e.error&&!e.disabled?e.color:void 0)),S=u(()=>a.value?e.trueIcon:e.falseIcon);return{group:i,densityClasses:s,trueValue:t,falseValue:m,model:a,textColorClasses:n,textColorStyles:b,icon:S}}const ie=_()({name:"VSelectionControl",directives:{Ripple:Q},inheritAttrs:!1,props:ae(),emits:{"update:modelValue":e=>!0},setup(e,i){let{attrs:s,slots:l}=i;const{group:t,densityClasses:m,icon:d,model:a,textColorClasses:n,textColorStyles:b,trueValue:S}=te(e),v=w(),r=u(()=>e.id||`input-${v}`),c=g(!1),V=g(!1),C=g();t==null||t.onForceUpdate(()=>{C.value&&(C.value.checked=a.value)});function I(y){c.value=!0,(!F||F&&y.target.matches(":focus-visible"))&&(V.value=!0)}function k(){c.value=!1,V.value=!1}function E(y){e.readonly&&t&&ee(()=>t.forceUpdate()),a.value=y.target.checked}return G(()=>{var h,B;const y=l.label?l.label({label:e.label,props:{for:r.value}}):e.label,[q,L]=W(s);return f("div",x({class:["v-selection-control",{"v-selection-control--dirty":a.value,"v-selection-control--disabled":e.disabled,"v-selection-control--error":e.error,"v-selection-control--focused":c.value,"v-selection-control--focus-visible":V.value,"v-selection-control--inline":e.inline},m.value,e.class]},q,{style:e.style}),[f("div",{class:["v-selection-control__wrapper",n.value],style:b.value},[(h=l.default)==null?void 0:h.call(l),z(f("div",{class:["v-selection-control__input"]},[d.value&&f(X,{key:"icon",icon:d.value},null),f("input",x({ref:C,checked:a.value,disabled:e.disabled,id:r.value,onBlur:k,onFocus:I,onInput:E,"aria-disabled":e.readonly,type:e.type,value:S.value,name:e.name,"aria-checked":e.type==="checkbox"?a.value:void 0},L),null),(B=l.input)==null?void 0:B.call(l,{model:a,textColorClasses:n,textColorStyles:b,props:{onFocus:I,onBlur:k,id:r.value}})]),[[J("ripple"),e.ripple&&[!e.disabled&&!e.readonly,null,["center","circle"]]]])]),y&&f(le,{for:r.value,clickable:!0},{default:()=>[y]})])}),{isFocused:c,input:C}}});export{ie as V,R as a,ue as b,ae as m};
