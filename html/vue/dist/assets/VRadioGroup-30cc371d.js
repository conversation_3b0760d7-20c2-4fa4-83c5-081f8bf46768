import{m as C,V as f,a as G,b as A}from"./VSelectionControl-182ca2ca.js";import{I as p,a7 as V,q as a,a8 as d,aZ as O,aQ as c,a_ as h,B as k,a0 as U,a$ as _,F as x}from"./index-9a5dc664.js";import{m as F,a as m,V as N}from"./VTextField-3e2d458d.js";const Q=p()({name:"VRadio",props:{...C({falseIcon:"$radioOff",trueIcon:"$radioOn"})},setup(e,o){let{slots:s}=o;return V(()=>a(f,d(e,{class:["v-radio",e.class],style:e.style,type:"radio"}),s)),{}}});const T=p()({name:"VRadioGroup",inheritAttrs:!1,props:{height:{type:[Number,String],default:"auto"},...F(),...O(G(),["multiple"]),trueIcon:{type:c,default:"$radioOn"},falseIcon:{type:c,default:"$radioOff"},type:{type:String,default:"radio"}},emits:{"update:modelValue":e=>!0},setup(e,o){let{attrs:s,slots:t}=o;const y=h(),i=k(()=>e.id||`radio-group-${y}`),l=U(e,"modelValue");return V(()=>{const[b,v]=_(s),[I,q]=m.filterProps(e),[g,B]=f.filterProps(e),r=t.label?t.label({label:e.label,props:{for:i.value}}):e.label;return a(m,d({class:["v-radio-group",e.class],style:e.style},b,I,{modelValue:l.value,"onUpdate:modelValue":u=>l.value=u,id:i.value}),{...t,default:u=>{let{id:n,messagesId:P,isDisabled:R,isReadonly:$}=u;return a(x,null,[r&&a(N,{id:n.value},{default:()=>[r]}),a(A,d(g,{id:n.value,"aria-describedby":P.value,defaultsTarget:"VRadio",trueIcon:e.trueIcon,falseIcon:e.falseIcon,type:e.type,disabled:R.value,readonly:$.value,"aria-labelledby":r?n.value:void 0,multiple:!1},v,{modelValue:l.value,"onUpdate:modelValue":S=>l.value=S}),t)])}})}),{}}});export{T as V,Q as a};
