import{a9 as R,l as E,E as D,B as z,r as L,o as m,b,w as o,q as a,a8 as U,ag as N,ar as q,s as g,ak as H,as as O,at as P,aC as x,aj as V,y as v,aD as y,aq as F,au as j,z as I,ai as J,az as K,c as T,F as B,a as G,A as C,aA as M,n as f,aE as W,aF as Q,t as X,aG as Y,aB as Z,av as ee,ah as te,ad as ae,af as se,D as oe,aH as ie}from"./index-9a5dc664.js";import{V as le,a as ne}from"./VBadge-6aed7f04.js";import{V as re}from"./VChip-a30ee730.js";import{V as ce}from"./VSpacer-d7832670.js";const de={class:"d-flex align-start gap-3"},ue={key:0},fe={class:"text-sm font-weight-medium mb-1"},me=["innerHTML"],pe={class:"text-body-2 mb-2",style:{"letter-spacing":"0.4px !important","line-height":"18px"}},ve=["innerHTML"],_e={class:"text-sm text-disabled mb-0",style:{"letter-spacing":"0.4px !important","line-height":"18px"}},he={class:"d-flex flex-column align-end"},ge={__name:"Notifications",props:{notifications:{type:Array,required:!0},badgeProps:{type:Object,required:!1,default:void 0},location:{type:null,required:!1,default:"bottom end"}},emits:["read","unread","remove","click:notification","clearAll"],setup(w,{emit:u}){const i=w,{t:d,locale:A}=R(),p=E(!1);let k=i.notifications.length;D(()=>i.notifications,(t,r)=>{if(t.length>k){const h=t.slice(k),e=d("New version");h.some(s=>s.title.startsWith(e))&&(p.value=!0)}k=t.length},{deep:!0});const _=z(()=>i.notifications.some(t=>t.isSeen===!1)),n=()=>{const t=i.notifications.map(r=>r.id);_.value?u("read",t):u("unread",t)},S=z(()=>i.notifications.filter(t=>t.isSeen===!1).length),$=(t,r)=>{t?u("unread",[r]):u("read",[r])};return(t,r)=>{const h=L("IconBtn");return m(),b(h,{id:"notification-btn"},{default:o(()=>[a(le,U(i.badgeProps,{"model-value":i.notifications.some(e=>!e.isSeen),color:"error",dot:"","offset-x":"2","offset-y":"3"}),{default:o(()=>[a(N,{icon:"tabler-bell"})]),_:1},16,["model-value"]),a(q,{modelValue:g(p),"onUpdate:modelValue":r[1]||(r[1]=e=>H(p)?p.value=e:null),activator:"parent",width:"380px",location:i.location,offset:"12px","close-on-content-click":!1},{default:o(()=>[a(O,{class:"d-flex flex-column"},{default:o(()=>[a(P,{class:"notification-section"},{append:o(()=>[x(a(re,{size:"small",color:"primary",class:"me-2"},{default:o(()=>[V(v(g(S))+" New ",1)]),_:1},512),[[y,i.notifications.some(e=>!e.isSeen)]]),x(a(h,{size:"34",onClick:n},{default:o(()=>[a(N,{size:"20",color:"high-emphasis",icon:g(_)?"tabler-mail-opened":"tabler-mail"},null,8,["icon"]),a(F,{activator:"parent",location:"start"},{default:o(()=>[V(v(g(_)?t.$t("Mark all as read"):t.$t("Mark all as unread"))+" ",1)]),_:1})]),_:1},512),[[y,i.notifications.length]])]),default:o(()=>[a(j,{class:"text-h6"},{default:o(()=>[V(v(t.$t("Notifications")),1)]),_:1})]),_:1}),a(I),a(g(J),{options:{wheelPropagation:!1},style:{"max-block-size":"23.75rem"}},{default:o(()=>[a(K,{class:"notification-list rounded-0 py-0"},{default:o(()=>[(m(!0),T(B,null,G(i.notifications,(e,s)=>(m(),T(B,{key:e.title},[s>0?(m(),b(I,{key:0})):C("",!0),a(M,{link:"",lines:"one","min-height":"66px",class:"list-item-hover-class",onClick:l=>t.$emit("click:notification",e)},{default:o(()=>[f("div",de,[a(W,{color:e.color&&!e.img?e.color:void 0,variant:e.img?void 0:"tonal"},{default:o(()=>[e.text?(m(),T("span",ue,v(t.avatarText(e.text)),1)):C("",!0),e.img?(m(),b(Q,{key:1,src:e.img},null,8,["src"])):C("",!0),e.icon?(m(),b(N,{key:2,icon:e.icon},null,8,["icon"])):C("",!0)]),_:2},1032,["color","variant"]),f("div",null,[f("p",fe,[f("span",{innerHTML:e.title},null,8,me)]),f("p",pe,[f("span",{innerHTML:e.subtitle},null,8,ve)]),f("p",_e,v(e.time),1)]),a(ce),f("div",he,[a(N,{size:"10",icon:"tabler-circle-filled",color:e.isSeen?"#a8aaae":"primary",class:X([`${e.isSeen?"visible-in-hover":""}`,"mb-2"]),onClick:Y(l=>$(e.isSeen,e.id),["stop"])},null,8,["color","class","onClick"]),a(N,{size:"20",icon:"tabler-x",class:"visible-in-hover",onClick:l=>t.$emit("remove",e.id)},null,8,["onClick"])])])]),_:2},1032,["onClick"])],64))),128)),x(a(M,{class:"text-center text-medium-emphasis",style:{"block-size":"56px"}},{default:o(()=>[a(Z,null,{default:o(()=>[V(v(t.$t("No Notification Found"))+"!",1)]),_:1})]),_:1},512),[[y,!i.notifications.length]])]),_:1})]),_:1}),a(I),x(a(ee,{class:"pa-4"},{default:o(()=>[a(te,{block:"",size:"small",color:"error",onClick:r[0]||(r[0]=e=>u("clearAll"))},{default:o(()=>[V(v(t.$t("Clear All Notifications")),1)]),_:1})]),_:1},512),[[y,i.notifications.length]])]),_:1})]),_:1},8,["modelValue","location"])]),_:1})}}},xe={__name:"NavBarNotifications",setup(w){const{t:u,locale:i}=R(),d=ae();d.cloud.expire_date;const A=d.userInfo.name,p=d.cloudVersion,k=d.version.value,_=d.updateInfoDesc,{notifications:n}=se(d);d.addNotifications({id:1,img:ne,title:"Congratulation "+A+"! 🎉 ",subtitle:"Start the adventure",time:"Today",isSeen:!0});const S=()=>{const s=`${u("New version")} v${p}`;n.value=n.value.filter(c=>!(c.title.includes(u("New version"))&&!c.title.includes(s)));const l=n.value.some(c=>c.title.includes(s));p>k&&!l&&d.addNotifications({id:new Date().getTime(),img:ie,title:s+" 👋🏻",subtitle:_.updateInfo,time:u("updated_at")+": "+_.time,isSeen:!1,color:"error"})};oe(()=>{const s=localStorage.getItem("notifications");s&&(n.value=JSON.parse(s)),S()}),D(n,s=>{localStorage.setItem("notifications",JSON.stringify(s))},{deep:!0});const $=s=>{n.value.forEach((l,c)=>{s===l.id&&n.value.splice(c,1)})},t=s=>{n.value.forEach(l=>{s.forEach(c=>{c===l.id&&(l.isSeen=!0)})})},r=s=>{n.value.forEach(l=>{s.forEach(c=>{c===l.id&&(l.isSeen=!1)})})},h=s=>{s.isSeen||t([s.id])},e=()=>{d.clearNotifications()};return(s,l)=>{const c=ge;return m(),b(c,{notifications:g(n),onRemove:$,onRead:t,onUnread:r,"onClick:notification":h,onClearAll:e},null,8,["notifications"])}}};export{xe as default};
