import{D as Pn,a3 as kr,E as Ee,b1 as jn,l as H,bQ as qa,bR as bl,bS as Cl,s as wl,B as q,bT as Sl,d as de,b2 as Ce,q as Le,bd as Ya,bk as xl,bU as Ol,o as E,c as D,n as _,t as oe,aY as ee,F as Ge,a as Kt,A as V,aC as vr,b4 as _l,aj as Xa,y as hr,r as G,b as J,aD as hn,p as gn,w as _o,e as Ao,a8 as $o,bV as Al,aN as Ot,aO as _t}from"./index-9a5dc664.js";import{t as W}from"./tinycolor-ea5bcbb6.js";function Ja(e){return bl()?(Cl(e),!0):!1}function qe(e){return typeof e=="function"?e():wl(e)}const Pr=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const $l=Object.prototype.toString,El=e=>$l.call(e)==="[object Object]",dt=()=>{},Tl=kl();function kl(){var e,t;return Pr&&((e=window==null?void 0:window.navigator)==null?void 0:e.userAgent)&&(/iP(?:ad|hone|od)/.test(window.navigator.userAgent)||((t=window==null?void 0:window.navigator)==null?void 0:t.maxTouchPoints)>2&&/iPad|Macintosh/.test(window==null?void 0:window.navigator.userAgent))}function Za(e,t){function r(...n){return new Promise((o,a)=>{Promise.resolve(e(()=>t.apply(this,n),{fn:t,thisArg:this,args:n})).then(o).catch(a)})}return r}const Qa=e=>e();function Pl(e,t={}){let r,n,o=dt;const a=i=>{clearTimeout(i),o(),o=dt};return i=>{const l=qe(e),c=qe(t.maxWait);return r&&a(r),l<=0||c!==void 0&&c<=0?(n&&(a(n),n=null),Promise.resolve(i())):new Promise((u,f)=>{o=t.rejectOnCancel?f:u,c&&!n&&(n=setTimeout(()=>{r&&a(r),n=null,u(i())},c)),r=setTimeout(()=>{n&&a(n),n=null,u(i())},l)})}}function jl(e=Qa){const t=H(!0);function r(){t.value=!1}function n(){t.value=!0}const o=(...a)=>{t.value&&e(...a)};return{isActive:qa(t),pause:r,resume:n,eventFilter:o}}function Rl(e){let t;function r(){return t||(t=e()),t}return r.reset=async()=>{const n=t;t=void 0,n&&await n},r}function Il(e){return e||jn()}function Ae(e,t=200,r={}){return Za(Pl(t,r),e)}function Ml(e,t,r={}){const{eventFilter:n=Qa,...o}=r;return Ee(e,Za(n,t),o)}function Ll(e,t,r={}){const{eventFilter:n,...o}=r,{eventFilter:a,pause:s,resume:i,isActive:l}=jl(n);return{stop:Ml(e,t,{...o,eventFilter:a}),pause:s,resume:i,isActive:l}}function Qe(e,t=!0,r){Il()?Pn(e,r):t?e():kr(e)}function Bl(e,t,r={}){const{immediate:n=!0}=r,o=H(!1);let a=null;function s(){a&&(clearTimeout(a),a=null)}function i(){o.value=!1,s()}function l(...c){s(),o.value=!0,a=setTimeout(()=>{o.value=!1,a=null,e(...c)},qe(t))}return n&&(o.value=!0,Pr&&l()),Ja(i),{isPending:qa(o),start:l,stop:i}}function re(e,t,r){const n=Ee(e,(o,a,s)=>{o&&(r!=null&&r.once&&kr(()=>n()),t(o,a,s))},{...r,once:!1});return n}function Dt(e){var t;const r=qe(e);return(t=r==null?void 0:r.$el)!=null?t:r}const Gt=Pr?window:void 0,ei=Pr?window.navigator:void 0;function Ue(...e){let t,r,n,o;if(typeof e[0]=="string"||Array.isArray(e[0])?([r,n,o]=e,t=Gt):[t,r,n,o]=e,!t)return dt;Array.isArray(r)||(r=[r]),Array.isArray(n)||(n=[n]);const a=[],s=()=>{a.forEach(u=>u()),a.length=0},i=(u,f,d,p)=>(u.addEventListener(f,d,p),()=>u.removeEventListener(f,d,p)),l=Ee(()=>[Dt(t),qe(o)],([u,f])=>{if(s(),!u)return;const d=El(f)?{...f}:f;a.push(...r.flatMap(p=>n.map(g=>i(u,p,g,d))))},{immediate:!0,flush:"post"}),c=()=>{l(),s()};return Ja(c),c}let Eo=!1;function Dl(e,t,r={}){const{window:n=Gt,ignore:o=[],capture:a=!0,detectIframe:s=!1}=r;if(!n)return dt;Tl&&!Eo&&(Eo=!0,Array.from(n.document.body.children).forEach(d=>d.addEventListener("click",dt)),n.document.documentElement.addEventListener("click",dt));let i=!0;const l=d=>o.some(p=>{if(typeof p=="string")return Array.from(n.document.querySelectorAll(p)).some(g=>g===d.target||d.composedPath().includes(g));{const g=Dt(p);return g&&(d.target===g||d.composedPath().includes(g))}}),u=[Ue(n,"click",d=>{const p=Dt(e);if(!(!p||p===d.target||d.composedPath().includes(p))){if(d.detail===0&&(i=!l(d)),!i){i=!0;return}t(d)}},{passive:!0,capture:a}),Ue(n,"pointerdown",d=>{const p=Dt(e);i=!l(d)&&!!(p&&!d.composedPath().includes(p))},{passive:!0}),s&&Ue(n,"blur",d=>{setTimeout(()=>{var p;const g=Dt(e);((p=n.document.activeElement)==null?void 0:p.tagName)==="IFRAME"&&!(g!=null&&g.contains(n.document.activeElement))&&t(d)},0)})].filter(Boolean);return()=>u.forEach(d=>d())}function Hl(){const e=H(!1),t=jn();return t&&Pn(()=>{e.value=!0},t),e}function ti(e){const t=Hl();return q(()=>(t.value,!!e()))}function To(e,t={}){const{controls:r=!1,navigator:n=ei}=t,o=ti(()=>n&&"permissions"in n);let a;const s=typeof e=="string"?{name:e}:e,i=H(),l=()=>{a&&(i.value=a.state)},c=Rl(async()=>{if(o.value){if(!a)try{a=await n.permissions.query(s),Ue(a,"change",l),l()}catch{i.value="prompt"}return a}});return c(),r?{state:i,isSupported:o,query:c}:i}function Nl(e={}){const{navigator:t=ei,read:r=!1,source:n,copiedDuring:o=1500,legacy:a=!1}=e,s=ti(()=>t&&"clipboard"in t),i=To("clipboard-read"),l=To("clipboard-write"),c=q(()=>s.value||a),u=H(""),f=H(!1),d=Bl(()=>f.value=!1,o);function p(){s.value&&y(i.value)?t.clipboard.readText().then(b=>{u.value=b}):u.value=h()}c.value&&r&&Ue(["copy","cut"],p);async function g(b=qe(n)){c.value&&b!=null&&(s.value&&y(l.value)?await t.clipboard.writeText(b):v(b),u.value=b,f.value=!0,d.start())}function v(b){const C=document.createElement("textarea");C.value=b??"",C.style.position="absolute",C.style.opacity="0",document.body.appendChild(C),C.select(),document.execCommand("copy"),C.remove()}function h(){var b,C,w;return(w=(C=(b=document==null?void 0:document.getSelection)==null?void 0:b.call(document))==null?void 0:C.toString())!=null?w:""}function y(b){return b==="granted"||b==="prompt"}return{isSupported:c,text:u,copied:f,copy:g}}const ir=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},lr="__vueuse_ssr_handlers__",Vl=Fl();function Fl(){return lr in ir||(ir[lr]=ir[lr]||{}),ir[lr]}function Wl(e,t){return Vl[e]||t}function zl(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}const Ul={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},ko="vueuse-storage";function Kl(e,t,r,n={}){var o;const{flush:a="pre",deep:s=!0,listenToStorageChanges:i=!0,writeDefaults:l=!0,mergeDefaults:c=!1,shallow:u,window:f=Gt,eventFilter:d,onError:p=$=>{console.error($)},initOnMounted:g}=n,v=(u?Sl:H)(typeof t=="function"?t():t);if(!r)try{r=Wl("getDefaultStorage",()=>{var $;return($=Gt)==null?void 0:$.localStorage})()}catch($){p($)}if(!r)return v;const h=qe(t),y=zl(h),b=(o=n.serializer)!=null?o:Ul[y],{pause:C,resume:w}=Ll(v,()=>x(v.value),{flush:a,deep:s,eventFilter:d});f&&i&&Qe(()=>{Ue(f,"storage",I),Ue(f,ko,j),g&&I()}),g||I();function O($,m){f&&f.dispatchEvent(new CustomEvent(ko,{detail:{key:e,oldValue:$,newValue:m,storageArea:r}}))}function x($){try{const m=r.getItem(e);if($==null)O(m,null),r.removeItem(e);else{const A=b.write($);m!==A&&(r.setItem(e,A),O(m,A))}}catch(m){p(m)}}function k($){const m=$?$.newValue:r.getItem(e);if(m==null)return l&&h!=null&&r.setItem(e,b.write(h)),h;if(!$&&c){const A=b.read(m);return typeof c=="function"?c(A,h):y==="object"&&!Array.isArray(A)?{...h,...A}:A}else return typeof m!="string"?m:b.read(m)}function I($){if(!($&&$.storageArea!==r)){if($&&$.key==null){v.value=h;return}if(!($&&$.key!==e)){C();try{($==null?void 0:$.newValue)!==b.write(v.value)&&(v.value=k($))}catch(m){p(m)}finally{$?kr(w):w()}}}}function j($){I($.detail)}return v}function Rn(e,t,r={}){const{window:n=Gt}=r;return Kl(e,t,n==null?void 0:n.localStorage,r)}var et=et||{};et.stringify=function(){var e={"visit_linear-gradient":function(t){return e.visit_gradient(t)},"visit_repeating-linear-gradient":function(t){return e.visit_gradient(t)},"visit_radial-gradient":function(t){return e.visit_gradient(t)},"visit_repeating-radial-gradient":function(t){return e.visit_gradient(t)},visit_gradient:function(t){var r=e.visit(t.orientation);return r&&(r+=", "),t.type+"("+r+e.visit(t.colorStops)+")"},visit_shape:function(t){var r=t.value,n=e.visit(t.at),o=e.visit(t.style);return o&&(r+=" "+o),n&&(r+=" at "+n),r},"visit_default-radial":function(t){var r="",n=e.visit(t.at);return n&&(r+=n),r},"visit_extent-keyword":function(t){var r=t.value,n=e.visit(t.at);return n&&(r+=" at "+n),r},"visit_position-keyword":function(t){return t.value},visit_position:function(t){return e.visit(t.value.x)+" "+e.visit(t.value.y)},"visit_%":function(t){return t.value+"%"},visit_em:function(t){return t.value+"em"},visit_px:function(t){return t.value+"px"},visit_literal:function(t){return e.visit_color(t.value,t)},visit_hex:function(t){return e.visit_color("#"+t.value,t)},visit_rgb:function(t){return e.visit_color("rgb("+t.value.join(", ")+")",t)},visit_rgba:function(t){return e.visit_color("rgba("+t.value.join(", ")+")",t)},visit_color:function(t,r){var n=t,o=e.visit(r.length);return o&&(n+=" "+o),n},visit_angular:function(t){return t.value+"deg"},visit_directional:function(t){return"to "+t.value},visit_array:function(t){var r="",n=t.length;return t.forEach(function(o,a){r+=e.visit(o),a<n-1&&(r+=", ")}),r},visit:function(t){if(!t)return"";var r="";if(t instanceof Array)return e.visit_array(t,r);if(t.type){var n=e["visit_"+t.type];if(n)return n(t);throw Error("Missing visitor visit_"+t.type)}else throw Error("Invalid node.")}};return function(t){return e.visit(t)}}();var et=et||{};et.parse=function(){var e={linearGradient:/^(\-(webkit|o|ms|moz)\-)?(linear\-gradient)/i,repeatingLinearGradient:/^(\-(webkit|o|ms|moz)\-)?(repeating\-linear\-gradient)/i,radialGradient:/^(\-(webkit|o|ms|moz)\-)?(radial\-gradient)/i,repeatingRadialGradient:/^(\-(webkit|o|ms|moz)\-)?(repeating\-radial\-gradient)/i,sideOrCorner:/^to (left (top|bottom)|right (top|bottom)|left|right|top|bottom)/i,extentKeywords:/^(closest\-side|closest\-corner|farthest\-side|farthest\-corner|contain|cover)/,positionKeywords:/^(left|center|right|top|bottom)/i,pixelValue:/^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))px/,percentageValue:/^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))\%/,emValue:/^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))em/,angleValue:/^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))deg/,startCall:/^\(/,endCall:/^\)/,comma:/^,/,hexColor:/^\#([0-9a-fA-F]+)/,literalColor:/^([a-zA-Z]+)/,rgbColor:/^rgb/i,rgbaColor:/^rgba/i,number:/^(([0-9]*\.[0-9]+)|([0-9]+\.?))/},t="";function r(S){var M=new Error(t+": "+S);throw M.source=t,M}function n(){var S=o();return t.length>0&&r("Invalid input not EOF"),S}function o(){return C(a)}function a(){return s("linear-gradient",e.linearGradient,l)||s("repeating-linear-gradient",e.repeatingLinearGradient,l)||s("radial-gradient",e.radialGradient,f)||s("repeating-radial-gradient",e.repeatingRadialGradient,f)}function s(S,M,B){return i(M,function(ie){var Re=B();return Re&&(L(e.comma)||r("Missing comma before color stops")),{type:S,orientation:Re,colorStops:C(w)}})}function i(S,M){var B=L(S);if(B){L(e.startCall)||r("Missing (");var ie=M(B);return L(e.endCall)||r("Missing )"),ie}}function l(){return c()||u()}function c(){return R("directional",e.sideOrCorner,1)}function u(){return R("angular",e.angleValue,1)}function f(){var S,M=d(),B;return M&&(S=[],S.push(M),B=t,L(e.comma)&&(M=d(),M?S.push(M):t=B)),S}function d(){var S=p()||g();if(S)S.at=h();else{var M=v();if(M){S=M;var B=h();B&&(S.at=B)}else{var ie=y();ie&&(S={type:"default-radial",at:ie})}}return S}function p(){var S=R("shape",/^(circle)/i,0);return S&&(S.style=P()||v()),S}function g(){var S=R("shape",/^(ellipse)/i,0);return S&&(S.style=m()||v()),S}function v(){return R("extent-keyword",e.extentKeywords,1)}function h(){if(R("position",/^at/,0)){var S=y();return S||r("Missing positioning value"),S}}function y(){var S=b();if(S.x||S.y)return{type:"position",value:S}}function b(){return{x:m(),y:m()}}function C(S){var M=S(),B=[];if(M)for(B.push(M);L(e.comma);)M=S(),M?B.push(M):r("One extra comma");return B}function w(){var S=O();return S||r("Expected color definition"),S.length=m(),S}function O(){return k()||j()||I()||x()}function x(){return R("literal",e.literalColor,0)}function k(){return R("hex",e.hexColor,1)}function I(){return i(e.rgbColor,function(){return{type:"rgb",value:C($)}})}function j(){return i(e.rgbaColor,function(){return{type:"rgba",value:C($)}})}function $(){return L(e.number)[1]}function m(){return R("%",e.percentageValue,1)||A()||P()}function A(){return R("position-keyword",e.positionKeywords,1)}function P(){return R("px",e.pixelValue,1)||R("em",e.emValue,1)}function R(S,M,B){var ie=L(M);if(ie)return{type:S,value:ie[B]}}function L(S){var M,B;return B=/^[\n\r\t\s]+/.exec(t),B&&X(B[0].length),M=S.exec(t),M&&X(M[0].length),M}function X(S){t=t.substr(S)}return function(S){return t=S.toString(),n()}}();var Gl=et.parse,ql=et.stringify,se="top",ye="bottom",me="right",ue="left",In="auto",Qt=[se,ye,me,ue],vt="start",qt="end",Yl="clippingParents",ri="viewport",Rt="popper",Xl="reference",Po=Qt.reduce(function(e,t){return e.concat([t+"-"+vt,t+"-"+qt])},[]),ni=[].concat(Qt,[In]).reduce(function(e,t){return e.concat([t,t+"-"+vt,t+"-"+qt])},[]),Jl="beforeRead",Zl="read",Ql="afterRead",es="beforeMain",ts="main",rs="afterMain",ns="beforeWrite",os="write",as="afterWrite",is=[Jl,Zl,Ql,es,ts,rs,ns,os,as];function Oe(e){return e?(e.nodeName||"").toLowerCase():null}function fe(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Ye(e){var t=fe(e).Element;return e instanceof t||e instanceof Element}function he(e){var t=fe(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function Mn(e){if(typeof ShadowRoot>"u")return!1;var t=fe(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function ls(e){var t=e.state;Object.keys(t.elements).forEach(function(r){var n=t.styles[r]||{},o=t.attributes[r]||{},a=t.elements[r];!he(a)||!Oe(a)||(Object.assign(a.style,n),Object.keys(o).forEach(function(s){var i=o[s];i===!1?a.removeAttribute(s):a.setAttribute(s,i===!0?"":i)}))})}function ss(e){var t=e.state,r={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,r.popper),t.styles=r,t.elements.arrow&&Object.assign(t.elements.arrow.style,r.arrow),function(){Object.keys(t.elements).forEach(function(n){var o=t.elements[n],a=t.attributes[n]||{},s=Object.keys(t.styles.hasOwnProperty(n)?t.styles[n]:r[n]),i=s.reduce(function(l,c){return l[c]="",l},{});!he(o)||!Oe(o)||(Object.assign(o.style,i),Object.keys(a).forEach(function(l){o.removeAttribute(l)}))})}}const us={name:"applyStyles",enabled:!0,phase:"write",fn:ls,effect:ss,requires:["computeStyles"]};function Se(e){return e.split("-")[0]}var Ke=Math.max,gr=Math.min,ht=Math.round;function yn(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function oi(){return!/^((?!chrome|android).)*safari/i.test(yn())}function gt(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!1);var n=e.getBoundingClientRect(),o=1,a=1;t&&he(e)&&(o=e.offsetWidth>0&&ht(n.width)/e.offsetWidth||1,a=e.offsetHeight>0&&ht(n.height)/e.offsetHeight||1);var s=Ye(e)?fe(e):window,i=s.visualViewport,l=!oi()&&r,c=(n.left+(l&&i?i.offsetLeft:0))/o,u=(n.top+(l&&i?i.offsetTop:0))/a,f=n.width/o,d=n.height/a;return{width:f,height:d,top:u,right:c+f,bottom:u+d,left:c,x:c,y:u}}function Ln(e){var t=gt(e),r=e.offsetWidth,n=e.offsetHeight;return Math.abs(t.width-r)<=1&&(r=t.width),Math.abs(t.height-n)<=1&&(n=t.height),{x:e.offsetLeft,y:e.offsetTop,width:r,height:n}}function ai(e,t){var r=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(r&&Mn(r)){var n=t;do{if(n&&e.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function Te(e){return fe(e).getComputedStyle(e)}function cs(e){return["table","td","th"].indexOf(Oe(e))>=0}function Ne(e){return((Ye(e)?e.ownerDocument:e.document)||window.document).documentElement}function jr(e){return Oe(e)==="html"?e:e.assignedSlot||e.parentNode||(Mn(e)?e.host:null)||Ne(e)}function jo(e){return!he(e)||Te(e).position==="fixed"?null:e.offsetParent}function fs(e){var t=/firefox/i.test(yn()),r=/Trident/i.test(yn());if(r&&he(e)){var n=Te(e);if(n.position==="fixed")return null}var o=jr(e);for(Mn(o)&&(o=o.host);he(o)&&["html","body"].indexOf(Oe(o))<0;){var a=Te(o);if(a.transform!=="none"||a.perspective!=="none"||a.contain==="paint"||["transform","perspective"].indexOf(a.willChange)!==-1||t&&a.willChange==="filter"||t&&a.filter&&a.filter!=="none")return o;o=o.parentNode}return null}function er(e){for(var t=fe(e),r=jo(e);r&&cs(r)&&Te(r).position==="static";)r=jo(r);return r&&(Oe(r)==="html"||Oe(r)==="body"&&Te(r).position==="static")?t:r||fs(e)||t}function Bn(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Nt(e,t,r){return Ke(e,gr(t,r))}function ds(e,t,r){var n=Nt(e,t,r);return n>r?r:n}function ii(){return{top:0,right:0,bottom:0,left:0}}function li(e){return Object.assign({},ii(),e)}function si(e,t){return t.reduce(function(r,n){return r[n]=e,r},{})}var ps=function(t,r){return t=typeof t=="function"?t(Object.assign({},r.rects,{placement:r.placement})):t,li(typeof t!="number"?t:si(t,Qt))};function vs(e){var t,r=e.state,n=e.name,o=e.options,a=r.elements.arrow,s=r.modifiersData.popperOffsets,i=Se(r.placement),l=Bn(i),c=[ue,me].indexOf(i)>=0,u=c?"height":"width";if(!(!a||!s)){var f=ps(o.padding,r),d=Ln(a),p=l==="y"?se:ue,g=l==="y"?ye:me,v=r.rects.reference[u]+r.rects.reference[l]-s[l]-r.rects.popper[u],h=s[l]-r.rects.reference[l],y=er(a),b=y?l==="y"?y.clientHeight||0:y.clientWidth||0:0,C=v/2-h/2,w=f[p],O=b-d[u]-f[g],x=b/2-d[u]/2+C,k=Nt(w,x,O),I=l;r.modifiersData[n]=(t={},t[I]=k,t.centerOffset=k-x,t)}}function hs(e){var t=e.state,r=e.options,n=r.element,o=n===void 0?"[data-popper-arrow]":n;o!=null&&(typeof o=="string"&&(o=t.elements.popper.querySelector(o),!o)||ai(t.elements.popper,o)&&(t.elements.arrow=o))}const gs={name:"arrow",enabled:!0,phase:"main",fn:vs,effect:hs,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function yt(e){return e.split("-")[1]}var ys={top:"auto",right:"auto",bottom:"auto",left:"auto"};function ms(e,t){var r=e.x,n=e.y,o=t.devicePixelRatio||1;return{x:ht(r*o)/o||0,y:ht(n*o)/o||0}}function Ro(e){var t,r=e.popper,n=e.popperRect,o=e.placement,a=e.variation,s=e.offsets,i=e.position,l=e.gpuAcceleration,c=e.adaptive,u=e.roundOffsets,f=e.isFixed,d=s.x,p=d===void 0?0:d,g=s.y,v=g===void 0?0:g,h=typeof u=="function"?u({x:p,y:v}):{x:p,y:v};p=h.x,v=h.y;var y=s.hasOwnProperty("x"),b=s.hasOwnProperty("y"),C=ue,w=se,O=window;if(c){var x=er(r),k="clientHeight",I="clientWidth";if(x===fe(r)&&(x=Ne(r),Te(x).position!=="static"&&i==="absolute"&&(k="scrollHeight",I="scrollWidth")),x=x,o===se||(o===ue||o===me)&&a===qt){w=ye;var j=f&&x===O&&O.visualViewport?O.visualViewport.height:x[k];v-=j-n.height,v*=l?1:-1}if(o===ue||(o===se||o===ye)&&a===qt){C=me;var $=f&&x===O&&O.visualViewport?O.visualViewport.width:x[I];p-=$-n.width,p*=l?1:-1}}var m=Object.assign({position:i},c&&ys),A=u===!0?ms({x:p,y:v},fe(r)):{x:p,y:v};if(p=A.x,v=A.y,l){var P;return Object.assign({},m,(P={},P[w]=b?"0":"",P[C]=y?"0":"",P.transform=(O.devicePixelRatio||1)<=1?"translate("+p+"px, "+v+"px)":"translate3d("+p+"px, "+v+"px, 0)",P))}return Object.assign({},m,(t={},t[w]=b?v+"px":"",t[C]=y?p+"px":"",t.transform="",t))}function bs(e){var t=e.state,r=e.options,n=r.gpuAcceleration,o=n===void 0?!0:n,a=r.adaptive,s=a===void 0?!0:a,i=r.roundOffsets,l=i===void 0?!0:i,c={placement:Se(t.placement),variation:yt(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:o,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,Ro(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:s,roundOffsets:l})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,Ro(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}const Cs={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:bs,data:{}};var sr={passive:!0};function ws(e){var t=e.state,r=e.instance,n=e.options,o=n.scroll,a=o===void 0?!0:o,s=n.resize,i=s===void 0?!0:s,l=fe(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&c.forEach(function(u){u.addEventListener("scroll",r.update,sr)}),i&&l.addEventListener("resize",r.update,sr),function(){a&&c.forEach(function(u){u.removeEventListener("scroll",r.update,sr)}),i&&l.removeEventListener("resize",r.update,sr)}}const Ss={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:ws,data:{}};var xs={left:"right",right:"left",bottom:"top",top:"bottom"};function dr(e){return e.replace(/left|right|bottom|top/g,function(t){return xs[t]})}var Os={start:"end",end:"start"};function Io(e){return e.replace(/start|end/g,function(t){return Os[t]})}function Dn(e){var t=fe(e),r=t.pageXOffset,n=t.pageYOffset;return{scrollLeft:r,scrollTop:n}}function Hn(e){return gt(Ne(e)).left+Dn(e).scrollLeft}function _s(e,t){var r=fe(e),n=Ne(e),o=r.visualViewport,a=n.clientWidth,s=n.clientHeight,i=0,l=0;if(o){a=o.width,s=o.height;var c=oi();(c||!c&&t==="fixed")&&(i=o.offsetLeft,l=o.offsetTop)}return{width:a,height:s,x:i+Hn(e),y:l}}function As(e){var t,r=Ne(e),n=Dn(e),o=(t=e.ownerDocument)==null?void 0:t.body,a=Ke(r.scrollWidth,r.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),s=Ke(r.scrollHeight,r.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),i=-n.scrollLeft+Hn(e),l=-n.scrollTop;return Te(o||r).direction==="rtl"&&(i+=Ke(r.clientWidth,o?o.clientWidth:0)-a),{width:a,height:s,x:i,y:l}}function Nn(e){var t=Te(e),r=t.overflow,n=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(r+o+n)}function ui(e){return["html","body","#document"].indexOf(Oe(e))>=0?e.ownerDocument.body:he(e)&&Nn(e)?e:ui(jr(e))}function Vt(e,t){var r;t===void 0&&(t=[]);var n=ui(e),o=n===((r=e.ownerDocument)==null?void 0:r.body),a=fe(n),s=o?[a].concat(a.visualViewport||[],Nn(n)?n:[]):n,i=t.concat(s);return o?i:i.concat(Vt(jr(s)))}function mn(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function $s(e,t){var r=gt(e,!1,t==="fixed");return r.top=r.top+e.clientTop,r.left=r.left+e.clientLeft,r.bottom=r.top+e.clientHeight,r.right=r.left+e.clientWidth,r.width=e.clientWidth,r.height=e.clientHeight,r.x=r.left,r.y=r.top,r}function Mo(e,t,r){return t===ri?mn(_s(e,r)):Ye(t)?$s(t,r):mn(As(Ne(e)))}function Es(e){var t=Vt(jr(e)),r=["absolute","fixed"].indexOf(Te(e).position)>=0,n=r&&he(e)?er(e):e;return Ye(n)?t.filter(function(o){return Ye(o)&&ai(o,n)&&Oe(o)!=="body"}):[]}function Ts(e,t,r,n){var o=t==="clippingParents"?Es(e):[].concat(t),a=[].concat(o,[r]),s=a[0],i=a.reduce(function(l,c){var u=Mo(e,c,n);return l.top=Ke(u.top,l.top),l.right=gr(u.right,l.right),l.bottom=gr(u.bottom,l.bottom),l.left=Ke(u.left,l.left),l},Mo(e,s,n));return i.width=i.right-i.left,i.height=i.bottom-i.top,i.x=i.left,i.y=i.top,i}function ci(e){var t=e.reference,r=e.element,n=e.placement,o=n?Se(n):null,a=n?yt(n):null,s=t.x+t.width/2-r.width/2,i=t.y+t.height/2-r.height/2,l;switch(o){case se:l={x:s,y:t.y-r.height};break;case ye:l={x:s,y:t.y+t.height};break;case me:l={x:t.x+t.width,y:i};break;case ue:l={x:t.x-r.width,y:i};break;default:l={x:t.x,y:t.y}}var c=o?Bn(o):null;if(c!=null){var u=c==="y"?"height":"width";switch(a){case vt:l[c]=l[c]-(t[u]/2-r[u]/2);break;case qt:l[c]=l[c]+(t[u]/2-r[u]/2);break}}return l}function Yt(e,t){t===void 0&&(t={});var r=t,n=r.placement,o=n===void 0?e.placement:n,a=r.strategy,s=a===void 0?e.strategy:a,i=r.boundary,l=i===void 0?Yl:i,c=r.rootBoundary,u=c===void 0?ri:c,f=r.elementContext,d=f===void 0?Rt:f,p=r.altBoundary,g=p===void 0?!1:p,v=r.padding,h=v===void 0?0:v,y=li(typeof h!="number"?h:si(h,Qt)),b=d===Rt?Xl:Rt,C=e.rects.popper,w=e.elements[g?b:d],O=Ts(Ye(w)?w:w.contextElement||Ne(e.elements.popper),l,u,s),x=gt(e.elements.reference),k=ci({reference:x,element:C,strategy:"absolute",placement:o}),I=mn(Object.assign({},C,k)),j=d===Rt?I:x,$={top:O.top-j.top+y.top,bottom:j.bottom-O.bottom+y.bottom,left:O.left-j.left+y.left,right:j.right-O.right+y.right},m=e.modifiersData.offset;if(d===Rt&&m){var A=m[o];Object.keys($).forEach(function(P){var R=[me,ye].indexOf(P)>=0?1:-1,L=[se,ye].indexOf(P)>=0?"y":"x";$[P]+=A[L]*R})}return $}function ks(e,t){t===void 0&&(t={});var r=t,n=r.placement,o=r.boundary,a=r.rootBoundary,s=r.padding,i=r.flipVariations,l=r.allowedAutoPlacements,c=l===void 0?ni:l,u=yt(n),f=u?i?Po:Po.filter(function(g){return yt(g)===u}):Qt,d=f.filter(function(g){return c.indexOf(g)>=0});d.length===0&&(d=f);var p=d.reduce(function(g,v){return g[v]=Yt(e,{placement:v,boundary:o,rootBoundary:a,padding:s})[Se(v)],g},{});return Object.keys(p).sort(function(g,v){return p[g]-p[v]})}function Ps(e){if(Se(e)===In)return[];var t=dr(e);return[Io(e),t,Io(t)]}function js(e){var t=e.state,r=e.options,n=e.name;if(!t.modifiersData[n]._skip){for(var o=r.mainAxis,a=o===void 0?!0:o,s=r.altAxis,i=s===void 0?!0:s,l=r.fallbackPlacements,c=r.padding,u=r.boundary,f=r.rootBoundary,d=r.altBoundary,p=r.flipVariations,g=p===void 0?!0:p,v=r.allowedAutoPlacements,h=t.options.placement,y=Se(h),b=y===h,C=l||(b||!g?[dr(h)]:Ps(h)),w=[h].concat(C).reduce(function(Ie,we){return Ie.concat(Se(we)===In?ks(t,{placement:we,boundary:u,rootBoundary:f,padding:c,flipVariations:g,allowedAutoPlacements:v}):we)},[]),O=t.rects.reference,x=t.rects.popper,k=new Map,I=!0,j=w[0],$=0;$<w.length;$++){var m=w[$],A=Se(m),P=yt(m)===vt,R=[se,ye].indexOf(A)>=0,L=R?"width":"height",X=Yt(t,{placement:m,boundary:u,rootBoundary:f,altBoundary:d,padding:c}),S=R?P?me:ue:P?ye:se;O[L]>x[L]&&(S=dr(S));var M=dr(S),B=[];if(a&&B.push(X[A]<=0),i&&B.push(X[S]<=0,X[M]<=0),B.every(function(Ie){return Ie})){j=m,I=!1;break}k.set(m,B)}if(I)for(var ie=g?3:1,Re=function(we){var jt=w.find(function(or){var Fe=k.get(or);if(Fe)return Fe.slice(0,we).every(function(zr){return zr})});if(jt)return j=jt,"break"},Ve=ie;Ve>0;Ve--){var at=Re(Ve);if(at==="break")break}t.placement!==j&&(t.modifiersData[n]._skip=!0,t.placement=j,t.reset=!0)}}const Rs={name:"flip",enabled:!0,phase:"main",fn:js,requiresIfExists:["offset"],data:{_skip:!1}};function Lo(e,t,r){return r===void 0&&(r={x:0,y:0}),{top:e.top-t.height-r.y,right:e.right-t.width+r.x,bottom:e.bottom-t.height+r.y,left:e.left-t.width-r.x}}function Bo(e){return[se,me,ye,ue].some(function(t){return e[t]>=0})}function Is(e){var t=e.state,r=e.name,n=t.rects.reference,o=t.rects.popper,a=t.modifiersData.preventOverflow,s=Yt(t,{elementContext:"reference"}),i=Yt(t,{altBoundary:!0}),l=Lo(s,n),c=Lo(i,o,a),u=Bo(l),f=Bo(c);t.modifiersData[r]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:f},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":f})}const Ms={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Is};function Ls(e,t,r){var n=Se(e),o=[ue,se].indexOf(n)>=0?-1:1,a=typeof r=="function"?r(Object.assign({},t,{placement:e})):r,s=a[0],i=a[1];return s=s||0,i=(i||0)*o,[ue,me].indexOf(n)>=0?{x:i,y:s}:{x:s,y:i}}function Bs(e){var t=e.state,r=e.options,n=e.name,o=r.offset,a=o===void 0?[0,0]:o,s=ni.reduce(function(u,f){return u[f]=Ls(f,t.rects,a),u},{}),i=s[t.placement],l=i.x,c=i.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=c),t.modifiersData[n]=s}const Ds={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Bs};function Hs(e){var t=e.state,r=e.name;t.modifiersData[r]=ci({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})}const Ns={name:"popperOffsets",enabled:!0,phase:"read",fn:Hs,data:{}};function Vs(e){return e==="x"?"y":"x"}function Fs(e){var t=e.state,r=e.options,n=e.name,o=r.mainAxis,a=o===void 0?!0:o,s=r.altAxis,i=s===void 0?!1:s,l=r.boundary,c=r.rootBoundary,u=r.altBoundary,f=r.padding,d=r.tether,p=d===void 0?!0:d,g=r.tetherOffset,v=g===void 0?0:g,h=Yt(t,{boundary:l,rootBoundary:c,padding:f,altBoundary:u}),y=Se(t.placement),b=yt(t.placement),C=!b,w=Bn(y),O=Vs(w),x=t.modifiersData.popperOffsets,k=t.rects.reference,I=t.rects.popper,j=typeof v=="function"?v(Object.assign({},t.rects,{placement:t.placement})):v,$=typeof j=="number"?{mainAxis:j,altAxis:j}:Object.assign({mainAxis:0,altAxis:0},j),m=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,A={x:0,y:0};if(x){if(a){var P,R=w==="y"?se:ue,L=w==="y"?ye:me,X=w==="y"?"height":"width",S=x[w],M=S+h[R],B=S-h[L],ie=p?-I[X]/2:0,Re=b===vt?k[X]:I[X],Ve=b===vt?-I[X]:-k[X],at=t.elements.arrow,Ie=p&&at?Ln(at):{width:0,height:0},we=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:ii(),jt=we[R],or=we[L],Fe=Nt(0,k[X],Ie[X]),zr=C?k[X]/2-ie-Fe-jt-$.mainAxis:Re-Fe-jt-$.mainAxis,pl=C?-k[X]/2+ie+Fe+or+$.mainAxis:Ve+Fe+or+$.mainAxis,Ur=t.elements.arrow&&er(t.elements.arrow),vl=Ur?w==="y"?Ur.clientTop||0:Ur.clientLeft||0:0,go=(P=m==null?void 0:m[w])!=null?P:0,hl=S+zr-go-vl,gl=S+pl-go,yo=Nt(p?gr(M,hl):M,S,p?Ke(B,gl):B);x[w]=yo,A[w]=yo-S}if(i){var mo,yl=w==="x"?se:ue,ml=w==="x"?ye:me,We=x[O],ar=O==="y"?"height":"width",bo=We+h[yl],Co=We-h[ml],Kr=[se,ue].indexOf(y)!==-1,wo=(mo=m==null?void 0:m[O])!=null?mo:0,So=Kr?bo:We-k[ar]-I[ar]-wo+$.altAxis,xo=Kr?We+k[ar]+I[ar]-wo-$.altAxis:Co,Oo=p&&Kr?ds(So,We,xo):Nt(p?So:bo,We,p?xo:Co);x[O]=Oo,A[O]=Oo-We}t.modifiersData[n]=A}}const Ws={name:"preventOverflow",enabled:!0,phase:"main",fn:Fs,requiresIfExists:["offset"]};function zs(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function Us(e){return e===fe(e)||!he(e)?Dn(e):zs(e)}function Ks(e){var t=e.getBoundingClientRect(),r=ht(t.width)/e.offsetWidth||1,n=ht(t.height)/e.offsetHeight||1;return r!==1||n!==1}function Gs(e,t,r){r===void 0&&(r=!1);var n=he(t),o=he(t)&&Ks(t),a=Ne(t),s=gt(e,o,r),i={scrollLeft:0,scrollTop:0},l={x:0,y:0};return(n||!n&&!r)&&((Oe(t)!=="body"||Nn(a))&&(i=Us(t)),he(t)?(l=gt(t,!0),l.x+=t.clientLeft,l.y+=t.clientTop):a&&(l.x=Hn(a))),{x:s.left+i.scrollLeft-l.x,y:s.top+i.scrollTop-l.y,width:s.width,height:s.height}}function qs(e){var t=new Map,r=new Set,n=[];e.forEach(function(a){t.set(a.name,a)});function o(a){r.add(a.name);var s=[].concat(a.requires||[],a.requiresIfExists||[]);s.forEach(function(i){if(!r.has(i)){var l=t.get(i);l&&o(l)}}),n.push(a)}return e.forEach(function(a){r.has(a.name)||o(a)}),n}function Ys(e){var t=qs(e);return is.reduce(function(r,n){return r.concat(t.filter(function(o){return o.phase===n}))},[])}function Xs(e){var t;return function(){return t||(t=new Promise(function(r){Promise.resolve().then(function(){t=void 0,r(e())})})),t}}function Js(e){var t=e.reduce(function(r,n){var o=r[n.name];return r[n.name]=o?Object.assign({},o,n,{options:Object.assign({},o.options,n.options),data:Object.assign({},o.data,n.data)}):n,r},{});return Object.keys(t).map(function(r){return t[r]})}var Do={placement:"bottom",modifiers:[],strategy:"absolute"};function Ho(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return!t.some(function(n){return!(n&&typeof n.getBoundingClientRect=="function")})}function Zs(e){e===void 0&&(e={});var t=e,r=t.defaultModifiers,n=r===void 0?[]:r,o=t.defaultOptions,a=o===void 0?Do:o;return function(i,l,c){c===void 0&&(c=a);var u={placement:"bottom",orderedModifiers:[],options:Object.assign({},Do,a),modifiersData:{},elements:{reference:i,popper:l},attributes:{},styles:{}},f=[],d=!1,p={state:u,setOptions:function(y){var b=typeof y=="function"?y(u.options):y;v(),u.options=Object.assign({},a,u.options,b),u.scrollParents={reference:Ye(i)?Vt(i):i.contextElement?Vt(i.contextElement):[],popper:Vt(l)};var C=Ys(Js([].concat(n,u.options.modifiers)));return u.orderedModifiers=C.filter(function(w){return w.enabled}),g(),p.update()},forceUpdate:function(){if(!d){var y=u.elements,b=y.reference,C=y.popper;if(Ho(b,C)){u.rects={reference:Gs(b,er(C),u.options.strategy==="fixed"),popper:Ln(C)},u.reset=!1,u.placement=u.options.placement,u.orderedModifiers.forEach(function($){return u.modifiersData[$.name]=Object.assign({},$.data)});for(var w=0;w<u.orderedModifiers.length;w++){if(u.reset===!0){u.reset=!1,w=-1;continue}var O=u.orderedModifiers[w],x=O.fn,k=O.options,I=k===void 0?{}:k,j=O.name;typeof x=="function"&&(u=x({state:u,options:I,name:j,instance:p})||u)}}}},update:Xs(function(){return new Promise(function(h){p.forceUpdate(),h(u)})}),destroy:function(){v(),d=!0}};if(!Ho(i,l))return p;p.setOptions(c).then(function(h){!d&&c.onFirstUpdate&&c.onFirstUpdate(h)});function g(){u.orderedModifiers.forEach(function(h){var y=h.name,b=h.options,C=b===void 0?{}:b,w=h.effect;if(typeof w=="function"){var O=w({state:u,name:y,instance:p,options:C}),x=function(){};f.push(O||x)}})}function v(){f.forEach(function(h){return h()}),f=[]}return p}}var Qs=[Ss,Ns,Cs,us,Ds,Rs,Ws,gs,Ms],eu=Zs({defaultModifiers:Qs});/*!
 * is-plain-object <https://github.com/jonschlinkert/is-plain-object>
 *
 * Copyright (c) 2014-2017, Jon Schlinkert.
 * Released under the MIT License.
 */function No(e){return Object.prototype.toString.call(e)==="[object Object]"}function tu(e){var t,r;return No(e)===!1?!1:(t=e.constructor,t===void 0?!0:(r=t.prototype,!(No(r)===!1||r.hasOwnProperty("isPrototypeOf")===!1)))}function Ft(){return Ft=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ft.apply(this,arguments)}function fi(e,t){if(e==null)return{};var r,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)t.indexOf(r=a[n])>=0||(o[r]=e[r]);return o}const ru={silent:!1,logLevel:"warn"},nu=["validator"],di=Object.prototype,pi=di.toString,ou=di.hasOwnProperty,vi=/^\s*function (\w+)/;function Vo(e){var t;const r=(t=e==null?void 0:e.type)!==null&&t!==void 0?t:e;if(r){const n=r.toString().match(vi);return n?n[1]:""}return""}const Xe=tu,au=e=>e;let ne=au;const mt=(e,t)=>ou.call(e,t),iu=Number.isInteger||function(e){return typeof e=="number"&&isFinite(e)&&Math.floor(e)===e},bt=Array.isArray||function(e){return pi.call(e)==="[object Array]"},Ct=e=>pi.call(e)==="[object Function]",yr=e=>Xe(e)&&mt(e,"_vueTypes_name"),hi=e=>Xe(e)&&(mt(e,"type")||["_vueTypes_name","validator","default","required"].some(t=>mt(e,t)));function Vn(e,t){return Object.defineProperty(e.bind(t),"__original",{value:e})}function tt(e,t,r=!1){let n,o=!0,a="";n=Xe(e)?e:{type:e};const s=yr(n)?n._vueTypes_name+" - ":"";if(hi(n)&&n.type!==null){if(n.type===void 0||n.type===!0||!n.required&&t===void 0)return o;bt(n.type)?(o=n.type.some(i=>tt(i,t,!0)===!0),a=n.type.map(i=>Vo(i)).join(" or ")):(a=Vo(n),o=a==="Array"?bt(t):a==="Object"?Xe(t):a==="String"||a==="Number"||a==="Boolean"||a==="Function"?function(i){if(i==null)return"";const l=i.constructor.toString().match(vi);return l?l[1]:""}(t)===a:t instanceof n.type)}if(!o){const i=`${s}value "${t}" should be of type "${a}"`;return r===!1?(ne(i),!1):i}if(mt(n,"validator")&&Ct(n.validator)){const i=ne,l=[];if(ne=c=>{l.push(c)},o=n.validator(t),ne=i,!o){const c=(l.length>1?"* ":"")+l.join(`
* `);return l.length=0,r===!1?(ne(c),o):c}}return o}function ce(e,t){const r=Object.defineProperties(t,{_vueTypes_name:{value:e,writable:!0},isRequired:{get(){return this.required=!0,this}},def:{value(o){return o===void 0?(mt(this,"default")&&delete this.default,this):Ct(o)||tt(this,o,!0)===!0?(this.default=bt(o)?()=>[...o]:Xe(o)?()=>Object.assign({},o):o,this):(ne(`${this._vueTypes_name} - invalid default value: "${o}"`),this)}}}),{validator:n}=r;return Ct(n)&&(r.validator=Vn(n,r)),r}function xe(e,t){const r=ce(e,t);return Object.defineProperty(r,"validate",{value(n){return Ct(this.validator)&&ne(`${this._vueTypes_name} - calling .validate() will overwrite the current custom validator function. Validator info:
${JSON.stringify(this)}`),this.validator=Vn(n,this),this}})}function Fo(e,t,r){const n=function(l){const c={};return Object.getOwnPropertyNames(l).forEach(u=>{c[u]=Object.getOwnPropertyDescriptor(l,u)}),Object.defineProperties({},c)}(t);if(n._vueTypes_name=e,!Xe(r))return n;const{validator:o}=r,a=fi(r,nu);if(Ct(o)){let{validator:l}=n;l&&(l=(i=(s=l).__original)!==null&&i!==void 0?i:s),n.validator=Vn(l?function(c){return l.call(this,c)&&o.call(this,c)}:o,n)}var s,i;return Object.assign(n,a)}function Rr(e){return e.replace(/^(?!\s*$)/gm,"  ")}const lu=()=>xe("any",{}),su=()=>xe("function",{type:Function}),uu=()=>xe("boolean",{type:Boolean}),cu=()=>xe("string",{type:String}),fu=()=>xe("number",{type:Number}),du=()=>xe("array",{type:Array}),pu=()=>xe("object",{type:Object}),vu=()=>ce("integer",{type:Number,validator:e=>iu(e)}),hu=()=>ce("symbol",{validator:e=>typeof e=="symbol"});function gu(e,t="custom validation failed"){if(typeof e!="function")throw new TypeError("[VueTypes error]: You must provide a function as argument");return ce(e.name||"<<anonymous function>>",{type:null,validator(r){const n=e(r);return n||ne(`${this._vueTypes_name} - ${t}`),n}})}function yu(e){if(!bt(e))throw new TypeError("[VueTypes error]: You must provide an array as argument.");const t=`oneOf - value should be one of "${e.join('", "')}".`,r=e.reduce((n,o)=>{if(o!=null){const a=o.constructor;n.indexOf(a)===-1&&n.push(a)}return n},[]);return ce("oneOf",{type:r.length>0?r:void 0,validator(n){const o=e.indexOf(n)!==-1;return o||ne(t),o}})}function mu(e){if(!bt(e))throw new TypeError("[VueTypes error]: You must provide an array as argument");let t=!1,r=[];for(let o=0;o<e.length;o+=1){const a=e[o];if(hi(a)){if(yr(a)&&a._vueTypes_name==="oneOf"&&a.type){r=r.concat(a.type);continue}if(Ct(a.validator)&&(t=!0),a.type===!0||!a.type){ne('oneOfType - invalid usage of "true" or "null" as types.');continue}r=r.concat(a.type)}else r.push(a)}r=r.filter((o,a)=>r.indexOf(o)===a);const n=r.length>0?r:null;return ce("oneOfType",t?{type:n,validator(o){const a=[],s=e.some(i=>{const l=tt(yr(i)&&i._vueTypes_name==="oneOf"?i.type||null:i,o,!0);return typeof l=="string"&&a.push(l),l===!0});return s||ne(`oneOfType - provided value does not match any of the ${a.length} passed-in validators:
${Rr(a.join(`
`))}`),s}}:{type:n})}function bu(e){return ce("arrayOf",{type:Array,validator(t){let r="";const n=t.every(o=>(r=tt(e,o,!0),r===!0));return n||ne(`arrayOf - value validation error:
${Rr(r)}`),n}})}function Cu(e){return ce("instanceOf",{type:e})}function wu(e){return ce("objectOf",{type:Object,validator(t){let r="";const n=Object.keys(t).every(o=>(r=tt(e,t[o],!0),r===!0));return n||ne(`objectOf - value validation error:
${Rr(r)}`),n}})}function Su(e){const t=Object.keys(e),r=t.filter(o=>{var a;return!((a=e[o])===null||a===void 0||!a.required)}),n=ce("shape",{type:Object,validator(o){if(!Xe(o))return!1;const a=Object.keys(o);if(r.length>0&&r.some(s=>a.indexOf(s)===-1)){const s=r.filter(i=>a.indexOf(i)===-1);return ne(s.length===1?`shape - required property "${s[0]}" is not defined.`:`shape - required properties "${s.join('", "')}" are not defined.`),!1}return a.every(s=>{if(t.indexOf(s)===-1)return this._vueTypes_isLoose===!0||(ne(`shape - shape definition does not include a "${s}" property. Allowed keys: "${t.join('", "')}".`),!1);const i=tt(e[s],o[s],!0);return typeof i=="string"&&ne(`shape - "${s}" property validation error:
 ${Rr(i)}`),i===!0})}});return Object.defineProperty(n,"_vueTypes_isLoose",{writable:!0,value:!1}),Object.defineProperty(n,"loose",{get(){return this._vueTypes_isLoose=!0,this}}),n}const xu=["name","validate","getter"],Ou=(()=>{var e;return(e=class{static get any(){return lu()}static get func(){return su().def(this.defaults.func)}static get bool(){return uu().def(this.defaults.bool)}static get string(){return cu().def(this.defaults.string)}static get number(){return fu().def(this.defaults.number)}static get array(){return du().def(this.defaults.array)}static get object(){return pu().def(this.defaults.object)}static get integer(){return vu().def(this.defaults.integer)}static get symbol(){return hu()}static get nullable(){return{type:null}}static extend(t){if(bt(t))return t.forEach(l=>this.extend(l)),this;const{name:r,validate:n=!1,getter:o=!1}=t,a=fi(t,xu);if(mt(this,r))throw new TypeError(`[VueTypes error]: Type "${r}" already defined`);const{type:s}=a;if(yr(s))return delete a.type,Object.defineProperty(this,r,o?{get:()=>Fo(r,s,a)}:{value(...l){const c=Fo(r,s,a);return c.validator&&(c.validator=c.validator.bind(c,...l)),c}});let i;return i=o?{get(){const l=Object.assign({},a);return n?xe(r,l):ce(r,l)},enumerable:!0}:{value(...l){const c=Object.assign({},a);let u;return u=n?xe(r,c):ce(r,c),c.validator&&(u.validator=c.validator.bind(u,...l)),u},enumerable:!0},Object.defineProperty(this,r,i)}}).defaults={},e.sensibleDefaults=void 0,e.config=ru,e.custom=gu,e.oneOf=yu,e.instanceOf=Cu,e.oneOfType=mu,e.arrayOf=bu,e.objectOf=wu,e.shape=Su,e.utils={validate:(t,r)=>tt(r,t,!0)===!0,toType:(t,r,n=!1)=>n?xe(t,r):ce(t,r)},e})();function _u(e={func:()=>{},bool:!0,string:"",number:0,array:()=>[],object:()=>({}),integer:0}){var t;return(t=class extends Ou{static get sensibleDefaults(){return Ft({},this.defaults)}static set sensibleDefaults(r){this.defaults=r!==!1?Ft({},r!==!0?r:e):{}}}).defaults=Ft({},e),t}let T=class extends _u(){};var Wo=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Fn(e){var t={exports:{}};return e(t,t.exports),t.exports}var ur=function(e){return e&&e.Math==Math&&e},Z=ur(typeof globalThis=="object"&&globalThis)||ur(typeof window=="object"&&window)||ur(typeof self=="object"&&self)||ur(typeof Wo=="object"&&Wo)||function(){return this}()||Function("return this")(),F=function(e){try{return!!e()}catch{return!0}},ve=!F(function(){return Object.defineProperty({},1,{get:function(){return 7}})[1]!=7}),zo={}.propertyIsEnumerable,Uo=Object.getOwnPropertyDescriptor,Au={f:Uo&&!zo.call({1:2},1)?function(e){var t=Uo(this,e);return!!t&&t.enumerable}:zo},Ir=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},$u={}.toString,$e=function(e){return $u.call(e).slice(8,-1)},Eu="".split,Mr=F(function(){return!Object("z").propertyIsEnumerable(0)})?function(e){return $e(e)=="String"?Eu.call(e,""):Object(e)}:Object,Be=function(e){if(e==null)throw TypeError("Can't call method on "+e);return e},At=function(e){return Mr(Be(e))},Q=function(e){return typeof e=="object"?e!==null:typeof e=="function"},Wn=function(e,t){if(!Q(e))return e;var r,n;if(t&&typeof(r=e.toString)=="function"&&!Q(n=r.call(e))||typeof(r=e.valueOf)=="function"&&!Q(n=r.call(e))||!t&&typeof(r=e.toString)=="function"&&!Q(n=r.call(e)))return n;throw TypeError("Can't convert object to primitive value")},Tu={}.hasOwnProperty,Y=function(e,t){return Tu.call(e,t)},bn=Z.document,ku=Q(bn)&&Q(bn.createElement),gi=function(e){return ku?bn.createElement(e):{}},yi=!ve&&!F(function(){return Object.defineProperty(gi("div"),"a",{get:function(){return 7}}).a!=7}),Ko=Object.getOwnPropertyDescriptor,zn={f:ve?Ko:function(e,t){if(e=At(e),t=Wn(t,!0),yi)try{return Ko(e,t)}catch{}if(Y(e,t))return Ir(!Au.f.call(e,t),e[t])}},le=function(e){if(!Q(e))throw TypeError(String(e)+" is not an object");return e},Go=Object.defineProperty,ke={f:ve?Go:function(e,t,r){if(le(e),t=Wn(t,!0),le(r),yi)try{return Go(e,t,r)}catch{}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},ge=ve?function(e,t,r){return ke.f(e,t,Ir(1,r))}:function(e,t,r){return e[t]=r,e},Un=function(e,t){try{ge(Z,e,t)}catch{Z[e]=t}return t},Je=Z["__core-js_shared__"]||Un("__core-js_shared__",{}),Pu=Function.toString;typeof Je.inspectSource!="function"&&(Je.inspectSource=function(e){return Pu.call(e)});var mr,Wt,br,mi=Je.inspectSource,qo=Z.WeakMap,ju=typeof qo=="function"&&/native code/.test(mi(qo)),bi=Fn(function(e){(e.exports=function(t,r){return Je[t]||(Je[t]=r!==void 0?r:{})})("versions",[]).push({version:"3.8.3",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})}),Ru=0,Iu=Math.random(),Kn=function(e){return"Symbol("+String(e===void 0?"":e)+")_"+(++Ru+Iu).toString(36)},Yo=bi("keys"),Gn=function(e){return Yo[e]||(Yo[e]=Kn(e))},Lr={},Mu=Z.WeakMap;if(ju){var it=Je.state||(Je.state=new Mu),Lu=it.get,Bu=it.has,Du=it.set;mr=function(e,t){return t.facade=e,Du.call(it,e,t),t},Wt=function(e){return Lu.call(it,e)||{}},br=function(e){return Bu.call(it,e)}}else{var It=Gn("state");Lr[It]=!0,mr=function(e,t){return t.facade=e,ge(e,It,t),t},Wt=function(e){return Y(e,It)?e[It]:{}},br=function(e){return Y(e,It)}}var De={set:mr,get:Wt,has:br,enforce:function(e){return br(e)?Wt(e):mr(e,{})},getterFor:function(e){return function(t){var r;if(!Q(t)||(r=Wt(t)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return r}}},He=Fn(function(e){var t=De.get,r=De.enforce,n=String(String).split("String");(e.exports=function(o,a,s,i){var l,c=!!i&&!!i.unsafe,u=!!i&&!!i.enumerable,f=!!i&&!!i.noTargetGet;typeof s=="function"&&(typeof a!="string"||Y(s,"name")||ge(s,"name",a),(l=r(s)).source||(l.source=n.join(typeof a=="string"?a:""))),o!==Z?(c?!f&&o[a]&&(u=!0):delete o[a],u?o[a]=s:ge(o,a,s)):u?o[a]=s:Un(a,s)})(Function.prototype,"toString",function(){return typeof this=="function"&&t(this).source||mi(this)})}),Gr=Z,Xo=function(e){return typeof e=="function"?e:void 0},Br=function(e,t){return arguments.length<2?Xo(Gr[e])||Xo(Z[e]):Gr[e]&&Gr[e][t]||Z[e]&&Z[e][t]},Hu=Math.ceil,Nu=Math.floor,$t=function(e){return isNaN(e=+e)?0:(e>0?Nu:Hu)(e)},Vu=Math.min,pe=function(e){return e>0?Vu($t(e),9007199254740991):0},Fu=Math.max,Wu=Math.min,Cr=function(e,t){var r=$t(e);return r<0?Fu(r+t,0):Wu(r,t)},Jo=function(e){return function(t,r,n){var o,a=At(t),s=pe(a.length),i=Cr(n,s);if(e&&r!=r){for(;s>i;)if((o=a[i++])!=o)return!0}else for(;s>i;i++)if((e||i in a)&&a[i]===r)return e||i||0;return!e&&-1}},Ci={includes:Jo(!0),indexOf:Jo(!1)},zu=Ci.indexOf,wi=function(e,t){var r,n=At(e),o=0,a=[];for(r in n)!Y(Lr,r)&&Y(n,r)&&a.push(r);for(;t.length>o;)Y(n,r=t[o++])&&(~zu(a,r)||a.push(r));return a},wr=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Uu=wr.concat("length","prototype"),Ku={f:Object.getOwnPropertyNames||function(e){return wi(e,Uu)}},Gu={f:Object.getOwnPropertySymbols},qu=Br("Reflect","ownKeys")||function(e){var t=Ku.f(le(e)),r=Gu.f;return r?t.concat(r(e)):t},Yu=function(e,t){for(var r=qu(t),n=ke.f,o=zn.f,a=0;a<r.length;a++){var s=r[a];Y(e,s)||n(e,s,o(t,s))}},Xu=/#|\.prototype\./,tr=function(e,t){var r=Zu[Ju(e)];return r==ec||r!=Qu&&(typeof t=="function"?F(t):!!t)},Ju=tr.normalize=function(e){return String(e).replace(Xu,".").toLowerCase()},Zu=tr.data={},Qu=tr.NATIVE="N",ec=tr.POLYFILL="P",Cn=tr,tc=zn.f,ae=function(e,t){var r,n,o,a,s,i=e.target,l=e.global,c=e.stat;if(r=l?Z:c?Z[i]||Un(i,{}):(Z[i]||{}).prototype)for(n in t){if(a=t[n],o=e.noTargetGet?(s=tc(r,n))&&s.value:r[n],!Cn(l?n:i+(c?".":"#")+n,e.forced)&&o!==void 0){if(typeof a==typeof o)continue;Yu(a,o)}(e.sham||o&&o.sham)&&ge(a,"sham",!0),He(r,n,a,e)}},qn=function(e,t){var r=[][e];return!!r&&F(function(){r.call(null,t||function(){throw 1},1)})},rc=Object.defineProperty,qr={},Zo=function(e){throw e},Et=function(e,t){if(Y(qr,e))return qr[e];t||(t={});var r=[][e],n=!!Y(t,"ACCESSORS")&&t.ACCESSORS,o=Y(t,0)?t[0]:Zo,a=Y(t,1)?t[1]:void 0;return qr[e]=!!r&&!F(function(){if(n&&!ve)return!0;var s={length:-1};n?rc(s,1,{enumerable:!0,get:Zo}):s[1]=1,r.call(s,o,a)})},nc=Ci.indexOf,Si=[].indexOf,Qo=!!Si&&1/[1].indexOf(1,-0)<0,oc=qn("indexOf"),ac=Et("indexOf",{ACCESSORS:!0,1:0});function rt(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ea(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function nt(e,t,r){return t&&ea(e.prototype,t),r&&ea(e,r),e}ae({target:"Array",proto:!0,forced:Qo||!oc||!ac},{indexOf:function(e){return Qo?Si.apply(this,arguments)||0:nc(this,e,arguments.length>1?arguments[1]:void 0)}});(function(){function e(){rt(this,e)}return nt(e,null,[{key:"isInBrowser",value:function(){return typeof window<"u"}},{key:"isServer",value:function(){return typeof window>"u"}},{key:"getUA",value:function(){return e.isInBrowser()?window.navigator.userAgent.toLowerCase():""}},{key:"isMobile",value:function(){return/Mobile|mini|Fennec|Android|iP(ad|od|hone)/.test(navigator.appVersion)}},{key:"isOpera",value:function(){return navigator.userAgent.indexOf("Opera")!==-1}},{key:"isIE",value:function(){var t=e.getUA();return t!==""&&t.indexOf("msie")>0}},{key:"isIE9",value:function(){var t=e.getUA();return t!==""&&t.indexOf("msie 9.0")>0}},{key:"isEdge",value:function(){var t=e.getUA();return t!==""&&t.indexOf("edge/")>0}},{key:"isChrome",value:function(){var t=e.getUA();return t!==""&&/chrome\/\d+/.test(t)&&!e.isEdge()}},{key:"isPhantomJS",value:function(){var t=e.getUA();return t!==""&&/phantomjs/.test(t)}},{key:"isFirefox",value:function(){var t=e.getUA();return t!==""&&/firefox/.test(t)}}]),e})();var ic=[].join,lc=Mr!=Object,sc=qn("join",",");ae({target:"Array",proto:!0,forced:lc||!sc},{join:function(e){return ic.call(At(this),e===void 0?",":e)}});var lt,Sr,Pe=function(e){return Object(Be(e))},wt=Array.isArray||function(e){return $e(e)=="Array"},xi=!!Object.getOwnPropertySymbols&&!F(function(){return!String(Symbol())}),uc=xi&&!Symbol.sham&&typeof Symbol.iterator=="symbol",cr=bi("wks"),zt=Z.Symbol,cc=uc?zt:zt&&zt.withoutSetter||Kn,U=function(e){return Y(cr,e)||(xi&&Y(zt,e)?cr[e]=zt[e]:cr[e]=cc("Symbol."+e)),cr[e]},fc=U("species"),Dr=function(e,t){var r;return wt(e)&&(typeof(r=e.constructor)!="function"||r!==Array&&!wt(r.prototype)?Q(r)&&(r=r[fc])===null&&(r=void 0):r=void 0),new(r===void 0?Array:r)(t===0?0:t)},St=function(e,t,r){var n=Wn(t);n in e?ke.f(e,n,Ir(0,r)):e[n]=r},Yr=Br("navigator","userAgent")||"",ta=Z.process,ra=ta&&ta.versions,na=ra&&ra.v8;na?Sr=(lt=na.split("."))[0]+lt[1]:Yr&&(!(lt=Yr.match(/Edge\/(\d+)/))||lt[1]>=74)&&(lt=Yr.match(/Chrome\/(\d+)/))&&(Sr=lt[1]);var xr=Sr&&+Sr,dc=U("species"),Yn=function(e){return xr>=51||!F(function(){var t=[];return(t.constructor={})[dc]=function(){return{foo:1}},t[e](Boolean).foo!==1})},pc=Yn("splice"),vc=Et("splice",{ACCESSORS:!0,0:0,1:2}),hc=Math.max,gc=Math.min;ae({target:"Array",proto:!0,forced:!pc||!vc},{splice:function(e,t){var r,n,o,a,s,i,l=Pe(this),c=pe(l.length),u=Cr(e,c),f=arguments.length;if(f===0?r=n=0:f===1?(r=0,n=c-u):(r=f-2,n=gc(hc($t(t),0),c-u)),c+r-n>9007199254740991)throw TypeError("Maximum allowed length exceeded");for(o=Dr(l,n),a=0;a<n;a++)(s=u+a)in l&&St(o,a,l[s]);if(o.length=n,r<n){for(a=u;a<c-n;a++)i=a+r,(s=a+n)in l?l[i]=l[s]:delete l[i];for(a=c;a>c-n+r;a--)delete l[a-1]}else if(r>n)for(a=c-n;a>u;a--)i=a+r-1,(s=a+n-1)in l?l[i]=l[s]:delete l[i];for(a=0;a<r;a++)l[a+u]=arguments[a+2];return l.length=c-n+r,o}});var Oi={};Oi[U("toStringTag")]="z";var Xn=String(Oi)==="[object z]",yc=U("toStringTag"),mc=$e(function(){return arguments}())=="Arguments",_i=Xn?$e:function(e){var t,r,n;return e===void 0?"Undefined":e===null?"Null":typeof(r=function(o,a){try{return o[a]}catch{}}(t=Object(e),yc))=="string"?r:mc?$e(t):(n=$e(t))=="Object"&&typeof t.callee=="function"?"Arguments":n},bc=Xn?{}.toString:function(){return"[object "+_i(this)+"]"};Xn||He(Object.prototype,"toString",bc,{unsafe:!0});var Ai=function(){var e=le(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t};function oa(e,t){return RegExp(e,t)}var Xr,Jr,aa={UNSUPPORTED_Y:F(function(){var e=oa("a","y");return e.lastIndex=2,e.exec("abcd")!=null}),BROKEN_CARET:F(function(){var e=oa("^r","gy");return e.lastIndex=2,e.exec("str")!=null})},Or=RegExp.prototype.exec,Cc=String.prototype.replace,$i=Or,Zr=(Xr=/a/,Jr=/b*/g,Or.call(Xr,"a"),Or.call(Jr,"a"),Xr.lastIndex!==0||Jr.lastIndex!==0),ia=aa.UNSUPPORTED_Y||aa.BROKEN_CARET,Qr=/()??/.exec("")[1]!==void 0;(Zr||Qr||ia)&&($i=function(e){var t,r,n,o,a=this,s=ia&&a.sticky,i=Ai.call(a),l=a.source,c=0,u=e;return s&&((i=i.replace("y","")).indexOf("g")===-1&&(i+="g"),u=String(e).slice(a.lastIndex),a.lastIndex>0&&(!a.multiline||a.multiline&&e[a.lastIndex-1]!==`
`)&&(l="(?: "+l+")",u=" "+u,c++),r=new RegExp("^(?:"+l+")",i)),Qr&&(r=new RegExp("^"+l+"$(?!\\s)",i)),Zr&&(t=a.lastIndex),n=Or.call(s?r:a,u),s?n?(n.input=n.input.slice(c),n[0]=n[0].slice(c),n.index=a.lastIndex,a.lastIndex+=n[0].length):a.lastIndex=0:Zr&&n&&(a.lastIndex=a.global?n.index+n[0].length:t),Qr&&n&&n.length>1&&Cc.call(n[0],r,function(){for(o=1;o<arguments.length-2;o++)arguments[o]===void 0&&(n[o]=void 0)}),n});var Xt=$i;ae({target:"RegExp",proto:!0,forced:/./.exec!==Xt},{exec:Xt});var Ei=RegExp.prototype,Ti=Ei.toString,wc=F(function(){return Ti.call({source:"a",flags:"b"})!="/a/b"}),Sc=Ti.name!="toString";(wc||Sc)&&He(RegExp.prototype,"toString",function(){var e=le(this),t=String(e.source),r=e.flags;return"/"+t+"/"+String(r===void 0&&e instanceof RegExp&&!("flags"in Ei)?Ai.call(e):r)},{unsafe:!0});var xc=U("species"),Oc=!F(function(){var e=/./;return e.exec=function(){var t=[];return t.groups={a:"7"},t},"".replace(e,"$<a>")!=="7"}),la="a".replace(/./,"$0")==="$0",sa=U("replace"),ua=!!/./[sa]&&/./[sa]("a","$0")==="",_c=!F(function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var r="ab".split(e);return r.length!==2||r[0]!=="a"||r[1]!=="b"}),ki=function(e,t,r,n){var o=U(e),a=!F(function(){var f={};return f[o]=function(){return 7},""[e](f)!=7}),s=a&&!F(function(){var f=!1,d=/a/;return e==="split"&&((d={}).constructor={},d.constructor[xc]=function(){return d},d.flags="",d[o]=/./[o]),d.exec=function(){return f=!0,null},d[o](""),!f});if(!a||!s||e==="replace"&&(!Oc||!la||ua)||e==="split"&&!_c){var i=/./[o],l=r(o,""[e],function(f,d,p,g,v){return d.exec===Xt?a&&!v?{done:!0,value:i.call(d,p,g)}:{done:!0,value:f.call(p,d,g)}:{done:!1}},{REPLACE_KEEPS_$0:la,REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE:ua}),c=l[0],u=l[1];He(String.prototype,e,c),He(RegExp.prototype,o,t==2?function(f,d){return u.call(f,this,d)}:function(f){return u.call(f,this)})}n&&ge(RegExp.prototype[o],"sham",!0)},Ac=U("match"),Pi=function(e){var t;return Q(e)&&((t=e[Ac])!==void 0?!!t:$e(e)=="RegExp")},Jn=function(e){if(typeof e!="function")throw TypeError(String(e)+" is not a function");return e},$c=U("species"),ca=function(e){return function(t,r){var n,o,a=String(Be(t)),s=$t(r),i=a.length;return s<0||s>=i?e?"":void 0:(n=a.charCodeAt(s))<55296||n>56319||s+1===i||(o=a.charCodeAt(s+1))<56320||o>57343?e?a.charAt(s):n:e?a.slice(s,s+2):o-56320+(n-55296<<10)+65536}},ji={codeAt:ca(!1),charAt:ca(!0)},Ec=ji.charAt,Ri=function(e,t,r){return t+(r?Ec(e,t).length:1)},wn=function(e,t){var r=e.exec;if(typeof r=="function"){var n=r.call(e,t);if(typeof n!="object")throw TypeError("RegExp exec method returned something other than an Object or null");return n}if($e(e)!=="RegExp")throw TypeError("RegExp#exec called on incompatible receiver");return Xt.call(e,t)},Tc=[].push,kc=Math.min,st=!F(function(){return!RegExp(4294967295,"y")});ki("split",2,function(e,t,r){var n;return n="abbc".split(/(b)*/)[1]=="c"||"test".split(/(?:)/,-1).length!=4||"ab".split(/(?:ab)*/).length!=2||".".split(/(.?)(.?)/).length!=4||".".split(/()()/).length>1||"".split(/.?/).length?function(o,a){var s=String(Be(this)),i=a===void 0?4294967295:a>>>0;if(i===0)return[];if(o===void 0)return[s];if(!Pi(o))return t.call(s,o,i);for(var l,c,u,f=[],d=(o.ignoreCase?"i":"")+(o.multiline?"m":"")+(o.unicode?"u":"")+(o.sticky?"y":""),p=0,g=new RegExp(o.source,d+"g");(l=Xt.call(g,s))&&!((c=g.lastIndex)>p&&(f.push(s.slice(p,l.index)),l.length>1&&l.index<s.length&&Tc.apply(f,l.slice(1)),u=l[0].length,p=c,f.length>=i));)g.lastIndex===l.index&&g.lastIndex++;return p===s.length?!u&&g.test("")||f.push(""):f.push(s.slice(p)),f.length>i?f.slice(0,i):f}:"0".split(void 0,0).length?function(o,a){return o===void 0&&a===0?[]:t.call(this,o,a)}:t,[function(o,a){var s=Be(this),i=o==null?void 0:o[e];return i!==void 0?i.call(o,s,a):n.call(String(s),o,a)},function(o,a){var s=r(n,o,this,a,n!==t);if(s.done)return s.value;var i=le(o),l=String(this),c=function(w,O){var x,k=le(w).constructor;return k===void 0||(x=le(k)[$c])==null?O:Jn(x)}(i,RegExp),u=i.unicode,f=(i.ignoreCase?"i":"")+(i.multiline?"m":"")+(i.unicode?"u":"")+(st?"y":"g"),d=new c(st?i:"^(?:"+i.source+")",f),p=a===void 0?4294967295:a>>>0;if(p===0)return[];if(l.length===0)return wn(d,l)===null?[l]:[];for(var g=0,v=0,h=[];v<l.length;){d.lastIndex=st?v:0;var y,b=wn(d,st?l:l.slice(v));if(b===null||(y=kc(pe(d.lastIndex+(st?0:v)),l.length))===g)v=Ri(l,v,u);else{if(h.push(l.slice(g,v)),h.length===p)return h;for(var C=1;C<=b.length-1;C++)if(h.push(b[C]),h.length===p)return h;v=g=y}}return h.push(l.slice(g)),h}]},!st);var Sn=`	
\v\f\r                　\u2028\u2029\uFEFF`,_r="["+Sn+"]",Pc=RegExp("^"+_r+_r+"*"),jc=RegExp(_r+_r+"*$"),en=function(e){return function(t){var r=String(Be(t));return 1&e&&(r=r.replace(Pc,"")),2&e&&(r=r.replace(jc,"")),r}},Rc={start:en(1),end:en(2),trim:en(3)},Ic=Rc.trim;ae({target:"String",proto:!0,forced:function(e){return F(function(){return!!Sn[e]()||"​᠎"[e]()!="​᠎"||Sn[e].name!==e})}("trim")},{trim:function(){return Ic(this)}});var Mc=Yn("slice"),Lc=Et("slice",{ACCESSORS:!0,0:0,1:2}),Bc=U("species"),Dc=[].slice,Hc=Math.max;ae({target:"Array",proto:!0,forced:!Mc||!Lc},{slice:function(e,t){var r,n,o,a=At(this),s=pe(a.length),i=Cr(e,s),l=Cr(t===void 0?s:t,s);if(wt(a)&&(typeof(r=a.constructor)!="function"||r!==Array&&!wt(r.prototype)?Q(r)&&(r=r[Bc])===null&&(r=void 0):r=void 0,r===Array||r===void 0))return Dc.call(a,i,l);for(n=new(r===void 0?Array:r)(Hc(l-i,0)),o=0;i<l;i++,o++)i in a&&St(n,o,a[i]);return n.length=o,n}});var Zn=Object.keys||function(e){return wi(e,wr)},Nc=F(function(){Zn(1)});ae({target:"Object",stat:!0,forced:Nc},{keys:function(e){return Zn(Pe(e))}});var tn,Vc=function(e){if(Pi(e))throw TypeError("The method doesn't accept regular expressions");return e},Fc=U("match"),Wc=zn.f,fa="".startsWith,zc=Math.min,Ii=function(e){var t=/./;try{"/./"[e](t)}catch{try{return t[Fc]=!1,"/./"[e](t)}catch{}}return!1}("startsWith"),Uc=!(Ii||(tn=Wc(String.prototype,"startsWith"),!tn||tn.writable));function Mi(e){return(Mi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}ae({target:"String",proto:!0,forced:!Uc&&!Ii},{startsWith:function(e){var t=String(Be(this));Vc(e);var r=pe(zc(arguments.length>1?arguments[1]:void 0,t.length)),n=String(e);return fa?fa.call(t,n,r):t.slice(r,r+n.length)===n}});var ut=function(e){return typeof e=="string"},ct=function(e){return e!==null&&Mi(e)==="object"},Jt=function(){function e(){rt(this,e)}return nt(e,null,[{key:"isWindow",value:function(t){return t===window}},{key:"addEventListener",value:function(t,r,n){var o=arguments.length>3&&arguments[3]!==void 0&&arguments[3];t&&r&&n&&t.addEventListener(r,n,o)}},{key:"removeEventListener",value:function(t,r,n){var o=arguments.length>3&&arguments[3]!==void 0&&arguments[3];t&&r&&n&&t.removeEventListener(r,n,o)}},{key:"triggerDragEvent",value:function(t,r){var n=!1,o=function(s){var i;(i=r.drag)===null||i===void 0||i.call(r,s)},a=function s(i){var l;e.removeEventListener(document,"mousemove",o),e.removeEventListener(document,"mouseup",s),document.onselectstart=null,document.ondragstart=null,n=!1,(l=r.end)===null||l===void 0||l.call(r,i)};e.addEventListener(t,"mousedown",function(s){var i;n||(document.onselectstart=function(){return!1},document.ondragstart=function(){return!1},e.addEventListener(document,"mousemove",o),e.addEventListener(document,"mouseup",a),n=!0,(i=r.start)===null||i===void 0||i.call(r,s))})}},{key:"getBoundingClientRect",value:function(t){return t&&ct(t)&&t.nodeType===1?t.getBoundingClientRect():null}},{key:"hasClass",value:function(t,r){return!!(t&&ct(t)&&ut(r)&&t.nodeType===1)&&t.classList.contains(r.trim())}},{key:"addClass",value:function(t,r){if(t&&ct(t)&&ut(r)&&t.nodeType===1&&(r=r.trim(),!e.hasClass(t,r))){var n=t.className;t.className=n?n+" "+r:r}}},{key:"removeClass",value:function(t,r){if(t&&ct(t)&&ut(r)&&t.nodeType===1&&typeof t.className=="string"){r=r.trim();for(var n=t.className.trim().split(" "),o=n.length-1;o>=0;o--)n[o]=n[o].trim(),n[o]&&n[o]!==r||n.splice(o,1);t.className=n.join(" ")}}},{key:"toggleClass",value:function(t,r,n){t&&ct(t)&&ut(r)&&t.nodeType===1&&t.classList.toggle(r,n)}},{key:"replaceClass",value:function(t,r,n){t&&ct(t)&&ut(r)&&ut(n)&&t.nodeType===1&&(r=r.trim(),n=n.trim(),e.removeClass(t,r),e.addClass(t,n))}},{key:"getScrollTop",value:function(t){var r="scrollTop"in t?t.scrollTop:t.pageYOffset;return Math.max(r,0)}},{key:"setScrollTop",value:function(t,r){"scrollTop"in t?t.scrollTop=r:t.scrollTo(t.scrollX,r)}},{key:"getRootScrollTop",value:function(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0}},{key:"setRootScrollTop",value:function(t){e.setScrollTop(window,t),e.setScrollTop(document.body,t)}},{key:"getElementTop",value:function(t,r){if(e.isWindow(t))return 0;var n=r?e.getScrollTop(r):e.getRootScrollTop();return t.getBoundingClientRect().top+n}},{key:"getVisibleHeight",value:function(t){return e.isWindow(t)?t.innerHeight:t.getBoundingClientRect().height}},{key:"isHidden",value:function(t){if(!t)return!1;var r=window.getComputedStyle(t),n=r.display==="none",o=t.offsetParent===null&&r.position!=="fixed";return n||o}},{key:"triggerEvent",value:function(t,r){if("createEvent"in document){var n=document.createEvent("HTMLEvents");n.initEvent(r,!1,!0),t.dispatchEvent(n)}}},{key:"calcAngle",value:function(t,r){var n=t.getBoundingClientRect(),o=n.left+n.width/2,a=n.top+n.height/2,s=Math.abs(o-r.clientX),i=Math.abs(a-r.clientY),l=i/Math.sqrt(Math.pow(s,2)+Math.pow(i,2)),c=Math.acos(l),u=Math.floor(180/(Math.PI/c));return r.clientX>o&&r.clientY>a&&(u=180-u),r.clientX==o&&r.clientY>a&&(u=180),r.clientX>o&&r.clientY==a&&(u=90),r.clientX<o&&r.clientY>a&&(u=180+u),r.clientX<o&&r.clientY==a&&(u=270),r.clientX<o&&r.clientY<a&&(u=360-u),u}},{key:"querySelector",value:function(t,r){return r?r.querySelector(t):document.querySelector(t)}},{key:"createElement",value:function(t){for(var r=document.createElement(t),n=arguments.length,o=new Array(n>1?n-1:0),a=1;a<n;a++)o[a-1]=arguments[a];for(var s=0;s<o.length;s++)o[s]&&r.classList.add(o[s]);return r}},{key:"appendChild",value:function(t){for(var r=0;r<(arguments.length<=1?0:arguments.length-1);r++)t.appendChild(r+1<1||arguments.length<=r+1?void 0:arguments[r+1])}},{key:"getWindow",value:function(t){if(t.toString()!=="[object Window]"){var r=t.ownerDocument;return r&&r.defaultView||window}return t}},{key:"isElement",value:function(t){return t instanceof this.getWindow(t).Element||t instanceof Element}},{key:"isHTMLElement",value:function(t){return t instanceof this.getWindow(t).HTMLElement||t instanceof HTMLElement}},{key:"isShadowRoot",value:function(t){return typeof ShadowRoot<"u"&&(t instanceof this.getWindow(t).ShadowRoot||t instanceof ShadowRoot)}},{key:"getWindowScroll",value:function(t){var r=this.getWindow(t);return{scrollLeft:r.pageXOffset||0,scrollTop:r.pageYOffset||0}}}]),e}(),Kc=Math.floor,Gc="".replace,qc=/\$([$&'`]|\d\d?|<[^>]*>)/g,Yc=/\$([$&'`]|\d\d?)/g,Xc=function(e,t,r,n,o,a){var s=r+e.length,i=n.length,l=Yc;return o!==void 0&&(o=Pe(o),l=qc),Gc.call(a,l,function(c,u){var f;switch(u.charAt(0)){case"$":return"$";case"&":return e;case"`":return t.slice(0,r);case"'":return t.slice(s);case"<":f=o[u.slice(1,-1)];break;default:var d=+u;if(d===0)return c;if(d>i){var p=Kc(d/10);return p===0?c:p<=i?n[p-1]===void 0?u.charAt(1):n[p-1]+u.charAt(1):c}f=n[d-1]}return f===void 0?"":f})},Jc=Math.max,Zc=Math.min;ki("replace",2,function(e,t,r,n){var o=n.REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE,a=n.REPLACE_KEEPS_$0,s=o?"$":"$0";return[function(i,l){var c=Be(this),u=i==null?void 0:i[e];return u!==void 0?u.call(i,c,l):t.call(String(c),i,l)},function(i,l){if(!o&&a||typeof l=="string"&&l.indexOf(s)===-1){var c=r(t,i,this,l);if(c.done)return c.value}var u=le(i),f=String(this),d=typeof l=="function";d||(l=String(l));var p=u.global;if(p){var g=u.unicode;u.lastIndex=0}for(var v=[];;){var h=wn(u,f);if(h===null||(v.push(h),!p))break;String(h[0])===""&&(u.lastIndex=Ri(f,pe(u.lastIndex),g))}for(var y,b="",C=0,w=0;w<v.length;w++){h=v[w];for(var O=String(h[0]),x=Jc(Zc($t(h.index),f.length),0),k=[],I=1;I<h.length;I++)k.push((y=h[I])===void 0?y:String(y));var j=h.groups;if(d){var $=[O].concat(k,x,f);j!==void 0&&$.push(j);var m=String(l.apply(void 0,$))}else m=Xc(O,f,x,k,j,l);x>=C&&(b+=f.slice(C,x)+m,C=x+O.length)}return b+f.slice(C)}]});(function(){function e(){rt(this,e)}return nt(e,null,[{key:"camelize",value:function(t){return t.replace(/-(\w)/g,function(r,n){return n?n.toUpperCase():""})}},{key:"capitalize",value:function(t){return t.charAt(0).toUpperCase()+t.slice(1)}}]),e})();(function(){function e(){rt(this,e)}return nt(e,null,[{key:"_clone",value:function(){}}]),e})();var Li=U("isConcatSpreadable"),Qc=xr>=51||!F(function(){var e=[];return e[Li]=!1,e.concat()[0]!==e}),ef=Yn("concat"),tf=function(e){if(!Q(e))return!1;var t=e[Li];return t!==void 0?!!t:wt(e)};ae({target:"Array",proto:!0,forced:!Qc||!ef},{concat:function(e){var t,r,n,o,a,s=Pe(this),i=Dr(s,0),l=0;for(t=-1,n=arguments.length;t<n;t++)if(tf(a=t===-1?s:arguments[t])){if(l+(o=pe(a.length))>9007199254740991)throw TypeError("Maximum allowed index exceeded");for(r=0;r<o;r++,l++)r in a&&St(i,l,a[r])}else{if(l>=9007199254740991)throw TypeError("Maximum allowed index exceeded");St(i,l++,a)}return i.length=l,i}});var rn,rr=function(e,t,r){if(Jn(e),t===void 0)return e;switch(r){case 0:return function(){return e.call(t)};case 1:return function(n){return e.call(t,n)};case 2:return function(n,o){return e.call(t,n,o)};case 3:return function(n,o,a){return e.call(t,n,o,a)}}return function(){return e.apply(t,arguments)}},da=[].push,Me=function(e){var t=e==1,r=e==2,n=e==3,o=e==4,a=e==6,s=e==7,i=e==5||a;return function(l,c,u,f){for(var d,p,g=Pe(l),v=Mr(g),h=rr(c,u,3),y=pe(v.length),b=0,C=f||Dr,w=t?C(l,y):r||s?C(l,0):void 0;y>b;b++)if((i||b in v)&&(p=h(d=v[b],b,g),e))if(t)w[b]=p;else if(p)switch(e){case 3:return!0;case 5:return d;case 6:return b;case 2:da.call(w,d)}else switch(e){case 4:return!1;case 7:da.call(w,d)}return a?-1:n||o?o:w}},Bi={forEach:Me(0),map:Me(1),filter:Me(2),some:Me(3),every:Me(4),find:Me(5),findIndex:Me(6),filterOut:Me(7)},rf=ve?Object.defineProperties:function(e,t){le(e);for(var r,n=Zn(t),o=n.length,a=0;o>a;)ke.f(e,r=n[a++],t[r]);return e},nf=Br("document","documentElement"),Di=Gn("IE_PROTO"),nn=function(){},pa=function(e){return"<script>"+e+"<\/script>"},pr=function(){try{rn=document.domain&&new ActiveXObject("htmlfile")}catch{}var e,t;pr=rn?function(n){n.write(pa("")),n.close();var o=n.parentWindow.Object;return n=null,o}(rn):((t=gi("iframe")).style.display="none",nf.appendChild(t),t.src="javascript:",(e=t.contentWindow.document).open(),e.write(pa("document.F=Object")),e.close(),e.F);for(var r=wr.length;r--;)delete pr.prototype[wr[r]];return pr()};Lr[Di]=!0;var Qn=Object.create||function(e,t){var r;return e!==null?(nn.prototype=le(e),r=new nn,nn.prototype=null,r[Di]=e):r=pr(),t===void 0?r:rf(r,t)},xn=U("unscopables"),On=Array.prototype;On[xn]==null&&ke.f(On,xn,{configurable:!0,value:Qn(null)});var pt=function(e){On[xn][e]=!0},of=Bi.find,va=!0,af=Et("find");"find"in[]&&Array(1).find(function(){va=!1}),ae({target:"Array",proto:!0,forced:va||!af},{find:function(e){return of(this,e,arguments.length>1?arguments[1]:void 0)}}),pt("find");var lf=Bi.findIndex,ha=!0,sf=Et("findIndex");"findIndex"in[]&&Array(1).findIndex(function(){ha=!1}),ae({target:"Array",proto:!0,forced:ha||!sf},{findIndex:function(e){return lf(this,e,arguments.length>1?arguments[1]:void 0)}}),pt("findIndex");var Hi=function(e,t,r,n,o,a,s,i){for(var l,c=o,u=0,f=!!s&&rr(s,i,3);u<n;){if(u in r){if(l=f?f(r[u],u,t):r[u],a>0&&wt(l))c=Hi(e,t,l,pe(l.length),c,a-1)-1;else{if(c>=9007199254740991)throw TypeError("Exceed the acceptable array length");e[c]=l}c++}u++}return c},uf=Hi;ae({target:"Array",proto:!0},{flat:function(){var e=arguments.length?arguments[0]:void 0,t=Pe(this),r=pe(t.length),n=Dr(t,0);return n.length=uf(n,t,t,r,0,e===void 0?1:$t(e)),n}});var _n=function(e){var t=e.return;if(t!==void 0)return le(t.call(e)).value},cf=function(e,t,r,n){try{return n?t(le(r)[0],r[1]):t(r)}catch(o){throw _n(e),o}},xt={},ff=U("iterator"),df=Array.prototype,Ni=function(e){return e!==void 0&&(xt.Array===e||df[ff]===e)},pf=U("iterator"),Vi=function(e){if(e!=null)return e[pf]||e["@@iterator"]||xt[_i(e)]},Fi=U("iterator"),Wi=!1;try{var vf=0,ga={next:function(){return{done:!!vf++}},return:function(){Wi=!0}};ga[Fi]=function(){return this},Array.from(ga,function(){throw 2})}catch{}var zi=function(e,t){if(!t&&!Wi)return!1;var r=!1;try{var n={};n[Fi]=function(){return{next:function(){return{done:r=!0}}}},e(n)}catch{}return r},hf=!zi(function(e){Array.from(e)});ae({target:"Array",stat:!0,forced:hf},{from:function(e){var t,r,n,o,a,s,i=Pe(e),l=typeof this=="function"?this:Array,c=arguments.length,u=c>1?arguments[1]:void 0,f=u!==void 0,d=Vi(i),p=0;if(f&&(u=rr(u,c>2?arguments[2]:void 0,2)),d==null||l==Array&&Ni(d))for(r=new l(t=pe(i.length));t>p;p++)s=f?u(i[p],p):i[p],St(r,p,s);else for(a=(o=d.call(i)).next,r=new l;!(n=a.call(o)).done;p++)s=f?cf(o,u,[n.value,p],!0):n.value,St(r,p,s);return r.length=p,r}});var ya=function(e){return function(t,r,n,o){Jn(r);var a=Pe(t),s=Mr(a),i=pe(a.length),l=e?i-1:0,c=e?-1:1;if(n<2)for(;;){if(l in s){o=s[l],l+=c;break}if(l+=c,e?l<0:i<=l)throw TypeError("Reduce of empty array with no initial value")}for(;e?l>=0:i>l;l+=c)l in s&&(o=r(o,s[l],l,a));return o}},gf={left:ya(!1),right:ya(!0)},yf=$e(Z.process)=="process",mf=gf.left,bf=qn("reduce"),Cf=Et("reduce",{1:0});ae({target:"Array",proto:!0,forced:!bf||!Cf||!yf&&xr>79&&xr<83},{reduce:function(e){return mf(this,e,arguments.length,arguments.length>1?arguments[1]:void 0)}}),pt("flat");var ze,ma,ba,wf=!F(function(){return Object.isExtensible(Object.preventExtensions({}))}),Ui=Fn(function(e){var t=ke.f,r=Kn("meta"),n=0,o=Object.isExtensible||function(){return!0},a=function(i){t(i,r,{value:{objectID:"O"+ ++n,weakData:{}}})},s=e.exports={REQUIRED:!1,fastKey:function(i,l){if(!Q(i))return typeof i=="symbol"?i:(typeof i=="string"?"S":"P")+i;if(!Y(i,r)){if(!o(i))return"F";if(!l)return"E";a(i)}return i[r].objectID},getWeakData:function(i,l){if(!Y(i,r)){if(!o(i))return!0;if(!l)return!1;a(i)}return i[r].weakData},onFreeze:function(i){return wf&&s.REQUIRED&&o(i)&&!Y(i,r)&&a(i),i}};Lr[r]=!0}),Mt=function(e,t){this.stopped=e,this.result=t},Ca=function(e,t,r){var n,o,a,s,i,l,c,u=r&&r.that,f=!(!r||!r.AS_ENTRIES),d=!(!r||!r.IS_ITERATOR),p=!(!r||!r.INTERRUPTED),g=rr(t,u,1+f+p),v=function(y){return n&&_n(n),new Mt(!0,y)},h=function(y){return f?(le(y),p?g(y[0],y[1],v):g(y[0],y[1])):p?g(y,v):g(y)};if(d)n=e;else{if(typeof(o=Vi(e))!="function")throw TypeError("Target is not iterable");if(Ni(o)){for(a=0,s=pe(e.length);s>a;a++)if((i=h(e[a]))&&i instanceof Mt)return i;return new Mt(!1)}n=o.call(e)}for(l=n.next;!(c=l.call(n)).done;){try{i=h(c.value)}catch(y){throw _n(n),y}if(typeof i=="object"&&i&&i instanceof Mt)return i}return new Mt(!1)},wa=function(e,t,r){if(!(e instanceof t))throw TypeError("Incorrect "+(r?r+" ":"")+"invocation");return e},Sf=ke.f,Sa=U("toStringTag"),An=function(e,t,r){e&&!Y(e=r?e:e.prototype,Sa)&&Sf(e,Sa,{configurable:!0,value:t})},Ar=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,r={};try{(e=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(r,[]),t=r instanceof Array}catch{}return function(n,o){return le(n),function(a){if(!Q(a)&&a!==null)throw TypeError("Can't set "+String(a)+" as a prototype")}(o),t?e.call(n,o):n.__proto__=o,n}}():void 0),xa=function(e,t,r){for(var n in t)He(e,n,t[n],r);return e},xf=!F(function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}),Oa=Gn("IE_PROTO"),Of=Object.prototype,$r=xf?Object.getPrototypeOf:function(e){return e=Pe(e),Y(e,Oa)?e[Oa]:typeof e.constructor=="function"&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?Of:null},on=U("iterator"),Ki=!1;[].keys&&("next"in(ba=[].keys())?(ma=$r($r(ba)))!==Object.prototype&&(ze=ma):Ki=!0),(ze==null||F(function(){var e={};return ze[on].call(e)!==e}))&&(ze={}),Y(ze,on)||ge(ze,on,function(){return this});var eo={IteratorPrototype:ze,BUGGY_SAFARI_ITERATORS:Ki},_f=eo.IteratorPrototype,Af=function(){return this},an=eo.IteratorPrototype,fr=eo.BUGGY_SAFARI_ITERATORS,Lt=U("iterator"),$f=function(){return this},to=function(e,t,r,n,o,a,s){(function(y,b,C){var w=b+" Iterator";y.prototype=Qn(_f,{next:Ir(1,C)}),An(y,w,!1),xt[w]=Af})(r,t,n);var i,l,c,u=function(y){if(y===o&&v)return v;if(!fr&&y in p)return p[y];switch(y){case"keys":case"values":case"entries":return function(){return new r(this,y)}}return function(){return new r(this)}},f=t+" Iterator",d=!1,p=e.prototype,g=p[Lt]||p["@@iterator"]||o&&p[o],v=!fr&&g||u(o),h=t=="Array"&&p.entries||g;if(h&&(i=$r(h.call(new e)),an!==Object.prototype&&i.next&&($r(i)!==an&&(Ar?Ar(i,an):typeof i[Lt]!="function"&&ge(i,Lt,$f)),An(i,f,!0))),o=="values"&&g&&g.name!=="values"&&(d=!0,v=function(){return g.call(this)}),p[Lt]!==v&&ge(p,Lt,v),xt[t]=v,o)if(l={values:u("values"),keys:a?v:u("keys"),entries:u("entries")},s)for(c in l)(fr||d||!(c in p))&&He(p,c,l[c]);else ae({target:t,proto:!0,forced:fr||d},l);return l},_a=U("species"),Ef=ke.f,Aa=Ui.fastKey,$a=De.set,ln=De.getterFor;(function(e,t,r){var n=e.indexOf("Map")!==-1,o=e.indexOf("Weak")!==-1,a=n?"set":"add",s=Z[e],i=s&&s.prototype,l=s,c={},u=function(h){var y=i[h];He(i,h,h=="add"?function(b){return y.call(this,b===0?0:b),this}:h=="delete"?function(b){return!(o&&!Q(b))&&y.call(this,b===0?0:b)}:h=="get"?function(b){return o&&!Q(b)?void 0:y.call(this,b===0?0:b)}:h=="has"?function(b){return!(o&&!Q(b))&&y.call(this,b===0?0:b)}:function(b,C){return y.call(this,b===0?0:b,C),this})};if(Cn(e,typeof s!="function"||!(o||i.forEach&&!F(function(){new s().entries().next()}))))l=r.getConstructor(t,e,n,a),Ui.REQUIRED=!0;else if(Cn(e,!0)){var f=new l,d=f[a](o?{}:-0,1)!=f,p=F(function(){f.has(1)}),g=zi(function(h){new s(h)}),v=!o&&F(function(){for(var h=new s,y=5;y--;)h[a](y,y);return!h.has(-0)});g||((l=t(function(h,y){wa(h,l,e);var b=function(C,w,O){var x,k;return Ar&&typeof(x=w.constructor)=="function"&&x!==O&&Q(k=x.prototype)&&k!==O.prototype&&Ar(C,k),C}(new s,h,l);return y!=null&&Ca(y,b[a],{that:b,AS_ENTRIES:n}),b})).prototype=i,i.constructor=l),(p||v)&&(u("delete"),u("has"),n&&u("get")),(v||d)&&u(a),o&&i.clear&&delete i.clear}c[e]=l,ae({global:!0,forced:l!=s},c),An(l,e),o||r.setStrong(l,e,n)})("Set",function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}},{getConstructor:function(e,t,r,n){var o=e(function(l,c){wa(l,o,t),$a(l,{type:t,index:Qn(null),first:void 0,last:void 0,size:0}),ve||(l.size=0),c!=null&&Ca(c,l[n],{that:l,AS_ENTRIES:r})}),a=ln(t),s=function(l,c,u){var f,d,p=a(l),g=i(l,c);return g?g.value=u:(p.last=g={index:d=Aa(c,!0),key:c,value:u,previous:f=p.last,next:void 0,removed:!1},p.first||(p.first=g),f&&(f.next=g),ve?p.size++:l.size++,d!=="F"&&(p.index[d]=g)),l},i=function(l,c){var u,f=a(l),d=Aa(c);if(d!=="F")return f.index[d];for(u=f.first;u;u=u.next)if(u.key==c)return u};return xa(o.prototype,{clear:function(){for(var l=a(this),c=l.index,u=l.first;u;)u.removed=!0,u.previous&&(u.previous=u.previous.next=void 0),delete c[u.index],u=u.next;l.first=l.last=void 0,ve?l.size=0:this.size=0},delete:function(l){var c=this,u=a(c),f=i(c,l);if(f){var d=f.next,p=f.previous;delete u.index[f.index],f.removed=!0,p&&(p.next=d),d&&(d.previous=p),u.first==f&&(u.first=d),u.last==f&&(u.last=p),ve?u.size--:c.size--}return!!f},forEach:function(l){for(var c,u=a(this),f=rr(l,arguments.length>1?arguments[1]:void 0,3);c=c?c.next:u.first;)for(f(c.value,c.key,this);c&&c.removed;)c=c.previous},has:function(l){return!!i(this,l)}}),xa(o.prototype,r?{get:function(l){var c=i(this,l);return c&&c.value},set:function(l,c){return s(this,l===0?0:l,c)}}:{add:function(l){return s(this,l=l===0?0:l,l)}}),ve&&Ef(o.prototype,"size",{get:function(){return a(this).size}}),o},setStrong:function(e,t,r){var n=t+" Iterator",o=ln(t),a=ln(n);to(e,t,function(s,i){$a(this,{type:n,target:s,state:o(s),kind:i,last:void 0})},function(){for(var s=a(this),i=s.kind,l=s.last;l&&l.removed;)l=l.previous;return s.target&&(s.last=l=l?l.next:s.state.first)?i=="keys"?{value:l.key,done:!1}:i=="values"?{value:l.value,done:!1}:{value:[l.key,l.value],done:!1}:(s.target=void 0,{value:void 0,done:!0})},r?"entries":"values",!r,!0),function(s){var i=Br(s),l=ke.f;ve&&i&&!i[_a]&&l(i,_a,{configurable:!0,get:function(){return this}})}(t)}});var Tf=ji.charAt,kf=De.set,Pf=De.getterFor("String Iterator");to(String,"String",function(e){kf(this,{type:"String Iterator",string:String(e),index:0})},function(){var e,t=Pf(this),r=t.string,n=t.index;return n>=r.length?{value:void 0,done:!0}:(e=Tf(r,n),t.index+=e.length,{value:e,done:!1})});var Ea={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},jf=De.set,Rf=De.getterFor("Array Iterator"),Ht=to(Array,"Array",function(e,t){jf(this,{type:"Array Iterator",target:At(e),index:0,kind:t})},function(){var e=Rf(this),t=e.target,r=e.kind,n=e.index++;return!t||n>=t.length?(e.target=void 0,{value:void 0,done:!0}):r=="keys"?{value:n,done:!1}:r=="values"?{value:t[n],done:!1}:{value:[n,t[n]],done:!1}},"values");xt.Arguments=xt.Array,pt("keys"),pt("values"),pt("entries");var sn=U("iterator"),Ta=U("toStringTag"),un=Ht.values;for(var cn in Ea){var ka=Z[cn],_e=ka&&ka.prototype;if(_e){if(_e[sn]!==un)try{ge(_e,sn,un)}catch{_e[sn]=un}if(_e[Ta]||ge(_e,Ta,cn),Ea[cn]){for(var ft in Ht)if(_e[ft]!==Ht[ft])try{ge(_e,ft,Ht[ft])}catch{_e[ft]=Ht[ft]}}}}(function(){function e(){rt(this,e)}return nt(e,null,[{key:"deduplicate",value:function(t){return Array.from(new Set(t))}},{key:"flat",value:function(t){return t.reduce(function(r,n){var o=Array.isArray(n)?e.flat(n):n;return r.concat(o)},[])}},{key:"find",value:function(t,r){return t.find(r)}},{key:"findIndex",value:function(t,r){return t.findIndex(r)}}]),e})();(function(){function e(){rt(this,e)}return nt(e,null,[{key:"today",value:function(){return new Date}}]),e})();(function(){function e(){rt(this,e)}return nt(e,null,[{key:"range",value:function(t,r,n){return Math.min(Math.max(t,r),n)}},{key:"clamp",value:function(t,r,n){return r<n?t<r?r:t>n?n:t:t<n?n:t>r?r:t}}]),e})();var If=typeof global=="object"&&global&&global.Object===Object&&global;const Gi=If;var Mf=typeof self=="object"&&self&&self.Object===Object&&self,Lf=Gi||Mf||Function("return this")();const Tt=Lf;var Bf=Tt.Symbol;const Er=Bf;var qi=Object.prototype,Df=qi.hasOwnProperty,Hf=qi.toString,Bt=Er?Er.toStringTag:void 0;function Nf(e){var t=Df.call(e,Bt),r=e[Bt];try{e[Bt]=void 0;var n=!0}catch{}var o=Hf.call(e);return n&&(t?e[Bt]=r:delete e[Bt]),o}var Vf=Object.prototype,Ff=Vf.toString;function Wf(e){return Ff.call(e)}var zf="[object Null]",Uf="[object Undefined]",Pa=Er?Er.toStringTag:void 0;function Hr(e){return e==null?e===void 0?Uf:zf:Pa&&Pa in Object(e)?Nf(e):Wf(e)}function nr(e){return e!=null&&typeof e=="object"}var Kf=Array.isArray;const $n=Kf;function ot(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}function Yi(e){return e}var Gf="[object AsyncFunction]",qf="[object Function]",Yf="[object GeneratorFunction]",Xf="[object Proxy]";function ro(e){if(!ot(e))return!1;var t=Hr(e);return t==qf||t==Yf||t==Gf||t==Xf}var Jf=Tt["__core-js_shared__"];const fn=Jf;var ja=function(){var e=/[^.]+$/.exec(fn&&fn.keys&&fn.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function Zf(e){return!!ja&&ja in e}var Qf=Function.prototype,ed=Qf.toString;function td(e){if(e!=null){try{return ed.call(e)}catch{}try{return e+""}catch{}}return""}var rd=/[\\^$.*+?()[\]{}|]/g,nd=/^\[object .+?Constructor\]$/,od=Function.prototype,ad=Object.prototype,id=od.toString,ld=ad.hasOwnProperty,sd=RegExp("^"+id.call(ld).replace(rd,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function ud(e){if(!ot(e)||Zf(e))return!1;var t=ro(e)?sd:nd;return t.test(td(e))}function cd(e,t){return e==null?void 0:e[t]}function no(e,t){var r=cd(e,t);return ud(r)?r:void 0}var Ra=Object.create,fd=function(){function e(){}return function(t){if(!ot(t))return{};if(Ra)return Ra(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}();const dd=fd;function pd(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}function vd(e,t){var r=-1,n=e.length;for(t||(t=Array(n));++r<n;)t[r]=e[r];return t}var hd=800,gd=16,yd=Date.now;function md(e){var t=0,r=0;return function(){var n=yd(),o=gd-(n-r);if(r=n,o>0){if(++t>=hd)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}function bd(e){return function(){return e}}var Cd=function(){try{var e=no(Object,"defineProperty");return e({},"",{}),e}catch{}}();const Tr=Cd;var wd=Tr?function(e,t){return Tr(e,"toString",{configurable:!0,enumerable:!1,value:bd(t),writable:!0})}:Yi;const Sd=wd;var xd=md(Sd);const Od=xd;var _d=9007199254740991,Ad=/^(?:0|[1-9]\d*)$/;function Xi(e,t){var r=typeof e;return t=t??_d,!!t&&(r=="number"||r!="symbol"&&Ad.test(e))&&e>-1&&e%1==0&&e<t}function oo(e,t,r){t=="__proto__"&&Tr?Tr(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}function Nr(e,t){return e===t||e!==e&&t!==t}var $d=Object.prototype,Ed=$d.hasOwnProperty;function Td(e,t,r){var n=e[t];(!(Ed.call(e,t)&&Nr(n,r))||r===void 0&&!(t in e))&&oo(e,t,r)}function kd(e,t,r,n){var o=!r;r||(r={});for(var a=-1,s=t.length;++a<s;){var i=t[a],l=n?n(r[i],e[i],i,r,e):void 0;l===void 0&&(l=e[i]),o?oo(r,i,l):Td(r,i,l)}return r}var Ia=Math.max;function Pd(e,t,r){return t=Ia(t===void 0?e.length-1:t,0),function(){for(var n=arguments,o=-1,a=Ia(n.length-t,0),s=Array(a);++o<a;)s[o]=n[t+o];o=-1;for(var i=Array(t+1);++o<t;)i[o]=n[o];return i[t]=r(s),pd(e,this,i)}}function jd(e,t){return Od(Pd(e,t,Yi),e+"")}var Rd=9007199254740991;function Ji(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=Rd}function ao(e){return e!=null&&Ji(e.length)&&!ro(e)}function Id(e,t,r){if(!ot(r))return!1;var n=typeof t;return(n=="number"?ao(r)&&Xi(t,r.length):n=="string"&&t in r)?Nr(r[t],e):!1}function Md(e){return jd(function(t,r){var n=-1,o=r.length,a=o>1?r[o-1]:void 0,s=o>2?r[2]:void 0;for(a=e.length>3&&typeof a=="function"?(o--,a):void 0,s&&Id(r[0],r[1],s)&&(a=o<3?void 0:a,o=1),t=Object(t);++n<o;){var i=r[n];i&&e(t,i,n,a)}return t})}var Ld=Object.prototype;function Zi(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||Ld;return e===r}function Bd(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}var Dd="[object Arguments]";function Ma(e){return nr(e)&&Hr(e)==Dd}var Qi=Object.prototype,Hd=Qi.hasOwnProperty,Nd=Qi.propertyIsEnumerable,Vd=Ma(function(){return arguments}())?Ma:function(e){return nr(e)&&Hd.call(e,"callee")&&!Nd.call(e,"callee")};const En=Vd;function Fd(){return!1}var el=typeof exports=="object"&&exports&&!exports.nodeType&&exports,La=el&&typeof module=="object"&&module&&!module.nodeType&&module,Wd=La&&La.exports===el,Ba=Wd?Tt.Buffer:void 0,zd=Ba?Ba.isBuffer:void 0,Ud=zd||Fd;const tl=Ud;var Kd="[object Arguments]",Gd="[object Array]",qd="[object Boolean]",Yd="[object Date]",Xd="[object Error]",Jd="[object Function]",Zd="[object Map]",Qd="[object Number]",ep="[object Object]",tp="[object RegExp]",rp="[object Set]",np="[object String]",op="[object WeakMap]",ap="[object ArrayBuffer]",ip="[object DataView]",lp="[object Float32Array]",sp="[object Float64Array]",up="[object Int8Array]",cp="[object Int16Array]",fp="[object Int32Array]",dp="[object Uint8Array]",pp="[object Uint8ClampedArray]",vp="[object Uint16Array]",hp="[object Uint32Array]",N={};N[lp]=N[sp]=N[up]=N[cp]=N[fp]=N[dp]=N[pp]=N[vp]=N[hp]=!0;N[Kd]=N[Gd]=N[ap]=N[qd]=N[ip]=N[Yd]=N[Xd]=N[Jd]=N[Zd]=N[Qd]=N[ep]=N[tp]=N[rp]=N[np]=N[op]=!1;function gp(e){return nr(e)&&Ji(e.length)&&!!N[Hr(e)]}function yp(e){return function(t){return e(t)}}var rl=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Ut=rl&&typeof module=="object"&&module&&!module.nodeType&&module,mp=Ut&&Ut.exports===rl,dn=mp&&Gi.process,bp=function(){try{var e=Ut&&Ut.require&&Ut.require("util").types;return e||dn&&dn.binding&&dn.binding("util")}catch{}}();const Da=bp;var Ha=Da&&Da.isTypedArray,Cp=Ha?yp(Ha):gp;const nl=Cp;var wp=Object.prototype,Sp=wp.hasOwnProperty;function xp(e,t){var r=$n(e),n=!r&&En(e),o=!r&&!n&&tl(e),a=!r&&!n&&!o&&nl(e),s=r||n||o||a,i=s?Bd(e.length,String):[],l=i.length;for(var c in e)(t||Sp.call(e,c))&&!(s&&(c=="length"||o&&(c=="offset"||c=="parent")||a&&(c=="buffer"||c=="byteLength"||c=="byteOffset")||Xi(c,l)))&&i.push(c);return i}function Op(e,t){return function(r){return e(t(r))}}function _p(e){var t=[];if(e!=null)for(var r in Object(e))t.push(r);return t}var Ap=Object.prototype,$p=Ap.hasOwnProperty;function Ep(e){if(!ot(e))return _p(e);var t=Zi(e),r=[];for(var n in e)n=="constructor"&&(t||!$p.call(e,n))||r.push(n);return r}function ol(e){return ao(e)?xp(e,!0):Ep(e)}var Tp=no(Object,"create");const Zt=Tp;function kp(){this.__data__=Zt?Zt(null):{},this.size=0}function Pp(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var jp="__lodash_hash_undefined__",Rp=Object.prototype,Ip=Rp.hasOwnProperty;function Mp(e){var t=this.__data__;if(Zt){var r=t[e];return r===jp?void 0:r}return Ip.call(t,e)?t[e]:void 0}var Lp=Object.prototype,Bp=Lp.hasOwnProperty;function Dp(e){var t=this.__data__;return Zt?t[e]!==void 0:Bp.call(t,e)}var Hp="__lodash_hash_undefined__";function Np(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=Zt&&t===void 0?Hp:t,this}function Ze(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Ze.prototype.clear=kp;Ze.prototype.delete=Pp;Ze.prototype.get=Mp;Ze.prototype.has=Dp;Ze.prototype.set=Np;function Vp(){this.__data__=[],this.size=0}function Vr(e,t){for(var r=e.length;r--;)if(Nr(e[r][0],t))return r;return-1}var Fp=Array.prototype,Wp=Fp.splice;function zp(e){var t=this.__data__,r=Vr(t,e);if(r<0)return!1;var n=t.length-1;return r==n?t.pop():Wp.call(t,r,1),--this.size,!0}function Up(e){var t=this.__data__,r=Vr(t,e);return r<0?void 0:t[r][1]}function Kp(e){return Vr(this.__data__,e)>-1}function Gp(e,t){var r=this.__data__,n=Vr(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this}function je(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}je.prototype.clear=Vp;je.prototype.delete=zp;je.prototype.get=Up;je.prototype.has=Kp;je.prototype.set=Gp;var qp=no(Tt,"Map");const al=qp;function Yp(){this.size=0,this.__data__={hash:new Ze,map:new(al||je),string:new Ze}}function Xp(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}function Fr(e,t){var r=e.__data__;return Xp(t)?r[typeof t=="string"?"string":"hash"]:r.map}function Jp(e){var t=Fr(this,e).delete(e);return this.size-=t?1:0,t}function Zp(e){return Fr(this,e).get(e)}function Qp(e){return Fr(this,e).has(e)}function ev(e,t){var r=Fr(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this}function kt(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}kt.prototype.clear=Yp;kt.prototype.delete=Jp;kt.prototype.get=Zp;kt.prototype.has=Qp;kt.prototype.set=ev;var tv=Op(Object.getPrototypeOf,Object);const il=tv;var rv="[object Object]",nv=Function.prototype,ov=Object.prototype,ll=nv.toString,av=ov.hasOwnProperty,iv=ll.call(Object);function lv(e){if(!nr(e)||Hr(e)!=rv)return!1;var t=il(e);if(t===null)return!0;var r=av.call(t,"constructor")&&t.constructor;return typeof r=="function"&&r instanceof r&&ll.call(r)==iv}function sv(){this.__data__=new je,this.size=0}function uv(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}function cv(e){return this.__data__.get(e)}function fv(e){return this.__data__.has(e)}var dv=200;function pv(e,t){var r=this.__data__;if(r instanceof je){var n=r.__data__;if(!al||n.length<dv-1)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new kt(n)}return r.set(e,t),this.size=r.size,this}function Pt(e){var t=this.__data__=new je(e);this.size=t.size}Pt.prototype.clear=sv;Pt.prototype.delete=uv;Pt.prototype.get=cv;Pt.prototype.has=fv;Pt.prototype.set=pv;var sl=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Na=sl&&typeof module=="object"&&module&&!module.nodeType&&module,vv=Na&&Na.exports===sl,Va=vv?Tt.Buffer:void 0,Fa=Va?Va.allocUnsafe:void 0;function hv(e,t){if(t)return e.slice();var r=e.length,n=Fa?Fa(r):new e.constructor(r);return e.copy(n),n}var gv=Tt.Uint8Array;const Wa=gv;function yv(e){var t=new e.constructor(e.byteLength);return new Wa(t).set(new Wa(e)),t}function mv(e,t){var r=t?yv(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)}function bv(e){return typeof e.constructor=="function"&&!Zi(e)?dd(il(e)):{}}function Cv(e){return function(t,r,n){for(var o=-1,a=Object(t),s=n(t),i=s.length;i--;){var l=s[e?i:++o];if(r(a[l],l,a)===!1)break}return t}}var wv=Cv();const Sv=wv;function Tn(e,t,r){(r!==void 0&&!Nr(e[t],r)||r===void 0&&!(t in e))&&oo(e,t,r)}function xv(e){return nr(e)&&ao(e)}function kn(e,t){if(!(t==="constructor"&&typeof e[t]=="function")&&t!="__proto__")return e[t]}function Ov(e){return kd(e,ol(e))}function _v(e,t,r,n,o,a,s){var i=kn(e,r),l=kn(t,r),c=s.get(l);if(c){Tn(e,r,c);return}var u=a?a(i,l,r+"",e,t,s):void 0,f=u===void 0;if(f){var d=$n(l),p=!d&&tl(l),g=!d&&!p&&nl(l);u=l,d||p||g?$n(i)?u=i:xv(i)?u=vd(i):p?(f=!1,u=hv(l,!0)):g?(f=!1,u=mv(l,!0)):u=[]:lv(l)||En(l)?(u=i,En(i)?u=Ov(i):(!ot(i)||ro(i))&&(u=bv(l))):f=!1}f&&(s.set(l,u),o(u,l,n,a,s),s.delete(l)),Tn(e,r,u)}function ul(e,t,r,n,o){e!==t&&Sv(t,function(a,s){if(o||(o=new Pt),ot(a))_v(e,t,s,r,ul,n,o);else{var i=n?n(kn(e,s),a,s+"",e,t,o):void 0;i===void 0&&(i=a),Tn(e,s,i)}},ol)}var Av=Md(function(e,t,r){ul(e,t,r)});const Wr=Av;var $v=Object.defineProperty,Ev=(e,t,r)=>t in e?$v(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,te=(e,t,r)=>(Ev(e,typeof t!="symbol"?t+"":t,r),r);const K=e=>Math.round(e*100)/100;class z{constructor(t){te(this,"instance"),te(this,"alphaValue",0),te(this,"redValue",0),te(this,"greenValue",0),te(this,"blueValue",0),te(this,"hueValue",0),te(this,"saturationValue",0),te(this,"brightnessValue",0),te(this,"hslSaturationValue",0),te(this,"lightnessValue",0),te(this,"initAlpha",()=>{const r=this.instance.getAlpha();this.alphaValue=Math.min(1,r)*100}),te(this,"initLightness",()=>{const{s:r,l:n}=this.instance.toHsl();this.hslSaturationValue=K(r),this.lightnessValue=K(n)}),te(this,"initRgb",()=>{const{r,g:n,b:o}=this.instance.toRgb();this.redValue=K(r),this.greenValue=K(n),this.blueValue=K(o)}),te(this,"initHsb",()=>{const{h:r,s:n,v:o}=this.instance.toHsv();this.hueValue=Math.min(360,Math.ceil(r)),this.saturationValue=K(n),this.brightnessValue=K(o)}),te(this,"toHexString",()=>this.instance.toHexString()),te(this,"toRgbString",()=>this.instance.toRgbString()),this.instance=W(t),this.initRgb(),this.initHsb(),this.initLightness(),this.initAlpha()}toString(t){return this.instance.toString(t)}get hex(){return this.instance.toHex()}set hex(t){this.instance=W(t),this.initHsb(),this.initRgb(),this.initAlpha(),this.initLightness()}set hue(t){this.saturation===0&&this.brightness===0&&(this.saturationValue=1,this.brightnessValue=1),this.instance=W({h:K(t),s:this.saturation,v:this.brightness,a:this.alphaValue/100}),this.initRgb(),this.initLightness(),this.hueValue=K(t)}get hue(){return this.hueValue}set saturation(t){this.instance=W({h:this.hue,s:K(t),v:this.brightness,a:this.alphaValue/100}),this.initRgb(),this.initLightness(),this.saturationValue=K(t)}get saturation(){return this.saturationValue}set brightness(t){this.instance=W({h:this.hue,s:this.saturation,v:K(t),a:this.alphaValue/100}),this.initRgb(),this.initLightness(),this.brightnessValue=K(t)}get brightness(){return this.brightnessValue}set lightness(t){this.instance=W({h:this.hue,s:this.hslSaturationValue,l:K(t),a:this.alphaValue/100}),this.initRgb(),this.initHsb(),this.lightnessValue=K(t)}get lightness(){return this.lightnessValue}set red(t){const r=this.instance.toRgb();this.instance=W({...r,r:K(t),a:this.alphaValue/100}),this.initHsb(),this.initLightness(),this.redValue=K(t)}get red(){return this.redValue}set green(t){const r=this.instance.toRgb();this.instance=W({...r,g:K(t),a:this.alphaValue/100}),this.initHsb(),this.initLightness(),this.greenValue=K(t)}get green(){return this.greenValue}set blue(t){const r=this.instance.toRgb();this.instance=W({...r,b:K(t),a:this.alphaValue/100}),this.initHsb(),this.initLightness(),this.blueValue=K(t)}get blue(){return this.blueValue}set alpha(t){this.instance.setAlpha(t/100),this.alphaValue=t}get alpha(){return this.alphaValue}get RGB(){return[this.red,this.green,this.blue,parseFloat((this.alpha/100).toFixed(2))]}get HSB(){return[this.hue,this.saturation,this.brightness,parseFloat((this.alpha/100).toFixed(2))]}get HSL(){return[this.hue,this.hslSaturationValue,this.lightness,parseFloat((this.alpha/100).toFixed(2))]}}function za(e,t,r,n){return`rgba(${[e,t,r,n/100].join(",")})`}const pn=(e,t,r)=>t<r?e<t?t:e>r?r:e:e<r?r:e>t?t:e,io="color-history",lo=8,be=(e,t)=>{const r=e.__vccOpts||e;for(const[n,o]of t)r[n]=o;return r},Tv=de({name:"Alpha",props:{color:T.instanceOf(z),size:T.oneOf(["small","default"]).def("default")},emits:["change"],setup(e,{emit:t}){const r=H(null),n=H(null);let o=e.color||new z;const a=Ce({red:o.red,green:o.green,blue:o.blue,alpha:o.alpha});Ee(()=>e.color,f=>{f&&(o=f,Wr(a,{red:f.red,green:f.green,blue:f.blue,alpha:f.alpha}))},{deep:!0});const s=q(()=>{const f=za(a.red,a.green,a.blue,0),d=za(a.red,a.green,a.blue,100);return{background:`linear-gradient(to right, ${f} , ${d})`}}),i=()=>{if(r.value&&n.value){const f=a.alpha/100,d=r.value.getBoundingClientRect(),p=n.value.offsetWidth;return Math.round(f*(d.width-p)+p/2)}return 0},l=q(()=>({left:i()+"px",top:0})),c=f=>{f.target!==r.value&&u(f)},u=f=>{if(f.stopPropagation(),r.value&&n.value){const d=r.value.getBoundingClientRect(),p=n.value.offsetWidth;let g=f.clientX-d.left;g=Math.max(p/2,g),g=Math.min(g,d.width-p/2);const v=Math.round((g-p/2)/(d.width-p)*100);o.alpha=v,a.alpha=v,t("change",v)}};return Qe(()=>{const f={drag:d=>{u(d)},end:d=>{u(d)}};r.value&&n.value&&Jt.triggerDragEvent(r.value,f)}),{barElement:r,cursorElement:n,getCursorStyle:l,getBackgroundStyle:s,onClickSider:c}}}),kv=e=>(Ot("data-v-18925ba6"),e=e(),_t(),e),Pv=kv(()=>_("div",{class:"vc-alpha-slider__bar-handle"},null,-1)),jv=[Pv];function Rv(e,t,r,n,o,a){return E(),D("div",{class:oe(["vc-alpha-slider","transparent",{"small-slider":e.size==="small"}])},[_("div",{ref:"barElement",class:"vc-alpha-slider__bar",style:ee(e.getBackgroundStyle),onClick:t[0]||(t[0]=(...s)=>e.onClickSider&&e.onClickSider(...s))},[_("div",{class:oe(["vc-alpha-slider__bar-pointer",{"small-bar":e.size==="small"}]),ref:"cursorElement",style:ee(e.getCursorStyle)},jv,6)],4)],2)}const so=be(Tv,[["render",Rv],["__scopeId","data-v-18925ba6"]]),Iv=[["#fcc02e","#f67c01","#e64a19","#d81b43","#8e24aa","#512da7","#1f87e8","#008781","#05a045"],["#fed835","#fb8c00","#f5511e","#eb1d4e","#9c28b1","#5d35b0","#2097f3","#029688","#4cb050"],["#ffeb3c","#ffa727","#fe5722","#eb4165","#aa47bc","#673bb7","#42a5f6","#26a59a","#83c683"],["#fff176","#ffb74e","#ff8a66","#f1627e","#b968c7","#7986cc","#64b5f6","#80cbc4","#a5d6a7"],["#fff59c","#ffcc80","#ffab91","#fb879e","#cf93d9","#9ea8db","#90caf8","#b2dfdc","#c8e6ca"],["transparent","#ffffff","#dedede","#a9a9a9","#4b4b4b","#353535","#212121","#000000","advance"]],Mv=de({name:"Palette",emits:["change"],setup(e,{emit:t}){return{palettes:Iv,computedBgStyle:r=>r==="transparent"?r:r==="advance"?{}:{background:W(r).toRgbString()},onColorChange:r=>{t("change",r)}}}}),Lv={class:"vc-compact"},Bv=["onClick"];function Dv(e,t,r,n,o,a){return E(),D("div",Lv,[(E(!0),D(Ge,null,Kt(e.palettes,(s,i)=>(E(),D("div",{key:i,class:"vc-compact__row"},[(E(!0),D(Ge,null,Kt(s,(l,c)=>(E(),D("div",{key:c,class:"vc-compact__color-cube--wrap",onClick:u=>e.onColorChange(l)},[_("div",{class:oe(["vc-compact__color_cube",{advance:l==="advance",transparent:l==="transparent"}]),style:ee(e.computedBgStyle(l))},null,6)],8,Bv))),128))]))),128))])}const cl=be(Mv,[["render",Dv],["__scopeId","data-v-b969fd48"]]),Hv=de({name:"Board",props:{color:T.instanceOf(z),round:T.bool.def(!1),hide:T.bool.def(!0)},emits:["change"],setup(e,{emit:t}){var r,n,o;const a=jn(),s={h:((r=e.color)==null?void 0:r.hue)||0,s:1,v:1},i=new z(s).toHexString(),l=Ce({hueColor:i,saturation:((n=e.color)==null?void 0:n.saturation)||0,brightness:((o=e.color)==null?void 0:o.brightness)||0}),c=H(0),u=H(0),f=H(),d=q(()=>({top:c.value+"px",left:u.value+"px"})),p=()=>{if(a){const C=a.vnode.el;u.value=l.saturation*(C==null?void 0:C.clientWidth),c.value=(1-l.brightness)*(C==null?void 0:C.clientHeight)}};let g=!1;const v=C=>{g=!0,b(C)},h=C=>{g&&b(C)},y=()=>{g=!1},b=C=>{if(a){const w=a.vnode.el,O=w==null?void 0:w.getBoundingClientRect();let x=C.clientX-O.left,k=C.clientY-O.top;x=pn(x,0,O.width),k=pn(k,0,O.height);const I=x/O.width,j=pn(-(k/O.height)+1,0,1);u.value=x,c.value=k,l.saturation=I,l.brightness=j,t("change",I,j)}};return Qe(()=>{a&&a.vnode.el&&f.value&&kr(()=>{p()})}),re(()=>e.color,C=>{Wr(l,{hueColor:new z({h:C.hue,s:1,v:1}).toHexString(),saturation:C.saturation,brightness:C.brightness}),p()},{deep:!0}),{state:l,cursorElement:f,getCursorStyle:d,onClickBoard:v,onDrag:h,onDragEnd:y}}}),uo=e=>(Ot("data-v-7f0cdcdf"),e=e(),_t(),e),Nv=uo(()=>_("div",{class:"vc-saturation__white"},null,-1)),Vv=uo(()=>_("div",{class:"vc-saturation__black"},null,-1)),Fv=uo(()=>_("div",null,null,-1)),Wv=[Fv];function zv(e,t,r,n,o,a){return E(),D("div",{ref:"boardElement",class:oe(["vc-saturation",{"vc-saturation__chrome":e.round,"vc-saturation__hidden":e.hide}]),style:ee({backgroundColor:e.state.hueColor}),onMousedown:t[0]||(t[0]=(...s)=>e.onClickBoard&&e.onClickBoard(...s)),onMousemove:t[1]||(t[1]=(...s)=>e.onDrag&&e.onDrag(...s)),onMouseup:t[2]||(t[2]=(...s)=>e.onDragEnd&&e.onDragEnd(...s))},[Nv,Vv,_("div",{class:"vc-saturation__cursor",ref:"cursorElement",style:ee(e.getCursorStyle)},Wv,4)],38)}const co=be(Hv,[["render",zv],["__scopeId","data-v-7f0cdcdf"]]),Uv=de({name:"Hue",props:{color:T.instanceOf(z),size:T.oneOf(["small","default"]).def("default")},emits:["change"],setup(e,{emit:t}){const r=H(null),n=H(null);let o=e.color||new z;const a=Ce({hue:o.hue||0});Ee(()=>e.color,u=>{u&&(o=u,Wr(a,{hue:o.hue}))},{deep:!0});const s=()=>{if(r.value&&n.value){const u=r.value.getBoundingClientRect(),f=n.value.offsetWidth;return a.hue===360?u.width-f/2:a.hue%360*(u.width-f)/360+f/2}return 0},i=q(()=>({left:s()+"px",top:0})),l=u=>{u.target!==r.value&&c(u)},c=u=>{if(u.stopPropagation(),r.value&&n.value){const f=r.value.getBoundingClientRect(),d=n.value.offsetWidth;let p=u.clientX-f.left;p=Math.min(p,f.width-d/2),p=Math.max(d/2,p);const g=Math.round((p-d/2)/(f.width-d)*360);o.hue=g,a.hue=g,t("change",g)}};return Qe(()=>{const u={drag:f=>{c(f)},end:f=>{c(f)}};r.value&&n.value&&Jt.triggerDragEvent(r.value,u)}),{barElement:r,cursorElement:n,getCursorStyle:i,onClickSider:l}}}),Kv=e=>(Ot("data-v-e1a08576"),e=e(),_t(),e),Gv=Kv(()=>_("div",{class:"vc-hue-slider__bar-handle"},null,-1)),qv=[Gv];function Yv(e,t,r,n,o,a){return E(),D("div",{class:oe(["vc-hue-slider",{"small-slider":e.size==="small"}])},[_("div",{ref:"barElement",class:"vc-hue-slider__bar",onClick:t[0]||(t[0]=(...s)=>e.onClickSider&&e.onClickSider(...s))},[_("div",{class:oe(["vc-hue-slider__bar-pointer",{"small-bar":e.size==="small"}]),ref:"cursorElement",style:ee(e.getCursorStyle)},qv,6)],512)],2)}const fo=be(Uv,[["render",Yv],["__scopeId","data-v-e1a08576"]]),Xv=de({name:"Lightness",props:{color:T.instanceOf(z),size:T.oneOf(["small","default"]).def("default")},emits:["change"],setup(e,{emit:t}){const r=H(null),n=H(null);let o=e.color||new z;const[a,s,i]=o.HSL,l=Ce({hue:a,saturation:s,lightness:i});Ee(()=>e.color,g=>{if(g){o=g;const[v,h,y]=o.HSL;Wr(l,{hue:v,saturation:h,lightness:y})}},{deep:!0});const c=q(()=>{const g=W({h:l.hue,s:l.saturation,l:.8}).toPercentageRgbString(),v=W({h:l.hue,s:l.saturation,l:.6}).toPercentageRgbString(),h=W({h:l.hue,s:l.saturation,l:.4}).toPercentageRgbString(),y=W({h:l.hue,s:l.saturation,l:.2}).toPercentageRgbString();return{background:[`linear-gradient(to right, rgb(255, 255, 255), ${g}, ${v}, ${h}, ${y}, rgb(0, 0, 0))`,`-webkit-linear-gradient(left, rgb(255, 255, 255), ${g}, ${v}, ${h}, ${y}, rgb(0, 0, 0))`,`-moz-linear-gradient(left, rgb(255, 255, 255), ${g}, ${v}, ${h}, ${y}, rgb(0, 0, 0))`,`-ms-linear-gradient(left, rgb(255, 255, 255), ${g}, ${v}, ${h}, ${y}, rgb(0, 0, 0))`]}}),u=()=>{if(r.value&&n.value){const g=l.lightness,v=r.value.getBoundingClientRect(),h=n.value.offsetWidth;return(1-g)*(v.width-h)+h/2}return 0},f=q(()=>({left:u()+"px",top:0})),d=g=>{g.target!==r.value&&p(g)},p=g=>{if(g.stopPropagation(),r.value&&n.value){const v=r.value.getBoundingClientRect(),h=n.value.offsetWidth;let y=g.clientX-v.left;y=Math.max(h/2,y),y=Math.min(y,v.width-h/2);const b=1-(y-h/2)/(v.width-h);o.lightness=b,t("change",b)}};return Qe(()=>{const g={drag:v=>{p(v)},end:v=>{p(v)}};r.value&&n.value&&Jt.triggerDragEvent(r.value,g)}),{barElement:r,cursorElement:n,getCursorStyle:f,getBackgroundStyle:c,onClickSider:d}}}),Jv=e=>(Ot("data-v-94a50a9e"),e=e(),_t(),e),Zv=Jv(()=>_("div",{class:"vc-lightness-slider__bar-handle"},null,-1)),Qv=[Zv];function eh(e,t,r,n,o,a){return E(),D("div",{class:oe(["vc-lightness-slider",{"small-slider":e.size==="small"}])},[_("div",{ref:"barElement",class:"vc-lightness-slider__bar",style:ee(e.getBackgroundStyle),onClick:t[0]||(t[0]=(...s)=>e.onClickSider&&e.onClickSider(...s))},[_("div",{class:oe(["vc-lightness-slider__bar-pointer",{"small-bar":e.size==="small"}]),ref:"cursorElement",style:ee(e.getCursorStyle)},Qv,6)],4)],2)}const fl=be(Xv,[["render",eh],["__scopeId","data-v-94a50a9e"]]),th=de({name:"History",props:{colors:T.arrayOf(String).def(()=>[]),round:T.bool.def(!1)},emits:["change"],setup(e,{emit:t}){return{onColorSelect:r=>{t("change",r)}}}}),rh={key:0,class:"vc-colorPicker__record"},nh={class:"color-list"},oh=["onClick"];function ah(e,t,r,n,o,a){return e.colors&&e.colors.length>0?(E(),D("div",rh,[_("div",nh,[(E(!0),D(Ge,null,Kt(e.colors,(s,i)=>(E(),D("div",{key:i,class:oe(["color-item","transparent",{"color-item__round":e.round}]),onClick:l=>e.onColorSelect(s)},[_("div",{class:"color-item__display",style:ee({backgroundColor:s})},null,4)],10,oh))),128))])])):V("",!0)}const po=be(th,[["render",ah],["__scopeId","data-v-0f657238"]]),ih=de({name:"Display",props:{color:T.instanceOf(z),disableAlpha:T.bool.def(!1)},emits:["update:color","change"],setup(e,{emit:t}){var r,n,o,a;const{copy:s,copied:i,isSupported:l}=Nl(),c=H("hex"),u=Ce({color:e.color,hex:(r=e.color)==null?void 0:r.hex,alpha:Math.round(((n=e.color)==null?void 0:n.alpha)||100),rgba:(o=e.color)==null?void 0:o.RGB,previewBgColor:(a=e.color)==null?void 0:a.toRgbString()}),f=q(()=>({background:u.previewBgColor})),d=()=>{c.value=c.value==="rgba"?"hex":"rgba"},p=Ae(y=>{if(!y.target.value)return;let b=parseInt(y.target.value.replace("%",""));b>100&&(y.target.value="100",b=100),b<0&&(y.target.value="0",b=0),isNaN(b)&&(y.target.value="100",b=100),!isNaN(b)&&u.color&&(u.color.alpha=b),t("change",u.color)},300),g=Ae((y,b)=>{if(u.color){if(c.value==="hex"){const C=y.target.value.replace("#","");W(C).isValid()?[3,4].includes(C.length)&&(u.color.hex=C):u.color.hex="000000",t("change",u.color)}else if(c.value==="rgba"&&b===3&&y.target.value.toString()==="0."&&u.rgba){u.rgba[b]=y.target.value;const[C,w,O,x]=u.rgba;u.color.hex=W({r:C,g:w,b:O}).toHex(),u.color.alpha=Math.round(x*100),t("change",u.color)}}},100),v=Ae((y,b)=>{if(y.target.value){if(c.value==="hex"){const C=y.target.value.replace("#","");W(C).isValid()&&u.color&&[6,8].includes(C.length)&&(u.color.hex=C)}else if(b!==void 0&&u.rgba&&u.color){if(y.target.value<0&&(y.target.value=0),b===3&&((y.target.value>1||isNaN(y.target.value))&&(y.target.value=1),y.target.value.toString()==="0."))return;b<3&&y.target.value>255&&(y.target.value=255),u.rgba[b]=y.target.value;const[C,w,O,x]=u.rgba;u.color.hex=W({r:C,g:w,b:O}).toHex(),u.color.alpha=Math.round(x*100)}t("change",u.color)}},300),h=()=>{if(l&&u.color){const y=c.value==="hex"?u.color.toString(u.color.alpha===100?"hex6":"hex8"):u.color.toRgbString();s(y||"")}};return re(()=>e.color,y=>{y&&(u.color=y,u.alpha=Math.round(u.color.alpha),u.hex=u.color.hex,u.rgba=u.color.RGB)},{deep:!0}),re(()=>u.color,()=>{u.color&&(u.previewBgColor=u.color.toRgbString())},{deep:!0}),{state:u,getBgColorStyle:f,inputType:c,copied:i,onInputTypeChange:d,onAlphaBlur:p,onInputChange:v,onBlurChange:g,onCopyColorStr:h}}}),lh={class:"vc-display"},sh={class:"vc-current-color vc-transparent"},uh={key:0,class:"copy-text"},ch={key:0,style:{display:"flex",flex:"1",gap:"4px",height:"100%"}},fh={class:"vc-color-input"},dh={key:0,class:"vc-alpha-input"},ph=["value"],vh={key:1,style:{display:"flex",flex:"1",gap:"4px",height:"100%"}},hh=["value","onInput","onBlur"];function gh(e,t,r,n,o,a){return E(),D("div",lh,[_("div",sh,[_("div",{class:"color-cube",style:ee(e.getBgColorStyle),onClick:t[0]||(t[0]=(...s)=>e.onCopyColorStr&&e.onCopyColorStr(...s))},[e.copied?(E(),D("span",uh,"Copied!")):V("",!0)],4)]),e.inputType==="hex"?(E(),D("div",ch,[_("div",fh,[vr(_("input",{"onUpdate:modelValue":t[1]||(t[1]=s=>e.state.hex=s),maxlength:"8",onInput:t[2]||(t[2]=(...s)=>e.onInputChange&&e.onInputChange(...s)),onBlur:t[3]||(t[3]=(...s)=>e.onBlurChange&&e.onBlurChange(...s))},null,544),[[_l,e.state.hex]])]),e.disableAlpha?V("",!0):(E(),D("div",dh,[_("input",{class:"vc-alpha-input__inner",value:e.state.alpha,onInput:t[4]||(t[4]=(...s)=>e.onAlphaBlur&&e.onAlphaBlur(...s))},null,40,ph),Xa("% ")]))])):e.state.rgba?(E(),D("div",vh,[(E(!0),D(Ge,null,Kt(e.state.rgba,(s,i)=>(E(),D("div",{class:"vc-color-input",key:i},[_("input",{value:s,onInput:l=>e.onInputChange(l,i),onBlur:l=>e.onBlurChange(l,i)},null,40,hh)]))),128))])):V("",!0),_("div",{class:"vc-input-toggle",onClick:t[5]||(t[5]=(...s)=>e.onInputTypeChange&&e.onInputTypeChange(...s))},hr(e.inputType),1)])}const vo=be(ih,[["render",gh],["__scopeId","data-v-7334ac20"]]),yh=de({name:"FkColorPicker",components:{Display:vo,Alpha:so,Palette:cl,Board:co,Hue:fo,Lightness:fl,History:po},props:{color:T.instanceOf(z),disableHistory:T.bool.def(!1),roundHistory:T.bool.def(!1),disableAlpha:T.bool.def(!1)},emits:["update:color","change","advanceChange"],setup(e,{emit:t}){const r=e.color||new z,n=Ce({color:r,hex:r.toHexString(),rgb:r.toRgbString()}),o=H(!1),a=q(()=>({background:n.rgb})),s=()=>{o.value=!1,t("advanceChange",!1)},i=Rn(io,[],{}),l=Ae(()=>{if(e.disableHistory)return;const v=n.color.toRgbString();if(i.value=i.value.filter(h=>!W.equals(h,v)),!i.value.includes(v)){for(;i.value.length>lo;)i.value.pop();i.value.unshift(v)}},500),c=v=>{v==="advance"?(o.value=!0,t("advanceChange",!0)):(n.color.hex=v,t("advanceChange",!1))},u=v=>{n.color.alpha=v},f=v=>{n.color.hue=v},d=(v,h)=>{n.color.saturation=v,n.color.brightness=h},p=v=>{n.color.lightness=v},g=v=>{const h=v.target.value.replace("#","");W(h).isValid()&&(n.color.hex=h)};return re(()=>e.color,v=>{v&&(n.color=v)},{deep:!0}),re(()=>n.color,()=>{n.hex=n.color.hex,n.rgb=n.color.toRgbString(),l(),t("update:color",n.color),t("change",n.color)},{deep:!0}),{state:n,advancePanelShow:o,onBack:s,onCompactChange:c,onAlphaChange:u,onHueChange:f,onBoardChange:d,onLightChange:p,onInputChange:g,previewStyle:a,historyColors:i}}}),mh=e=>(Ot("data-v-48e3c224"),e=e(),_t(),e),bh={class:"vc-fk-colorPicker"},Ch={class:"vc-fk-colorPicker__inner"},wh={class:"vc-fk-colorPicker__header"},Sh=mh(()=>_("div",{class:"back"},null,-1)),xh=[Sh];function Oh(e,t,r,n,o,a){const s=G("Palette"),i=G("Board"),l=G("Hue"),c=G("Lightness"),u=G("Alpha"),f=G("Display"),d=G("History");return E(),D("div",bh,[_("div",Ch,[_("div",wh,[e.advancePanelShow?(E(),D("span",{key:0,style:{cursor:"pointer"},onClick:t[0]||(t[0]=(...p)=>e.onBack&&e.onBack(...p))},xh)):V("",!0)]),e.advancePanelShow?V("",!0):(E(),J(s,{key:0,onChange:e.onCompactChange},null,8,["onChange"])),e.advancePanelShow?(E(),J(i,{key:1,color:e.state.color,onChange:e.onBoardChange},null,8,["color","onChange"])):V("",!0),e.advancePanelShow?(E(),J(l,{key:2,color:e.state.color,onChange:e.onHueChange},null,8,["color","onChange"])):V("",!0),e.advancePanelShow?V("",!0):(E(),J(c,{key:3,color:e.state.color,onChange:e.onLightChange},null,8,["color","onChange"])),e.disableAlpha?V("",!0):(E(),J(u,{key:4,color:e.state.color,onChange:e.onAlphaChange},null,8,["color","onChange"])),Le(f,{color:e.state.color,"disable-alpha":e.disableAlpha},null,8,["color","disable-alpha"]),e.disableHistory?V("",!0):(E(),J(d,{key:5,round:e.roundHistory,colors:e.historyColors,onChange:e.onCompactChange},null,8,["round","colors","onChange"]))])])}const Ua=be(yh,[["render",Oh],["__scopeId","data-v-48e3c224"]]),_h=de({name:"ChromeColorPicker",components:{Display:vo,Alpha:so,Board:co,Hue:fo,History:po},props:{color:T.instanceOf(z),disableHistory:T.bool.def(!1),roundHistory:T.bool.def(!1),disableAlpha:T.bool.def(!1)},emits:["update:color","change"],setup(e,{emit:t}){const r=e.color||new z,n=Ce({color:r,hex:r.toHexString(),rgb:r.toRgbString()}),o=q(()=>({background:n.rgb})),a=Rn(io,[],{}),s=Ae(()=>{if(e.disableHistory)return;const d=n.color.toRgbString();if(a.value=a.value.filter(p=>!W.equals(p,d)),!a.value.includes(d)){for(;a.value.length>lo;)a.value.pop();a.value.unshift(d)}},500),i=d=>{n.color.alpha=d},l=d=>{n.color.hue=d},c=d=>{d.hex!==void 0&&(n.color.hex=d.hex),d.alpha!==void 0&&(n.color.alpha=d.alpha)},u=(d,p)=>{n.color.saturation=d,n.color.brightness=p},f=d=>{d!=="advance"&&(n.color.hex=d)};return re(()=>e.color,d=>{d&&(n.color=d)},{deep:!0}),re(()=>n.color,()=>{n.hex=n.color.hex,n.rgb=n.color.toRgbString(),s(),t("update:color",n.color),t("change",n.color)},{deep:!0}),{state:n,previewStyle:o,historyColors:a,onAlphaChange:i,onHueChange:l,onBoardChange:u,onInputChange:c,onCompactChange:f}}}),Ah={class:"vc-chrome-colorPicker"},$h={class:"vc-chrome-colorPicker-body"},Eh={class:"chrome-controls"},Th={class:"chrome-sliders"};function kh(e,t,r,n,o,a){const s=G("Board"),i=G("Hue"),l=G("Alpha"),c=G("Display"),u=G("History");return E(),D("div",Ah,[Le(s,{round:!0,hide:!1,color:e.state.color,onChange:e.onBoardChange},null,8,["color","onChange"]),_("div",$h,[_("div",Eh,[_("div",Th,[Le(i,{size:"small",color:e.state.color,onChange:e.onHueChange},null,8,["color","onChange"]),e.disableAlpha?V("",!0):(E(),J(l,{key:0,size:"small",color:e.state.color,onChange:e.onAlphaChange},null,8,["color","onChange"]))])]),Le(c,{color:e.state.color,"disable-alpha":e.disableAlpha},null,8,["color","disable-alpha"]),e.disableHistory?V("",!0):(E(),J(u,{key:0,round:e.roundHistory,colors:e.historyColors,onChange:e.onCompactChange},null,8,["round","colors","onChange"]))])])}const Ka=be(_h,[["render",kh],["__scopeId","data-v-2611d66c"]]),ho="Vue3ColorPickerProvider",Ph=(e,t)=>{const r=e.getBoundingClientRect(),n=r.left+r.width/2,o=r.top+r.height/2,a=Math.abs(n-t.clientX),s=Math.abs(o-t.clientY),i=Math.sqrt(Math.pow(a,2)+Math.pow(s,2)),l=s/i,c=Math.acos(l);let u=Math.floor(180/(Math.PI/c));return t.clientX>n&&t.clientY>o&&(u=180-u),t.clientX==n&&t.clientY>o&&(u=180),t.clientX>n&&t.clientY==o&&(u=90),t.clientX<n&&t.clientY>o&&(u=180+u),t.clientX<n&&t.clientY==o&&(u=270),t.clientX<n&&t.clientY<o&&(u=360-u),u};let vn=!1;const jh=(e,t)=>{const r=function(o){var a;(a=t.drag)==null||a.call(t,o)},n=function(o){var a;document.removeEventListener("mousemove",r,!1),document.removeEventListener("mouseup",n,!1),document.onselectstart=null,document.ondragstart=null,vn=!1,(a=t.end)==null||a.call(t,o)};e&&e.addEventListener("mousedown",o=>{var a;vn||(document.onselectstart=()=>!1,document.ondragstart=()=>!1,document.addEventListener("mousemove",r,!1),document.addEventListener("mouseup",n,!1),vn=!0,(a=t.start)==null||a.call(t,o))})},Rh={angle:{type:Number,default:0},size:{type:Number,default:16,validator:e=>e>=16},borderWidth:{type:Number,default:1,validator:e=>e>=1},borderColor:{type:String,default:"#666"}},Ih=de({name:"Angle",props:Rh,emits:["update:angle","change"],setup(e,{emit:t}){const r=H(null),n=H(0);Ee(()=>e.angle,i=>{n.value=i});const o=()=>{let i=Number(n.value);isNaN(i)||(i=i>360||i<0?e.angle:i,n.value=i===360?0:i,t("update:angle",n.value),t("change",n.value))},a=q(()=>({width:e.size+"px",height:e.size+"px",borderWidth:e.borderWidth+"px",borderColor:e.borderColor,transform:`rotate(${n.value}deg)`})),s=i=>{r.value&&(n.value=Ph(r.value,i)%360,o())};return Pn(()=>{const i={drag:l=>{s(l)},end:l=>{s(l)}};r.value&&jh(r.value,i)}),()=>Le("div",{class:"bee-angle"},[Le("div",{class:"bee-angle__round",ref:r,style:a.value},null)])}}),Mh=de({name:"GradientColorPicker",components:{Angle:Ih,Display:vo,Alpha:so,Palette:cl,Board:co,Hue:fo,Lightness:fl,History:po},props:{startColor:T.instanceOf(z).isRequired,endColor:T.instanceOf(z).isRequired,startColorStop:T.number.def(0),endColorStop:T.number.def(100),angle:T.number.def(0),type:T.oneOf(["linear","radial"]).def("linear"),disableHistory:T.bool.def(!1),roundHistory:T.bool.def(!1),disableAlpha:T.bool.def(!1),pickerType:T.oneOf(["fk","chrome"]).def("fk")},emits:["update:startColor","update:endColor","update:angle","update:startColorStop","update:endColorStop","startColorChange","endColorChange","advanceChange","angleChange","startColorStopChange","endColorStopChange","typeChange"],setup(e,{emit:t}){const r=Ce({startActive:!0,startColor:e.startColor,endColor:e.endColor,startColorStop:e.startColorStop,endColorStop:e.endColorStop,angle:e.angle,type:e.type,startColorRgba:e.startColor.toRgbString(),endColorRgba:e.endColor.toRgbString()}),n=Ya(ho),o=H(e.pickerType==="chrome"),a=H(),s=H(),i=H();Ee(()=>[e.startColor,e.endColor,e.angle],m=>{r.startColor=m[0],r.endColor=m[1],r.angle=m[2]}),Ee(()=>e.type,m=>{r.type=m});const l=q({get:()=>r.startActive?r.startColor:r.endColor,set:m=>{if(r.startActive){r.startColor=m;return}r.endColor=m}}),c=q(()=>{if(i.value&&a.value){const m=r.startColorStop/100,A=i.value.getBoundingClientRect(),P=a.value.offsetWidth;return Math.round(m*(A.width-P)+P/2)}return 0}),u=q(()=>{if(i.value&&s.value){const m=r.endColorStop/100,A=i.value.getBoundingClientRect(),P=s.value.offsetWidth;return Math.round(m*(A.width-P)+P/2)}return 0}),f=q(()=>{let m=`background: linear-gradient(${r.angle}deg, ${r.startColorRgba} ${r.startColorStop}%, ${r.endColorRgba} ${r.endColorStop}%)`;return r.type==="radial"&&(m=`background: radial-gradient(circle, ${r.startColorRgba} ${r.startColorStop}%, ${r.endColorRgba} ${r.endColorStop}%)`),m}),d=m=>{var A;if(r.startActive=!0,i.value&&a.value){const P=(A=i.value)==null?void 0:A.getBoundingClientRect();let R=m.clientX-P.left;R=Math.max(a.value.offsetWidth/2,R),R=Math.min(R,P.width-a.value.offsetWidth/2),r.startColorStop=Math.round((R-a.value.offsetWidth/2)/(P.width-a.value.offsetWidth)*100),t("update:startColorStop",r.startColorStop),t("startColorStopChange",r.startColorStop)}},p=m=>{var A;if(r.startActive=!1,i.value&&s.value){const P=(A=i.value)==null?void 0:A.getBoundingClientRect();let R=m.clientX-P.left;R=Math.max(s.value.offsetWidth/2,R),R=Math.min(R,P.width-s.value.offsetWidth/2),r.endColorStop=Math.round((R-s.value.offsetWidth/2)/(P.width-s.value.offsetWidth)*100),t("update:endColorStop",r.endColorStop),t("endColorStopChange",r.endColorStop)}},g=m=>{const A=m.target,P=parseInt(A.value.replace("°",""));isNaN(P)||(r.angle=P%360),t("update:angle",r.angle),t("angleChange",r.angle)},v=m=>{r.angle=m,t("update:angle",r.angle),t("angleChange",r.angle)},h=m=>{m==="advance"?(o.value=!0,t("advanceChange",!0)):(l.value.hex=m,t("advanceChange",!1)),x()},y=m=>{l.value.alpha=m,x()},b=m=>{l.value.hue=m,x()},C=(m,A)=>{l.value.saturation=m,l.value.brightness=A,x()},w=m=>{l.value.lightness=m,x()},O=()=>{x()},x=()=>{r.startActive?(t("update:startColor",r.startColor),t("startColorChange",r.startColor)):(t("update:endColor",r.endColor),t("endColorChange",r.endColor))},k=()=>{o.value=!1,t("advanceChange",!1)},I=()=>{r.type=r.type==="linear"?"radial":"linear",t("typeChange",r.type)},j=Rn(io,[],{}),$=Ae(()=>{if(e.disableHistory)return;const m=l.value.toRgbString();if(j.value=j.value.filter(A=>!W.equals(A,m)),!j.value.includes(m)){for(;j.value.length>lo;)j.value.pop();j.value.unshift(m)}},500);return Qe(()=>{s.value&&a.value&&(Jt.triggerDragEvent(s.value,{drag:m=>{p(m)},end:m=>{p(m)}}),Jt.triggerDragEvent(a.value,{drag:m=>{d(m)},end:m=>{d(m)}}))}),re(()=>r.startColor,m=>{r.startColorRgba=m.toRgbString()},{deep:!0}),re(()=>r.endColor,m=>{r.endColorRgba=m.toRgbString()},{deep:!0}),re(()=>l.value,()=>{$()},{deep:!0}),{startGradientRef:a,stopGradientRef:s,colorRangeRef:i,state:r,currentColor:l,getStartColorLeft:c,getEndColorLeft:u,gradientBg:f,advancePanelShow:o,onDegreeBlur:g,onCompactChange:h,onAlphaChange:y,onHueChange:b,onBoardChange:C,onLightChange:w,historyColors:j,onBack:k,onDegreeChange:v,onDisplayChange:O,onTypeChange:I,lang:n==null?void 0:n.lang}}}),dl=e=>(Ot("data-v-c4d6d6ea"),e=e(),_t(),e),Lh={class:"vc-gradient-picker"},Bh={class:"vc-gradient-picker__header"},Dh={class:"vc-gradient__types"},Hh={class:"vc-gradient-wrap__types"},Nh={class:"vc-picker-degree-input vc-degree-input"},Vh={class:"vc-degree-input__control"},Fh=["value"],Wh={class:"vc-degree-input__panel"},zh={class:"vc-degree-input__disk"},Uh={class:"vc-gradient-picker__body"},Kh={class:"vc-color-range",ref:"colorRangeRef"},Gh={class:"vc-color-range__container"},qh={class:"vc-gradient__stop__container"},Yh=["title"],Xh=dl(()=>_("span",{class:"vc-gradient__stop--inner"},null,-1)),Jh=[Xh],Zh=["title"],Qh=dl(()=>_("span",{class:"vc-gradient__stop--inner"},null,-1)),eg=[Qh];function tg(e,t,r,n,o,a){var s,i;const l=G("Angle"),c=G("Board"),u=G("Hue"),f=G("Palette"),d=G("Lightness"),p=G("Alpha"),g=G("Display"),v=G("History");return E(),D("div",Lh,[_("div",Bh,[_("div",null,[vr(_("div",{class:"back",style:{cursor:"pointer"},onClick:t[0]||(t[0]=(...h)=>e.onBack&&e.onBack(...h))},null,512),[[hn,e.pickerType==="fk"&&e.advancePanelShow]])]),_("div",Dh,[_("div",Hh,[(E(),D(Ge,null,Kt(["linear","radial"],h=>_("div",{class:oe(["vc-gradient__type",{active:e.state.type===h}]),key:h,onClick:t[1]||(t[1]=(...y)=>e.onTypeChange&&e.onTypeChange(...y))},hr(e.lang?e.lang[h]:h),3)),64))]),vr(_("div",Nh,[_("div",Vh,[_("input",{value:e.state.angle,onBlur:t[2]||(t[2]=(...h)=>e.onDegreeBlur&&e.onDegreeBlur(...h))},null,40,Fh),Xa("deg ")]),_("div",Wh,[_("div",zh,[Le(l,{angle:e.state.angle,"onUpdate:angle":t[3]||(t[3]=h=>e.state.angle=h),size:40,onChange:e.onDegreeChange},null,8,["angle","onChange"])])])],512),[[hn,e.state.type==="linear"]])])]),_("div",Uh,[_("div",Kh,[_("div",Gh,[_("div",{class:"vc-background",style:ee(e.gradientBg)},null,4),_("div",qh,[_("div",{class:oe(["vc-gradient__stop",{"vc-gradient__stop--current":e.state.startActive}]),ref:"startGradientRef",title:(s=e.lang)==null?void 0:s.start,style:ee({left:e.getStartColorLeft+"px",backgroundColor:e.state.startColorRgba})},Jh,14,Yh),_("div",{class:oe(["vc-gradient__stop",{"vc-gradient__stop--current":!e.state.startActive}]),ref:"stopGradientRef",title:(i=e.lang)==null?void 0:i.end,style:ee({left:e.getEndColorLeft+"px",backgroundColor:e.state.endColorRgba})},eg,14,Zh)])])],512)]),e.advancePanelShow?(E(),J(c,{key:0,color:e.currentColor,onChange:e.onBoardChange},null,8,["color","onChange"])):V("",!0),e.advancePanelShow?(E(),J(u,{key:1,color:e.currentColor,onChange:e.onHueChange},null,8,["color","onChange"])):V("",!0),e.advancePanelShow?V("",!0):(E(),J(f,{key:2,onChange:e.onCompactChange},null,8,["onChange"])),e.advancePanelShow?V("",!0):(E(),J(d,{key:3,color:e.currentColor,onChange:e.onLightChange},null,8,["color","onChange"])),e.disableAlpha?V("",!0):(E(),J(p,{key:4,color:e.currentColor,onChange:e.onAlphaChange},null,8,["color","onChange"])),Le(g,{color:e.currentColor,"disable-alpha":e.disableAlpha,onChange:e.onDisplayChange},null,8,["color","disable-alpha","onChange"]),e.disableHistory?V("",!0):(E(),J(v,{key:5,round:e.roundHistory,colors:e.historyColors,onChange:e.onCompactChange},null,8,["round","colors","onChange"]))])}const Ga=be(Mh,[["render",tg],["__scopeId","data-v-c4d6d6ea"]]),rg=de({name:"WrapContainer",props:{theme:T.oneOf(["white","black"]).def("white"),showTab:T.bool.def(!1),activeKey:T.oneOf(["pure","gradient"]).def("pure")},emits:["update:activeKey","change"],setup(e,{emit:t}){const r=Ce({activeKey:e.activeKey}),n=Ya(ho),o=a=>{r.activeKey=a,t("update:activeKey",a),t("change",a)};return re(()=>e.activeKey,a=>{r.activeKey=a}),{state:r,onActiveKeyChange:o,lang:n==null?void 0:n.lang}}}),ng={class:"vc-colorpicker--container"},og={key:0,class:"vc-colorpicker--tabs"},ag={class:"vc-colorpicker--tabs__inner"},ig={class:"vc-btn__content"},lg={class:"vc-btn__content"};function sg(e,t,r,n,o,a){var s,i;return E(),D("div",{class:oe(["vc-colorpicker",e.theme])},[_("div",ng,[e.showTab?(E(),D("div",og,[_("div",ag,[_("div",{class:oe(["vc-colorpicker--tabs__btn",{"vc-btn-active":e.state.activeKey==="pure"}]),onClick:t[0]||(t[0]=l=>e.onActiveKeyChange("pure"))},[_("button",null,[_("div",ig,hr((s=e.lang)==null?void 0:s.pure),1)])],2),_("div",{class:oe(["vc-colorpicker--tabs__btn",{"vc-btn-active":e.state.activeKey==="gradient"}]),onClick:t[1]||(t[1]=l=>e.onActiveKeyChange("gradient"))},[_("button",null,[_("div",lg,hr((i=e.lang)==null?void 0:i.gradient),1)])],2),_("div",{class:"vc-colorpicker--tabs__bg",style:ee({width:"50%",left:`calc(${e.state.activeKey==="gradient"?50:0}%)`})},null,4)])])):V("",!0),gn(e.$slots,"default",{},void 0,!0)])],2)}const ug=be(rg,[["render",sg],["__scopeId","data-v-0492277d"]]),cg={start:"Start",end:"End",pure:"Pure",gradient:"Gradient",linear:"linear",radial:"radial"},fg={start:"开始",end:"结束",pure:"纯色",gradient:"渐变",linear:"线性",radial:"径向"},dg={En:cg,"ZH-cn":fg},pg={isWidget:T.bool.def(!1),pickerType:T.oneOf(["fk","chrome"]).def("fk"),shape:T.oneOf(["circle","square"]).def("square"),pureColor:{type:[String,Object],default:"#000000"},gradientColor:T.string.def("linear-gradient(90deg, rgba(255, 255, 255, 1) 0%, rgba(0, 0, 0, 1) 100%)"),format:{type:String,default:"rgb"},disableAlpha:T.bool.def(!1),disableHistory:T.bool.def(!1),roundHistory:T.bool.def(!1),useType:T.oneOf(["pure","gradient","both"]).def("pure"),activeKey:T.oneOf(["pure","gradient"]).def("pure"),lang:{type:String,default:"ZH-cn"},zIndex:T.number.def(9999),pickerContainer:{type:[String,HTMLElement],default:"body"},debounce:T.number.def(100),theme:T.oneOf(["white","black"]).def("white"),blurClose:T.bool.def(!1),defaultPopup:T.bool.def(!1)},vg=de({name:"ColorPicker",components:{FkColorPicker:Ua,ChromeColorPicker:Ka,GradientColorPicker:Ga,WrapContainer:ug},inheritAttrs:!1,props:pg,emits:["update:pureColor","pureColorChange","update:gradientColor","gradientColorChange","update:activeKey","activeKeyChange"],setup(e,{emit:t}){xl(ho,{lang:q(()=>dg[e.lang||"ZH-cn"])});const r=!!Ol().extra,n=Ce({pureColor:e.pureColor||"",activeKey:e.useType==="gradient"?"gradient":e.activeKey,isAdvanceMode:!1}),o=new z("#000"),a=new z("#000"),s=new z(n.pureColor),i=Ce({startColor:o,endColor:a,startColorStop:0,endColorStop:100,angle:0,type:"linear",gradientColor:e.gradientColor}),l=H(s),c=H(e.defaultPopup),u=H(null),f=H(null);let d=null;const p=q(()=>({background:n.activeKey!=="gradient"?W(n.pureColor).toRgbString():i.gradientColor})),g=q(()=>n.activeKey==="gradient"?Ga.name:e.pickerType==="fk"?Ua.name:Ka.name),v=m=>{n.isAdvanceMode=m},h=q(()=>{const m={disableAlpha:e.disableAlpha,disableHistory:e.disableHistory,roundHistory:e.roundHistory,pickerType:e.pickerType};return n.activeKey==="gradient"?{...m,startColor:i.startColor,endColor:i.endColor,angle:i.angle,type:i.type,startColorStop:i.startColorStop,endColorStop:i.endColorStop,onStartColorChange:A=>{i.startColor=A,O()},onEndColorChange:A=>{i.endColor=A,O()},onStartColorStopChange:A=>{i.startColorStop=A,O()},onEndColorStopChange:A=>{i.endColorStop=A,O()},onAngleChange:A=>{i.angle=A,O()},onTypeChange:A=>{i.type=A,O()},onAdvanceChange:v}:{...m,disableAlpha:e.disableAlpha,disableHistory:e.disableHistory,roundHistory:e.roundHistory,color:l.value,onChange:I,onAdvanceChange:v}}),y=()=>{c.value=!0,d?d.update():k()},b=()=>{c.value=!1},C=Ae(()=>{!e.isWidget&&e.blurClose&&b()},100);Dl(f,()=>{b()});const w=()=>{var m,A,P,R;try{const[L]=Gl(i.gradientColor);if(L&&L.type.includes("gradient")&&L.colorStops.length>=2){const X=L.colorStops[0],S=L.colorStops[1];i.startColorStop=Number((m=X.length)==null?void 0:m.value)||0,i.endColorStop=Number((A=S.length)==null?void 0:A.value)||0,L.type==="linear-gradient"&&((P=L.orientation)==null?void 0:P.type)==="angular"&&(i.angle=Number((R=L.orientation)==null?void 0:R.value)||0),i.type=L.type.split("-")[0];const[M,B,ie,Re]=X.value,[Ve,at,Ie,we]=S.value;i.startColor=new z({r:Number(M),g:Number(B),b:Number(ie),a:Number(Re)}),i.endColor=new z({r:Number(Ve),g:Number(at),b:Number(Ie),a:Number(we)})}}catch(L){console.log(`[Parse Color]: ${L}`)}},O=Ae(()=>{const m=x();try{i.gradientColor=ql(m),t("update:gradientColor",i.gradientColor),t("gradientColorChange",i.gradientColor)}catch(A){console.log(A)}},e.debounce),x=()=>{const m=[],A=i.startColor.RGB.map(L=>L.toString()),P=i.endColor.RGB.map(L=>L.toString()),R=[{type:"rgba",value:[A[0],A[1],A[2],A[3]],length:{value:i.startColorStop+"",type:"%"}},{type:"rgba",value:[P[0],P[1],P[2],P[3]],length:{value:i.endColorStop+"",type:"%"}}];return i.type==="linear"?m.push({type:"linear-gradient",orientation:{type:"angular",value:i.angle+""},colorStops:R}):i.type==="radial"&&m.push({type:"radial-gradient",orientation:[{type:"shape",value:"circle"}],colorStops:R}),m},k=()=>{u.value&&f.value&&(d=eu(u.value,f.value,{placement:"auto",modifiers:[{name:"offset",options:{offset:[0,8]}},{name:"flip",options:{allowedAutoPlacements:["top","bottom","left","right"],rootBoundary:"viewport"}}]}))},I=m=>{l.value=m,n.pureColor=m.toString(e.format),j()},j=Ae(()=>{t("update:pureColor",n.pureColor),t("pureColorChange",n.pureColor)},e.debounce),$=m=>{n.activeKey=m,t("update:activeKey",m),t("activeKeyChange",m)};return Qe(()=>{w(),d||k()}),re(()=>e.gradientColor,m=>{m!=i.gradientColor&&(i.gradientColor=m)}),re(()=>i.gradientColor,()=>{w()}),re(()=>e.activeKey,m=>{n.activeKey=m}),re(()=>e.useType,m=>{n.activeKey!=="gradient"&&m==="gradient"?n.activeKey="gradient":n.activeKey="pure"}),re(()=>e.pureColor,m=>{W.equals(m,n.pureColor)||(n.pureColor=m,l.value=new z(m))},{deep:!0}),{colorCubeRef:u,pickerRef:f,showPicker:c,colorInstance:l,getBgColorStyle:p,getComponentName:g,getBindArgs:h,state:n,hasExtra:r,onColorChange:I,onShowPicker:y,onActiveKeyChange:$,onAutoClose:C}}}),hg={key:0,class:"vc-color-extra"},gg={key:0,class:"vc-color-extra"};function yg(e,t,r,n,o,a){const s=G("WrapContainer");return E(),D(Ge,null,[e.isWidget?(E(),J(s,{key:0,"active-key":e.state.activeKey,"onUpdate:activeKey":t[0]||(t[0]=i=>e.state.activeKey=i),"show-tab":e.useType==="both",style:ee({zIndex:e.zIndex}),theme:e.theme,onChange:e.onActiveKeyChange},{default:_o(()=>[(E(),J(Ao(e.getComponentName),$o({key:e.getComponentName},e.getBindArgs),null,16)),e.hasExtra?(E(),D("div",hg,[gn(e.$slots,"extra",{},void 0,!0)])):V("",!0)]),_:3},8,["active-key","show-tab","style","theme","onChange"])):V("",!0),e.isWidget?V("",!0):(E(),D(Ge,{key:1},[_("div",{class:oe(["vc-color-wrap transparent",{round:e.shape==="circle"}]),ref:"colorCubeRef"},[_("div",{class:"current-color",style:ee(e.getBgColorStyle),onClick:t[1]||(t[1]=(...i)=>e.onShowPicker&&e.onShowPicker(...i))},null,4)],2),(E(),J(Al,{to:e.pickerContainer},[vr(_("div",{ref:"pickerRef",style:ee({zIndex:e.zIndex}),onMouseleave:t[3]||(t[3]=(...i)=>e.onAutoClose&&e.onAutoClose(...i))},[e.showPicker?(E(),J(s,{key:0,"show-tab":e.useType==="both"&&!e.state.isAdvanceMode,theme:e.theme,"active-key":e.state.activeKey,"onUpdate:activeKey":t[2]||(t[2]=i=>e.state.activeKey=i),onChange:e.onActiveKeyChange},{default:_o(()=>[(E(),J(Ao(e.getComponentName),$o({key:e.getComponentName},e.getBindArgs),null,16)),e.hasExtra?(E(),D("div",gg,[gn(e.$slots,"extra",{},void 0,!0)])):V("",!0)]),_:3},8,["show-tab","theme","active-key","onChange"])):V("",!0)],36),[[hn,e.showPicker]])],8,["to"]))],64))],64)}const wg=be(vg,[["render",yg],["__scopeId","data-v-354ca836"]]);export{wg as r};
