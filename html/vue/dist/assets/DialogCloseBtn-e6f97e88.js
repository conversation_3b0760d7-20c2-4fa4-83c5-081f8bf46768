import{r as t,o as a,b as c,w as s,q as r,ag as i}from"./index-9a5dc664.js";const u={__name:"DialogCloseBtn",props:{icon:{type:String,required:!1,default:"tabler-x"},iconSize:{type:String,required:!1,default:"22"}},setup(o){const e=o;return(l,p)=>{const n=t("IconBtn");return a(),c(n,{variant:"elevated",class:"v-dialog-close-btn"},{default:s(()=>[r(i,{icon:e.icon,size:e.iconSize},null,8,["icon","size"])]),_:1})}}};export{u as _};
