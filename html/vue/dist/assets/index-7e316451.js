import{_ as Rt}from"./AppTextField-8c148b8f.js";import{_ as It}from"./DialogCloseBtn-e6f97e88.js";import{p as Bt,V as Lt,_ as Mt}from"./VPagination-964310e8.js";import{d as xe,l as g,B as b,h as Pt,b1 as Ve,b2 as at,E as Se,D as Ue,G as Ot,a3 as Ut,o as q,c as te,r as Me,n as _,t as I,aC as Pe,aD as Ze,b as ye,A as ve,a8 as Ne,b3 as et,e as Tt,F as Dt,aj as le,y as R,aY as _t,a as At,aG as Ht,p as _e,q as N,b4 as Wt,b5 as jt,w,b6 as zt,b7 as qt,ad as Gt,a9 as Yt,am as Ee,as as Re,av as be,s as x,ah as oe,z as St,ag as xt,ao as Xt,ak as Ie,b0 as Jt,al as Qt,ap as Ce}from"./index-9a5dc664.js";import{V as Zt}from"./VDataTableServer-26da2b3a.js";import{V as Qe}from"./VDialog-0870f7b8.js";import"./VTextField-3e2d458d.js";import"./VDataTable-d690b508.js";import"./VSelectionControl-182ca2ca.js";import"./VChip-a30ee730.js";var ea=Object.defineProperty,ta=(e,a,l)=>a in e?ea(e,a,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[a]=l,z=(e,a,l)=>(ta(e,typeof a!="symbol"?a+"":a,l),l);const aa=["_level","_filterVisible","_parent","_loading","_loaded","_remote","_keyField","children","setChildren"];class me{constructor(a,l=null,t="id",n=!1){z(this,"_level",0),z(this,"checked",!1),z(this,"selected",!1),z(this,"indeterminate",!1),z(this,"disabled",!1),z(this,"expand",!1),z(this,"visible",!0),z(this,"_filterVisible",!0),z(this,"_parent",null),z(this,"children",[]),z(this,"isLeaf",!1),z(this,"_loading",!1),z(this,"_loaded",!1),this._keyField=t,this._remote=n;for(let i in a)aa.indexOf(i)===-1&&(this[i]=a[i]);this[t]==null&&(this[t]=Math.random().toString(36).substring(2)),this._parent=l,this._parent&&(this._level=this._parent._level+1),this.visible=this._parent===null||this._parent.expand&&this._parent.visible,Array.isArray(a.children)&&this.setChildren(a.children),this.children.length&&(this._loaded=!0),this._remote||(this.isLeaf=!this.children.length)}setChildren(a){this.children=a.map(l=>new me(Object.assign({},l),this,this._keyField,this._remote))}}var Oe=(e=>(e.none="none",e.parents="parents",e.children="children",e))(Oe||{});const la=["setData","setChecked","setCheckedKeys","checkAll","clearChecked","setSelected","clearSelected","setExpand","setExpandKeys","setExpandAll","getCheckedNodes","getCheckedKeys","getIndeterminateNodes","getSelectedNode","getSelectedKey","getExpandNodes","getExpandKeys","getCurrentVisibleNodes","getNode","getTreeData","getFlatData","getNodesCount","insertBefore","insertAfter","append","prepend","remove","filter","showCheckedNodes","loadRootNodes","scrollTo"];var Ft=(e=>(e["bottom-start"]="bottom-start",e["bottom-end"]="bottom-end",e.bottom="bottom",e["top-start"]="top-start",e["top-end"]="top-end",e.top="top",e))(Ft||{}),Le=(e=>(e.top="top",e.center="center",e.bottom="bottom",e))(Le||{}),ce=(e=>(e.before="before",e.body="body",e.after="after",e))(ce||{});const tt=["expand","check","click","select","node-dblclick","node-right-click","node-dragstart","node-dragenter","node-dragover","node-dragleave","node-drop"];class na{constructor(a){z(this,"data",[]),z(this,"flatData",[]),z(this,"mapData",Object.create(null)),z(this,"unloadCheckedKeys",[]),z(this,"unloadSelectedKey",null),z(this,"currentSelectedKey",null),z(this,"listenersMap",{}),this.options=a}setData(a,l=null,t=null){this.data=a.map(n=>new me(n,null,this.options.keyField,!!this.options.load));for(let n in this.mapData)delete this.mapData[n];this.currentSelectedKey=null,this.flatData=this.flattenData(this.data),this.setUnloadCheckedKeys(t||[]),l&&(this.currentSelectedKey=null,this.setUnloadSelectedKey(l)),this.emit("visible-data-change"),this.emit("set-data")}setChecked(a,l,t=!0,n=!0,i=!1){const d=this.mapData[a];if(!d)return this.setUnloadChecked(a,l,t,n);d.disabled||d.checked&&l||!d.checked&&!d.indeterminate&&!l||(this.options.cascade?(this.checkNodeDownward(d,l,i),this.checkNodeUpward(d)):d.checked=l,t&&(d.checked?this.emit("check",d):this.emit("uncheck",d)),this.triggerCheckedChange(t,n))}setUnloadChecked(a,l,t=!0,n=!0){const i=this.findIndex(a,this.unloadCheckedKeys);l?i===-1&&this.unloadCheckedKeys.push(a):i!==-1&&this.unloadCheckedKeys.splice(i,1),this.triggerCheckedChange(t,n)}setCheckedKeys(a,l,t=!0,n=!0){a.forEach(i=>{this.setChecked(i,l,!1,!1)}),this.triggerCheckedChange(t,n)}checkAll(a=!0,l=!0){if(this.options.cascade){const t=n=>{n.forEach(i=>{i.disabled?t(i.children):this.setChecked(i[this.options.keyField],!0,!1,!1)})};t(this.data)}else{const t=this.flatData.length;for(let n=0;n<t;n++){const i=this.flatData[n];this.setChecked(i[this.options.keyField],!0,!1,!1)}}this.triggerCheckedChange(a,l)}clearChecked(a=!0,l=!0){this.getCheckedNodes().forEach(t=>{this.setChecked(t[this.options.keyField],!1,!1,!1)}),this.unloadCheckedKeys=[],this.triggerCheckedChange(a,l)}triggerCheckedChange(a=!0,l=!0){a&&this.emit("checked-change",this.getCheckedNodes(),this.getCheckedKeys()),l&&this.emit("render-data-change")}setSelected(a,l,t=!0,n=!0){const i=this.mapData[a];if(!i)return this.setUnloadSelected(a,l,t,n);i.disabled||i.selected!==l&&(a===this.currentSelectedKey?l||(i.selected=l,this.currentSelectedKey=null):l&&(this.currentSelectedKey===null?(i.selected=l,this.currentSelectedKey=i[this.options.keyField]):(this.mapData[this.currentSelectedKey]&&(this.mapData[this.currentSelectedKey].selected=!1),i.selected=l,this.currentSelectedKey=i[this.options.keyField])),t&&(i.selected?this.emit("select",i):this.emit("unselect",i),this.emit("selected-change",this.getSelectedNode(),this.getSelectedKey())),n&&this.emit("render-data-change"))}setUnloadSelected(a,l,t=!0,n=!0){l?(this.currentSelectedKey&&this.setSelected(this.currentSelectedKey,!1,!1,!1),this.unloadSelectedKey=a):this.unloadSelectedKey===a&&(this.unloadSelectedKey=null),t&&this.emit("selected-change",this.getSelectedNode(),this.getSelectedKey()),n&&this.emit("render-data-change")}clearSelected(a=!0,l=!0){this.currentSelectedKey&&this.mapData[this.currentSelectedKey]?this.setSelected(this.currentSelectedKey,!1,a,l):this.unloadSelectedKey!==null&&(this.unloadSelectedKey=null,a&&this.emit("selected-change",this.getSelectedNode(),this.getSelectedKey()),l&&this.emit("render-data-change"))}setExpand(a,l,t=!1,n=!0,i=!0){const d=this.mapData[a];if(!(!d||!t&&d.isLeaf)&&d.expand!==l){if(!d.isLeaf){if(typeof this.options.load=="function"){if(!d._loaded&&!d._loading&&l){d._loading=!0,i&&this.emit("visible-data-change"),new Promise((h,y)=>{const O=this.options.load;O(d,h,y)}).then(h=>{if(Array.isArray(h)){const y=this.findIndex(d);if(y===-1)return;d._loaded=!0,d.expand=l,d.setChildren(h);const O=this.getCheckedKeys(),K=this.flattenData(d.children,this.getSelectedKey===null);this.flatData.splice(y+1,0,...K),this.setUnloadCheckedKeys(O),this.unloadSelectedKey!==null&&this.setUnloadSelectedKey(this.unloadSelectedKey),this.emit("set-data")}}).catch(h=>{}).then(()=>{d._loading=!1,n&&this.emit("expand",d),i&&this.emit("visible-data-change")});return}else if(d._loading)return}d.expand=l;const o=[...d.children];for(;o.length;)o[0].expand&&o[0].children.length&&o.push(...o[0].children),o[0]._filterVisible===!1?o[0].visible=!1:o[0].visible=o[0]._parent===null||o[0]._parent.expand&&o[0]._parent.visible,o.shift();n&&this.emit("expand",d),i&&this.emit("visible-data-change")}t&&d._parent&&l&&this.setExpand(d._parent[this.options.keyField],l,t,!1,i)}}setExpandKeys(a,l,t=!0){a.forEach(n=>{this.setExpand(n,l,!1,!1,!1)}),t&&this.emit("visible-data-change")}setExpandAll(a,l=!0){this.flatData.forEach(t=>{(!this.options.load||t._loaded)&&this.setExpand(t[this.options.keyField],a,!1,!1,!1)}),l&&this.emit("visible-data-change")}getCheckedNodes(a=this.options.ignoreMode){if(a===Oe.children){const l=[],t=n=>{n.forEach(i=>{i.checked?l.push(i):!i.isLeaf&&i.indeterminate&&t(i.children)})};return t(this.data),l}else return this.flatData.filter(l=>a===Oe.parents?l.checked&&l.isLeaf:l.checked)}getCheckedKeys(a=this.options.ignoreMode){return this.getCheckedNodes(a).map(l=>l[this.options.keyField]).concat(this.unloadCheckedKeys)}getIndeterminateNodes(){return this.flatData.filter(a=>a.indeterminate)}getSelectedNode(){return this.currentSelectedKey===null?null:this.mapData[this.currentSelectedKey]||null}getSelectedKey(){return this.currentSelectedKey||this.unloadSelectedKey||null}getUnloadCheckedKeys(){return this.unloadCheckedKeys}getExpandNodes(){return this.flatData.filter(a=>!a.isLeaf&&a.expand)}getExpandKeys(){return this.getExpandNodes().map(a=>a[this.options.keyField])}getNode(a){return this.mapData[a]||null}insertBefore(a,l){const t=this.getInsertedNode(a,l);if(!t)return null;this.remove(t[this.options.keyField],!1);const n=this.mapData[l]._parent,i=this.findIndex(l,n&&n.children),d=this.findIndex(l),o=n&&-1||this.findIndex(l,this.data);return this.insertIntoStore(t,n,i,d,o),this.emit("visible-data-change"),t}insertAfter(a,l){const t=this.getInsertedNode(a,l);if(!t)return null;this.remove(t[this.options.keyField],!1);const n=this.mapData[l],i=n._parent,d=this.findIndex(l,i&&i.children)+1,o=this.flatData.length,h=this.findIndex(l);let y=h+1;for(let K=h+1;K<=o;K++){if(K===o){y=K;break}if(this.flatData[K]._level<=n._level){y=K;break}}const O=i&&-1||this.findIndex(l,this.data)+1;return this.insertIntoStore(t,i,d,y,O),this.emit("visible-data-change"),t}append(a,l){const t=this.mapData[l];if(!t.isLeaf){const d=t.children.length;return this.insertAfter(a,t.children[d-1][this.options.keyField])}const n=this.getInsertedNode(a,l,!0);if(!n)return null;this.remove(n[this.options.keyField],!1);const i=this.findIndex(l)+1;return this.insertIntoStore(n,t,0,i),this.emit("visible-data-change"),n}prepend(a,l){const t=this.mapData[l];if(!t.isLeaf)return this.insertBefore(a,t.children[0][this.options.keyField]);const n=this.getInsertedNode(a,l,!0);if(!n)return null;this.remove(n[this.options.keyField],!1);const i=this.findIndex(l)+1;return this.insertIntoStore(n,t,0,i),this.emit("visible-data-change"),n}remove(a,l=!0){const t=this.mapData[a];if(!t)return null;const n=this.findIndex(t);if(n===-1)return null;let i=1;const d=this.flatData.length;for(let h=n+1;h<d&&this.flatData[h]._level>t._level;h++)i++;this.flatData.splice(n,i);const o=h=>{const y=this.mapData[h];delete this.mapData[h],y.children.forEach(O=>o(O[this.options.keyField]))};if(o(a),!t._parent){const h=this.findIndex(t,this.data);h>-1&&this.data.splice(h,1)}if(t._parent){const h=this.findIndex(t,t._parent.children);h!==-1&&t._parent.children.splice(h,1),t._parent.isLeaf=!t._parent.children.length,t._parent.isLeaf&&(t._parent.expand=!1,t._parent.indeterminate=!1),this.updateMovingNodeStatus(t)}return l&&this.emit("visible-data-change"),t}getInsertedNode(a,l,t=!1){const n=this.mapData[l];if(!n)return null;const i=t?n:n._parent;return a instanceof me?a[this.options.keyField]===l?null:a:typeof a=="object"?a[this.options.keyField]===l?null:this.mapData[a[this.options.keyField]]||new me(a,i,this.options.keyField,!!this.options.load):!this.mapData[a]||a===l?null:this.mapData[a]}insertIntoStore(a,l,t,n,i){if(n===-1)return;t!==-1&&l&&l.children.splice(t,0,a),a._parent=l,l?(l.isLeaf=!1,this.setExpand(l[this.options.keyField],!0,!1,!1,!1)):typeof i=="number"&&i>-1&&this.data.splice(i,0,a);const d=this.flattenData([a]);a._level=l?l._level+1:0,d.forEach(o=>o._level=o._parent?o._parent._level+1:0),this.flatData.splice(n,0,...d),this.updateMovingNodeStatus(a)}updateMovingNodeStatus(a){this.checkNodeUpward(a),this.triggerCheckedChange(),a.selected&&this.setSelected(a[this.options.keyField],!0)}filter(a,l){const t=[];this.flatData.forEach(n=>{n._filterVisible=n._parent&&n._parent._filterVisible||l(a,n),n.visible=n._filterVisible,n._filterVisible&&t.push(n)}),t.forEach(n=>{const i=[];let d=n._parent;for(;d;)i.unshift(d),d=d._parent;i.forEach(o=>{o._filterVisible=!0,o.visible=(o._parent===null||o._parent.expand&&o._parent.visible)&&o._filterVisible,this.options.expandOnFilter&&this.setExpand(o[this.options.keyField],!0,!1,!1,!1)}),n.visible=n._parent===null||n._parent.expand&&n._parent.visible}),this.emit("visible-data-change")}setUnloadCheckedKeys(a){this.unloadCheckedKeys=a;const l=a.concat(),t=this.unloadCheckedKeys.length;for(let i=t-1;i>=0;i--){const d=this.unloadCheckedKeys[i];this.mapData[d]&&(this.setChecked(d,!0,!1,!1),this.unloadCheckedKeys.splice(i,1))}const n=this.getCheckedKeys();n.length===l.length&&n.every(i=>l.some(d=>d===i))||this.emit("checked-change",this.getCheckedNodes(),n)}setUnloadSelectedKey(a){const l=this.getSelectedKey();this.mapData[a]?(this.setSelected(a,!0,!1),this.unloadSelectedKey=null):(this.currentSelectedKey&&this.setSelected(this.currentSelectedKey,!1,!1),this.unloadSelectedKey=a);const t=this.getSelectedKey();t!==l&&this.emit("selected-change",this.getSelectedNode(),t)}flattenData(a,l=!0,t=[]){const n=a.length;for(let i=0;i<n;i++){const d=a[i],o=d[this.options.keyField];if(t.push(d),this.mapData[o])throw new Error("[CTree] Duplicate tree node key.");this.mapData[o]=d,d.checked&&this.options.cascade&&(this.checkNodeDownward(d,!0),this.checkNodeUpward(d)),d.selected&&l&&(this.clearSelected(!1,!1),this.currentSelectedKey=d[this.options.keyField],this.emit("selected-change",d,this.currentSelectedKey)),(this.options.defaultExpandAll||d.expand)&&!this.options.load&&!d.isLeaf&&(d.expand=!1,this.setExpand(d[this.options.keyField],!0,!1,!1,!1)),d.children.length&&this.flattenData(d.children,l,t)}return t}checkNodeDownward(a,l,t=!1){if(a.children.forEach(n=>{this.checkNodeDownward(n,l,t)}),a.isLeaf||this.options.load&&!a.children.length){if(!a.disabled){if(t&&!this.options.filteredNodeCheckable&&!a._filterVisible)return;a.checked=l,a.indeterminate=!1}}else this.checkParentNode(a)}checkNodeUpward(a){let l=a._parent;for(;l;)this.checkParentNode(l),l=l._parent}checkParentNode(a){const l=a.children.length;if(!l)return;let t=!1,n=!1,i=!1;for(let d=0;d<l;d++){const o=a.children[d];if(o.checked?t=!0:n=!0,t&&n||o.indeterminate){i=!0,a.checked=!1,a.indeterminate=!0;break}}i||(a.checked=t,a.indeterminate=!1)}findIndex(a,l=this.flatData){if(l!==null){let t=a instanceof me?a[this.options.keyField]:a;const n=l.length;for(let i=0;i<n;i++)if(l[0]instanceof me){if(t===l[i][this.options.keyField])return i}else if(t===l[i])return i}return-1}on(a,l){this.listenersMap[a]||(this.listenersMap[a]=[]);let t=[];Array.isArray(l)?t=l:t=[l],t.forEach(n=>{this.listenersMap[a].indexOf(n)===-1&&this.listenersMap[a].push(n)})}off(a,l){if(this.listenersMap[a])if(!l)this.listenersMap[a]=[];else{const t=this.listenersMap[a].indexOf(l);t>-1&&this.listenersMap[a].splice(t,1)}}emit(a,...l){if(!this.listenersMap[a])return;const t=this.listenersMap[a].length;for(let n=0;n<t;n++)this.listenersMap[a][n](...l)}disposeListeners(){for(const a in this.listenersMap)this.listenersMap[a]=[]}}const ia=xe({name:"CLoadingIcon"}),Te=(e,a)=>{const l=e.__vccOpts||e;for(const[t,n]of a)l[t]=n;return l},sa={viewBox:"0 0 50 50"},da=_("circle",{class:"ctree-loading-icon__circle",cx:"25",cy:"25",r:"20",fill:"none","stroke-width":"5","stroke-miterlimit":"10"},null,-1),oa=[da];function ra(e,a,l,t,n,i){return q(),te("svg",sa,oa)}const $t=Te(ia,[["render",ra]]),P="ctree-tree-node",ca=xe({name:"CTreeNode",components:{LoadingIcon:$t},props:{data:Object,titleField:{type:String,default:""},keyField:String,render:Function,checkable:Boolean,selectable:Boolean,unselectOnClick:Boolean,disableAll:Boolean,draggable:Boolean,droppable:Boolean,getNode:Function},emits:[...tt],setup(e,{emit:a}){var l;const t=g(!1),n=g(!1),i=g(!1),d=e.keyField,o=e.getNode,h=b(()=>{var u,v,A,$,M;return[`${P}__wrapper`,{[`${P}__wrapper_is-leaf`]:(u=e.data)==null?void 0:u.isLeaf,[`${P}_disabled`]:e.disableAll||((v=e.data)==null?void 0:v.disabled)},{[`${P}_checked`]:e.checkable&&((A=e.data)==null?void 0:A.checked),[`${P}_indeterminate`]:e.checkable&&(($=e.data)==null?void 0:$.indeterminate)},{[`${P}_selected`]:(M=e.data)==null?void 0:M.selected}]}),y=b(()=>[`${P}__node-body`,{[`${P}__drop_active`]:t.value}]),O=b(()=>[`${P}__drop`,{[`${P}__drop_active`]:n.value}]),K=b(()=>[`${P}__drop`,{[`${P}__drop_active`]:i.value}]),G=b(()=>[`${P}__square`,`${P}__checkbox_wrapper`]),ne=b(()=>{var u;return[`${P}__square`,`${P}__expand`,{[`${P}__expand_active`]:(u=e.data)==null?void 0:u.expand}]}),ee=b(()=>[`${P}__loading-icon`]),E=b(()=>{var u,v,A;return[`${P}__checkbox`,{[`${P}__checkbox_checked`]:(u=e.data)==null?void 0:u.checked,[`${P}__checkbox_indeterminate`]:(v=e.data)==null?void 0:v.indeterminate,[`${P}__checkbox_disabled`]:e.disableAll||((A=e.data)==null?void 0:A.disabled)}]}),B=b(()=>{var u,v;return[`${P}__title`,{[`${P}__title_selected`]:(u=e.data)==null?void 0:u.selected,[`${P}__title_disabled`]:e.disableAll||((v=e.data)==null?void 0:v.disabled)}]}),C=b(()=>o(e.data?e.data[d]:"")||e.data||{}),F=b(()=>e.checkable),Y=((l=e.data)==null?void 0:l.render)||e.render||null,se=b(()=>xe({name:"Render",functional:!0,render(){return typeof Y!="function"?Pt("div"):Y(C.value)}})),ue=b(()=>{var u;let v={};return e.draggable&&!e.disableAll&&!((u=e.data)!=null&&u.disabled)&&(v={dragstart:T}),v}),ie=b(()=>{let u={};return e.droppable&&(u={dragenter:c.bind(Ve()),dragover:X.bind(Ve()),dragleave:V.bind(Ve()),drop:U.bind(Ve())}),u});function L(){var u;(u=e.data)!=null&&u.isLeaf||a("expand",C.value)}function W(){var u;e.disableAll||(u=e.data)!=null&&u.disabled||!e.checkable||a("check",C.value)}function J(u){var v,A;if(a("click",C.value,u),e.selectable){if(e.disableAll||(v=e.data)!=null&&v.disabled||(A=e.data)!=null&&A.selected&&!e.unselectOnClick)return;a("select",C.value)}else e.checkable?W():L()}function he(u){a("node-dblclick",C.value,u)}function pe(u){a("node-right-click",C.value,u)}const Q=g();function f(u){const v=Q.value.getBoundingClientRect(),A=v.height,$=u.clientY-v.top;return $<=A*.3?ce.before:$<=A*(.3+.4)?ce.body:ce.after}function S(u,v=!1){n.value=!1,t.value=!1,i.value=!1,v||(u===ce.before?n.value=!0:u===ce.body?t.value=!0:u===ce.after&&(i.value=!0))}function T(u){var v;u.dataTransfer&&u.dataTransfer.setData("node",JSON.stringify(e.data)),(v=e.data)!=null&&v.expand&&L(),a("node-dragstart",C.value,u)}function c(u){u.preventDefault();const v=f(u);S(v),a("node-dragenter",C.value,u,v)}function X(u){u.preventDefault();const v=f(u);S(v),a("node-dragover",C.value,u,v)}function V(u){const v=f(u);S(v,!0),a("node-dragleave",C.value,u,v)}function U(u){const v=f(u);S(v,!0),a("node-drop",C.value,u,v)}return{dragoverBody:t,dragoverBefore:n,dragoverAfter:i,wrapperCls:h,nodeBodyCls:y,dropBeforeCls:O,dropAfterCls:K,checkboxWrapperCls:G,expandCls:ne,loadingIconCls:ee,checkboxCls:E,titleCls:B,fullData:C,showCheckbox:F,renderFunction:Y,renderComponent:se,dragListeners:ue,dropListeners:ie,handleExpand:L,handleCheck:W,handleSelect:J,handleDblclick:he,handleRightClick:pe,nodeBody:Q}}}),ua=["draggable"];function ha(e,a,l,t,n,i){var d,o,h,y;const O=Me("LoadingIcon");return q(),te("div",{class:I(e.wrapperCls)},[_("div",{class:I(e.dropBeforeCls)},null,2),_("div",Ne({ref:"nodeBody",class:e.nodeBodyCls},et(e.dropListeners,!0)),[_("div",{class:I(e.expandCls)},[Pe(_("i",{onClick:a[0]||(a[0]=(...K)=>e.handleExpand&&e.handleExpand(...K))},null,512),[[Ze,!((d=e.data)!=null&&d.isLeaf)&&!((o=e.data)!=null&&o._loading)]]),(h=e.data)!=null&&h._loading?(q(),ye(O,{key:0,class:I(e.loadingIconCls)},null,8,["class"])):ve("",!0)],2),e.showCheckbox?(q(),te("div",{key:0,class:I(e.checkboxWrapperCls)},[_("div",{class:I(e.checkboxCls),onClick:a[1]||(a[1]=(...K)=>e.handleCheck&&e.handleCheck(...K))},null,2)],2)):ve("",!0),_("div",Ne({class:e.titleCls,onClick:a[2]||(a[2]=(...K)=>e.handleSelect&&e.handleSelect(...K)),onDblclick:a[3]||(a[3]=(...K)=>e.handleDblclick&&e.handleDblclick(...K)),onContextmenu:a[4]||(a[4]=(...K)=>e.handleRightClick&&e.handleRightClick(...K))},et(e.dragListeners,!0),{draggable:e.draggable&&!e.disableAll&&!((y=e.data)!=null&&y.disabled)}),[e.renderFunction?(q(),ye(Tt(e.renderComponent),{key:0})):(q(),te(Dt,{key:1},[le(R(e.data?e.data[e.titleField]:""),1)],64))],16,ua)],16),_("div",{class:I(e.dropAfterCls)},null,2)],2)}const pa=Te(ca,[["render",ha]]),Nt=["expand","select","unselect","selected-change","check","uncheck","checked-change","set-data"],fa=["node-drop","check","select","expand"],ga=xe({name:"CTree",components:{CTreeNode:pa,LoadingIcon:$t},emits:["update:modelValue",...tt,...Nt],props:{modelValue:[String,Number,Array],data:{type:Array,default:()=>[]},unloadDataList:{type:Array,default:()=>[]},showUnloadCheckedNodes:{type:Boolean,default:!0},emptyText:{type:String,default:"暂无数据"},titleField:{type:String,default:"title"},keyField:{type:String,default:"id"},separator:{type:String,default:","},checkable:{type:Boolean,default:!1},selectable:{type:Boolean,default:!1},filteredNodeCheckable:{type:Boolean,default:!1},cascade:{type:Boolean,default:!0},enableLeafOnly:{type:Boolean,default:!1},disableAll:{type:Boolean,default:!1},defaultExpandAll:{type:Boolean,default:!1},defaultExpandedKeys:{type:Array,default:()=>[]},expandedKeys:{type:Array,default:()=>[]},draggable:{type:Boolean,default:!1},droppable:{type:Boolean,default:!1},beforeDropMethod:{type:Function,default:()=>()=>!0},ignoreMode:{type:String,default:Oe.none},autoLoad:{type:Boolean,default:!0},load:Function,render:Function,filterMethod:Function,expandOnFilter:{type:Boolean,default:!0},unselectOnClick:{type:Boolean,default:!0},loading:{type:Boolean,default:!1},nodeMinHeight:{type:Number,default:30},nodeIndent:{type:Number,default:20},renderNodeAmount:{type:Number,default:100},bufferNodeAmount:{type:Number,default:20},nodeClassName:{type:[String,Object,Array,Function]}},setup(e,a){const l="ctree-tree",t=(s,r)=>{if(Array.isArray(s)&&Array.isArray(r)){if(s.length===r.length&&s.every(D=>r.some(j=>j===D)))return!0}else if(s===r)return!0;return!1};let n=at([]),i=g([]);const d=g(0),o=g(0),h=g(0),y=g(0),O=g(0),K=g(0),G=g(0),ne=g(0),ee=g(!1),E=g(Array.isArray(e.modelValue)?e.modelValue.concat():e.modelValue),B=g(void 0),C=g(),F=g(),Y=b(()=>({height:`${h.value}px`})),se=b(()=>({height:`${y.value}px`})),ue=b(()=>[`${l}__wrapper`]),ie=b(()=>[`${l}__scroll-area`]),L=b(()=>[`${l}__block-area`]),W=b(()=>[`${l}__empty`]),J=b(()=>[`${l}__empty-text_default`]),he=b(()=>[`${l}__loading`]),pe=b(()=>[`${l}__loading-wrapper`]),Q=b(()=>[`${l}__loading-icon`]),f=b(()=>[`${l}__iframe`]),S=tt.reduce((s,r)=>(fa.indexOf(r)<0&&(s[r]=(...D)=>{a.emit.apply(a,[r,...D])}),s),{}),T=()=>({store:new na({keyField:e.keyField,ignoreMode:e.ignoreMode,filteredNodeCheckable:e.filteredNodeCheckable,cascade:e.cascade,defaultExpandAll:e.defaultExpandAll,load:e.load,expandOnFilter:e.expandOnFilter}),blockNodes:[]});let c=T();Se(()=>e.modelValue,s=>{if(e.checkable){if(t(s,E.value))return;let r=[];Array.isArray(s)?r=s.concat():typeof s=="string"&&(r=s===""?[]:s.split(e.separator)),c.store.clearChecked(!1,!1),c.store.setCheckedKeys(r,!0)}else if(e.selectable){if(s===E.value)return;const r=c.store.getSelectedKey();s!==""&&s!=null?c.store.setSelected(s,!0):(s===""||s==null)&&r&&c.store.setSelected(r,!1)}}),Se(()=>e.data,s=>{X(s)}),Se(()=>e.expandedKeys,()=>{Fe()});function X(s){Ge();let r=null,D=null;e.checkable?Array.isArray(e.modelValue)?r=e.modelValue.concat():typeof e.modelValue=="string"&&(r=e.modelValue===""?[]:e.modelValue.split(e.separator)):e.selectable&&!Array.isArray(e.modelValue)&&(D=e.modelValue),c.store.setData(s,D,r),Fe()}function V(s,r){c.store.setChecked(s,r)}function U(s,r){c.store.setCheckedKeys(s,r)}function u(){c.store.checkAll()}function v(){c.store.clearChecked()}function A(s,r){c.store.setSelected(s,r)}function $(){c.store.clearSelected()}function M(s,r,D=!0){c.store.setExpand(s,r,D)}function we(s,r){c.store.setExpandKeys(s,r)}function De(s){c.store.setExpandAll(s)}function p(s){return s=s||e.ignoreMode,c.store.getCheckedNodes(s)}function k(s){return s=s||e.ignoreMode,c.store.getCheckedKeys(s)}function fe(){return c.store.getIndeterminateNodes()}function ge(){return c.store.getSelectedNode()}function de(){return c.store.getSelectedKey()}function m(){return c.store.getExpandNodes()}function Ae(){return c.store.getExpandKeys()}function lt(){return c.store.flatData.filter(s=>s._filterVisible)}function He(s){return c.store.getNode(s)}function nt(){return c.store.data}function it(){return c.store.flatData}function st(){return c.store.flatData.length}function dt(s,r){return c.store.insertBefore(s,r)}function ot(s,r){return c.store.insertAfter(s,r)}function rt(s,r){return c.store.append(s,r)}function ct(s,r){return c.store.prepend(s,r)}function ut(s){return c.store.remove(s)}function ht(s,r){const D=(j,H)=>{const Z=H[e.titleField];return Z==null||!Z.toString?!1:Z.toString().toLowerCase().indexOf(j.toLowerCase())>-1};r=r||e.filterMethod||D,c.store.filter(s,r)}function pt(s){if(!e.checkable)return;s=s??e.showUnloadCheckedNodes;const r=c.store.getCheckedNodes();if(c.store.filter("",(j,H)=>H.checked),!s)return;const D=c.store.getUnloadCheckedKeys();if(D.length){const j=D.map(H=>{const Z=e.unloadDataList.concat(r);let Ct=H;return Z.some(Je=>Je[e.keyField]===H&&Je[e.titleField]!=null?(Ct=Je[e.titleField],!0):!1),new me({[e.keyField]:H,[e.titleField]:Ct,checked:!0,isLeaf:!0},null,e.keyField,!!e.load)});n=j,c.blockNodes.push(...j),$e(),ke()}}function We(){return ee.value=!0,new Promise((s,r)=>{e.load&&e.load(null,s,r)}).then(s=>{Array.isArray(s)&&X(s)}).catch(()=>{}).then(()=>{ee.value=!1})}function ft(s,r=Le.top){const D=c.store.mapData[s];if(!D||!D.visible)return;let j=-1;for(let Z=0;Z<d.value;Z++)if(c.blockNodes[Z][e.keyField]===s){j=Z;break}if(j===-1)return;let H=j*e.nodeMinHeight;if(r===Le.center){const Z=C.value.clientHeight;H=H-(Z-e.nodeMinHeight)/2}else if(r===Le.bottom){const Z=C.value.clientHeight;H=H-(Z-e.nodeMinHeight)}else typeof r=="number"&&(H=H-r);C.value&&(C.value.scrollTop=H)}function Fe(){e.expandedKeys.length&&(c.store.setExpandAll(!1,!1),c.store.setExpandKeys(e.expandedKeys,!0))}function je(){if(n.length){const s=c.store.getUnloadCheckedKeys();n.forEach(r=>{r.checked=s.indexOf(r[e.keyField])>-1})}}function gt(s){!e.cascade&&e.enableLeafOnly&&!s.isLeaf||c.store.setChecked(s[e.keyField],s.indeterminate?!0:!s.checked,!0,!0,!0)}function mt(s){e.enableLeafOnly&&!s.isLeaf||c.store.setSelected(s[e.keyField],!s.selected)}function vt(s){c.store.setExpand(s[e.keyField],!s.expand)}function kt(s,r,D){if(e.droppable&&r.dataTransfer)try{const j=JSON.parse(r.dataTransfer.getData("node"))[e.keyField],H=s[e.keyField];if(e.beforeDropMethod(j,H,D)){if(j===H)return;D===ce.before?c.store.insertBefore(j,H):D===ce.body||!s.isLeaf&&s.expand?c.store.prepend(j,H):D===ce.after&&c.store.insertAfter(j,H),a.emit("node-drop",s,r,D,He(j))}}catch(j){throw new Error(j)}}function ze(s,r){if(e.checkable){let D=r;Array.isArray(e.modelValue)||(D=D.join(e.separator)),Array.isArray(D)?E.value=D.concat():E.value=D,a.emit("update:modelValue",D)}}function qe(s,r){if(e.selectable&&!e.checkable){const D=r||"";E.value=D,a.emit("update:modelValue",D)}}function yt(){Nt.forEach(s=>{c.store.on(s,(...r)=>{a.emit.apply(a,[s,...r])})})}function Ge(){h.value=0,y.value=0,C.value&&(C.value.scrollTop=0)}function Ye(){c.blockNodes=c.store.flatData.filter(s=>s.visible),$e(),ke()}function $e(){d.value=c.blockNodes.length,o.value=e.nodeMinHeight*d.value}function ke(){Xe(),Ke()}function Xe(){const s=C.value.clientHeight;O.value=Math.max(e.renderNodeAmount,Math.ceil(s/e.nodeMinHeight)+e.bufferNodeAmount)}function Ke(s=!1){if(d.value>O.value){const r=C.value.scrollTop,D=Math.floor(r/e.nodeMinHeight);G.value=Math.floor(D/e.bufferNodeAmount)*e.bufferNodeAmount}else G.value=0;s&&K.value===O.value&&ne.value===G.value||(i.value=c.blockNodes.slice(G.value,G.value+O.value).map(r=>Object.assign({},r,{_parent:null,children:[]})),h.value=G.value*e.nodeMinHeight,y.value=o.value-(h.value+i.value.length*e.nodeMinHeight))}function bt(){B.value&&window.cancelAnimationFrame(B.value),K.value=O.value,ne.value=G.value,B.value=window.requestAnimationFrame(Ke.bind(null,!0))}const Et={setData:X,setChecked:V,setCheckedKeys:U,checkAll:u,clearChecked:v,setSelected:A,clearSelected:$,setExpand:M,setExpandKeys:we,setExpandAll:De,getCheckedNodes:p,getCheckedKeys:k,getIndeterminateNodes:fe,getSelectedNode:ge,getSelectedKey:de,getExpandNodes:m,getExpandKeys:Ae,getCurrentVisibleNodes:lt,getTreeData:nt,getFlatData:it,getNodesCount:st,insertBefore:dt,insertAfter:ot,append:rt,prepend:ct,remove:ut,filter:ht,showCheckedNodes:pt,loadRootNodes:We,scrollTo:ft,updateExpandedKeys:Fe,updateUnloadStatus:je,handleTreeScroll:bt,handleNodeCheck:gt,handleNodeSelect:mt,handleNodeExpand:vt,handleNodeDrop:kt,emitCheckableInput:ze,emitSelectableInput:qe,resetSpaceHeights:Ge,updateBlockNodes:Ye,updateBlockData:$e,updateRender:ke,updateRenderAmount:Xe,updateRenderNodes:Ke,getNode:He};return Ue(()=>{c.store.on("visible-data-change",Ye),c.store.on("render-data-change",ke),c.store.on("checked-change",(r,D)=>{ze(r,D),je()}),c.store.on("selected-change",qe),e.data.length?(X(e.data),e.defaultExpandedKeys.length&&c.store.setExpandKeys(e.defaultExpandedKeys,!0)):typeof e.load=="function"&&e.autoLoad&&((e.modelValue||e.unloadDataList)&&X([]),We());const s=F.value;s!=null&&s.contentWindow&&s.contentWindow.addEventListener("resize",ke)}),Ot(()=>{const s=F.value;s!=null&&s.contentWindow&&s.contentWindow.removeEventListener("resize",ke),c.store.disposeListeners();const r=T();c.store=r.store,c.blockNodes=r.blockNodes}),yt(),{nonReactive:c,unloadCheckedNodes:n,blockLength:d,blockAreaHeight:o,topSpaceHeight:h,bottomSpaceHeight:y,renderAmount:O,renderAmountCache:K,renderNodes:i,renderStart:G,renderStartCache:ne,isRootLoading:ee,valueCache:E,debounceTimer:B,topSpaceStyles:Y,bottomSpaceStyles:se,wrapperCls:ue,scrollAreaCls:ie,blockAreaCls:L,emptyCls:W,emptyTextDefaultCls:J,loadingCls:he,loadingWrapperCls:pe,loadingIconCls:Q,iframeCls:f,treeNodeListeners:S,setData:X,setChecked:V,setCheckedKeys:U,checkAll:u,clearChecked:v,setSelected:A,clearSelected:$,setExpand:M,setExpandKeys:we,setExpandAll:De,getCheckedNodes:p,getCheckedKeys:k,getIndeterminateNodes:fe,getSelectedNode:ge,getSelectedKey:de,getExpandNodes:m,getExpandKeys:Ae,getCurrentVisibleNodes:lt,getTreeData:nt,getFlatData:it,getNodesCount:st,insertBefore:dt,insertAfter:ot,append:rt,prepend:ct,remove:ut,filter:ht,showCheckedNodes:pt,loadRootNodes:We,scrollTo:ft,updateExpandedKeys:Fe,updateUnloadStatus:je,handleTreeScroll:bt,handleNodeCheck:gt,handleNodeSelect:mt,handleNodeExpand:vt,handleNodeDrop:kt,emitCheckableInput:ze,emitSelectableInput:qe,attachStoreEvents:yt,resetSpaceHeights:Ge,updateBlockNodes:Ye,updateBlockData:$e,updateRender:ke,updateRenderAmount:Xe,updateRenderNodes:Ke,getNode:He,scrollArea:C,iframe:F,methods:Et}}});function ma(e,a,l,t,n,i){const d=Me("CTreeNode"),o=Me("LoadingIcon");return q(),te("div",{class:I(e.wrapperCls)},[_("div",{ref:"scrollArea",class:I(e.scrollAreaCls),onScrollPassive:a[0]||(a[0]=Ht((...h)=>e.handleTreeScroll&&e.handleTreeScroll(...h),["stop"]))},[_("div",{class:I(e.blockAreaCls)},[_("div",{style:_t(e.topSpaceStyles)},null,4),(q(!0),te(Dt,null,At(e.renderNodes,h=>(q(),ye(d,Ne(e.$props,{key:h[e.keyField],data:h,getNode:e.getNode},et(e.treeNodeListeners),{class:typeof e.nodeClassName=="function"?e.nodeClassName(h):e.nodeClassName,style:{minHeight:`${e.nodeMinHeight}px`,paddingLeft:`${h._level*e.nodeIndent}px`},onCheck:e.handleNodeCheck,onSelect:e.handleNodeSelect,onExpand:e.handleNodeExpand,onNodeDrop:e.handleNodeDrop}),null,16,["data","getNode","class","style","onCheck","onSelect","onExpand","onNodeDrop"]))),128)),_("div",{style:_t(e.bottomSpaceStyles)},null,4)],2)],34),Pe(_("div",{class:I(e.emptyCls)},[_("span",{class:I(e.emptyTextDefaultCls)},[_e(e.$slots,"empty",{},()=>[le(R(e.emptyText),1)])],2)],2),[[Ze,!e.blockLength]]),Pe(_("div",{class:I(e.loadingCls)},[_("div",{class:I(e.loadingWrapperCls)},[_e(e.$slots,"loading",{},()=>[N(o,{class:I(e.loadingIconCls)},null,8,["class"])])],2)],2),[[Ze,e.loading||e.isRootLoading]]),_("iframe",{ref:"iframe",class:I(e.iframeCls)},null,2)],2)}const Kt=Te(ga,[["render",ma]]);function Vt(e){return la.reduce((a,l)=>(a[l]=function(...t){return e.value[l].apply(e.value,t)},a),{})}const ae="ctree-tree-search",Be="ctree-tree-node",va=xe({name:"CTreeSearch",inheritAttrs:!1,emits:["checked-change","search","set-data","update:modelValue"],components:{CTree:Kt},props:{modelValue:[String,Number,Array],searchPlaceholder:{type:String,default:"搜索关键字"},showCheckAll:{type:Boolean,default:!0},showCheckedButton:{type:Boolean,default:!0},checkedButtonText:{type:String,default:"已选"},showFooter:{type:Boolean,default:!0},searchMethod:Function,searchLength:{type:Number,default:1},searchDisabled:{type:Boolean,default:!1},searchRemote:{type:Boolean,default:!1},searchDebounceTime:{type:Number,default:300}},setup(e,{emit:a,attrs:l,expose:t}){const n=at({checked:!1,indeterminate:!1,disabled:!1}),i=g(!1),d=g(""),o=g(void 0),h=g(0),y=g();let O=Vt(y);const K=b({get:()=>e.modelValue,set:V=>{T(),a("update:modelValue",V)}}),G=b(()=>[`${ae}__wrapper`]),ne=b(()=>[`${ae}__search`]),ee=b(()=>[`${ae}__check-all-wrapper`]),E=b(()=>[`${ae}__check-all`,`${Be}__checkbox`,{[`${Be}__checkbox_checked`]:n.checked,[`${Be}__checkbox_indeterminate`]:n.indeterminate,[`${Be}__checkbox_disabled`]:e.searchDisabled||n.disabled}]),B=b(()=>[`${ae}__input-wrapper`]),C=b(()=>[`${ae}__input`,{[`${ae}__input_disabled`]:e.searchDisabled}]),F=b(()=>[`${ae}__action-wrapper`]),Y=b(()=>[`${ae}__checked-button`,{[`${ae}__checked-button_active`]:i.value}]),se=b(()=>[`${ae}__tree-wrapper`]),ue=b(()=>[`${ae}__footer`]),ie=b(()=>"checkable"in l&&l.checkable!==!1);function L(){d.value=""}function W(){return d.value}function J(V){let U=V;return typeof V!="string"&&(U=d.value),new Promise((u,v)=>{clearTimeout(o.value),o.value=setTimeout(()=>{if(!(U.length>0&&U.length<e.searchLength))if(i.value=!1,a("search",U),typeof e.searchMethod=="function"){const A=e.searchMethod(U);Promise.resolve(A).then(()=>{S(),u()})}else e.searchRemote?y.value.loadRootNodes().then(()=>{S(),u()}):(y.value.filter(U),S(),u())},e.searchDebounceTime)})}function he(){if(e.searchDisabled||n.disabled)return;const V=y.value.getCurrentVisibleNodes().map(U=>U[y.value.keyField]);n.checked||n.indeterminate?y.value.setCheckedKeys(V,!1):y.value.setCheckedKeys(V,!0),S()}function pe(){J()}function Q(){const V=()=>{i.value=!i.value,i.value?y.value.showCheckedNodes():y.value.filter(d.value,()=>!0),S()};d.value?(L(),J().then(()=>{V()})):V()}function f(){T(),S()}function S(){const V=y.value.getCurrentVisibleNodes(),U=V.length;let u=!1,v=!1,A=!1;for(let $=0;$<U;$++){const M=V[$];if(M.checked?u=!0:v=!0,u&&v||M.indeterminate){A=!0,n.checked=!1,n.indeterminate=!0;break}}A||(n.checked=u,n.indeterminate=!1)}function T(){h.value=y.value.getCheckedKeys().length}function c(V,U){S(),a("checked-change",V,U)}function X(){a("set-data"),f()}return Ue(()=>{ie.value&&!h.value&&f()}),{...O,treeModelValue:K,setChecked:(V,U)=>{y.value.setChecked(V,U)},checkAllStatus:n,isShowingChecked:i,keyword:d,debounceTimer:o,checkedCount:h,wrapperCls:G,searchCls:ne,checkAllWrapperCls:ee,checkboxCls:E,inputWrapperCls:B,inputCls:C,actionWrapperCls:F,checkedButtonCls:Y,treeWrapperCls:se,footerCls:ue,checkable:ie,treeRef:y,handleCheckAll:he,handleSearch:pe,handleShowChecked:Q,updateCheckedCount:T,handleSetData:f,updateCheckAllStatus:S,getKeyword:W,checkedChange:c,onSetData:X,clearKeyword:L,search:J}}}),ka=["placeholder","disabled"],ya={style:{float:"right"}};function ba(e,a,l,t,n,i){const d=Me("CTree");return q(),te("div",{class:I(e.wrapperCls)},[_("div",{class:I(e.searchCls)},[e.showCheckAll&&e.checkable?(q(),te("div",{key:0,class:I(e.checkAllWrapperCls)},[_("div",{class:I(e.checkboxCls),onClick:a[0]||(a[0]=(...o)=>e.handleCheckAll&&e.handleCheckAll(...o))},null,2)],2)):ve("",!0),_("div",{class:I(e.inputWrapperCls)},[_e(e.$slots,"search-input",{},()=>[Pe(_("input",{"onUpdate:modelValue":a[1]||(a[1]=o=>e.keyword=o),type:"text",class:I(e.inputCls),placeholder:e.searchPlaceholder,disabled:e.searchDisabled,onInput:a[2]||(a[2]=(...o)=>e.handleSearch&&e.handleSearch(...o))},null,42,ka),[[Wt,e.keyword]])])],2),_("div",{class:I(e.actionWrapperCls)},[e.showCheckedButton&&e.checkable?(q(),te("span",{key:0,class:I(e.checkedButtonCls),onClick:a[3]||(a[3]=(...o)=>e.handleShowChecked&&e.handleShowChecked(...o))},R(e.checkedButtonText),3)):ve("",!0),_e(e.$slots,"actions")],2)],2),_("div",{class:I(e.treeWrapperCls)},[N(d,Ne({ref:"treeRef"},e.$attrs,{modelValue:e.treeModelValue,"onUpdate:modelValue":a[4]||(a[4]=o=>e.treeModelValue=o),onSetData:e.onSetData,onCheckedChange:e.checkedChange}),jt({_:2},[At(e.$slots,(o,h)=>({name:"default",fn:w(y=>[_e(e.$slots,h,zt(qt(y)))])}))]),1040,["modelValue","onSetData","onCheckedChange"])],2),e.showFooter&&e.checkable?(q(),te("div",{key:0,class:I(e.footerCls)},[_e(e.$slots,"footer",{},()=>[_("span",ya,"已选 "+R(e.checkedCount)+" 个",1)])],2)):ve("",!0)],2)}const Ca=Te(va,[["render",ba]]),re="ctree-tree-drop",wt="ctree-tree-search";xe({name:"CTreeDrop",inheritAttrs:!1,emits:["clear","checked-change","dropdown-visible-change","update:modelValue"],components:{CTreeSearch:Ca},props:{modelValue:[String,Number,Array],dropHeight:{type:Number,default:300},dropPlaceholder:{type:String},dropDisabled:{type:Boolean,default:!1},clearable:{type:Boolean,default:!1},placement:{type:String,default:Ft["bottom-start"]},transfer:{type:Boolean,default:!1},dropdownClassName:[String,Array],dropdownMinWidth:{type:Number},dropdownWidthFixed:{type:Boolean,default:!1}},setup(e,{attrs:a,emit:l}){const t=g(!1),n=g(0),i=g(""),d=g(),o=g(),h=g(),y=at({checkedNodes:[],checkedKeys:[],selectedNode:void 0,selectedKey:void 0}),O=b({get:()=>e.modelValue,set:f=>{l("update:modelValue",f)}}),K=b(()=>[`${re}__wrapper`]),G=b(()=>[`${re}__reference`]),ne=b(()=>[`${wt}__input`,`${re}__display-input`,{[`${re}__display-input_focus`]:t.value,[`${wt}__input_disabled`]:e.dropDisabled}]),ee=b(()=>{let f=!1;return typeof e.dropPlaceholder=="string"&&(F.value?f=n.value===0:Y.value&&(f=i.value==="")),[`${re}__display-input-text`,{[`${re}__display-input-placeholder`]:f}]}),E=b(()=>[`${re}__display-icon-drop`,{[`${re}__display-icon-drop_active`]:t.value}]),B=b(()=>[`${re}__display-icon-clear`]),C=b(()=>{const f=Array.isArray(e.dropdownClassName)?e.dropdownClassName:[e.dropdownClassName];return[`${re}__dropdown`,...f]}),F=b(()=>"checkable"in a&&a.checkable!==!1),Y=b(()=>"selectable"in a&&a.selectable!==!1),se=b(()=>F.value?n.value===0&&typeof e.dropPlaceholder=="string"?e.dropPlaceholder:`已选 ${n.value} 个`:Y.value?i.value===""&&typeof e.dropPlaceholder=="string"?e.dropPlaceholder:i.value:e.dropPlaceholder||""),ue=b(()=>F.value?n.value!==0:Y.value?i.value!=="":!1);function ie(){const f=d.value.getBoundingClientRect(),S=f.width,T=f.height,c=`${typeof e.dropdownMinWidth=="number"?e.dropdownMinWidth:S}px`;o.value.style.minWidth=c,o.value.style.width=e.dropdownWidthFixed?c:"auto";const X=o.value.getBoundingClientRect(),V=window.getComputedStyle(o.value),U=parseFloat(V.marginLeft)+parseFloat(V.marginRight),u=parseFloat(V.marginTop)+parseFloat(V.marginBottom),v=X.width+U,A=X.height/.8+u;let $=0,M=0;switch(e.transfer&&($=-999,M=-999),e.placement){case"bottom-start":e.transfer?($=window.pageYOffset+f.bottom,M=window.pageXOffset+f.left):$=T;break;case"bottom-end":e.transfer?($=window.pageYOffset+f.bottom,M=window.pageXOffset+f.right-v):($=T,M=S-v);break;case"bottom":e.transfer?($=window.pageYOffset+f.bottom,M=window.pageXOffset+f.left+(S-v)/2):($=T,M=(S-v)/2);break;case"top-start":e.transfer?($=window.pageYOffset+f.top-A,M=window.pageXOffset+f.left):$=-A;break;case"top-end":e.transfer?($=window.pageYOffset+f.top-A,M=window.pageXOffset+f.right-v):($=-A,M=S-v);break;case"top":e.transfer?($=window.pageYOffset+f.top-A,M=window.pageXOffset+f.left+(S-v)/2):($=-A,M=(S-v)/2);break}o.value.style.top=`${$}px`,o.value.style.left=`${M}px`}function L(){e.dropDisabled||(t.value=!t.value)}function W(f){var S,T;!((S=d.value)!=null&&S.contains(f.target))&&!((T=o.value)!=null&&T.contains(f.target))&&(t.value=!1)}function J(){l("clear"),F.value?h.value.clearChecked():Y.value&&h.value.clearSelected()}function he(f,S){y.checkedNodes=f,y.checkedKeys=S,n.value=S.length,l("checked-change",f,S)}function pe(f,S){if(y.selectedNode=f,y.selectedKey=S,f){const T=h.value.$refs.treeRef.titleField;i.value=f[T]}else S?i.value=S:i.value="";t.value=!1}function Q(){const f=h.value.$refs.treeRef;if(y.checkedNodes=h.value.getCheckedNodes(),y.checkedKeys=h.value.getCheckedKeys(),y.selectedNode=h.value.getSelectedNode(),y.selectedKey=h.value.getSelectedKey(),F.value&&(n.value=y.checkedKeys.length),Y.value&&e.modelValue!=null){const S=h.value.getNode(e.modelValue);if(S){const T=f.titleField;i.value=S[T]}else i.value=e.modelValue}}return Ue(()=>{document.addEventListener("click",W),e.transfer&&document.body.appendChild(o.value),Q()}),Se(()=>t.value,f=>{l("dropdown-visible-change",f),f?Ut(()=>{ie()}):h.value.getKeyword()&&(h.value.clearKeyword(),h.value.search())}),{...Vt(h),treeSearchValue:O,dropdownVisible:t,checkedCount:n,selectedTitle:i,slotProps:y,wrapperCls:K,referenceCls:G,displayInputCls:ne,displayInputTextCls:ee,dropIconCls:E,clearIconCls:B,dropdownCls:C,checkable:F,selectable:Y,displayValue:se,showClearIcon:ue,referenceRef:d,dropdownRef:o,treeSearchRef:h,locateDropdown:ie,handleRefClick:L,handleDocumentClick:W,handleClear:J,handleCheckedChange:he,handleSelectedChange:pe,handleSetData:Q}}});const _a={class:"text-h4 mb-6"},Sa={class:"mb-0"},xa={class:"mb-2"},Na={class:"mb-0"},wa={class:"mb-0 text-error"},Da={class:"d-flex align-center gap-4 flex-wrap"},Aa={key:0},Fa={key:1},$a={key:2},Ka={class:"d-flex align-center justify-space-between flex-wrap gap-3 pa-5 pt-3"},Va={class:"text-sm text-medium-emphasis mb-0"},Ea={class:"text-h3 text-center mt-8"},Ra={class:"text-base text-center mt-2 mb-0 v-col-12"},Ia={class:"v-form",novalidate:""},Ba={class:"text-h4 mb-3"},La={class:"panel"},Ma={class:"body"},Pa={class:"interface"},Oa={class:"mr-2"},Ua={class:""},Ta=_("span",{class:"ml-2"},"?",-1),Za={__name:"index",setup(e){const l=Gt().permissions,{t,locale:n}=Yt(),i=g(t("Role")),d=g(t("created_at")),o=g(t("updated_at")),h=g(t("Actions")),y=g(t("No Data Text"));Se(n,()=>{i.value=t("Role"),d.value=t("created_at"),o.value=t("updated_at"),y.value=t("No Data Text")});const O=[{title:i,key:"name",sortable:!1},{title:d,key:"created_at",sortable:!1},{title:o,key:"updated_at",sortable:!1},{title:h,key:"actions",sortable:!1,align:"center"}],K=g([]),G=g(0),ne=g(1),ee=g("primary"),E=g(""),B=g("warning"),C=g(!1),F=g({name:"",permissionIds:""}),Y=g([{title:t("Dashboard"),id:99999,checked:!0,disabled:!0},{title:t("Access Control"),id:1e4,checked:!0,children:[{title:t("Referer")+"/"+t("Front Desk Name")+" ("+t("Function")+")",id:10001},{title:t("Product Name")+" ("+t("Function")+")",id:10002},{title:t("Product Price")+" ("+t("Function")+")",id:10003},{title:t("Synchronous Control")+" ("+t("Function")+")",id:10004}],expand:!0},{title:t("Data Center"),id:2e4,checked:!0,children:[{title:t("All"),id:20001},{title:t("Pending"),id:20002,checked:!0},{title:t("Bound"),id:20003},{title:t("Completed"),id:20004},{title:t("Deleted"),id:20005},{title:t("Blacklist"),id:20006},{title:t("Domain List")+" ("+t("Function")+"-"+t("Global effect")+")",id:3,checked:!0},{title:t("Data Display")+" ("+t("Function")+")",id:1,checked:!0},{title:t("Data Operations")+" ("+t("Function")+")",id:2},{title:t("Order Export")+" ("+t("Function")+")",id:5}]},{title:t("Order Statistics"),id:4,checked:!0},{title:t("Log information"),id:6,children:[{title:t("One-click clearing")+" ("+t("Function")+")",id:7}]},{title:t("Roles & Permissions"),id:5e4,children:[{title:t("Roles"),id:50001,children:[{title:t("Account Details")+" ("+t("Function")+")",id:12,checked:!0,disabled:!0},{title:t("Account list")+" ("+t("Function")+")",id:9,children:[]},{title:t("Add User")+" ("+t("Function")+")",id:10,children:[]},{title:t("Edit User")+" ("+t("Function")+")",id:11,children:[]},{title:t("Delete Users")+" ("+t("Function")+")",id:19,children:[]}]},{title:t("Permissions"),id:50002,children:[{title:t("Role Details")+" ("+t("Function")+")",id:14,checked:!0,disabled:!0,children:[]},{title:t("Role List")+" ("+t("Function")+")",id:13,children:[]},{title:t("Add Role")+" ("+t("Function")+")",id:15,children:[]},{title:t("Edit Role")+" ("+t("Function")+")",id:16,children:[]},{title:t("Delete Role")+" ("+t("Function")+")",id:20,children:[]}]}]},{title:t("Access Record"),id:17,children:[{title:t("One-click clearing")+" ("+t("Function")+")",id:18}]},{title:t("Source configuration"),id:80003},{title:t("Extensions"),id:8e4,children:[{title:t("Activate & Renew"),id:80001},{title:t("Tour"),id:80004}]},{title:t("System Settings"),id:7e4,children:[{title:t("Get System Settings"),id:70002,checked:!0,disabled:!0},{title:t("Update System Settings"),id:70001}]}]),se=p=>{p.forEach(k=>{k.checked=!1,k.children&&se(k.children)})},ue=g("none"),ie=g(!1),L=g({page:1,itemsPerPage:10}),W=g(!1),J=()=>{l.includes(13)&&(ee.value="primary",Ce.get("/api/role/roleList").then(p=>{p.data.code===200&&(K.value=p.data.data.data,G.value=p.data.data.total,ne.value=p.data.data.last_page,L.value.page=p.data.data.current_page)}).catch(p=>{E.value=t("Request failed"),B.value="error",C.value=!0}).finally(()=>{ee.value=!1}))},he=g([]),pe=()=>{Ce.get("/api/role/roleDetail").then(p=>{p.data.code===200&&(he.value=p.data.data.data)}).catch(p=>{E.value=t("Request failed"),B.value="error",C.value=!0})};Ue(()=>{J(),pe()}),Se(()=>L.value.itemsPerPage,(p,k)=>{L.value.page=1,J()});const Q=g(!1),f=g(""),S=g(!1),T=()=>{F.value.name="",F.value.permissionIds="",S.value=!1,Y.value=[{title:t("Dashboard"),id:99999,checked:!0,disabled:!0},{title:t("Access Control"),id:1e4,checked:!0,children:[{title:t("Referer")+"/"+t("Front Desk Name")+" ("+t("Function")+")",id:10001},{title:t("Product Name")+" ("+t("Function")+")",id:10002},{title:t("Product Price")+" ("+t("Function")+")",id:10003},{title:t("Synchronous Control")+" ("+t("Function")+")",id:10004}],expand:!0},{title:t("Data Center"),id:2e4,checked:!0,children:[{title:t("All"),id:20001},{title:t("Pending"),id:20002,checked:!0},{title:t("Bound"),id:20003},{title:t("Completed"),id:20004},{title:t("Deleted"),id:20005},{title:t("Blacklist"),id:20006},{title:t("Domain List")+" ("+t("Function")+"-"+t("Global effect")+")",id:3,checked:!0},{title:t("Data Display")+" ("+t("Function")+")",id:1,checked:!0},{title:t("Data Operations")+" ("+t("Function")+")",id:2},{title:t("Order Export")+" ("+t("Function")+")",id:5}]},{title:t("Order Statistics"),id:4,checked:!0},{title:t("Log information"),id:6,children:[{title:t("One-click clearing")+" ("+t("Function")+")",id:7}]},{title:t("Roles & Permissions"),id:5e4,children:[{title:t("Roles"),id:50001,children:[{title:t("Account Details")+" ("+t("Function")+")",id:12,checked:!0,disabled:!0},{title:t("Account list")+" ("+t("Function")+")",id:9},{title:t("Add User")+" ("+t("Function")+")",id:10,children:[]},{title:t("Edit User")+" ("+t("Function")+")",id:11,children:[]},{title:t("Delete Users")+" ("+t("Function")+")",id:19}]},{title:t("Permissions"),id:50002,children:[{title:t("Role Details")+" ("+t("Function")+")",id:14,checked:!0,disabled:!0,children:[]},{title:t("Role List")+" ("+t("Function")+")",id:13,children:[]},{title:t("Add Role")+" ("+t("Function")+")",id:15,children:[]},{title:t("Edit Role")+" ("+t("Function")+")",id:16,children:[]},{title:t("Delete Role")+" ("+t("Function")+")",id:20,children:[]}]}]},{title:t("Access Record"),id:17,children:[{title:t("One-click clearing")+" ("+t("Function")+")",id:18}]},{title:t("Source configuration"),id:80003},{title:t("Extensions"),id:8e4,children:[{title:t("Activate & Renew"),id:80001},{title:t("Tour"),id:80004}]},{title:t("System Settings"),id:7e4,children:[{title:t("Get System Settings"),id:70002,checked:!0,disabled:!0},{title:t("Update System Settings"),id:70001}]}],W.value=!0},c=()=>{Q.value=!0,S.value=!1,l.includes(15)&&Ce.post("/api/role/addRole",{name:F.value.name,permissionIds:F.value.permissionIds?F.value.permissionIds.split(",").map(p=>Number(p)):[]}).then(p=>{p.data.code===200?(E.value=t("Operation successful"),B.value="success",C.value=!0,J()):(E.value=t("Operation failed"),B.value="error",C.value=!0)}).catch(p=>{E.value=t("Request failed"),B.value="error",C.value=!0}).finally(()=>{Q.value=!1,W.value=!1,F.value.name="",F.value.permissionIds="",f.value=""})},X=g(!1),V=p=>{if(p=="")return!1;X.value=!0,S.value=!0,f.value=p,se(Y.value),Ce.get("/api/role/roleDetail",{params:{id:p}}).then(k=>{if(X.value=!1,k.data.code===200){F.value.name=k.data.data.name;let fe=k.data.data.permission_list;U(fe,Y.value),setTimeout(()=>{W.value=!0},300)}else E.value=t("Operation failed"),B.value="error",C.value=!0}).catch(k=>{E.value=t("Request failed"),B.value="error",C.value=!0})};function U(p,k){const fe=p.filter(de=>de.is_select===1).map(de=>de.id);function ge(de){de.forEach(m=>{fe.includes(m.id)&&(m.checked=!0),m.children&&m.children.length>0&&ge(m.children)})}ge(k)}const u=()=>{Q.value=!0,l.includes(16)&&Ce.post("/api/role/editRole",{id:f.value,name:F.value.name,is_del:0,permissionIds:F.value.permissionIds?F.value.permissionIds.split(",").map(p=>Number(p)):[]}).then(p=>{p.data.code===200?(E.value=t("Operation successful Login"),B.value="success",C.value=!0,J()):(E.value=t("Operation failed"),B.value="error",C.value=!0)}).catch(p=>{E.value=t("Request failed"),B.value="error",C.value=!0}).finally(()=>{Q.value=!1,W.value=!1,F.value.name="",F.value.permissionIds="",f.value=""})},v=()=>{S.value?u():c()},A=g(!1),$=g(!1),M=g(""),we=(p,k)=>{f.value=p,M.value=k,A.value=!0},De=()=>{$.value=!0,l.includes(20)&&Ce.get("/api/role/delRole",{params:{id:f.value}}).then(p=>{p.data.code===200?(E.value=t("Operation successful Login"),B.value="success",C.value=!0,J()):(E.value=t("Operation failed"),B.value="error",C.value=!0)}).catch(p=>{E.value=t("Request failed"),B.value="error",C.value=!0}).finally(()=>{$.value=!1,A.value=!1,F.value.name="",F.value.permissionIds="",f.value="",M.value=""})};return(p,k)=>{const fe=Mt,ge=It,de=Rt;return q(),ye(Qt,null,{default:w(()=>[N(Ee,{cols:"12"},{default:w(()=>[N(Re,null,{default:w(()=>[N(Ee,{cols:"12"},{default:w(()=>[_("h4",_a,R(p.$t("Roles List")),1),_("p",Sa,R(p.$t("Roles Desc")),1),_("p",xa,R(p.$t("Roles Desc1")),1),_("p",Na,R(p.$t("Roles Desc2")),1),_("p",wa,R(p.$t("Roles Desc3")),1)]),_:1}),N(be,{class:"d-flex align-center justify-space-between flex-wrap gap-4"},{default:w(()=>[N(fe,{"model-value":x(L).itemsPerPage,items:[{value:10,title:"10"},{value:25,title:"25"},{value:50,title:"50"},{value:100,title:"100"}],style:{width:"5rem"},"onUpdate:modelValue":k[0]||(k[0]=m=>x(L).itemsPerPage=parseInt(m,10))},null,8,["model-value"]),_("div",Da,[x(l).includes(15)?(q(),ye(oe,{key:0,density:"default",onClick:T,"prepend-icon":"tabler-plus"},{default:w(()=>[le(R(p.$t("Add Role")),1)]),_:1})):ve("",!0)])]),_:1}),N(St),N(x(Zt),{"items-per-page":x(L).itemsPerPage,"onUpdate:itemsPerPage":k[3]||(k[3]=m=>x(L).itemsPerPage=m),page:x(L).page,"onUpdate:page":k[4]||(k[4]=m=>x(L).page=m),"items-length":x(G),headers:O,items:x(K),loading:x(ee),class:"text-medium-emphasis text-no-wrap","onUpdate:options":k[5]||(k[5]=m=>L.value=m)},{"item.name":w(({item:m})=>[m.raw.id==1?(q(),te("span",Aa,R(p.$t("Superadmin")),1)):m.raw.id==999?(q(),te("span",Fa,R(p.$t("Visitor")),1)):(q(),te("span",$a,R(m.raw.name),1))]),"item.createdDate":w(({item:m})=>[_("span",null,R(m.raw.createdDate),1)]),"item.actions":w(({item:m})=>[x(l).includes(16)&&m.raw.id!=1?(q(),ye(oe,{key:0,icon:"",size:"small",color:"medium-emphasis",variant:"text",onClick:Ae=>V(m.raw.id)},{default:w(()=>[N(xt,{size:"22",icon:"tabler-edit"})]),_:2},1032,["onClick"])):ve("",!0),x(l).includes(20)&&m.raw.id!=1&&m.raw.id!=999&&m.raw.id!=2?(q(),ye(oe,{key:1,icon:"",size:"small",variant:"text",color:"medium-emphasis",onClick:Ae=>we(m.raw.id,m.raw.name)},{default:w(()=>[N(xt,{size:"22",icon:"tabler-trash"})]),_:2},1032,["onClick"])):ve("",!0)]),bottom:w(()=>[N(St),_("div",Ka,[_("p",Va,R(x(Bt)(x(L),x(G))),1),N(Lt,{modelValue:x(L).page,"onUpdate:modelValue":k[1]||(k[1]=m=>x(L).page=m),length:Math.ceil(x(G)/x(L).itemsPerPage),"total-visible":"5",onClick:k[2]||(k[2]=m=>J())},{prev:w(m=>[N(oe,Ne({variant:"tonal",color:"default"},m,{icon:!1}),{default:w(()=>[le(R(p.$t("$vuetify.pagination.ariaLabel.previous")),1)]),_:2},1040)]),next:w(m=>[N(oe,Ne({variant:"tonal",color:"default"},m,{icon:!1}),{default:w(()=>[le(R(p.$t("$vuetify.pagination.ariaLabel.next")),1)]),_:2},1040)]),_:1},8,["modelValue","length"])])]),_:1},8,["items-per-page","page","items-length","items","loading"])]),_:1}),N(Xt,{modelValue:x(C),"onUpdate:modelValue":k[7]||(k[7]=m=>Ie(C)?C.value=m:null),transition:"scale-transition",location:"top",timeout:2500,color:x(B),variant:"tonal"},{actions:w(()=>[N(oe,{color:"secondary",onClick:k[6]||(k[6]=m=>C.value=!1)},{default:w(()=>[le(" ❤️ ")]),_:1})]),default:w(()=>[le(R(x(E))+" ",1)]),_:1},8,["modelValue","color"]),N(Qe,{modelValue:x(W),"onUpdate:modelValue":k[12]||(k[12]=m=>Ie(W)?W.value=m:null),"max-width":"900"},{default:w(()=>[N(ge,{onClick:k[8]||(k[8]=m=>W.value=!x(W))}),N(Re,null,{default:w(()=>[_("h5",Ea,R(x(S)?p.$t("Edit Role"):p.$t("Add New Role")),1),_("p",Ra,R(p.$t("Roles Desc1")),1),N(be,null,{default:w(()=>[_("form",Ia,[N(Ee,{cols:"12"},{default:w(()=>[N(de,{modelValue:x(F).name,"onUpdate:modelValue":k[9]||(k[9]=m=>x(F).name=m),label:p.$t("Role Name"),placeholder:p.$t("Do not enter pure numbers")},null,8,["modelValue","label","placeholder"])]),_:1}),N(Ee,{cols:"12"},{default:w(()=>[_("h6",Ba,R(p.$t("Role Permissions")),1),_("div",La,[_("div",Ma,[_("div",Pa,[_("div",null,[N(x(Kt),{modelValue:x(F).permissionIds,"onUpdate:modelValue":k[10]||(k[10]=m=>x(F).permissionIds=m),data:x(Y),checkable:"","ignore-mode":x(ue),cascade:x(ie),emptyText:x(y)},null,8,["modelValue","data","ignore-mode","cascade","emptyText"])])])])])]),_:1})])]),_:1}),N(be,{class:"d-flex align-center justify-center gap-3 mt-6"},{default:w(()=>[N(oe,{loading:x(Q),disabled:x(Q),onClick:v},{default:w(()=>[le(R(p.$t("Confirm")),1)]),_:1},8,["loading","disabled"]),N(oe,{variant:"tonal",color:"secondary",onClick:k[11]||(k[11]=m=>W.value=!1)},{default:w(()=>[le(R(p.$t("Cancel")),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"]),N(Qe,{modelValue:x(X),"onUpdate:modelValue":k[13]||(k[13]=m=>Ie(X)?X.value=m:null),width:"300"},{default:w(()=>[N(Re,{color:"primary",width:"300"},{default:w(()=>[N(be,{class:"pt-3"},{default:w(()=>[le(" Please stand by "),N(Jt,{indeterminate:"",class:"mb-0"})]),_:1})]),_:1})]),_:1},8,["modelValue"]),N(Qe,{modelValue:x(A),"onUpdate:modelValue":k[16]||(k[16]=m=>Ie(A)?A.value=m:null),persistent:"",class:"v-dialog-sm"},{default:w(()=>[N(ge,{onClick:k[14]||(k[14]=m=>A.value=!x(A))}),N(Re,{title:p.$t("Operation tips")},{default:w(()=>[N(be,null,{default:w(()=>[_("span",Oa,R(p.$t("Delete Role Msg"))+": ",1),_("span",Ua,R(x(M)),1),Ta]),_:1}),N(be,{class:"d-flex justify-end gap-3 flex-wrap"},{default:w(()=>[N(oe,{color:"secondary",variant:"tonal",onClick:k[15]||(k[15]=m=>A.value=!1)},{default:w(()=>[le(R(p.$t("Cancel")),1)]),_:1}),N(oe,{loading:x($),disabled:x($),onClick:De},{default:w(()=>[le(R(p.$t("Confirm")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"])]),_:1})]),_:1})}}};export{Za as default};
