import{_ as n}from"./ErrorHeader-d0ed5b30.js";import{u as c,m,a as i}from"./misc-mask-light-eca946dc.js";import{p as _}from"./404-aa4980a7.js";import{b as s}from"./route-block-83d24a4e.js";import{o as d,c as l,q as e,w as u,aj as p,ah as f,n as h,s as a,aF as o}from"./index-9a5dc664.js";const k={class:"misc-wrapper"},g={class:"misc-avatar w-100 text-center"},x={__name:"not-found",setup(v){const t=c(i,m);return(w,V)=>{const r=n;return d(),l("div",k,[e(r,{"error-title":"Page Not Found :(","error-description":"Oops! 😖 The requested URL was not found on this server."}),e(f,{to:"/",class:"mb-12"},{default:u(()=>[p(" Back to Home ")]),_:1}),h("div",g,[e(o,{src:a(_),alt:"Coming Soon","max-width":200,class:"mx-auto"},null,8,["src"])]),e(o,{src:a(t),class:"misc-footer-img d-none d-md-block"},null,8,["src"])])}}};typeof s=="function"&&s(x);export{x as default};
