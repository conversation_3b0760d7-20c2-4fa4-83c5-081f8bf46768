import{u as o,m as r,a as i}from"./misc-mask-light-eca946dc.js";import{b as a}from"./route-block-83d24a4e.js";import{o as m,c as _,n as e,q as t,w as d,aj as l,ah as u,s as n,aF as s}from"./index-9a5dc664.js";const h=""+new URL("misc-under-maintenance-b2823f61.webp",import.meta.url).href;const f={class:"misc-wrapper"},p={class:"text-center mb-12"},x=e("h4",{class:"text-h4 font-weight-medium mb-3"}," Under Maintenance! 🚧 ",-1),k=e("p",null,"Sorry for the inconvenience but we're performing some maintenance at the moment",-1),w={class:"misc-avatar w-100 text-center"},b={__name:"under-maintenance",setup(g){const c=o(i,r);return(v,M)=>(m(),_("div",f,[e("div",p,[x,k,t(u,{to:"/"},{default:d(()=>[l(" Back to Home ")]),_:1})]),e("div",w,[t(s,{src:n(h),alt:"Coming Soon","max-width":550,class:"mx-auto"},null,8,["src"])]),t(s,{src:n(c),class:"misc-footer-img d-none d-md-block"},null,8,["src"])]))}};typeof a=="function"&&a(b);export{b as default};
