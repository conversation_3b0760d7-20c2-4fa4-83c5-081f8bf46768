import{_ as Cl}from"./AppTextarea-36c843b3.js";import{_ as hl}from"./AppTextField-8c148b8f.js";import{_ as gl}from"./DialogCloseBtn-e6f97e88.js";import{bb as wl,l as k,B as ie,I as $l,W as Ca,a6 as Pl,a7 as Sl,q as a,C as fa,aJ as Vl,bv as Il,bw as xl,E as et,ad as xt,ae as Ml,af as Tl,a9 as Al,D as zl,co as Dl,G as Bl,o as m,c as h,w as l,am as z,as as O,av as R,s as g,n as i,y as s,b as B,aj as r,A as w,ag as d,ah as E,aY as Pe,t as Oa,al as L,aq as at,at as de,au as ve,cp as Re,ao as El,az as ha,aA as v,aB as p,F as Ze,a as ga,ar as Ua,aC as y,aD as _,z as ma,a3 as tt,ap as Mt,aN as Nl,aO as Gl}from"./index-9a5dc664.js";import{u as Rl}from"./index-9465fde1.js";import{b as wa}from"./index-f0b62869.js";import{t as jl}from"./tinycolor-ea5bcbb6.js";import{r as Ol}from"./style-7dcccb91.js";import{c as lt,v as ot,a as st,m as nt,d as it,b as ut,j as rt}from"./visa-8076fdb1.js";import{u as Ul}from"./entry-a444d4f4.js";import{v as Hl,p as Ll,a as Wl,b as Fl,c as Jl,d as Kl,V as Xl}from"./filepond-plugin-image-preview.esm-68bdc77d.js";import{m as Tt,g as ql,a as Yl,b as Zl,c as Ql,d as eo,e as ao,y as to,h as lo,i as oo,k as so,u as no,z as io,l as uo,n as ro,A as co,q as vo,r as po,p as fo,s as mo,t as yo,v as _o,x as bo}from"./VDataTable-d690b508.js";import{V as G}from"./VChip-a30ee730.js";import{V as W}from"./VDialog-0870f7b8.js";import"./VTextField-3e2d458d.js";import"./shepherd.esm-7d7e64e6.js";import"./VSelectionControl-182ca2ca.js";const ko=wl({visibleItems:{type:[String,Number],default:20},itemHeight:{type:[String,Number],default:52}},"virtual"),At=-1,zt=1;function Co(A,pe){const fe=k(0),be=ie(()=>parseInt(A.itemHeight,10)),me=ie(()=>parseInt(A.visibleItems,10)),D=k(),ke=k(!1);function Se(oe){return oe*be.value}function U(oe){let se=0,J=pe.value.length;for(;se<=J;){const ne=se+Math.floor((J-se)/2),je=Se(ne);if(je===oe)return ne;je<oe?se=ne+1:je>oe&&(J=ne-1)}return se}let re=0,Qe;function ea(){if(!D.value)return;ke.value=!0,clearTimeout(Qe),Qe=setTimeout(()=>{ke.value=!1},100);const oe=D.value.scrollTop,se=oe<re?At:zt,J=U(oe),ne=Math.round(me.value/3);se===At&&J<=fe.value?fe.value=Math.max(J-ne,0):se===zt&&J>=fe.value+ne*2&&(fe.value=Math.min(Math.max(0,J-ne),pe.value.length-me.value)),re=D.value.scrollTop}const F=ie(()=>Math.min(pe.value.length,fe.value+me.value)),T=ie(()=>Se(fe.value)),Ce=ie(()=>Se(pe.value.length)-Se(F.value));return{startIndex:fe,stopIndex:F,paddingTop:T,paddingBottom:Ce,handleScroll:ea,containerRef:D,itemHeight:be,isScrolling:ke}}const ho=$l()({name:"VDataTableVirtual",props:{...Tt(),...Tt(),...ql(),...Yl(),...Zl(),...Ql(),...eo(),...ao(),...ko(),...to()},emits:{"update:modelValue":A=>!0,"update:sortBy":A=>!0,"update:options":A=>!0,"update:groupBy":A=>!0,"update:expanded":A=>!0,"click:row":(A,pe)=>!0},setup(A,pe){let{emit:fe,slots:be}=pe;const{groupBy:me}=lo(A),{sortBy:D,multiSort:ke,mustSort:Se}=oo(A),{columns:U}=so(A,{groupBy:me,showSelect:Ca(A,"showSelect"),showExpand:Ca(A,"showExpand")}),{items:re}=no(A,U),Qe=ie(()=>U.value.map(aa=>"columns."+aa.key)),ea=Ca(A,"search"),{filteredItems:F}=io(A,re,ea,{filterKeys:Qe});uo({sortBy:D,multiSort:ke,mustSort:Se});const{sortByWithGroups:T,opened:Ce,extractRows:oe}=ro({groupBy:me,sortBy:D}),{sortedItems:se}=co(F,T,U),{flatItems:J}=vo(se,me,Ce),ne=ie(()=>oe(J.value));po(A,ne),fo(A);const{containerRef:je,paddingTop:c,paddingBottom:H,startIndex:K,stopIndex:X,itemHeight:Oe,handleScroll:u}=Co(A,J),Ha=ie(()=>J.value.slice(K.value,X.value));mo({sortBy:D,page:k(1),itemsPerPage:k(-1),groupBy:me,search:ea}),Pl({VDataTableRows:{hideNoData:Ca(A,"hideNoData"),noDataText:Ca(A,"noDataText")}}),Sl(()=>a(bo,{class:"v-data-table",style:{"--v-table-row-height":fa(Oe.value)},fixedHeader:A.fixedHeader,fixedFooter:A.fixedFooter,height:A.height,hover:A.hover},{top:be.top,wrapper:()=>a("div",{ref:je,onScroll:u,class:"v-table__wrapper",style:{height:fa(A.height)}},[a("table",null,[a("thead",null,[a(yo,{sticky:A.fixedHeader,multiSort:A.multiSort},be)]),a("tbody",null,[a("tr",{style:{height:fa(c.value),border:0}},[a("td",{colspan:U.value.length,style:{height:fa(c.value),border:0}},null)]),a(_o,{items:Ha.value,"onClick:row":A["onClick:row"]},be),a("tr",{style:{height:fa(H.value),border:0}},[a("td",{colspan:U.value.length,style:{height:fa(H.value),border:0}},null)])])])]),bottom:be.bottom}))}});const ue=A=>(Nl("data-v-ce4013fd"),A=A(),Gl(),A),go={class:"d-flex align-center id-cell",style:{"min-width":"150px"}},wo={class:"d-flex flex-column cursor-pointer"},$o=["onClick"],Po={key:1,style:{color:"rgb(135 136 138)","font-size":"75%","font-weight":"400"}},So={key:2,class:"text-secondary"},Vo={key:0,class:"ml-1"},Io={key:0,class:"cursor-pointer"},xo={class:"mr-1",style:{"font-size":"13px"}},Mo={href:"javascript:;",class:"user-list-name",style:{"font-size":"13px"}},To={key:1,class:"cursor-pointer"},Ao=["onClick"],zo=["onClick"],Do=["onClick"],Bo={class:"mr-1",style:{"font-size":"13px"}},Eo={href:"javascript:;",class:"user-list-name",style:{"font-size":"14px"}},No=["onClick"],Go={class:"mr-1",style:{"font-size":"13px"}},Ro={href:"javascript:;",class:"user-list-name",style:{"font-size":"14px"}},jo=["onClick"],Oo={class:"mr-1",style:{"font-size":"13px"}},Uo={href:"javascript:;",class:"user-list-name",style:{"font-size":"14px"}},Ho=["onClick"],Lo={class:"mr-1",style:{"font-size":"13px"}},Wo={href:"javascript:;",class:"user-list-name",style:{"font-size":"14px"}},Fo=["onClick"],Jo={class:"mr-1",style:{"font-size":"13px"}},Ko={href:"javascript:;",class:"user-list-name",style:{"font-size":"13px"}},Xo=["onClick"],qo={class:"mr-1",style:{"font-size":"13px"}},Yo={href:"javascript:;",class:"user-list-name",style:{"font-size":"13px"}},Zo=["onClick"],Qo={class:"mr-1",style:{"font-size":"13px"}},es={href:"javascript:;",class:"user-list-name",style:{"font-size":"13px"}},as=["onClick"],ts={class:"mr-1",style:{"font-size":"13px"}},ls={href:"javascript:;",class:"user-list-name",style:{"font-size":"13px"}},os=["onClick"],ss={class:"mr-1",style:{"font-size":"13px"}},ns={href:"javascript:;",class:"user-list-name",style:{"font-size":"13px"}},is=["onClick"],us={class:"mr-1",style:{"font-size":"13px"}},rs={href:"javascript:;",class:"user-list-name",style:{"font-size":"13px"}},cs=["onClick"],ds={class:"mr-1",style:{"font-size":"13px"}},vs={href:"javascript:;",class:"user-list-name",style:{"font-size":"13px"}},ps=["onClick"],fs={class:"mr-1",style:{"font-size":"13px"}},ms={href:"javascript:;",class:"user-list-name",style:{"font-size":"13px"}},ys=["onClick"],_s={class:"mr-1",style:{"font-size":"13px"}},bs={href:"javascript:;",class:"user-list-name",style:{"font-size":"13px"}},ks=["onClick"],Cs={class:"mr-1",style:{"font-size":"13px"}},hs={href:"javascript:;",class:"user-list-name",style:{"font-size":"13px"}},gs=["onClick"],ws={class:"mr-1",style:{"font-size":"13px"}},$s={href:"javascript:;",class:"user-list-name",style:{"font-size":"13px"}},Ps=["onClick"],Ss={style:{"font-size":"14px"}},Vs=["onClick"],Is={style:{"font-size":"14px"}},xs=["onClick"],Ms={class:"d-flex flex-column align-center"},Ts={class:"text-base"},As={href:"javascript:;",class:"font-weight-medium user-list-name",style:{"font-size":"13px","font-weight":"400 !important"}},zs=["onClick"],Ds={class:"d-flex flex-column align-center"},Bs={class:"d-flex align-center pt-1"},Es={class:"text-base"},Ns={class:"font-weight-medium user-list-name text-no-wrap d-flex align-center"},Gs={class:"d-flex"},Rs={key:0,class:"mr-3"},js={class:"",style:{"font-size":"75%"}},Os={class:"mr-1"},Us={key:1,class:"mr-3"},Hs={class:"text-success cursor-pointer",style:{"font-size":"75%"}},Ls={class:"mr-1"},Ws=["onClick"],Fs={class:"text-primary cursor-pointer",style:{"font-size":"75%"}},Js={class:"mr-1"},Ks=["onClick"],Xs=["onClick"],qs=["onMouseenter"],Ys={class:"d-flex flex-column align-center"},Zs={class:"d-flex align-center justify-center"},Qs={class:"ml-1"},en={key:1,style:{"margin-top":"2px"}},an={key:0,class:"text-primary cursor-pointer",style:{"padding-left":"2px","font-size":"12px"}},tn={key:1,class:"cursor-pointer",style:{"padding-left":"2px","font-size":"12px"}},ln={key:0,class:"text-h6 my-2"},on={class:"card-left-title"},sn={class:"text-body-1"},nn={key:1,class:"text-h6 my-2 d-flex"},un={class:"card-left-title"},rn={class:"text-body-1 overflow-hide-1 cursor-pointer"},cn={key:2,class:"text-h6 my-2"},dn={class:"card-left-title"},vn={class:"text-body-1"},pn={key:3,class:"text-h6 my-2"},fn={class:"card-left-title"},mn={class:"text-body-1"},yn={key:4,class:"text-h6 my-2"},_n={class:"card-left-title"},bn={class:"text-body-1"},kn={key:5,class:"text-h6 my-2"},Cn={class:"card-left-title"},hn={class:"text-body-1"},gn={class:"text-h6 my-2"},wn={class:"card-left-title"},$n={class:"text-body-1"},Pn={key:6,class:"text-h6 my-2"},Sn={class:"card-left-title"},Vn={class:"text-body-1"},In={key:7,class:"text-h6 my-2"},xn={class:"card-left-title"},Mn={class:"text-body-1"},Tn={key:8,class:"text-h6 my-2"},An={class:"card-left-title"},zn={class:"text-body-1"},Dn={key:9,class:"text-h6 my-2"},Bn={class:"card-left-title"},En={class:"text-body-1"},Nn={class:"text-h6 my-2"},Gn={class:"card-left-title"},Rn={class:"text-body-1"},jn={class:"text-h6 my-2"},On={class:"card-left-title"},Un={class:"text-body-1"},Hn={key:10,class:"text-h6 my-2"},Ln={class:"card-left-title"},Wn={class:"text-body-1"},Fn={class:"text-h6 my-2"},Jn={class:"card-left-title"},Kn={class:"text-body-1"},Xn={class:"text-h6 my-2"},qn={class:"card-left-title"},Yn={class:"text-body-1"},Zn={class:"text-h6 my-1"},Qn={class:"card-left-title"},ei={class:"text-body-1"},ai={class:"text-h6 my-2"},ti={class:"card-left-title"},li={class:"text-body-1"},oi={class:"text-h6 my-2"},si={class:"card-left-title"},ni={class:"text-body-1"},ii={class:"text-h6 my-2"},ui=ue(()=>i("span",{class:"card-left-title"},"IP",-1)),ri={class:"text-body-1"},ci={class:"text-h6 my-2"},di={class:"card-left-title card-left-title-1"},vi={class:"text-body-1"},pi={class:"text-h6 my-2"},fi={class:"card-left-title card-left-title-1"},mi={class:"text-body-1"},yi={class:"text-h6 my-2"},_i={class:"card-left-title card-left-title-1"},bi={class:"text-body-1"},ki={class:"text-h6 my-2 text-success"},Ci={class:"card-right-title card-left-title-1"},hi={class:"text-body-1"},gi={class:"text-h6 my-2"},wi={class:"card-right-title card-left-title-1"},$i={class:"text-body-1"},Pi={class:"text-h6 my-2"},Si={class:"card-right-title card-left-title-1"},Vi={class:"text-body-1 sentence"},Ii={class:"text-h6 my-2"},xi={class:"card-right-title card-left-title-1"},Mi={class:"text-body-1 sentence"},Ti={class:"text-h6 my-2"},Ai={class:"card-right-title card-left-title-1"},zi={class:"text-body-1 sentence"},Di={class:"text-h6 my-2"},Bi={class:"card-right-title card-left-title-1"},Ei={class:"text-body-1 sentence"},Ni={class:"text-h6 my-2"},Gi={class:"card-right-title card-left-title-1"},Ri={class:"bank_card"},ji={class:"bank_card_top"},Oi=["src"],Ui=["src"],Hi=["src"],Li=["src"],Wi=["src"],Fi=["src"],Ji=["src"],Ki={key:6,class:"text-white"},Xi={class:"bank_card_footer"},qi={class:"bank_card_lfooter"},Yi=ue(()=>i("div",{class:"bank_card_tit"},"Card Holder",-1)),Zi={class:"bank_card_rfooter"},Qi={class:"bank_card_vcc"},eu=ue(()=>i("div",{class:"bank_card_tit"},"CVV",-1)),au=ue(()=>i("div",{class:"bank_card_tit"},"Expires",-1)),tu={key:0,class:"text-h6 my-2"},lu={class:"card-left-title"},ou={class:"text-body-1"},su={key:1,class:"text-h6 my-2 d-flex"},nu={class:"card-left-title"},iu={class:"text-body-1 overflow-hide-1 cursor-pointer"},uu={key:2,class:"text-h6 my-2"},ru={class:"card-left-title"},cu={class:"text-body-1"},du={key:3,class:"text-h6 my-2"},vu={class:"card-left-title"},pu={class:"text-body-1"},fu={key:4,class:"text-h6 my-2"},mu={class:"card-left-title"},yu={class:"text-body-1"},_u={key:5,class:"text-h6 my-2"},bu={class:"card-left-title"},ku={class:"text-body-1"},Cu={class:"text-h6 my-2"},hu={class:"card-left-title"},gu={class:"text-body-1"},wu={key:6,class:"text-h6 my-2"},$u={class:"card-left-title"},Pu={class:"text-body-1"},Su={key:7,class:"text-h6 my-2"},Vu={class:"card-left-title"},Iu={class:"text-body-1"},xu={key:8,class:"text-h6 my-2"},Mu={class:"card-left-title"},Tu={class:"text-body-1"},Au={key:9,class:"text-h6 my-2"},zu={class:"card-left-title"},Du={class:"text-body-1"},Bu={class:"text-h6 my-2"},Eu={class:"card-left-title"},Nu={class:"text-body-1"},Gu={class:"text-h6 my-2"},Ru={class:"card-left-title"},ju={class:"text-body-1"},Ou={key:10,class:"text-h6 my-2"},Uu={class:"card-left-title"},Hu={class:"text-body-1"},Lu={class:"text-h6 my-2"},Wu={class:"card-left-title"},Fu={class:"text-body-1"},Ju={class:"text-h6 my-2"},Ku={class:"card-left-title"},Xu={class:"text-body-1"},qu={class:"text-h6 my-1"},Yu={class:"card-left-title"},Zu={class:"text-body-1"},Qu={class:"text-h6 my-2"},er={class:"card-left-title"},ar={class:"text-body-1"},tr={class:"text-h6 my-2"},lr={class:"card-left-title"},or={class:"text-body-1"},sr={class:"text-h6 my-2"},nr=ue(()=>i("span",{class:"card-left-title"},"IP",-1)),ir={class:"text-body-1"},ur={class:"text-h6 my-2"},rr={class:"card-left-title card-left-title-1"},cr={class:"text-body-1"},dr={class:"text-h6 my-2"},vr={class:"card-left-title card-left-title-1"},pr={class:"text-body-1"},fr={class:"text-h6 my-2"},mr={class:"card-left-title card-left-title-1"},yr={class:"text-body-1"},_r={class:"text-h6 my-2 text-success"},br={class:"card-right-title card-left-title-1"},kr={class:"text-body-1"},Cr={class:"text-h6 my-2"},hr={class:"card-right-title card-left-title-1"},gr={class:"text-body-1"},wr={class:"text-h6 my-2"},$r={class:"card-right-title card-left-title-1"},Pr={class:"text-body-1 sentence"},Sr={class:"text-h6 my-2"},Vr={class:"card-right-title card-left-title-1"},Ir={class:"text-body-1 sentence"},xr={class:"text-h6 my-2"},Mr={class:"card-right-title card-left-title-1"},Tr={class:"text-body-1 sentence"},Ar={class:"text-h6 my-2"},zr={class:"card-right-title card-left-title-1"},Dr={class:"text-body-1 sentence"},Br={class:"text-h6 my-2"},Er={class:"card-right-title card-left-title-1"},Nr={class:"bank_card"},Gr={class:"bank_card_top"},Rr=["src"],jr=["src"],Or=["src"],Ur=["src"],Hr=["src"],Lr=["src"],Wr=["src"],Fr={key:6,class:"text-white"},Jr={class:"bank_card_footer"},Kr={class:"bank_card_lfooter"},Xr=ue(()=>i("div",{class:"bank_card_tit"},"Card Holder",-1)),qr={class:"bank_card_rfooter"},Yr={class:"bank_card_vcc"},Zr=ue(()=>i("div",{class:"bank_card_tit"},"CVV",-1)),Qr=ue(()=>i("div",{class:"bank_card_tit"},"Expires",-1)),ec={class:"mb-0"},ac={class:"mb-0"},tc={class:"mb-0"},lc={class:"mb-0"},oc={class:"mb-0"},sc={class:"mb-0"},nc={class:"mb-0"},ic={class:"mb-0"},uc={key:0,class:"text-error text-center"},rc={class:"mb-0"},cc={key:0,class:"text-error text-center"},dc={class:"bank_card"},vc={class:"bank_card_top"},pc=["src"],fc=["src"],mc=["src"],yc=["src"],_c=["src"],bc=["src"],kc=["src"],Cc={key:6,class:"text-white",style:{"font-size":"20px"}},hc={class:"bank_card_footer"},gc={class:"bank_card_lfooter"},wc=ue(()=>i("div",{class:"bank_card_tit"},"Card Holder",-1)),$c={class:"bank_card_rfooter"},Pc={class:"bank_card_vcc"},Sc=ue(()=>i("div",{class:"bank_card_tit"},"CVV",-1)),Vc=ue(()=>i("div",{class:"bank_card_tit"},"Expires",-1)),Ic={class:"text-h6 my-2"},xc={class:"card-right-title card-left-title-1 text-primary"},Mc={class:"text-h6 my-2"},Tc={class:"card-right-title card-left-title-1 text-primary"},Ac={class:"text-h6 my-2 mt-3"},zc={class:"card-right-title card-left-title-1"},Dc={class:"text-body-1 sentence"},Bc={class:"text-h6 my-2"},Ec={class:"card-right-title card-left-title-1"},Nc={class:"text-body-1 sentence"},Gc={class:"text-h6 my-2"},Rc={class:"card-right-title card-left-title-1"},jc={class:"text-body-1 sentence"},Oc={class:"text-h6 my-2"},Uc={class:"card-right-title card-left-title-1"},Hc=["onClick"],Lc={href:"javascript:;",class:"d-flex user-list-name"},Wc={class:"d-flex pr-5"},Fc={key:0,class:"ml-2"},Jc={class:"text-primary"},Kc={class:"text-error"},Xc=ue(()=>i("br",null,null,-1)),qc={key:0,class:"v-input__details text-error"},Yc={__name:"access-control",setup(A){const pe=Hl(Kl,Jl,Fl,Wl,Ll),fe=Il(),{ctrl_k:be,meta_k:me}=xl();let D=null;const ke="tourShown",Se=localStorage.getItem(ke)==="true";et([be,me,()=>fe.path],()=>{D&&D.isActive()&&D.cancel()});const U=xt();xt().cloud.coding;const re=Ml(),Qe=k(ea());function ea(){return Math.max(window.innerHeight-248)}const F=U.permissions,T=U.settings,{ob_control:Ce,hidePhoneColumn:oe,hideBirthColumn:se,hideCustomColumn:J,hideBINColumn:ne}=Tl(U);U.isShunt;const je=U.userInfo.role_id,c=k({});et(()=>re.receivedData,(o,t)=>{c.value&&o.has(c.value.uuid)&&(c.value=o.get(c.value.uuid))},{deep:!0});const H=k(!1),K=k(""),X=k("warning"),Oe=k(!1),{t:u,locale:Ha}=Al(),aa=k(u("UID")),Dt=k(u("Order ID")),Bt=k(u("Country")),Et=k(u("Username")),La=k(u("Login Information")),Nt=k(u("Password")),Gt=k(u("Name")),$a=k(u("Phone")),Wa=k(u("Payment")),Rt=k(u("Card Type")),Fa=k(u("BIN remarks")),jt=k(u("Expires")),Ot=k(u("CVV")),Pa=k(u("OTP")),Sa=k(u("CustomOTP")),Va=k(u("Birth")),ct=k(u("User Info")),Ut=k(u("Status")),Ht=k(u("created_at")),Lt=k(u("updated_at")),Wt=k(u("Diversion account")),Ft=k(u("Actions")),Jt=k(u("Card Info")),Ia=k(u("User Action")),Kt=k(u("Online")),Xt=k(u("Offline")),dt=k(u("No Data Text"));et(Ha,()=>{aa.value=u("UID"),Dt.value=u("Order ID"),Bt.value=u("Country"),Et.value=u("Username"),La.value=u("Login Information"),Nt.value=u("Password"),Gt.value=u("Name"),$a.value=u("Phone"),Wa.value=u("Payment"),Rt.value=u("Card Type"),Fa.value=u("BIN remarks"),jt.value=u("Expires"),Ot.value=u("CVV"),Pa.value=u("OTP"),Sa.value=u("CustomOTP"),ct.value=u("User Info"),Ut.value=u("Status"),Ht.value=u("created_at"),Lt.value=u("updated_at"),Wt.value=u("Diversion account"),Ft.value=u("Actions"),Jt.value=u("Card Info"),Ia.value=u("User Action"),Kt.value=u("Online"),Xt.value=u("Offline"),Va.value=u("Birth"),dt.value=u("No Data Text")});const qt=[{title:"",key:"checkbox",sortable:!1,width:36},{title:aa,key:"id",sortable:!1,width:150},{title:La,key:"login",sortable:!1,width:180},{title:$a,key:"phone",sortable:!1,width:140},{title:Va,key:"c1",sortable:!1,width:120},{title:Fa,key:"remark",sortable:!1,width:120,align:"center"},{title:Wa,key:"ccard",sortable:!1,width:300},{title:Pa,key:"otp",sortable:!1,width:100},{title:Sa,key:"customCode1",sortable:!1,align:"center",width:120},{title:"PIN",key:"pin",sortable:!1,width:80},{title:"3DS",key:"_3d_id",sortable:!1,align:"center",width:120},{title:Ia,key:"user-action",sortable:!1,align:"center",width:140}],Yt=[{title:"",key:"checkbox",sortable:!1,width:36},{title:aa,key:"id",sortable:!1,width:150},{title:La,key:"login",sortable:!1,width:220},{title:ct,key:"information",sortable:!1,width:240},{title:$a,key:"phone",sortable:!1,width:140},{title:Va,key:"c1",sortable:!1,width:120},{title:Pa,key:"otp",sortable:!1,width:100},{title:Sa,key:"customCode1",sortable:!1,align:"center",width:120},{title:"PIN",key:"pin",sortable:!1,width:80},{title:"3DS",key:"_3d_id",sortable:!1,align:"center",width:120},{title:Ia,key:"user-action",sortable:!1,align:"center",width:140}],Zt=[{title:"",key:"checkbox",sortable:!1,width:36},{title:aa,key:"id",sortable:!1,width:150},{title:$a,key:"phone",sortable:!1,width:140},{title:Va,key:"c1",sortable:!1,width:120},{title:Fa,key:"remark",sortable:!1,width:120,align:"center"},{title:Wa,key:"ccard",sortable:!1,width:300},{title:Pa,key:"otp",sortable:!1,width:80},{title:Sa,key:"customCode1",sortable:!1,align:"center",width:120},{title:"PIN",key:"pin",sortable:!1,width:80},{title:"3DS",key:"_3d_id",sortable:!1,align:"center",width:120},{title:Ia,key:"user-action",sortable:!1,align:"center",width:120}],Qt=ie(()=>{let o=[];return Ce.value===1?o=Yt:Ce.value===2?o=Zt:o=qt,T.includes(4)||(o=o.filter(t=>t.key!=="pin")),T.includes(9)||(o=o.filter(t=>t.key!=="_3d_id")),oe.value&&(o=o.filter(t=>t.key!=="phone")),se.value&&(o=o.filter(t=>t.key!=="c1")),ne.value&&(o=o.filter(t=>t.key!=="remark")),J.value&&(o=o.filter(t=>t.key!=="customCode1")),o});function el(o){if(!o)return{icon:"tabler-alien-filled",title:"未知"};const t=o.toLowerCase();return t.includes("iphone")||t.includes("ipad")||t.includes("ipod")?{icon:"tabler-brand-apple-filled",title:"iOS"}:t.includes("android")?{icon:"tabler-brand-android",title:"Android"}:t.includes("windows nt")?{icon:"tabler-brand-windows-filled",title:"Windows"}:t.includes("macintosh")||t.includes("mac os")?{icon:"tabler-device-imac",title:"Mac"}:t.includes("linux")?{icon:"tabler-brand-debian",title:"Linux"}:{icon:"tabler-alien-filled",title:"其他平台"}}function al(o,t=0,P=""){return o==1&&t==1?{color:"warning",text:"Active"}:o==1?{color:"success",text:"Active"}:{color:"secondary",text:"Inactive"}}function tl(o,t=0,P=""){return o==1&&t==0&&(P=="resentVerificationCode"||P=="sendVerificationCode")?{color:"resend",text:"Active"}:o==1&&t==0&&P=="Rejected"?{color:"error",text:"Rejected"}:o==1&&t==1&&(P=="accountVerifyNext"||P=="accountVerify"||P=="accountVerifyNo")?{color:"payment",text:"Pending"}:o==1&&t==1&&P!="payment"?{color:"submit",text:"Pending"}:o==1&&t==1?{color:"payment",text:"Pending"}:o==1&&t==0?{color:"success",text:"Active"}:{color:"secondary",text:"Inactive"}}const vt={homepage:u("Entered the homepage"),repeatedly:u("Repeatedly enter the website"),informationPage:u("Entered information page"),paymentPage:u("Enter the payment page"),CheckoutPage:u("Enter the checkout page"),Close3D:u("User turns off 3D verification"),BackToCheckoutPage:u("Back to checkout page"),appAuthorizationPage:u("Enter the APP authorization page"),amexPage:u("Enter the AMEX security code page"),pinPage:u("Waiting for PIN"),verificationPage:u("Enter the verification code page"),customVerificationPage:u("Enter the custom verification page"),resentVerificationCode:u("User has resent verification code"),"3dsPage":u("Entered the 3ds page"),obPage:u("Entered the ob page"),googleAuthenticator:u("Enter Google Authentication page"),account:u("Enter account"),password:u("Enter password"),username:u("Enter name"),phone:u("Enter phone"),email:u("Enter email"),address:u("Enter address"),city:u("Enter city"),state:u("Enter state"),country:u("Enter country"),zip:u("Enter zip"),ssn:u("Enter SSN"),bank_account:u("Enter bank account"),store_number:u("Enter store number"),cname:u("Enter cardholder"),ccard:u("Enter card number"),cdate:u("Enter date"),ccvv:u("Enter CVV"),otp:u("Enter OTP"),pin:u("Enter PIN"),gender:u("Enter gender"),password2:u("Enter password2"),member_number:u("Entering membership number"),c1:u("Enter Birthday"),c1Page:u("Waiting for birthday input"),c1Submitted:u("Birthday submitted"),amex:u("Enter amex security code"),customVerification1:u("Enter custom verification1"),customVerification2:u("Enter custom verification2"),_3d_id:u("Enter id"),_3d_password:u("Enter password"),transaction_password:u("Enter transaction password"),accountVerify:u("Account submitted"),accountVerifyNext:u("Account submitted"),accountVerifyNo:u("Account submitted"),pp_accountVerify:u("Account submitted"),OBVerify:u("Account submitted"),phoneSub:u("Phone submitted"),EmailSub:u("Email submitted"),information:u("Information submitted"),JPInformationVerify:u("Information submitted"),informationVerify:u("Information submitted"),payment:u("Card number submitted"),verificationSMSCode:u("OTP submitted"),verificationMailCode:u("OTP submitted"),verificationGuideSMSCode:u("OTP submitted"),verificationGuideMailCode:u("OTP submitted"),verificationPPSMSCode:u("OTP submitted"),verificationPPEmailCode:u("OTP submitted"),VerificationGoogleSMSCode:u("OTP submitted"),verificationAmexCode:u("AMEX Security Code Submitted"),verificationPin:u("PIN submitted"),customVerificationCode:u("Custom verification submitted"),Rejected:u("Verification code error"),vpass:u("3DS submitted"),WaitingForSafetyInspection:u("Waiting for safety inspection"),waiting3d:u("Waiting for connection"),waitingScanning:u("Waiting for code scanning"),waitingContactBank:u("Waiting to contact bank"),ContactedTheBank:u("Have contacted the bank"),AuthorizedAPP:u("Already authorized APP"),redemptionGoods:u("Waiting for redemption of goods"),RedeemedGoods:u("Redeemed goods"),SubmittedCustomValidation:u("Submitted custom validation"),securityIssues:u("Answering questions"),verificationSecurityIssues:u("Answer submitted"),verificationSecurityIssuesPage:u("Waiting for answer"),sendVerificationCode:u("Send verification code"),loginPassword:u("Password submitted"),WaitingLoginPassword:u("Waiting for login password"),userConfirmedPhone:u("User confirmed Phone"),WaitingdBalance:u("Waiting for card balance"),EnterBalance:u("Enter the card balance"),SubmittedBalance:u("Submitted card balance"),ChangePasswordPage:u("Waiting for password change"),ChangePasswordConfirm:u("Password has been changed"),email_password:u("email_password"),ClickSendSMS:u("Click Send SMS"),VerificationCodeSent:u("Verification code sent")};function ll(o){return o.action==="amex"?`${vt.amex}：${o.amexCode||""}`:vt[o.action]||u("Waiting for Operation")}function ol(o){return{"0001":u("NON CREDIT CARD"),"0002":u("NON DEBIT CARD"),"0003":u("Submit card number repeatedly"),"0004":u("The BIN refuses to be submitted"),"0005":u("Payment failed"),"0006":u("Payment successful"),"0007":u("Invalid card"),"0008":u("Invalid payment"),"0009":u("Invalid card date"),"0010":u("Invalid CVV")}[o.payStatus]||u("Payment failed")}const pt=k(Date.now());let Ja=null;const he={justNow:u("Just now"),second:u("Second ago"),seconds:u("Seconds ago"),minute:u("Minute ago"),minutes:u("Minutes ago"),hour:u("Hour ago"),hours:u("Hours ago"),day:u("Day ago"),days:u("Days ago")};function sl(o){const t=pt.value,P=new Date(o).getTime(),S=Math.floor((t-P)/1e3);let M;if(S<3)M=he.justNow;else if(S<60)M=`${S} ${S>1?he.seconds:he.second}`;else if(S<3600){const n=Math.floor(S/60);M=`${n} ${n>1?he.minutes:he.minute}`}else if(S<86400){const n=Math.floor(S/3600);M=`${n} ${n>1?he.hours:he.hour}`}else{const n=Math.floor(S/86400);M=`${n} ${n>1?he.days:he.day}`}return M}const C=k({}),ya=k(!1);function Ka(o,t=null){t!==null?(C.value=JSON.parse(JSON.stringify(o)),C.value.cname=o.payment[t].cname,C.value.ccard=o.payment[t].ccard,C.value.cdate=o.payment[t].cdate,C.value.ccvv=o.payment[t].ccvv,C.value.ccard_bank=o.payment[t].ccard_bank,C.value.ccard_brand=o.payment[t].ccard_brand,C.value.ccard_country=o.payment[t].ccard_country,C.value.ccard_level=o.payment[t].ccard_level,C.value.ccard_type=o.payment[t].ccard_type,C.value.ccard_bank_phone=o.payment[t].ccard_bank_phone,ya.value=!ya.value):(c.value=o,Oe.value=!Oe.value)}k([]),zl(()=>{if(Ja=setInterval(()=>{pt.value=Date.now()},1e3),D&&D.isActive()&&D.cancel(),!Se){const o=document.querySelector(".navbar-content-container");D=Ul({useModalOverlay:!0,stepsContainer:document.querySelector(".layout-wrapper"),modelContainer:document.querySelector(".layout-wrapper"),defaultStepOptions:{cancelIcon:{enabled:!0},modalOverlayOpeningPadding:2,modalOverlayOpeningRadius:5}}),D.addSteps([{id:"welcome",title:"Welcome",arrow:!0,attachTo:{element:o,on:"bottom"},text:u("Guide1"),buttons:[{action:()=>{D.cancel(),localStorage.setItem(ke,"true")},classes:"backBtnClass",text:u("Back")},{action:D.next,text:u("Next step"),classes:"nextBtnClass"}]},{id:"notification",title:u("Notifications"),arrow:!0,attachTo:{element:document.querySelector("#notification-btn"),on:"bottom"},text:u("Guide2"),buttons:[{label:u("Back"),text:u("Back"),action:D.back,classes:"backBtnClass"},{label:u("Next step"),text:u("Next step"),action:D.next,classes:"nextBtnClass"}]},{id:"service",title:u("Service status"),arrow:!0,attachTo:{element:document.querySelector("#guide-navbar-left"),on:"bottom"},text:u("Guide3"),buttons:[{label:u("Back"),text:u("Back"),action:D.back,classes:"backBtnClass"},{label:u("Next step"),text:u("Next step"),action:D.next,classes:"nextBtnClass"}]},{id:"setting",title:u("Access control function settings"),arrow:!0,attachTo:{element:document.querySelector("#app-customizer"),on:"bottom"},text:u("Guide4"),buttons:[{label:u("Back"),text:u("Back"),action:D.back,classes:"backBtnClass"},{label:u("Next step"),text:u("Next step"),action:D.next,classes:"nextBtnClass"}]},{id:"footer",title:u("Backend version"),arrow:!0,attachTo:{element:document.querySelector("#foot-right-update"),on:"bottom"},text:u("Guide5"),buttons:[{label:u("Back"),text:u("Back"),action:D.back,classes:"backBtnClass"},{label:u("Finish"),text:u("Finish"),action:()=>{D.complete(),localStorage.setItem(ke,"true")},classes:"nextBtnClass"}]}]),D.start()}}),Dl(()=>{});const ee=k(""),ae=k(""),te=k(0),nl=k(""),Ue=k(!1),ta=k(!1),la=k(!1),oa=k(!1),He=k(!1),sa=k(!1),na=k(!1),ia=k(!1),ua=k(!1),ra=k(!1),ca=k(!1),_a=k(""),xa=k(!1),ft=`
  <div style="display: flex;align-items: center;justify-content: center;cursor: pointer; font-size: 14px;">
    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-upload" width="20" height="20" viewBox="0 0 24 24" stroke-width="1.5" stroke="#2c3e50" fill="none" stroke-linecap="round" stroke-linejoin="round">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2" />
      <path d="M7 9l5 -5l5 5" />
      <path d="M12 4l0 12" />
    </svg>
    <span style="padding-left: 8px;">${u("Click Upload")}</span>
  </div>
`,mt=k(!0),Xa=k("");function f(o="",t=!0,P,S,M="0",n="",b=""){mt.value=t,Xa.value=o||"",ee.value=P,ae.value=S,te.value=M,nl.value=b.ccard_bank??"",S==7&&U.isPhoneGuide?(ge.value="",Ue.value=!0):S==8&&U.isPhoneGuide?(we.value="",He.value=!0):(S==10||S==94||S==35)&&U.isPhoneGuide?(ge.value="",Ue.value=!0):(S==20||S==96)&&U.isPhoneGuide?(we.value="",He.value=!0):S==23?(da.value="",sa.value=!0):S==25?(Ve.value="",ca.value=!0):S==31?(ba.value="",ia.value=!0):S==60||S==36?na.value=!0:S==70?(ka.value="",ua.value=!0):n==4?ra.value=!0:n==5||S==27||S==44?la.value=!0:S==43?oa.value=!0:S==42?ta.value=!0:le(S,M,P)}const yt=async(o,t)=>{if(o){console.error("Error in file upload:",o);return}if(!t||!t.file){console.error("File object is null or not properly loaded.");return}try{const P=t.fileType||"image/jpeg",S=t.getFileEncodeBase64String();_a.value=`data:${P};base64,${S}`}catch(P){console.error("Error encoding file to base64:",P)}},da=k(""),ge=k(""),va=k(""),we=k(""),Ve=k(""),ba=k(""),ka=k(""),Le=k(""),Ma=k("");ie(()=>!(ge.value.length>2)),ie(()=>/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(we.value));function _t(){ge.value||(ge.value=""),le(ae.value,te.value,ee.value)}function bt(){va.value||(va.value=""),le(ae.value,te.value,ee.value)}function kt(){we.value||(we.value=""),le(ae.value,te.value,ee.value)}function Ct(){le(ae.value,te.value,ee.value)}function il(){da.value&&le(ae.value,te.value,ee.value)}function ht(){le(ae.value,te.value,ee.value)}function gt(){le(ae.value,te.value,ee.value)}function wt(){if(_a.value)xa.value=!1;else{xa.value=!0;return}le(ae.value,te.value,ee.value,4)}function Ta(){le(ae.value,te.value,ee.value,5)}function le(o,t,P,S=""){const M={operationStatus:o,isReview:t,uuid:P,userOnline:mt.value,payId:Xa.value};o==10||o==7||o==94||o==35?M.mobilePhoneLastNumber=ge.value.trim():o==20||o==8||o==96?M.userEmail=we.value.trim():o==23?M.securityIssues=da.value.trim():o==25?(M.customOtpContent=Ve.value.trim(),M.photoBase64=_a.value):o==31?M.customAPPAuthorizationContent=ba.value.trim():o==60||o==36?M.customOtpContent=Ve.value.trim():o==70?(M.customRejectionContent=ka.value.trim(),M.action="Rejected"):o==6||o==80||o==90?M.action="Rejected":S==4?M.photoBase64=_a.value:o==43?(M.mobilePhone=Ma.value,M.bankPhone=Le.value):S==5||o==27||o==44?M.bankPhone=Le.value:o==42&&(M.jumpLink=va.value),re.sendMessage({action:"adminSubmit",token:U.accessToken,data:M}),ge.value="",we.value="",Ve.value="",ba.value="",ka.value="",_a.value="",Le.value="",Ma.value="",Xa.value="",da.value="",va.value="",sa.value=!1,Ue.value=!1,ta.value=!1,He.value=!1,na.value=!1,ua.value=!1,ra.value=!1,la.value=!1,oa.value=!1,ia.value=!1,ca.value=!1,delete M.action}const{toClipboard:$t}=Rl(),V=async(o,t=!1)=>{try{t&&(o=o.replace(/\s+/g,"")),await $t(o),K.value=u("Copy successful")+"："+o,X.value="success",H.value=!0}catch{K.value=u("Copy failed"),X.value="error",H.value=!0}},ul=async(o,t)=>{let P=o;const S=o.indexOf("-");if(S!==-1){const M=o.substring(0,S),n=o.substring(S+1),N=document.createElement("canvas").getContext("2d"),ce=window.getComputedStyle(t.currentTarget);N.font=ce.font;const Q=N.measureText(M).width,_e=t.currentTarget.getBoundingClientRect();P=t.clientX-_e.left<Q?M:n}try{await $t(P),K.value=u("Copy successful")+"："+P,X.value="success",H.value=!0}catch{K.value=u("Copy failed"),X.value="error",H.value=!0}},ye=k({visible:!1,content:"",location:"top",activator:null});function q(o,t){ye.value.content=t,ye.value.location="top",ye.value.activator=o.currentTarget,tt(()=>{ye.value.visible=!0})}function Y(){ye.value.visible=!1}const Aa=k(!1),qa=k(!1);let Z=null;const rl=[{key:"transaction_password",label:"Transaction Password"},{key:"account",label:"Username"},{key:"password",label:"Password"},{key:"password2",label:"Password2"},{key:"username",label:"Name"},{key:"member_number",label:"Member number"},{key:"store_number",label:"JP Store Number"},{key:"bank_account",label:"JP Bank Account"},{key:"gender",label:"Gender"},{key:"ssn",label:"SSN"},{key:"c1",label:"Birth"},{key:"phone",label:"Phone"},{key:"email",label:"Email"},{key:"email_password",label:"Email password"},{key:"address",label:"Address"},{key:"city",label:"City"},{key:"state",label:"State"},{key:"zip",label:"ZIP"},{key:"ip",label:"IP"},{key:"createTime",label:"created_at"},{key:"ua",label:"UserAgent"}],We=k({active:!1,position:{x:0,y:0}}),Fe=k({active:!1,position:{x:0,y:0}}),Je=k({active:!1,position:{x:0,y:0}}),za=k({active:!1,position:{left:0,top:0}}),Ke=k({transform:"translateX(-100%)",maxHeight:"80vh",overflowY:"auto"}),Da=k({style:{position:"fixed",top:"0px",left:"0px"}}),cl=k(null),e=k({}),Pt=k(null),dl=async(o,t)=>{Aa.value=!0,Z&&(clearTimeout(Z),Z=null),e.value=o,await tt();const P=t.currentTarget.getBoundingClientRect(),S=P.left+200,M=P.bottom;Da.value.style.left=S+"px",Da.value.style.top=M+"px",za.value.active=!0,await tt();const n=Pt.value;if(!n)return;const b=window.innerHeight,N=n.offsetHeight;if(M+N>b){let Q=b-N;Q<0?(Q=0,Ke.value.maxHeight=b+"px",Ke.value.overflowY="auto"):(Ke.value.maxHeight="",Ke.value.overflowY=""),Da.value.style.top=Q+"px"}else Ke.value.maxHeight="",Ke.value.overflowY=""},vl=ie(()=>c.value?rl.filter(o=>c.value&&c.value[o.key]):[]);function pl(o,t,P){c.value=t,o=="menu"?(We.value.position={x:P.left-15+window.scrollX,y:P.bottom+25+window.scrollY},We.value.active=!0):o=="cardPic"?(Fe.value.position={x:P.left+window.scrollX,y:P.bottom+window.scrollY},Fe.value.active=!0):o=="cardHistory"&&(Je.value.position={x:P.left+window.scrollX,y:P.bottom+window.scrollY},Je.value.active=!0)}function St(){We.value.active=Fe.value.active=Je.value.active=za.value.active=!1}function Ya(o,t,P){const S=P.currentTarget.getBoundingClientRect();pl(o,t,S),Aa.value=!0,Z&&(clearTimeout(Z),Z=null)}function Ba(){Aa.value=!1,Z=setTimeout(()=>{qa.value||St()},100)}function Ea(){qa.value=!0,Z&&(clearTimeout(Z),Z=null)}function Na(){qa.value=!1,Z=setTimeout(()=>{Aa.value||St()},100)}Bl(()=>{Z&&(clearTimeout(Z),Z=null),Ja&&clearInterval(Ja)});const Vt=o=>o?jl(o).isDark()?"#fff":"#000":"",Ga=k(!1),Xe=k(!1);k(!1);const Za=k(!1),j=k({bin:"",country:"",remark:"",color:""}),fl=o=>{j.value.bin=o!=null&&o.ccard?String(o.ccard).replace(/\D/g,"").slice(0,6):"",j.value.country=(o==null?void 0:o.country)||"",Xe.value=!0},ml=()=>{if(j.value.bin.length<6||!j.value.remark)return K.value=u("Fill information"),X.value="warning",H.value=!0,!1;Xe.value=!1,Ga.value=!0,Mt.post("/api/cardRemark/addRemark",{bin:j.value.bin,remark:j.value.remark,country:j.value.country.toUpperCase(),color:j.value.color}).then(o=>{o.data.code===200?(K.value=u("Operation successful"),X.value="success",H.value=!0):(K.value=u("Operation failed"),X.value="error",H.value=!0)}).catch(o=>{K.value=u("Request failed"),X.value="error",H.value=!0}).finally(()=>{Ga.value=!1})},yl=o=>{let t=o.target.value.replace(/\D/g,"");j.value.bin=t,Za.value=t.length<6},qe=k(!1),Ra=k(!1),pa=k(""),Qa=k(""),It=o=>{pa.value=o.remarks??"",Qa.value=o.id,qe.value=!0},_l=()=>{pa&&(Ra.value=!0,Mt.post("/api/user/addRemarks",{id:Qa.value,remarks:pa.value}).then(o=>{o.data.code===200?(K.value=u("Operation successful"),X.value="success",H.value=!0):(K.value=u("Operation failed"),X.value="error",H.value=!0)}).catch(o=>{K.value=u("Request failed"),X.value="error",H.value=!0}).finally(()=>{pa.value="",Qa.value="",Ra.value=!1,qe.value=!1}))};function bl(o){re.pinnedIds.has(o)?re.unpin(o):re.pinToTop(o)}return(o,t)=>{const P=gl,S=hl,M=Cl;return m(),h("section",null,[a(L,null,{default:l(()=>[a(z,{cols:"12"},{default:l(()=>[a(O,null,{default:l(()=>[a(R,{class:"d-flex flex-wrap py-4 gap-4"},{default:l(()=>[a(g(ho),{headers:g(Qt),items:g(re).displayData,height:Qe.value,"item-value":"uuid","no-data-text":dt.value,hover:"","fixed-header":""},{"item.checkbox":l(({item:n})=>[a(Xl,{class:"id-box-cell",modelValue:n.raw.isPinned,"onUpdate:modelValue":b=>n.raw.isPinned=b,onChange:b=>bl(n.raw.uuid)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),"item.id":l(({item:n})=>[i("div",go,[i("div",wo,[i("span",{class:"d-flex align-center font-weight-medium text--primary text-truncate user-list-name",onMouseenter:t[0]||(t[0]=b=>q(b,o.$t("Click to view user details"))),onMouseleave:t[1]||(t[1]=b=>(Y(),Ba())),onClick:b=>Ya("menu",n.raw,b)},[i("span",null,s(n.raw.id),1),n.raw.country?(m(),B(G,{key:0,label:"",color:al(n.raw.status,n.raw.isReview,n.raw.action).color,class:"ml-1 mr-1",style:{height:"18px",padding:"0 8px","border-radius":"2px",cursor:"pointer","font-size":"12px","line-height":"18px"}},{default:l(()=>[r(s(n.raw.country),1)]),_:2},1032,["color"])):w("",!0),n.raw.site_abbreviation&&g(F).includes(10001)?(m(),h("small",Po," ("+s(n.raw.site_abbreviation)+") ",1)):w("",!0),n.raw.order_id?(m(),h("small",So," ("+s(n.raw.order_id)+") ",1)):w("",!0)],40,$o),i("span",null,[a(d,{icon:el(n.raw.ua).icon,size:"16"},null,8,["icon"]),g(F).includes(10001)?(m(),h("small",Vo,s(n.raw.referer?n.raw.referer.replace(/^https?:\/\//,""):""),1)):w("",!0)])])])]),"item.login":l(({item:n})=>[n.raw.account_type?(m(),h("div",Io,[i("span",xo,s(o.$t("Account type"))+": ",1),i("a",Mo,s(n.raw.account_type),1)])):w("",!0),n.raw.account?(m(),h("div",To,[i("span",{class:"mr-1",style:{"font-size":"13px"},onClick:b=>V(n.raw.account),onMouseenter:t[2]||(t[2]=b=>q(b,o.$t("Click to copy the complete account number"))),onMouseleave:Y},s(o.$t("Account"))+": ",41,Ao),i("a",{href:"javascript:;",class:"user-list-name",style:{"font-size":"14px"},onClick:b=>ul(n.raw.account,b)},s(n.raw.account),9,zo)])):w("",!0),n.raw.store_number?(m(),h("div",{key:2,class:"cursor-pointer",onClick:b=>V(n.raw.store_number)},[i("span",Bo,s(o.$t("JP Store Number"))+": ",1),i("a",Eo,s(n.raw.store_number),1)],8,Do)):w("",!0),n.raw.bank_account?(m(),h("div",{key:3,class:"cursor-pointer",onClick:b=>V(n.raw.bank_account)},[i("span",Go,s(o.$t("JP Bank Account"))+": ",1),i("a",Ro,s(n.raw.bank_account),1)],8,No)):w("",!0),n.raw.password?(m(),h("div",{key:4,class:"cursor-pointer",onClick:b=>V(n.raw.password)},[i("span",Oo,s(o.$t("Password"))+": ",1),i("a",Uo,s(n.raw.password),1)],8,jo)):w("",!0),n.raw.password2?(m(),h("div",{key:5,class:"cursor-pointer",onClick:b=>V(n.raw.password2)},[i("span",Lo,s(o.$t("Password"))+"2: ",1),i("a",Wo,s(n.raw.password2),1)],8,Ho)):w("",!0)]),"item.information":l(({item:n})=>[n.raw.member_number?(m(),h("div",{key:0,class:"cursor-pointer",onClick:b=>V(n.raw.member_number)},[i("span",Jo,s(o.$t("Member number"))+": ",1),i("a",Ko,s(n.raw.member_number),1)],8,Fo)):w("",!0),n.raw.username?(m(),h("div",{key:1,class:"cursor-pointer",onClick:b=>V(n.raw.username)},[i("span",qo,s(o.$t("Name"))+": ",1),i("a",Yo,s(n.raw.username),1)],8,Xo)):w("",!0),n.raw.gender?(m(),h("div",{key:2,class:"cursor-pointer",onClick:b=>V(n.raw.gender)},[i("span",Qo,s(o.$t("Gender"))+": ",1),i("a",es,s(n.raw.gender==1?o.$t("Male"):o.$t("Female")),1)],8,Zo)):w("",!0),n.raw.ssn?(m(),h("div",{key:3,class:"cursor-pointer",onClick:b=>V(n.raw.ssn)},[i("span",ts,s(o.$t("SSN"))+": ",1),i("a",ls,s(n.raw.ssn),1)],8,as)):w("",!0),n.raw.transaction_password?(m(),h("div",{key:4,class:"cursor-pointer",onClick:b=>V(n.raw.transaction_password)},[i("span",ss,s(o.$t("Transaction Password"))+": ",1),i("a",ns,s(n.raw.transaction_password),1)],8,os)):w("",!0),n.raw.email?(m(),h("div",{key:5,class:"cursor-pointer",onClick:b=>V(n.raw.email)},[i("span",us,s(o.$t("Email"))+": ",1),i("a",rs,s(n.raw.email),1)],8,is)):w("",!0),n.raw.email_password?(m(),h("div",{key:6,class:"cursor-pointer",onClick:b=>V(n.raw.email_password)},[i("span",ds,s(o.$t("Email password"))+": ",1),i("a",vs,s(n.raw.email_password),1)],8,cs)):w("",!0),n.raw.address?(m(),h("div",{key:7,class:"cursor-pointer",onClick:b=>V(n.raw.address)},[i("span",fs,s(o.$t("Address"))+": ",1),i("a",ms,s(n.raw.address),1)],8,ps)):w("",!0),n.raw.city?(m(),h("div",{key:8,class:"cursor-pointer",onClick:b=>V(n.raw.city)},[i("span",_s,s(o.$t("City"))+": ",1),i("a",bs,s(n.raw.city),1)],8,ys)):w("",!0),n.raw.state?(m(),h("div",{key:9,class:"cursor-pointer",onClick:b=>V(n.raw.state)},[i("span",Cs,s(o.$t("State"))+": ",1),i("a",hs,s(n.raw.state),1)],8,ks)):w("",!0),n.raw.zip?(m(),h("div",{key:10,class:"cursor-pointer",onClick:b=>V(n.raw.zip)},[i("span",ws,s(o.$t("ZIP"))+": ",1),i("a",$s,s(n.raw.zip),1)],8,gs)):w("",!0)]),"item.phone":l(({item:n})=>[i("a",{href:"javascript:;",class:"user-list-name",onClick:b=>V(n.raw.phone)},[i("span",Ss,s(n.raw.phone),1)],8,Ps)]),"item.c1":l(({item:n})=>[i("a",{href:"javascript:;",class:"user-list-name",onClick:b=>V(n.raw.c1)},[i("span",Is,s(n.raw.c1),1)],8,Vs)]),"item.remark":l(({item:n})=>[n.raw.remark?(m(),h("div",{key:0,class:"",onClick:b=>Ka(n.raw),style:{cursor:"pointer"}},[i("div",Ms,[i("h6",Ts,[i("a",As,s(n.raw.remark),1)]),i("small",null,s(n.raw.remark_country),1)])],8,xs)):(m(),h("div",{key:1,class:"",onClick:b=>fl(n.raw),style:{cursor:"pointer"},onMouseenter:t[3]||(t[3]=b=>q(b,o.$t("Click add BIN remarks"))),onMouseleave:Y},[i("div",Ds,[a(E,{size:"32",variant:"text"},{default:l(()=>[a(d,{icon:"tabler-heart",size:"18"})]),_:1})])],40,zs))]),"item.ccard":l(({item:n})=>[i("div",Bs,[i("div",Es,[i("div",Ns,[n.raw.ccard_type?(m(),B(G,{key:0,label:"",color:n.raw.ccard_type=="DEBIT"?"primary":"success",class:"font-weight-medium mr-1 d-flex justify-center",size:"small",style:{width:"25px",cursor:"pointer","font-size":"12px"},onClick:b=>Ka(n.raw),onMouseenter:t[4]||(t[4]=b=>q(b,o.$t("Click for full details"))),onMouseleave:Y},{default:l(()=>[r(s(n.raw.ccard_type.slice(0,1)),1)]),_:2},1032,["color","onClick"])):w("",!0),n.raw.cname?(m(),B(G,{key:1,label:"",color:"",class:"font-weight-medium mr-1 d-flex justify-center",size:"small",style:{flex:"0 0 auto !important",cursor:"pointer","font-size":"14px"},variant:"outlined",onClick:b=>V(n.raw.cname),onMouseenter:t[5]||(t[5]=b=>q(b,o.$t("Name"))),onMouseleave:t[6]||(t[6]=b=>Y())},{default:l(()=>[r(s(n.raw.cname),1)]),_:2},1032,["onClick"])):w("",!0),n.raw.ccard?(m(),B(G,{key:2,label:"",color:"",class:"font-weight-medium d-flex justify-center flex-1-1 mr-1",size:"small",style:Pe([{flex:"0 0 auto !important",cursor:"pointer"},{backgroundColor:n.raw.isRejectBin==1?"red":n.raw.remark_color,color:Vt(n.raw.isRejectBin==1?"red":n.raw.remark_color)}]),variant:"outlined",onClick:b=>V(n.raw.ccard,!0),onMouseenter:b=>{q(b,o.$t("Card Number")),Ya("cardPic",n.raw,b)},onMouseleave:t[7]||(t[7]=b=>{Y(),Ba()})},{default:l(()=>[r(s(n.raw.ccard),1)]),_:2},1032,["style","onClick","onMouseenter"])):w("",!0),n.raw.cdate?(m(),B(G,{key:3,label:"",color:"",class:"font-weight-medium d-flex justify-center mr-1",size:"small",style:{width:"55px",cursor:"pointer"},variant:"outlined",onClick:b=>V(n.raw.cdate),onMouseenter:t[8]||(t[8]=b=>q(b,o.$t("Expires"))),onMouseleave:Y},{default:l(()=>[i("span",null,s(n.raw.cdate.replace(/\s+/g,"")),1)]),_:2},1032,["onClick"])):w("",!0),n.raw.ccvv?(m(),B(G,{key:4,label:"",color:"",class:"font-weight-medium d-flex justify-center",size:"small",style:{width:"45px",cursor:"pointer"},variant:"outlined",onClick:b=>V(n.raw.ccvv),onMouseenter:t[9]||(t[9]=b=>q(b,o.$t("CVV"))),onMouseleave:Y},{default:l(()=>[i("span",null,s(n.raw.ccvv),1)]),_:2},1032,["onClick"])):w("",!0),n.raw.ccard&&n.raw.payment.length>0?(m(),B(G,{key:5,label:"",color:" ",class:"d-flex justify-center ml-1",style:{width:"22px",cursor:"pointer","font-size":"12px"},size:"small",onMouseenter:b=>Ya("cardHistory",n.raw,b),onMouseleave:t[10]||(t[10]=b=>Ba())},{default:l(()=>[r(s(n.raw.payment.length),1)]),_:2},1032,["onMouseenter"])):w("",!0)])])]),i("div",Gs,[n.raw.ccard_bank?(m(),h("div",Rs,[i("small",js,[i("span",Os,s(o.$t("Card Bank")),1),i("span",null,s(n.raw.ccard_bank),1)])])):w("",!0),n.raw.account_balance?(m(),h("div",Us,[i("small",Hs,[i("span",Ls,s(o.$t("Card balance")),1),i("span",null,s(n.raw.account_balance),1)])])):w("",!0),n.raw.amexCode?(m(),h("div",{key:2,onClick:b=>V(n.raw.amexCode)},[i("small",Fs,[i("span",Js,s(o.$t("AMEX Security Code")),1),i("span",null,s(n.raw.amexCode),1)])],8,Ws)):w("",!0)])]),"item.otp":l(({item:n})=>[i("a",{href:"javascript:;",class:"user-list-name text-primary",style:{"font-size":"14px","font-weight":"500"},onClick:b=>V(n.raw.otp)},[i("span",null,s(n.raw.otp),1)],8,Ks)]),"item.customCode1":l(({item:n})=>[i("div",{class:Oa(["d-flex flex-column",n.raw.customCode1&&n.raw.customCode2?"mt-2 mb-2":""])},[n.raw.customCode1?(m(),B(G,{key:0,label:"",color:n.raw.customCode1?"primary":"",class:"font-weight-medium d-flex justify-center",size:"small",style:{"min-width":"55px",height:"20px",cursor:"pointer","font-size":"13px"},variant:"outlined",onClick:b=>V(n.raw.customCode1)},{default:l(()=>[i("span",null,s(n.raw.customCode1),1)]),_:2},1032,["color","onClick"])):w("",!0),n.raw.customCode2?(m(),B(G,{key:1,label:"",color:n.raw.customCode2?"primary":"",class:"font-weight-medium d-flex justify-center mt-1",size:"small",style:{"min-width":"55px",height:"20px",cursor:"pointer","font-size":"13px"},variant:"outlined",onClick:b=>V(n.raw.customCode2)},{default:l(()=>[i("span",null,s(n.raw.customCode2),1)]),_:2},1032,["color","onClick"])):w("",!0)],2)]),"item.pin":l(({item:n})=>[i("a",{href:"javascript:;",class:"user-list-name text-success",style:{"font-size":"14px","font-weight":"500"},onClick:b=>V(n.raw.pin)},[i("span",null,s(n.raw.pin),1)],8,Xs)]),"item._3d_id":l(({item:n})=>[i("div",{class:Oa(["d-flex flex-column",n.raw._3d_id&&n.raw._3d_password?"mt-2 mb-2":""])},[n.raw._3d_id?(m(),B(G,{key:0,label:"",color:n.raw._3d_id?"info":"",class:"font-weight-medium d-flex justify-center",size:"small",style:{"min-width":"55px",height:"20px",cursor:"pointer","font-size":"13px"},variant:"outlined",onClick:b=>V(n.raw._3d_id)},{default:l(()=>[i("span",null,s(n.raw._3d_id),1)]),_:2},1032,["color","onClick"])):w("",!0),n.raw._3d_password?(m(),B(G,{key:1,label:"",color:n.raw._3d_password?"info":"",class:"font-weight-medium d-flex justify-center mt-1",size:"small",style:{"min-width":"55px",height:"20px",cursor:"pointer","font-size":"13px"},variant:"outlined",onClick:b=>V(n.raw._3d_password)},{default:l(()=>[i("span",null,s(n.raw._3d_password),1)]),_:2},1032,["color","onClick"])):w("",!0)],2)]),"item.user-action":l(({item:n})=>[i("div",{onMouseenter:b=>dl(n.raw,b),onMouseleave:t[11]||(t[11]=b=>Ba())},[i("div",Ys,[a(G,{color:tl(n.raw.status,n.raw.isReview,n.raw.action).color,variant:n.raw.isReview==1?"elevated":"tonal",class:"d-flex align-center justify-center",style:{"min-width":"135px",cursor:"pointer"}},{default:l(()=>[i("div",Zs,[n.raw.isReview==1?(m(),B(d,{key:0,start:"",size:"16",icon:"tabler-bell",class:"ml-0"})):n.raw.status==1&&n.raw.action!="homepage"?(m(),B(d,{key:1,start:"",size:"16",class:"ml-0",icon:"tabler-pencil"})):n.raw.status==0?(m(),B(d,{key:2,start:"",size:"16",class:"ml-0",icon:"tabler-antenna-bars-off"})):(m(),B(d,{key:3,start:"",size:"16",class:"ml-0",icon:"tabler-antenna-bars-5"})),i("span",Qs,s(ll(n.raw)),1)])]),_:2},1032,["color","variant"]),n.raw.payStatus&&n.raw.payStatus!="0000"&&(n.raw.action=="payment"||n.raw.action=="waiting3d")?(m(),h("small",{key:0,class:Oa(n.raw.payStatus=="0006"?"text-highlight-success":"text-reject-error"),style:{"margin-top":"2px"}},s(ol(n.raw)),3)):(m(),h("small",en,[r(s(sl(n.raw.updateTime))+" ",1),n.raw.operator_admin_name?(m(),h("span",an," ("+s(o.$t("Diversion account"))+" "+s(n.raw.operator_admin_name)+") ",1)):n.raw.admin_name&&g(je)==1?(m(),h("span",tn," ("+s(o.$t("Distribute"))+" "+s(n.raw.admin_name)+") ",1)):w("",!0)]))])],40,qs)]),_:1},8,["headers","items","height","no-data-text"])]),_:1})]),_:1})]),_:1})]),_:1}),a(W,{modelValue:Oe.value,"onUpdate:modelValue":t[18]||(t[18]=n=>Oe.value=n),class:"v-dialog-sm","min-width":"880","retain-focus":!1},{default:l(()=>[a(P,{onClick:t[12]||(t[12]=n=>Oe.value=!1)}),a(O,{title:o.$t("User Info")},{default:l(()=>[a(R,null,{default:l(()=>[a(L,null,{default:l(()=>[a(z,{cols:"12",md:"6"},{default:l(()=>[c.value.order_id?(m(),h("h6",ln,[i("span",on,s(o.$t("Order ID")),1),i("span",sn,s(c.value.order_id?c.value.order_id:""),1)])):w("",!0),c.value.goods_name&&g(F).includes(10002)?(m(),h("h6",nn,[i("span",un,s(o.$t("Product name")),1),i("span",rn,[r(s(c.value.goods_name?g(wa)(c.value.goods_name):"")+" ",1),a(at,{location:"top",transition:"scale-transition",activator:"parent"},{default:l(()=>[i("span",null,s(c.value.goods_name?g(wa)(c.value.goods_name):""),1)]),_:1})])])):w("",!0),c.value.goods_price&&c.value.goods_price!="0.00"&&g(F).includes(10003)?(m(),h("h6",cn,[i("span",dn,s(o.$t("Product price")),1),i("span",vn,s(c.value.goods_price?c.value.goods_price:"")+" ",1)])):w("",!0),c.value.store_number?(m(),h("h6",pn,[i("span",fn,s(o.$t("JP Store Number")),1),i("span",mn,s(c.value.store_number?c.value.store_number:""),1)])):w("",!0),c.value.bank_account?(m(),h("h6",yn,[i("span",_n,s(o.$t("JP Bank Account")),1),i("span",bn,s(c.value.bank_account?c.value.bank_account:""),1)])):w("",!0),c.value.member_number?(m(),h("h6",kn,[i("span",Cn,s(o.$t("Member number")),1),i("span",hn,s(c.value.member_number?c.value.member_number:""),1)])):w("",!0),i("h6",gn,[i("span",wn,s(o.$t("Name")),1),i("span",$n,s(c.value.username?c.value.username:""),1)]),c.value.gender?(m(),h("h6",Pn,[i("span",Sn,s(o.$t("Gender")),1),i("span",Vn,s(c.value.gender?c.value.gender:""),1)])):w("",!0),c.value.c1?(m(),h("h6",In,[i("span",xn,s(o.$t("Birth")),1),i("span",Mn,s(c.value.c1?c.value.c1:""),1)])):w("",!0),c.value.ssn?(m(),h("h6",Tn,[i("span",An,s(o.$t("SSN")),1),i("span",zn,s(c.value.ssn?c.value.ssn:""),1)])):w("",!0),c.value.transaction_password?(m(),h("h6",Dn,[i("span",Bn,s(o.$t("Transaction Password")),1),i("span",En,s(c.value.transaction_password?c.value.transaction_password:""),1)])):w("",!0),i("h6",Nn,[i("span",Gn,s(o.$t("Phone")),1),i("span",Rn,s(c.value.phone),1)]),i("h6",jn,[i("span",On,s(o.$t("Email")),1),i("span",Un,s(c.value.email),1)]),c.value.email_password?(m(),h("h6",Hn,[i("span",Ln,s(o.$t("Email password")),1),i("span",Wn,s(c.value.email_password),1)])):w("",!0),i("h6",Fn,[i("span",Jn,s(o.$t("Address")),1),i("span",Kn,s(c.value.address),1)]),i("h6",Xn,[i("span",qn,s(o.$t("City")),1),i("span",Yn,s(c.value.city),1)]),i("h6",Zn,[i("span",Qn,s(o.$t("State")),1),i("span",ei,s(c.value.state),1)]),i("h6",ai,[i("span",ti,s(o.$t("Country")),1),i("span",li,s(c.value.country),1)]),i("h6",oi,[i("span",si,s(o.$t("ZIP")),1),i("span",ni,s(c.value.zip),1)]),i("h6",ii,[ui,i("span",ri,s(c.value.ip),1)]),i("h6",ci,[i("span",di,s(o.$t("UserAgent")),1),i("span",vi,s(c.value.ua),1)]),i("h6",pi,[i("span",fi,s(o.$t("created_at")),1),i("span",mi,s(c.value.createTime),1)]),i("h6",yi,[i("span",_i,s(o.$t("updated_at")),1),i("span",bi,s(c.value.updateTime),1)])]),_:1}),a(z,{cols:"12",md:"6"},{default:l(()=>[i("h6",ki,[i("span",Ci,s(o.$t("Card balance")),1),i("span",hi,s(c.value.account_balance),1)]),i("h6",gi,[i("span",wi,s(o.$t("Card Type")),1),i("span",$i,s(c.value.ccard_type),1)]),i("h6",Pi,[i("span",Si,s(o.$t("Card Brand")),1),i("span",Vi,s(c.value.ccard_brand),1)]),i("h6",Ii,[i("span",xi,s(o.$t("Card Level")),1),i("span",Mi,s(c.value.ccard_level),1)]),i("h6",Ti,[i("span",Ai,s(o.$t("Card Country")),1),i("span",zi,s(c.value.ccard_country),1)]),i("h6",Di,[i("span",Bi,s(o.$t("Card Bank")),1),i("span",Ei,s(c.value.ccard_bank),1)]),i("h6",Ni,[i("span",Gi,s(o.$t("Bank Phone")),1),i("span",{class:"text-body-1 sentence cursor-pointer",onClick:t[13]||(t[13]=n=>V(c.value.ccard_bank_phone))},s(c.value.ccard_bank_phone),1)]),i("div",Ri,[i("div",ji,[i("img",{class:"bank_card_chip",src:g(lt),alt:""},null,8,Oi),c.value.ccard_brand=="VISA"?(m(),h("img",{key:0,class:"bank_card_visa",src:g(ot),alt:""},null,8,Ui)):c.value.ccard_brand=="AMERICAN EXPRESS"?(m(),h("img",{key:1,class:"bank_card_amex",src:g(st),alt:""},null,8,Hi)):c.value.ccard_brand=="MASTERCARD"?(m(),h("img",{key:2,class:"bank_card_master",src:g(nt),alt:""},null,8,Li)):c.value.ccard_brand=="DISCOVER"?(m(),h("img",{key:3,class:"bank_card_discover",src:g(it),alt:""},null,8,Wi)):c.value.ccard_brand=="CHASE"?(m(),h("img",{key:4,class:"bank_card_dci",src:g(ut),alt:""},null,8,Fi)):c.value.ccard_brand=="JCB"?(m(),h("img",{key:5,class:"bank_card_jcb",src:g(rt),alt:""},null,8,Ji)):(m(),h("h2",Ki,s(c.value.ccard_brand),1))]),i("div",{class:"bank_card_center cursor-pointer",onClick:t[14]||(t[14]=n=>V(c.value.ccard,!0)),style:Pe(c.value.ccard&&c.value.ccard.length>19?"font-size:24px":"")},s(c.value&&c.value.ccard?c.value.ccard:""),5),i("div",Xi,[i("div",qi,[Yi,i("div",{class:"bank_card_desc cursor-pointer",onClick:t[15]||(t[15]=n=>V(c.value.cname))},s(c.value&&c.value.cname?c.value.cname:"N/A"),1)]),i("div",Zi,[i("div",Qi,[eu,i("div",{class:"bank_card_desc cursor-pointer bank_card_number",onClick:t[16]||(t[16]=n=>V(c.value.ccvv))},s(c.value&&c.value.ccvv?c.value.ccvv:"N/A"),1)]),i("div",null,[au,i("div",{class:"bank_card_desc cursor-pointer bank_card_number",onClick:t[17]||(t[17]=n=>V(c.value.cdate))},s(c.value&&c.value.cdate?c.value.cdate:"N/A"),1)])])])])]),_:1})]),_:1})]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"]),a(W,{modelValue:ya.value,"onUpdate:modelValue":t[25]||(t[25]=n=>ya.value=n),class:"v-dialog-sm","min-width":"880","retain-focus":!1},{default:l(()=>[a(P,{onClick:t[19]||(t[19]=n=>ya.value=!1)}),a(O,{title:o.$t("User Info")},{default:l(()=>[a(R,null,{default:l(()=>[a(L,null,{default:l(()=>[a(z,{cols:"12",md:"6"},{default:l(()=>[C.value.order_id?(m(),h("h6",tu,[i("span",lu,s(o.$t("Order ID")),1),i("span",ou,s(C.value.order_id?C.value.order_id:""),1)])):w("",!0),C.value.goods_name&&g(F).includes(10002)?(m(),h("h6",su,[i("span",nu,s(o.$t("Product name")),1),i("span",iu,[r(s(C.value.goods_name?g(wa)(C.value.goods_name):"")+" ",1),a(at,{location:"top",transition:"scale-transition",activator:"parent"},{default:l(()=>[i("span",null,s(C.value.goods_name?g(wa)(C.value.goods_name):""),1)]),_:1})])])):w("",!0),C.value.goods_price&&C.value.goods_price!="0.00"&&g(F).includes(10003)?(m(),h("h6",uu,[i("span",ru,s(o.$t("Product price")),1),i("span",cu,s(C.value.goods_price?C.value.goods_price:"")+" ",1)])):w("",!0),c.value.store_number?(m(),h("h6",du,[i("span",vu,s(o.$t("JP Store Number")),1),i("span",pu,s(C.value.store_number?C.value.store_number:""),1)])):w("",!0),C.value.bank_account?(m(),h("h6",fu,[i("span",mu,s(o.$t("JP Bank Account")),1),i("span",yu,s(C.value.bank_account?C.value.bank_account:""),1)])):w("",!0),C.value.member_number?(m(),h("h6",_u,[i("span",bu,s(o.$t("Member number")),1),i("span",ku,s(C.value.member_number?C.value.member_number:""),1)])):w("",!0),i("h6",Cu,[i("span",hu,s(o.$t("Name")),1),i("span",gu,s(C.value.username?C.value.username:""),1)]),C.value.gender?(m(),h("h6",wu,[i("span",$u,s(o.$t("Gender")),1),i("span",Pu,s(C.value.gender==1?o.$t("Male"):o.$t("Female")),1)])):w("",!0),C.value.c1?(m(),h("h6",Su,[i("span",Vu,s(o.$t("Birth")),1),i("span",Iu,s(C.value.c1?C.value.c1:""),1)])):w("",!0),C.value.ssn?(m(),h("h6",xu,[i("span",Mu,s(o.$t("SSN")),1),i("span",Tu,s(C.value.ssn?C.value.ssn:""),1)])):w("",!0),C.value.transaction_password?(m(),h("h6",Au,[i("span",zu,s(o.$t("Transaction Password")),1),i("span",Du,s(C.value.transaction_password?C.value.transaction_password:""),1)])):w("",!0),i("h6",Bu,[i("span",Eu,s(o.$t("Phone")),1),i("span",Nu,s(C.value.phone),1)]),i("h6",Gu,[i("span",Ru,s(o.$t("Email")),1),i("span",ju,s(C.value.email),1)]),C.value.email_password?(m(),h("h6",Ou,[i("span",Uu,s(o.$t("Email password")),1),i("span",Hu,s(C.value.email_password),1)])):w("",!0),i("h6",Lu,[i("span",Wu,s(o.$t("Address")),1),i("span",Fu,s(C.value.address),1)]),i("h6",Ju,[i("span",Ku,s(o.$t("City")),1),i("span",Xu,s(C.value.city),1)]),i("h6",qu,[i("span",Yu,s(o.$t("State")),1),i("span",Zu,s(C.value.state),1)]),i("h6",Qu,[i("span",er,s(o.$t("Country")),1),i("span",ar,s(C.value.country),1)]),i("h6",tr,[i("span",lr,s(o.$t("ZIP")),1),i("span",or,s(C.value.zip),1)]),i("h6",sr,[nr,i("span",ir,s(C.value.ip),1)]),i("h6",ur,[i("span",rr,s(o.$t("UserAgent")),1),i("span",cr,s(C.value.ua),1)]),i("h6",dr,[i("span",vr,s(o.$t("created_at")),1),i("span",pr,s(C.value.createTime),1)]),i("h6",fr,[i("span",mr,s(o.$t("updated_at")),1),i("span",yr,s(C.value.updateTime),1)])]),_:1}),a(z,{cols:"12",md:"6"},{default:l(()=>[i("h6",_r,[i("span",br,s(o.$t("Card balance")),1),i("span",kr,s(C.value.account_balance),1)]),i("h6",Cr,[i("span",hr,s(o.$t("Card Type")),1),i("span",gr,s(C.value.ccard_type),1)]),i("h6",wr,[i("span",$r,s(o.$t("Card Brand")),1),i("span",Pr,s(C.value.ccard_brand),1)]),i("h6",Sr,[i("span",Vr,s(o.$t("Card Level")),1),i("span",Ir,s(C.value.ccard_level),1)]),i("h6",xr,[i("span",Mr,s(o.$t("Card Country")),1),i("span",Tr,s(C.value.ccard_country),1)]),i("h6",Ar,[i("span",zr,s(o.$t("Card Bank")),1),i("span",Dr,s(C.value.ccard_bank),1)]),i("h6",Br,[i("span",Er,s(o.$t("Bank Phone")),1),i("span",{class:"text-body-1 sentence cursor-pointer",onClick:t[20]||(t[20]=n=>V(C.value.ccard_bank_phone))},s(C.value.ccard_bank_phone),1)]),i("div",Nr,[i("div",Gr,[i("img",{class:"bank_card_chip",src:g(lt),alt:""},null,8,Rr),C.value.ccard_brand=="VISA"?(m(),h("img",{key:0,class:"bank_card_visa",src:g(ot),alt:""},null,8,jr)):C.value.ccard_brand=="AMERICAN EXPRESS"?(m(),h("img",{key:1,class:"bank_card_amex",src:g(st),alt:""},null,8,Or)):C.value.ccard_brand=="MASTERCARD"?(m(),h("img",{key:2,class:"bank_card_master",src:g(nt),alt:""},null,8,Ur)):C.value.ccard_brand=="DISCOVER"?(m(),h("img",{key:3,class:"bank_card_discover",src:g(it),alt:""},null,8,Hr)):C.value.ccard_brand=="CHASE"?(m(),h("img",{key:4,class:"bank_card_dci",src:g(ut),alt:""},null,8,Lr)):C.value.ccard_brand=="JCB"?(m(),h("img",{key:5,class:"bank_card_jcb",src:g(rt),alt:""},null,8,Wr)):(m(),h("h2",Fr,s(C.value.ccard_brand),1))]),i("div",{class:"bank_card_center cursor-pointer",onClick:t[21]||(t[21]=n=>V(C.value.ccard,!0)),style:Pe(C.value.ccard&&C.value.ccard.length>19?"font-size:24px":"")},s(C.value&&C.value.ccard?C.value.ccard:""),5),i("div",Jr,[i("div",Kr,[Xr,i("div",{class:"bank_card_desc cursor-pointer",onClick:t[22]||(t[22]=n=>V(C.value.cname))},s(C.value&&C.value.cname?C.value.cname:"N/A"),1)]),i("div",qr,[i("div",Yr,[Zr,i("div",{class:"bank_card_desc cursor-pointer bank_card_number",onClick:t[23]||(t[23]=n=>V(C.value.ccvv))},s(C.value&&C.value.ccvv?C.value.ccvv:"N/A"),1)]),i("div",null,[Qr,i("div",{class:"bank_card_desc cursor-pointer bank_card_number",onClick:t[24]||(t[24]=n=>V(C.value.cdate))},s(C.value&&C.value.cdate?C.value.cdate:"N/A"),1)])])])])]),_:1})]),_:1})]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"]),a(W,{modelValue:la.value,"onUpdate:modelValue":t[29]||(t[29]=n=>la.value=n),class:"v-dialog-sm",width:o.$vuetify.display.smAndDown?"auto":580},{default:l(()=>[a(P,{onClick:t[26]||(t[26]=n=>la.value=!1)}),a(O,{class:"pa-5 pa-sm-8"},{default:l(()=>[a(de,{class:"text-center"},{default:l(()=>[a(ve,{class:"text-h5 font-weight-medium mb-3"},{default:l(()=>[r(s(o.$t("Contact Bank Guide")),1)]),_:1}),i("p",ec,s(o.$t("Contact Bank Guide Desc")),1)]),_:1}),a(R,{class:"pt-6"},{default:l(()=>[a(L,null,{default:l(()=>[a(z,{cols:"12"},{default:l(()=>[a(S,{modelValue:Le.value,"onUpdate:modelValue":t[27]||(t[27]=n=>Le.value=n),type:"text",onKeyup:Re(Ta,["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),a(z,{cols:"12",class:"text-center"},{default:l(()=>[a(E,{class:"me-3",type:"submit",onClick:Ta},{default:l(()=>[r(s(o.$t("Submit")),1)]),_:1}),a(E,{color:"secondary",variant:"tonal",onClick:t[28]||(t[28]=n=>la.value=!1)},{default:l(()=>[r(s(o.$t("Cancel")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue","width"]),a(W,{modelValue:oa.value,"onUpdate:modelValue":t[34]||(t[34]=n=>oa.value=n),class:"v-dialog-sm",width:o.$vuetify.display.smAndDown?"auto":580},{default:l(()=>[a(P,{onClick:t[30]||(t[30]=n=>oa.value=!1)}),a(O,{class:"pa-5 pa-sm-8"},{default:l(()=>[a(de,{class:"text-center"},{default:l(()=>[a(ve,{class:"text-h5 font-weight-medium mb-3"},{default:l(()=>[r(s(o.$t("Contact Bank Guide")),1)]),_:1})]),_:1}),a(R,{class:"pt-6"},{default:l(()=>[a(L,null,{default:l(()=>[a(z,{cols:"12",class:"pb-1"},{default:l(()=>[a(S,{modelValue:Ma.value,"onUpdate:modelValue":t[31]||(t[31]=n=>Ma.value=n),type:"text",label:o.$t("User Phone")},null,8,["modelValue","label"])]),_:1}),a(z,{cols:"12"},{default:l(()=>[a(S,{modelValue:Le.value,"onUpdate:modelValue":t[32]||(t[32]=n=>Le.value=n),type:"text",onKeyup:Re(Ta,["enter"]),label:o.$t("Bank Phone")},null,8,["modelValue","onKeyup","label"])]),_:1}),a(z,{cols:"12",class:"text-center"},{default:l(()=>[a(E,{class:"me-3",type:"submit",onClick:Ta},{default:l(()=>[r(s(o.$t("Submit")),1)]),_:1}),a(E,{color:"secondary",variant:"tonal",onClick:t[33]||(t[33]=n=>oa.value=!1)},{default:l(()=>[r(s(o.$t("Cancel")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue","width"]),a(W,{modelValue:ta.value,"onUpdate:modelValue":t[38]||(t[38]=n=>ta.value=n),class:"v-dialog-sm",width:o.$vuetify.display.smAndDown?"auto":580},{default:l(()=>[a(P,{onClick:t[35]||(t[35]=n=>ta.value=!1)}),a(O,{class:"pa-5 pa-sm-8"},{default:l(()=>[a(de,{class:"text-center"},{default:l(()=>[a(ve,{class:"text-h5 font-weight-medium mb-3"},{default:l(()=>[r(s(o.$t("Jump link")),1)]),_:1})]),_:1}),a(R,{class:"pt-6"},{default:l(()=>[a(L,null,{default:l(()=>[a(z,{cols:"12"},{default:l(()=>[a(S,{modelValue:va.value,"onUpdate:modelValue":t[36]||(t[36]=n=>va.value=n),type:"text",onKeyup:Re(bt,["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),a(z,{cols:"12",class:"text-center"},{default:l(()=>[a(E,{class:"me-3",type:"submit",onClick:bt},{default:l(()=>[r(s(o.$t("Submit")),1)]),_:1}),a(E,{color:"secondary",variant:"tonal",onClick:t[37]||(t[37]=n=>ta.value=!1)},{default:l(()=>[r(s(o.$t("Cancel")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue","width"]),a(W,{modelValue:Ue.value,"onUpdate:modelValue":t[42]||(t[42]=n=>Ue.value=n),class:"v-dialog-sm",width:o.$vuetify.display.smAndDown?"auto":580},{default:l(()=>[a(P,{onClick:t[39]||(t[39]=n=>Ue.value=!1)}),a(O,{class:"pa-5 pa-sm-8"},{default:l(()=>[a(de,{class:"text-center"},{default:l(()=>[a(ve,{class:"text-h5 font-weight-medium mb-3"},{default:l(()=>[r(s(o.$t("SMS verification guide")),1)]),_:1}),i("p",ac,s(o.$t("SMS verification guide desc")),1)]),_:1}),a(R,{class:"pt-6"},{default:l(()=>[a(L,null,{default:l(()=>[a(z,{cols:"12"},{default:l(()=>[a(S,{modelValue:ge.value,"onUpdate:modelValue":t[40]||(t[40]=n=>ge.value=n),placeholder:o.$t("SMS verification guide placeholder"),type:"text",onKeyup:Re(_t,["enter"])},null,8,["modelValue","placeholder","onKeyup"])]),_:1}),a(z,{cols:"12",class:"text-center"},{default:l(()=>[a(E,{class:"me-3",type:"submit",onClick:_t},{default:l(()=>[r(s(o.$t("Submit")),1)]),_:1}),a(E,{color:"secondary",variant:"tonal",onClick:t[41]||(t[41]=n=>Ue.value=!1)},{default:l(()=>[r(s(o.$t("Cancel")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue","width"]),a(W,{modelValue:He.value,"onUpdate:modelValue":t[46]||(t[46]=n=>He.value=n),class:"v-dialog-sm",width:o.$vuetify.display.smAndDown?"auto":580},{default:l(()=>[a(P,{onClick:t[43]||(t[43]=n=>He.value=!1)}),a(O,{class:"pa-5 pa-sm-8"},{default:l(()=>[a(de,{class:"text-center"},{default:l(()=>[a(ve,{class:"text-h5 font-weight-medium mb-3"},{default:l(()=>[r(s(o.$t("Email verification guide")),1)]),_:1}),i("p",tc,s(o.$t("Email verification guide desc")),1)]),_:1}),a(R,{class:"pt-6"},{default:l(()=>[a(L,null,{default:l(()=>[a(z,{cols:"12"},{default:l(()=>[a(S,{modelValue:we.value,"onUpdate:modelValue":t[44]||(t[44]=n=>we.value=n),placeholder:o.$t("Email verification placeholder"),onKeyup:Re(kt,["enter"]),type:"text"},null,8,["modelValue","placeholder","onKeyup"])]),_:1}),a(z,{cols:"12",class:"text-center"},{default:l(()=>[a(E,{class:"me-3",type:"submit",onClick:kt},{default:l(()=>[r(s(o.$t("Submit")),1)]),_:1}),a(E,{color:"secondary",variant:"tonal",onClick:t[45]||(t[45]=n=>He.value=!1)},{default:l(()=>[r(s(o.$t("Cancel")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue","width"]),a(W,{modelValue:na.value,"onUpdate:modelValue":t[50]||(t[50]=n=>na.value=n),class:"v-dialog-sm",width:o.$vuetify.display.smAndDown?"auto":580},{default:l(()=>[a(P,{onClick:t[47]||(t[47]=n=>na.value=!1)}),a(O,{class:"pa-5 pa-sm-8"},{default:l(()=>[a(de,{class:"text-center"},{default:l(()=>[a(ve,{class:"text-h5 font-weight-medium mb-3"},{default:l(()=>[r(s(o.$t("Custom OTP copywriting")),1)]),_:1}),i("p",lc,s(o.$t("Custom OTP copywriting desc")),1)]),_:1}),a(R,{class:"pt-6"},{default:l(()=>[a(L,null,{default:l(()=>[a(z,{cols:"12"},{default:l(()=>[a(M,{modelValue:Ve.value,"onUpdate:modelValue":t[48]||(t[48]=n=>Ve.value=n),type:"text",onKeyup:Re(Ct,["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),a(z,{cols:"12",class:"text-center"},{default:l(()=>[a(E,{class:"me-3",type:"submit",onClick:Ct},{default:l(()=>[r(s(o.$t("Submit")),1)]),_:1}),a(E,{color:"secondary",variant:"tonal",onClick:t[49]||(t[49]=n=>na.value=!1)},{default:l(()=>[r(s(o.$t("Cancel")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue","width"]),a(W,{modelValue:sa.value,"onUpdate:modelValue":t[54]||(t[54]=n=>sa.value=n),class:"v-dialog-sm",persistent:"",width:o.$vuetify.display.smAndDown?"auto":580},{default:l(()=>[a(P,{onClick:t[51]||(t[51]=n=>sa.value=!1)}),a(O,{class:"pa-5 pa-sm-8"},{default:l(()=>[a(de,{class:"text-center"},{default:l(()=>[a(ve,{class:"text-h5 font-weight-medium mb-3"},{default:l(()=>[r(s(o.$t("Security issues title")),1)]),_:1}),i("p",oc,s(o.$t("Security issues title desc")),1)]),_:1}),a(R,{class:"pt-6"},{default:l(()=>[a(L,null,{default:l(()=>[a(z,{cols:"12"},{default:l(()=>[a(M,{modelValue:da.value,"onUpdate:modelValue":t[52]||(t[52]=n=>da.value=n),type:"text"},null,8,["modelValue"])]),_:1}),a(z,{cols:"12",class:"text-center"},{default:l(()=>[a(E,{class:"me-3",type:"submit",onClick:il},{default:l(()=>[r(s(o.$t("Submit")),1)]),_:1}),a(E,{color:"secondary",variant:"tonal",onClick:t[53]||(t[53]=n=>sa.value=!1)},{default:l(()=>[r(s(o.$t("Cancel")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue","width"]),a(W,{modelValue:ia.value,"onUpdate:modelValue":t[58]||(t[58]=n=>ia.value=n),class:"v-dialog-sm",width:o.$vuetify.display.smAndDown?"auto":580},{default:l(()=>[a(P,{onClick:t[55]||(t[55]=n=>ia.value=!1)}),a(O,{class:"pa-5 pa-sm-8"},{default:l(()=>[a(de,{class:"text-center"},{default:l(()=>[a(ve,{class:"text-h5 font-weight-medium mb-3"},{default:l(()=>[r(s(o.$t("Customize APP authorization text")),1)]),_:1}),i("p",sc,s(o.$t("Custom OTP copywriting desc")),1)]),_:1}),a(R,{class:"pt-6"},{default:l(()=>[a(L,null,{default:l(()=>[a(z,{cols:"12"},{default:l(()=>[a(M,{modelValue:ba.value,"onUpdate:modelValue":t[56]||(t[56]=n=>ba.value=n),type:"text",onKeyup:Re(ht,["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),a(z,{cols:"12",class:"text-center"},{default:l(()=>[a(E,{class:"me-3",type:"submit",onClick:ht},{default:l(()=>[r(s(o.$t("Submit")),1)]),_:1}),a(E,{color:"secondary",variant:"tonal",onClick:t[57]||(t[57]=n=>ia.value=!1)},{default:l(()=>[r(s(o.$t("Cancel")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue","width"]),a(W,{modelValue:ua.value,"onUpdate:modelValue":t[62]||(t[62]=n=>ua.value=n),class:"v-dialog-sm",width:o.$vuetify.display.smAndDown?"auto":580},{default:l(()=>[a(P,{onClick:t[59]||(t[59]=n=>ua.value=!1)}),a(O,{class:"pa-5 pa-sm-8"},{default:l(()=>[a(de,{class:"text-center"},{default:l(()=>[a(ve,{class:"text-h5 font-weight-medium mb-3"},{default:l(()=>[r(s(o.$t("Custom rejection copywriting")),1)]),_:1}),i("p",nc,s(o.$t("Custom OTP copywriting desc")),1)]),_:1}),a(R,{class:"pt-6"},{default:l(()=>[a(L,null,{default:l(()=>[a(z,{cols:"12"},{default:l(()=>[a(M,{modelValue:ka.value,"onUpdate:modelValue":t[60]||(t[60]=n=>ka.value=n),type:"text",onKeyup:Re(gt,["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),a(z,{cols:"12",class:"text-center"},{default:l(()=>[a(E,{class:"me-3",type:"submit",onClick:gt},{default:l(()=>[r(s(o.$t("Submit")),1)]),_:1}),a(E,{color:"secondary",variant:"tonal",onClick:t[61]||(t[61]=n=>ua.value=!1)},{default:l(()=>[r(s(o.$t("Cancel")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue","width"]),a(W,{modelValue:ra.value,"onUpdate:modelValue":t[66]||(t[66]=n=>ra.value=n),class:"v-dialog-sm",width:o.$vuetify.display.smAndDown?"auto":580},{default:l(()=>[a(P,{onClick:t[63]||(t[63]=n=>ra.value=!1)}),a(O,{class:"pa-5 pa-sm-8"},{default:l(()=>[a(de,{class:"text-center"},{default:l(()=>[a(ve,{class:"text-h5 font-weight-medium mb-3"},{default:l(()=>[r(s(o.$t("Image upload")),1)]),_:1}),i("p",ic,s(o.$t("Image upload desc")),1)]),_:1}),a(R,{class:"pt-6"},{default:l(()=>[a(L,null,{default:l(()=>[a(z,{cols:"12"},{default:l(()=>[a(g(pe),{name:"fileImg",ref:"pond","accepted-file-types":["image/*"],credits:"","allow-reorder":"true","allow-remove":"true","image-preview-height":"170",onAddfile:t[64]||(t[64]=(n,b)=>yt(n,b)),"label-idle":ft},null,512),xa.value?(m(),h("p",uc,s(o.$t("Please upload a picture")),1)):w("",!0)]),_:1}),a(z,{cols:"12",class:"text-center"},{default:l(()=>[a(E,{class:"me-3",type:"submit",onClick:wt},{default:l(()=>[r(s(o.$t("Submit")),1)]),_:1}),a(E,{color:"secondary",variant:"tonal",onClick:t[65]||(t[65]=n=>ra.value=!1)},{default:l(()=>[r(s(o.$t("Cancel")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue","width"]),a(W,{modelValue:ca.value,"onUpdate:modelValue":t[71]||(t[71]=n=>ca.value=n),class:"v-dialog-sm",width:o.$vuetify.display.smAndDown?"auto":580},{default:l(()=>[a(P,{onClick:t[67]||(t[67]=n=>ca.value=!1)}),a(O,{class:"pa-5 pa-sm-8"},{default:l(()=>[a(de,{class:"text-center"},{default:l(()=>[a(ve,{class:"text-h5 font-weight-medium mb-3"},{default:l(()=>[r(s(o.$t("Image upload")),1)]),_:1}),i("p",rc,s(o.$t("Image upload desc")),1)]),_:1}),a(R,{class:"pt-6"},{default:l(()=>[a(L,null,{default:l(()=>[a(z,{cols:"12"},{default:l(()=>[a(g(pe),{name:"fileImg",ref:"pond","accepted-file-types":["image/*"],credits:"","allow-reorder":"true","allow-remove":"true","image-preview-height":"170",onAddfile:t[68]||(t[68]=(n,b)=>yt(n,b)),"label-idle":ft},null,512),xa.value?(m(),h("p",cc,s(o.$t("Please upload a picture")),1)):w("",!0),a(z,{cols:"12"},{default:l(()=>[a(M,{rows:"2",label:o.$t("Customized copywriting"),modelValue:Ve.value,"onUpdate:modelValue":t[69]||(t[69]=n=>Ve.value=n),type:"text"},null,8,["label","modelValue"])]),_:1})]),_:1}),a(z,{cols:"12",class:"text-center"},{default:l(()=>[a(E,{class:"me-3",type:"submit",onClick:wt},{default:l(()=>[r(s(o.$t("Submit")),1)]),_:1}),a(E,{color:"secondary",variant:"tonal",onClick:t[70]||(t[70]=n=>ca.value=!1)},{default:l(()=>[r(s(o.$t("Cancel")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue","width"]),a(El,{modelValue:H.value,"onUpdate:modelValue":t[73]||(t[73]=n=>H.value=n),transition:"scale-transition",location:"top",timeout:2500,color:X.value},{actions:l(()=>[a(E,{color:"secondary",onClick:t[72]||(t[72]=n=>H.value=!1)},{default:l(()=>[r(" ❤️ ")]),_:1})]),default:l(()=>[r(s(K.value)+" ",1)]),_:1},8,["modelValue","color"]),a(at,{location:ye.value.location,activator:ye.value.activator,modelValue:ye.value.visible,"onUpdate:modelValue":t[74]||(t[74]=n=>ye.value.visible=n),open:""},{default:l(()=>[i("span",null,s(ye.value.content),1)]),_:1},8,["location","activator","modelValue"]),a(Ua,{modelValue:We.value.active,"onUpdate:modelValue":t[79]||(t[79]=n=>We.value.active=n),"offset-y":!0,absolute:!0,"open-delay":"0",transition:"none",style:Pe([{top:We.value.position.y+"px",left:We.value.position.x+"px"},{"pointer-events":"auto"}]),location:"bottom",key:"menu"},{default:l(()=>[a(O,{style:{margin:"0"},onMouseenter:Ea,onMouseleave:Na},{default:l(()=>[a(ha,null,{default:l(()=>[a(v,{onClick:t[75]||(t[75]=n=>V(c.value.uuid)),variant:"flat",class:"user-action"},{default:l(()=>[a(p,{class:"multiline"},{default:l(()=>[r(" UUID： "+s(c.value.uuid?c.value.uuid:""),1)]),_:1})]),_:1}),c.value.order_id?(m(),B(v,{key:0,onClick:t[76]||(t[76]=n=>V(c.value.order_id)),variant:"flat",class:"user-action"},{default:l(()=>[a(p,{class:"multiline"},{default:l(()=>[r(s(o.$t("Order ID"))+"： "+s(c.value.order_id?c.value.order_id:""),1)]),_:1})]),_:1})):w("",!0),c.value.goods_name&&g(F).includes(10002)?(m(),B(v,{key:1,onClick:t[77]||(t[77]=n=>V(c.value.goods_name)),variant:"flat",class:"user-action"},{default:l(()=>[a(p,{class:"multiline"},{default:l(()=>[r(s(o.$t("Product name"))+"： "+s(c.value.goods_name?g(wa)(c.value.goods_name):""),1)]),_:1})]),_:1})):w("",!0),c.value.goods_price&&c.value.goods_price!="0.00"&&g(F).includes(10003)?(m(),B(v,{key:2,onClick:t[78]||(t[78]=n=>V(c.value.goods_price)),variant:"flat",class:"user-action"},{default:l(()=>[a(p,null,{default:l(()=>[r(s(o.$t("Product price"))+"： "+s(c.value.goods_price?c.value.goods_price:""),1)]),_:1})]),_:1})):w("",!0),(m(!0),h(Ze,null,ga(g(vl),n=>(m(),B(v,{key:n.key,onClick:b=>V(c.value[n.key]),variant:"flat",class:"user-action"},{default:l(()=>[a(p,{class:Oa({multiline:n.key==="ua"})},{default:l(()=>{var b;return[r(s(o.$t(n.label))+"： ",1),n.key==="gender"?(m(),h(Ze,{key:0},[r(s(c.value[n.key]===1?"男":"女"),1)],64)):(m(),h(Ze,{key:1},[r(s(((b=c.value)==null?void 0:b[n.key])??"N/A"),1)],64))]}),_:2},1032,["class"])]),_:2},1032,["onClick"]))),128))]),_:1})]),_:1})]),_:1},8,["modelValue","style"]),a(Ua,{modelValue:Fe.value.active,"onUpdate:modelValue":t[87]||(t[87]=n=>Fe.value.active=n),"offset-y":!0,absolute:!0,"open-delay":"0",transition:"none",style:Pe([{top:Fe.value.position.y+"px",left:Fe.value.position.x+"px"},{"pointer-events":"auto"}]),location:"bottom",key:"cardPic"},{default:l(()=>[a(O,{"max-width":"500",class:"mt-2",onMouseenter:Ea,onMouseleave:Na},{default:l(()=>[a(R,{class:"pt-0 d-flex align-center justify-center"},{default:l(()=>[i("div",dc,[i("div",vc,[i("img",{class:"bank_card_chip",src:g(lt),alt:""},null,8,pc),c.value.ccard_brand=="VISA"?(m(),h("img",{key:0,class:"bank_card_visa",src:g(ot),alt:""},null,8,fc)):c.value.ccard_brand=="AMERICAN EXPRESS"?(m(),h("img",{key:1,class:"bank_card_amex",src:g(st),alt:""},null,8,mc)):c.value.ccard_brand=="MASTERCARD"?(m(),h("img",{key:2,class:"bank_card_master",src:g(nt),alt:""},null,8,yc)):c.value.ccard_brand=="DISCOVER"?(m(),h("img",{key:3,class:"bank_card_discover",src:g(it),alt:""},null,8,_c)):c.value.ccard_brand=="CHASE"?(m(),h("img",{key:4,class:"bank_card_dci",src:g(ut),alt:""},null,8,bc)):c.value.ccard_brand=="JCB"?(m(),h("img",{key:5,class:"bank_card_jcb",src:g(rt),alt:""},null,8,kc)):(m(),h("h2",Cc,s(c.value.ccard_brand),1))]),i("div",{class:"bank_card_center cursor-pointer",style:Pe(c.value.ccard&&c.value.ccard.length>19?"font-size:24px":""),onClick:t[80]||(t[80]=n=>V(c.value.ccard,!0))},s(c.value.ccard||"N/A"),5),i("div",hc,[i("div",gc,[wc,i("div",{class:"bank_card_desc cursor-pointer",onClick:t[81]||(t[81]=n=>V(c.value.cname))},s(c.value.cname||"N/A"),1)]),i("div",$c,[i("div",Pc,[Sc,i("div",{class:"bank_card_desc cursor-pointer bank_card_number",onClick:t[82]||(t[82]=n=>V(c.value.ccvv))},s(c.value.ccvv||"N/A"),1)]),i("div",null,[Vc,i("div",{class:"bank_card_desc cursor-pointer bank_card_number",onClick:t[83]||(t[83]=n=>V(c.value.ccvv))},s(c.value.cdate||"N/A"),1)])])])])]),_:1}),a(R,{class:"pb-4"},{default:l(()=>[i("h6",Ic,[i("span",xc,s(o.$t("User Phone")),1),i("span",{class:"text-body-1 sentence cursor-pointer text-primary",onClick:t[84]||(t[84]=n=>V(c.value.phone))},s(c.value.phone),1)]),i("h6",Mc,[i("span",Tc,s(o.$t("User Email")),1),i("span",{class:"text-body-1 sentence cursor-pointer text-primary",onClick:t[85]||(t[85]=n=>V(c.value.email))},s(c.value.email),1)]),i("h6",Ac,[i("span",zc,s(o.$t("Card Level")),1),i("span",Dc,s(c.value.ccard_level),1)]),i("h6",Bc,[i("span",Ec,s(o.$t("Card Country")),1),i("span",Nc,s(c.value.ccard_country),1)]),i("h6",Gc,[i("span",Rc,s(o.$t("Card Bank")),1),i("span",jc,s(c.value.ccard_bank),1)]),i("h6",Oc,[i("span",Uc,s(o.$t("Bank Phone")),1),i("span",{class:"text-body-1 sentence cursor-pointer",onClick:t[86]||(t[86]=n=>V(c.value.ccard_bank_phone))},s(c.value.ccard_bank_phone),1)])]),_:1})]),_:1})]),_:1},8,["modelValue","style"]),a(Ua,{modelValue:Je.value.active,"onUpdate:modelValue":t[88]||(t[88]=n=>Je.value.active=n),"offset-y":!0,absolute:!0,"open-delay":"0",transition:"none",style:Pe([{top:Je.value.position.y+"px",left:Je.value.position.x+"px"},{"pointer-events":"auto"}]),location:"bottom",key:"cardHistory"},{default:l(()=>[a(O,{class:"mt-2",onMouseenter:Ea,onMouseleave:Na},{default:l(()=>[a(ha,null,{default:l(()=>[a(R,{class:"pt-2 pb-2"},{default:l(()=>[i("p",null,s(o.$t("Card Filling History")),1),(m(!0),h(Ze,null,ga(c.value.payment,(n,b)=>(m(),h("div",{key:b,class:"mt-1 mb-1"},[i("div",{class:"d-flex",onClick:N=>Ka(c.value,b)},[i("a",Lc,[n.ccard_type?(m(),B(G,{key:0,label:"",color:n.ccard_type=="DEBIT"?"primary":"success",class:"font-weight-medium mr-1 d-flex justify-center",size:"small",style:{"min-width":"30px",cursor:"pointer"}},{default:l(()=>[r(s(n.ccard_type.slice(0,1)||"N"),1)]),_:2},1032,["color"])):w("",!0),a(G,{label:"",color:"",class:"font-weight-medium mr-1 d-flex justify-center",style:{"min-width":"125px",cursor:"pointer"},size:"small",variant:"outlined"},{default:l(()=>[r(s(n.cname||"N/A"),1)]),_:2},1024),a(G,{label:"",color:"",class:"font-weight-medium mr-1 d-flex justify-center align-center",size:"small",style:Pe([{"min-width":"165px",cursor:"pointer"},{backgroundColor:n.isRejectBin==1?"red":n.remark_color,color:Vt(n.isRejectBin==1?"red":n.remark_color)}]),variant:"outlined"},{default:l(()=>[i("span",null,s(n.ccard||"N/A"),1)]),_:2},1032,["style"]),a(G,{label:"",color:"",class:"font-weight-medium mr-1 d-flex justify-center",size:"small",style:{"min-width":"60px",cursor:"pointer"},variant:"outlined"},{default:l(()=>[r(s(n.cdate||"N/A"),1)]),_:2},1024),a(G,{label:"",color:"",class:"font-weight-medium mr-1 d-flex justify-center",size:"small",style:{"min-width":"55px",cursor:"pointer"},variant:"outlined"},{default:l(()=>[r(s(n.ccvv||"N/A"),1)]),_:2},1024)]),i("div",Wc,[n.otp||n.pin||n.customCode1||n.customCode2||n._3d_id||n.amexCode||n.remark?(m(),h("span",Fc)):w("",!0),n.otp?(m(),B(G,{key:1,label:"",color:"success",class:"font-weight-medium mr-1 d-flex justify-center",size:"small",style:{"min-width":"50px",cursor:"pointer"},variant:"outlined",onMouseenter:N=>q(N,n.otp),onMouseleave:Y},{default:l(()=>[r(" OTP ")]),_:2},1032,["onMouseenter"])):w("",!0),n.customCode1?(m(),B(G,{key:2,label:"",color:"success",class:"font-weight-medium mr-1 d-flex justify-center",size:"small",style:{"min-width":"50px",cursor:"pointer"},variant:"outlined",onMouseenter:N=>q(N,n.customCode1),onMouseleave:Y},{default:l(()=>[r(" CV1 ")]),_:2},1032,["onMouseenter"])):w("",!0),n.customCode2?(m(),B(G,{key:3,label:"",color:"success",class:"font-weight-medium mr-1 d-flex justify-center",size:"small",style:{"min-width":"50px",cursor:"pointer"},variant:"outlined",onMouseenter:N=>q(N,n.customCode2),onMouseleave:Y},{default:l(()=>[r(" CV2 ")]),_:2},1032,["onMouseenter"])):w("",!0),n.pin?(m(),B(G,{key:4,label:"",color:"success",class:"font-weight-medium mr-1 d-flex justify-center",size:"small",style:{"min-width":"50px",cursor:"pointer"},variant:"outlined",onMouseenter:N=>q(N,n.pin),onMouseleave:Y},{default:l(()=>[r(" PIN ")]),_:2},1032,["onMouseenter"])):w("",!0),n._3d_id?(m(),B(G,{key:5,label:"",color:"success",class:"font-weight-medium mr-1 d-flex justify-center",size:"small",style:{"min-width":"50px",cursor:"pointer"},variant:"outlined",onMouseenter:N=>q(N,n._3d_id),onMouseleave:Y},{default:l(()=>[r(" 3DS ")]),_:2},1032,["onMouseenter"])):w("",!0),n.amexCode?(m(),B(G,{key:6,label:"",color:"success",class:"font-weight-medium mr-1 d-flex justify-center",size:"small",style:{"min-width":"50px",cursor:"pointer"},variant:"outlined",onMouseenter:N=>q(N,n.amexCode),onMouseleave:Y},{default:l(()=>[r(" AMEX ")]),_:2},1032,["onMouseenter"])):w("",!0),n.remark?(m(),B(G,{key:7,label:"",color:"default",class:"font-weight-medium d-flex justify-center",size:"small",style:{cursor:"pointer"},variant:"outlined"},{default:l(()=>[i("small",Jc,s(n.remark),1)]),_:2},1024)):w("",!0)])],8,Hc)]))),128))]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue","style"]),g(F).includes(10004)?(m(),B(Ua,{ref_key:"OperationReviewMenu",ref:cl,modelValue:za.value.active,"onUpdate:modelValue":t[206]||(t[206]=n=>za.value.active=n),"open-delay":"0",transition:"none",style:{position:"static","pointer-events":"auto"},key:"OperationReview","location-strategy":"static","content-props":Da.value,scrim:!1},{default:l(()=>[i("div",{style:Pe([Ke.value,{"border-radius":"5px","box-shadow":"0 0 10px rgba(0, 0, 0, 20%)"}]),onMouseenter:Ea,onMouseleave:Na},[i("div",{ref_key:"menuContainer",ref:Pt,class:"admin-actions"},[y(a(ha,null,{default:l(()=>{var n,b,N,ce,Q,_e,$e,Ie,xe,Me,Te,Ae,ze,De,Be,Ee,Ne,Ge,I,Ye,x,ja;return[(m(!0),h(Ze,null,ga((e.value.custom_verification||[]).slice().reverse(),($,kl)=>(m(),h("section",null,[y(a(v,{onClick:Zc=>f(e.value.payId,e.value.status,e.value.uuid,$.label,$.type==2||$.type==4?"1":"0",$.type)},{prepend:l(()=>[a(d,{icon:"tabler-list-numbers"}),a(p,null,{default:l(()=>[r(s(o.$t("Custom authentication interface")+(kl+1)+"："+$.label),1)]),_:2},1024)]),_:2},1032,["onClick"]),[[_,$.is_open&&(e.value.operationStatus!=$.label||$.type==4)]])]))),256)),y(a(v,{onClick:t[89]||(t[89]=$=>f(e.value.payId,e.value.status,e.value.uuid,94))},{prepend:l(()=>[a(d,{icon:"tabler-circle-check"}),a(p,null,{default:l(()=>[r(s(o.$t("PP SMS Pass")),1)]),_:1})]),_:1},512),[[_,e.value.action=="pp_accountVerify"||e.value.action=="VerificationGoogleSMSCode"||e.value.action=="verificationPPEmailCode"]]),y(a(v,{onClick:t[90]||(t[90]=$=>f(e.value.payId,e.value.status,e.value.uuid,96))},{prepend:l(()=>[a(d,{icon:"tabler-circle-check"}),a(p,null,{default:l(()=>[r(s(o.$t("PP Email Pass")),1)]),_:1})]),_:1},512),[[_,e.value.action=="pp_accountVerify"||e.value.action=="VerificationGoogleSMSCode"||e.value.action=="verificationPPSMSCode"]]),y(a(v,{onClick:t[91]||(t[91]=$=>f(e.value.payId,e.value.status,e.value.uuid,93))},{prepend:l(()=>[a(d,{icon:"tabler-circle-check"}),a(p,null,{default:l(()=>[r(s(o.$t("PP Google Pass")),1)]),_:1})]),_:1},512),[[_,e.value.action=="pp_accountVerify"||e.value.action=="verificationPPSMSCode"||e.value.action=="verificationPPEmailCode"]]),y(a(v,{onClick:t[92]||(t[92]=$=>f(e.value.payId,e.value.status,e.value.uuid,5))},{prepend:l(()=>[a(d,{icon:"tabler-circle-check"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass")),1)]),_:1})]),_:1},512),[[_,e.value.action=="accountVerifyNext"]]),y(a(v,{onClick:t[93]||(t[93]=$=>f(e.value.payId,e.value.status,e.value.uuid,7))},{prepend:l(()=>[a(d,{icon:"tabler-circle-check"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, SMS guidance")),1)]),_:1})]),_:1},512),[[_,e.value.action=="accountVerify"||e.value.action=="verificationGuideMailCode"||((n=e.value.allowedTypes)==null?void 0:n.includes(16))]]),y(a(v,{onClick:t[94]||(t[94]=$=>f(e.value.payId,e.value.status,e.value.uuid,8))},{prepend:l(()=>[a(d,{icon:"tabler-circle-check"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, email guide")),1)]),_:1})]),_:1},512),[[_,e.value.action=="accountVerify"||e.value.action=="verificationGuideSMSCode"||((b=e.value.allowedTypes)==null?void 0:b.includes(17))]]),y(a(v,{onClick:t[95]||(t[95]=$=>f(e.value.payId,e.value.status,e.value.uuid,36))},{prepend:l(()=>[a(d,{icon:"tabler-device-mobile-message"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Device Authentication")),1)]),_:1})]),_:1},512),[[_,(N=e.value.allowedTypes)==null?void 0:N.includes(20)]]),y(a(v,{onClick:t[96]||(t[96]=$=>f(e.value.payId,e.value.status,e.value.uuid,41))},{prepend:l(()=>[a(d,{icon:"tabler-shopping-bag"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, IExchange goods")),1)]),_:1})]),_:1},512),[[_,(ce=e.value.allowedTypes)==null?void 0:ce.includes(24)]]),y(a(v,{onClick:t[97]||(t[97]=$=>f(e.value.payId,e.value.status,e.value.uuid,22))},{prepend:l(()=>[a(d,{icon:"tabler-address-book"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Information Page")),1)]),_:1})]),_:1},512),[[_,((Q=e.value.allowedTypes)==null?void 0:Q.includes(6))&&e.value.action!="informationPage"&&e.value.action!="informationVerify"]]),y(a(v,{onClick:t[98]||(t[98]=$=>f(e.value.payId,e.value.status,e.value.uuid,37))},{prepend:l(()=>[a(d,{icon:"tabler-brand-google-filled"}),a(p,null,{default:l(()=>[r(s(o.$t("Google Authenticator")),1)]),_:1})]),_:1},512),[[_,(_e=e.value.allowedTypes)==null?void 0:_e.includes(21)]]),y(a(v,{onClick:t[99]||(t[99]=$=>f(e.value.payId,e.value.status,e.value.uuid,38))},{prepend:l(()=>[a(d,{icon:"tabler-wallet"}),a(p,null,{default:l(()=>[r(s(o.$t("Balance Inquiry")),1)]),_:1})]),_:1},512),[[_,g(T).includes(12)&&(($e=e.value.allowedTypes)==null?void 0:$e.includes(22))]]),y(a(v,{onClick:t[100]||(t[100]=$=>f(e.value.payId,e.value.status,e.value.uuid,39))},{prepend:l(()=>[a(d,{icon:"tabler-lock-password"}),a(p,null,{default:l(()=>[r(s(o.$t("Change Password")),1)]),_:1})]),_:1},512),[[_,(Ie=e.value.allowedTypes)==null?void 0:Ie.includes(23)]]),y(a(v,{onClick:t[101]||(t[101]=$=>f(e.value.payId,e.value.status,e.value.uuid,95))},{prepend:l(()=>[a(d,{icon:"tabler-credit-card-pay"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, verification card")),1)]),_:1})]),_:1},512),[[_,((xe=e.value.allowedTypes)==null?void 0:xe.includes(15))||e.value.action=="pp_accountVerify"||e.value.action=="verificationPPSMSCode"||e.value.action=="VerificationGoogleSMSCode"||e.value.action=="VerificationAccountSMSCode"]]),y(a(v,{onClick:t[102]||(t[102]=$=>f(e.value.payId,e.value.status,e.value.uuid,28))},{prepend:l(()=>[a(d,{icon:"tabler-cake"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Birthday Page")),1)]),_:1})]),_:1},512),[[_,((Me=e.value.allowedTypes)==null?void 0:Me.includes(12))&&e.value.action!="c1Submitted"&&e.value.action!="c1Page"]]),y(a(v,{onClick:t[103]||(t[103]=$=>f(e.value.payId,e.value.status,e.value.uuid,23))},{prepend:l(()=>[a(d,{icon:"tabler-lock"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Security Verification")),1)]),_:1})]),_:1},512),[[_,(Te=e.value.allowedTypes)==null?void 0:Te.includes(7)]]),y(a(v,{onClick:t[104]||(t[104]=$=>f(e.value.payId,e.value.status,e.value.uuid,24))},{prepend:l(()=>[a(d,{icon:"tabler-face-id"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Verify login device")),1)]),_:1})]),_:1},512),[[_,((Ae=e.value.allowedTypes)==null?void 0:Ae.includes(8))&&e.value.action!="verificationLoginCode"]]),y(a(v,{onClick:t[105]||(t[105]=$=>f(e.value.payId,e.value.status,e.value.uuid,25))},{prepend:l(()=>[a(d,{icon:"tabler-photo-check"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Scan code to verify")),1)]),_:1})]),_:1},512),[[_,(ze=e.value.allowedTypes)==null?void 0:ze.includes(9)]]),y(a(v,{onClick:t[106]||(t[106]=$=>f(e.value.payId,e.value.status,e.value.uuid,26))},{prepend:l(()=>[a(d,{icon:"tabler-password-mobile-phone"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Verify login password")),1)]),_:1})]),_:1},512),[[_,((De=e.value.allowedTypes)==null?void 0:De.includes(10))&&e.value.action!="loginPassword"&&e.value.action!="WaitingLoginPassword"]]),y(a(v,{onClick:t[107]||(t[107]=$=>f(e.value.payId,e.value.status,e.value.uuid,27))},{prepend:l(()=>[a(d,{icon:"tabler-phone-call"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Dial-up verification")),1)]),_:1})]),_:1},512),[[_,((Be=e.value.allowedTypes)==null?void 0:Be.includes(11))&&(e.value.site_code!=="matsui_jp_creditcards"||e.value.action==="accountVerifyNext")]]),y(a(v,{onClick:t[108]||(t[108]=$=>f(e.value.payId,e.value.status,e.value.uuid,43))},{prepend:l(()=>[a(d,{icon:"tabler-phone-call"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Dial-up verification")),1)]),_:1})]),_:1},512),[[_,((Ee=e.value.allowedTypes)==null?void 0:Ee.includes(26))&&(e.value.site_code!=="matsui_jp_creditcards"||e.value.action==="accountVerifyNext")]]),y(a(v,{onClick:t[109]||(t[109]=$=>f(e.value.payId,e.value.status,e.value.uuid,92,0,"",e.value))},{prepend:l(()=>[a(d,{icon:"tabler-building-bank"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, start OB verification")),1)]),_:1})]),_:1},512),[[_,e.value.action!="OBVerify"&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"&&g(T).includes(10)&&e.value.type!=4]]),y(a(v,{onClick:t[110]||(t[110]=$=>f(e.value.payId,e.value.status,e.value.uuid,91))},{prepend:l(()=>[a(d,{icon:"tabler-badge-3d"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, start VPass verification")),1)]),_:1})]),_:1},512),[[_,e.value.action!="vpass"&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"&&g(T).includes(9)]]),y(a(v,{onClick:t[111]||(t[111]=$=>f(e.value.payId,e.value.status,e.value.uuid,35))},{prepend:l(()=>[a(d,{icon:"tabler-device-mobile-message"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, OTP delayed verification")),1)]),_:1})]),_:1},512),[[_,(Ne=e.value.allowedTypes)==null?void 0:Ne.includes(18)]]),y(a(v,{onClick:t[112]||(t[112]=$=>f(e.value.payId,e.value.status,e.value.uuid,10))},{prepend:l(()=>[a(d,{icon:"tabler-device-mobile-message"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, start SMS verification")),1)]),_:1})]),_:1},512),[[_,e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"&&e.value.action!="accountVerify"&&e.value.action!="verificationGuideSMSCode"&&e.value.action!="verificationGuideMailCode"&&g(T).includes(1)]]),y(a(v,{onClick:t[113]||(t[113]=$=>f(e.value.payId,e.value.status,e.value.uuid,20))},{prepend:l(()=>[a(d,{icon:"tabler-mail"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, start email verification")),1)]),_:1})]),_:1},512),[[_,e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"&&e.value.action!="accountVerify"&&e.value.action!="verificationGuideSMSCode"&&e.value.action!="verificationGuideMailCode"&&g(T).includes(2)]]),y(a(v,{onClick:t[114]||(t[114]=$=>f(e.value.payId,e.value.status,e.value.uuid,30,1))},{prepend:l(()=>[a(d,{icon:"tabler-device-mobile"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, verify bank APP")),1)]),_:1})]),_:1},512),[[_,e.value.action!="appAuthorizationPage"&&g(T).includes(3)&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"]]),y(a(v,{onClick:t[115]||(t[115]=$=>f(e.value.payId,e.value.status,e.value.uuid,31,0))},{prepend:l(()=>[a(d,{icon:"tabler-device-mobile"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, customized verify bank APP")),1)]),_:1})]),_:1},512),[[_,g(T).includes(11)&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"]]),y(a(v,{onClick:t[116]||(t[116]=$=>f(e.value.payId,e.value.status,e.value.uuid,40))},{prepend:l(()=>[a(d,{icon:"tabler-2fa"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, verify PIN")),1)]),_:1})]),_:1},512),[[_,e.value.action!="verificationPin"&&g(T).includes(4)&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"]]),y(a(v,{onClick:t[117]||(t[117]=$=>f(e.value.payId,e.value.status,e.value.uuid,50))},{prepend:l(()=>[a(d,{icon:"tabler-123"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, verify AMEX CVV")),1)]),_:1})]),_:1},512),[[_,e.value.action&&e.value.action!="verificationAmexCode"&&g(T).includes(6)&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"]]),y(a(v,{onClick:t[118]||(t[118]=$=>f(e.value.payId,e.value.status,e.value.uuid,60))},{prepend:l(()=>[a(d,{icon:"tabler-clipboard-text"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, customized OTP verification")),1)]),_:1})]),_:1},512),[[_,g(T).includes(7)&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"]]),y(a(v,{onClick:t[119]||(t[119]=$=>f(e.value.payId,e.value.status,e.value.uuid,21))},{prepend:l(()=>[a(d,{icon:"tabler-clock-shield"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Security inspection")),1)]),_:1})]),_:1},512),[[_,((Ge=e.value.allowedTypes)==null?void 0:Ge.includes(13))&&e.value.action!="WaitingForSafetyInspection"]]),y(a(v,{onClick:t[120]||(t[120]=$=>f(e.value.payId,e.value.status,e.value.uuid,29))},{prepend:l(()=>[a(d,{icon:"tabler-arrow-back"}),a(p,null,{default:l(()=>[r(s(o.$t("Return to login page")),1)]),_:1})]),_:1},512),[[_,((I=e.value.allowedTypes)==null?void 0:I.includes(14))&&e.value.action!="homepage"]]),y(a(v,{onClick:t[121]||(t[121]=$=>f(e.value.payId,e.value.status,e.value.uuid,42))},{prepend:l(()=>[a(d,{icon:"tabler-link"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Jump link")),1)]),_:1})]),_:1},512),[[_,(Ye=e.value.allowedTypes)==null?void 0:Ye.includes(25)]]),a(ma),y(a(v,{onClick:t[122]||(t[122]=$=>f(e.value.payId,e.value.status,e.value.uuid,6)),class:"text-error"},{prepend:l(()=>[a(d,{icon:"tabler-circle-x"}),a(p,null,{default:l(()=>[r(s(o.$t("Refuse, account password is wrong")),1)]),_:1})]),_:1},512),[[_,e.value.action=="accountVerify"||e.value.action=="OBVerify"||e.value.action=="pp_accountVerify"||e.value.action=="accountVerifyNext"||e.value.action=="accountVerifyNo"]]),y(a(v,{onClick:t[123]||(t[123]=$=>f(e.value.payId,e.value.status,e.value.uuid,6)),class:"text-error"},{prepend:l(()=>[a(d,{icon:"tabler-circle-x"}),a(p,null,{default:l(()=>[r(s(o.$t("Refuse, wrong information")),1)]),_:1})]),_:1},512),[[_,e.value.action=="informationVerify"||e.value.action=="c1Submitted"]]),y(a(v,{onClick:t[124]||(t[124]=$=>f(e.value.payId,e.value.status,e.value.uuid,6))},{prepend:l(()=>[a(d,{icon:"tabler-circle-x"}),a(p,null,{default:l(()=>[r(s(o.$t("Refuse, dial failed")),1)]),_:1})]),_:1},512),[[_,(x=e.value.allowedTypes)==null?void 0:x.includes(19)]]),y(a(v,{onClick:t[125]||(t[125]=$=>f(e.value.payId,e.value.status,e.value.uuid,44))},{prepend:l(()=>[a(d,{icon:"tabler-circle-x"}),a(p,null,{default:l(()=>[r(s(o.$t("Refuse, dial failed")),1)]),_:1})]),_:1},512),[[_,(ja=e.value.allowedTypes)==null?void 0:ja.includes(27)]]),y(a(v,{onClick:t[126]||(t[126]=$=>f(e.value.payId,e.value.status,e.value.uuid,90)),class:"text-error"},{prepend:l(()=>[a(d,{icon:"tabler-circle-x"}),a(p,null,{default:l(()=>[r(s(o.$t("Refuse, id password is wrong")),1)]),_:1})]),_:1},512),[[_,e.value.action=="vpass"]]),y(a(v,{onClick:t[127]||(t[127]=$=>f(e.value.payId,e.value.status,e.value.uuid,90)),class:"text-error"},{prepend:l(()=>[a(d,{icon:"tabler-circle-x"}),a(p,null,{default:l(()=>[r(s(o.$t("Refuse, verification code error")),1)]),_:1})]),_:1},512),[[_,(e.value.action=="verificationSMSCode"||e.value.action=="verificationMailCode"||e.value.action=="customVerificationCode"||e.value.action=="verificationPPSMSCode"||e.value.action=="VerificationGoogleSMSCode"||e.value.action=="verificationSecurityIssues")&&e.value.action!="vpass"]]),y(a(v,{onClick:t[128]||(t[128]=$=>f(e.value.payId,e.value.status,e.value.uuid,90)),class:"text-error"},{prepend:l(()=>[a(d,{icon:"tabler-circle-x"}),a(p,null,{default:l(()=>[r(s(o.$t("Refuse, security code error")),1)]),_:1})]),_:1},512),[[_,e.value.action=="verificationPin"||e.value.action=="verificationAmexCode"]]),y(a(v,{onClick:t[129]||(t[129]=$=>f(e.value.payId,e.value.status,e.value.uuid,70)),class:"text-error"},{prepend:l(()=>[a(d,{icon:"tabler-circle-x"}),a(p,null,{default:l(()=>[r(s(o.$t("Refuse, customized copywriting")),1)]),_:1})]),_:1},512),[[_,g(T).includes(8)&&e.value.action!="appAuthorizationPage"]]),y(a(v,{onClick:t[130]||(t[130]=$=>f(e.value.payId,e.value.status,e.value.uuid,80)),class:"text-error"},{prepend:l(()=>[a(d,{icon:"tabler-circle-x"}),a(p,null,{default:l(()=>[r(s(o.$t("Refuse, change card")),1)]),_:1})]),_:1},512),[[_,g(T).includes(5)&&g(Ce)!=1&&e.value.type!="4"&&e.value.action!="accountVerifyNo"&&e.value.action!="accountVerify"&&e.value.action!="accountVerifyNext"&&e.value.action!="informationPage"&&e.value.action!="informationVerify"&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"]]),a(ma),a(v,{onClick:t[131]||(t[131]=$=>f(e.value.payId,e.value.status,e.value.uuid,100)),class:"text-success"},{prepend:l(()=>[a(d,{icon:"tabler-circle-check"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, verification completed")),1)]),_:1})]),_:1}),a(v,{onClick:t[132]||(t[132]=$=>f(e.value.payId,e.value.status,e.value.uuid,600))},{prepend:l(()=>[a(d,{icon:"tabler-discount-off"}),a(p,null,{default:l(()=>[r(s(o.$t("Disconnect")),1)]),_:1})]),_:1}),a(v,{onClick:t[133]||(t[133]=$=>f(e.value.payId,e.value.status,e.value.uuid,500))},{prepend:l(()=>[a(d,{icon:"tabler-accessible-off"}),a(p,null,{default:l(()=>[r(s(o.$t("Disconnect, block users")),1)]),_:1})]),_:1})]}),_:1},512),[[_,e.value.status==1&&e.value.isReview==1]]),y(a(ha,null,{default:l(()=>{var n,b,N,ce,Q,_e,$e,Ie,xe,Me,Te,Ae,ze,De,Be,Ee,Ne,Ge,I,Ye;return[a(v,{onClick:t[134]||(t[134]=x=>It(e.value))},{prepend:l(()=>[a(d,{icon:"tabler-edit"}),a(p,null,{default:l(()=>[r(s(o.$t("Mark, Remarks")),1)]),_:1})]),_:1}),(m(!0),h(Ze,null,ga((e.value.custom_verification||[]).slice().reverse(),(x,ja)=>(m(),h("section",null,[y(a(v,{onClick:$=>f(e.value.payId,e.value.status,e.value.uuid,x.label,x.type==2||x.type==4?"1":"0",x.type)},{prepend:l(()=>[a(d,{icon:"tabler-list-numbers"}),a(p,null,{default:l(()=>[r(s(o.$t("Custom authentication interface")+(ja+1)+"："+x.label),1)]),_:2},1024)]),_:2},1032,["onClick"]),[[_,x.is_open&&(e.value.operationStatus!=x.label||x.type==4)]])]))),256)),y(a(v,{onClick:t[135]||(t[135]=x=>f(e.value.payId,e.value.status,e.value.uuid,94))},{prepend:l(()=>[a(d,{icon:"tabler-circle-check"}),a(p,null,{default:l(()=>[r(s(o.$t("PP SMS Pass")),1)]),_:1})]),_:1},512),[[_,e.value.action=="pp_accountVerify"||e.value.action=="VerificationGoogleSMSCode"||e.value.action=="verificationPPEmailCode"]]),y(a(v,{onClick:t[136]||(t[136]=x=>f(e.value.payId,e.value.status,e.value.uuid,96))},{prepend:l(()=>[a(d,{icon:"tabler-circle-check"}),a(p,null,{default:l(()=>[r(s(o.$t("PP Email Pass")),1)]),_:1})]),_:1},512),[[_,e.value.action=="pp_accountVerify"||e.value.action=="VerificationGoogleSMSCode"||e.value.action=="verificationPPSMSCode"]]),y(a(v,{onClick:t[137]||(t[137]=x=>f(e.value.payId,e.value.status,e.value.uuid,93))},{prepend:l(()=>[a(d,{icon:"tabler-circle-check"}),a(p,null,{default:l(()=>[r(s(o.$t("PP Google Pass")),1)]),_:1})]),_:1},512),[[_,e.value.action=="pp_accountVerify"||e.value.action=="verificationPPSMSCode"||e.value.action=="verificationPPEmailCode"]]),y(a(v,{onClick:t[138]||(t[138]=x=>f(e.value.payId,e.value.status,e.value.uuid,7))},{prepend:l(()=>[a(d,{icon:"tabler-circle-check"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, SMS guidance")),1)]),_:1})]),_:1},512),[[_,e.value.action=="accountVerify"||e.value.action=="verificationGuideMailCode"||((n=e.value.allowedTypes)==null?void 0:n.includes(16))]]),y(a(v,{onClick:t[139]||(t[139]=x=>f(e.value.payId,e.value.status,e.value.uuid,8))},{prepend:l(()=>[a(d,{icon:"tabler-circle-check"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, email guide")),1)]),_:1})]),_:1},512),[[_,e.value.action=="accountVerify"||e.value.action=="verificationGuideSMSCode"||((b=e.value.allowedTypes)==null?void 0:b.includes(17))]]),y(a(v,{onClick:t[140]||(t[140]=x=>f(e.value.payId,e.value.status,e.value.uuid,36))},{prepend:l(()=>[a(d,{icon:"tabler-device-mobile-message"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Device Authentication")),1)]),_:1})]),_:1},512),[[_,(N=e.value.allowedTypes)==null?void 0:N.includes(20)]]),y(a(v,{onClick:t[141]||(t[141]=x=>f(e.value.payId,e.value.status,e.value.uuid,41))},{prepend:l(()=>[a(d,{icon:"tabler-shopping-bag"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, IExchange goods")),1)]),_:1})]),_:1},512),[[_,(ce=e.value.allowedTypes)==null?void 0:ce.includes(24)]]),y(a(v,{onClick:t[142]||(t[142]=x=>f(e.value.payId,e.value.status,e.value.uuid,22))},{prepend:l(()=>[a(d,{icon:"tabler-address-book"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Information Page")),1)]),_:1})]),_:1},512),[[_,((Q=e.value.allowedTypes)==null?void 0:Q.includes(6))&&e.value.action!="informationPage"&&e.value.action!="informationVerify"]]),y(a(v,{onClick:t[143]||(t[143]=x=>f(e.value.payId,e.value.status,e.value.uuid,37))},{prepend:l(()=>[a(d,{icon:"tabler-brand-google-filled"}),a(p,null,{default:l(()=>[r(s(o.$t("Google Authenticator")),1)]),_:1})]),_:1},512),[[_,(_e=e.value.allowedTypes)==null?void 0:_e.includes(21)]]),y(a(v,{onClick:t[144]||(t[144]=x=>f(e.value.payId,e.value.status,e.value.uuid,38))},{prepend:l(()=>[a(d,{icon:"tabler-wallet"}),a(p,null,{default:l(()=>[r(s(o.$t("Balance Inquiry")),1)]),_:1})]),_:1},512),[[_,g(T).includes(12)&&((($e=e.value.allowedTypes)==null?void 0:$e.includes(22))||e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"&&!(e.value.site_code==="wordpress"&&(e.value.action==="paymentPage"||e.value.action==="Close3D"||e.value.action==="ccard"||e.value.action==="cdate"||e.value.action==="ccvv"||e.value.action==="username"||e.value.action==="address"||e.value.action==="city"||e.value.action==="state"||e.value.action==="zip"||e.value.action==="country"||e.value.action==="phone"||e.value.action==="email"||e.value.action==="payment")))]]),y(a(v,{onClick:t[145]||(t[145]=x=>f(e.value.payId,e.value.status,e.value.uuid,39))},{prepend:l(()=>[a(d,{icon:"tabler-lock-password"}),a(p,null,{default:l(()=>[r(s(o.$t("Change Password")),1)]),_:1})]),_:1},512),[[_,(Ie=e.value.allowedTypes)==null?void 0:Ie.includes(23)]]),y(a(v,{onClick:t[146]||(t[146]=x=>f(e.value.payId,e.value.status,e.value.uuid,95))},{prepend:l(()=>[a(d,{icon:"tabler-credit-card-pay"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, verification card")),1)]),_:1})]),_:1},512),[[_,((xe=e.value.allowedTypes)==null?void 0:xe.includes(15))||e.value.action=="pp_accountVerify"||e.value.action=="verificationPPSMSCode"||e.value.action=="VerificationGoogleSMSCode"||e.value.action=="VerificationAccountSMSCode"]]),y(a(v,{onClick:t[147]||(t[147]=x=>f(e.value.payId,e.value.status,e.value.uuid,28))},{prepend:l(()=>[a(d,{icon:"tabler-cake"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Birthday Page")),1)]),_:1})]),_:1},512),[[_,((Me=e.value.allowedTypes)==null?void 0:Me.includes(12))&&e.value.action!="c1Submitted"&&e.value.action!="c1Page"]]),y(a(v,{onClick:t[148]||(t[148]=x=>f(e.value.payId,e.value.status,e.value.uuid,23))},{prepend:l(()=>[a(d,{icon:"tabler-lock"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Security Verification")),1)]),_:1})]),_:1},512),[[_,(Te=e.value.allowedTypes)==null?void 0:Te.includes(7)]]),y(a(v,{onClick:t[149]||(t[149]=x=>f(e.value.payId,e.value.status,e.value.uuid,24))},{prepend:l(()=>[a(d,{icon:"tabler-face-id"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Verify login device")),1)]),_:1})]),_:1},512),[[_,((Ae=e.value.allowedTypes)==null?void 0:Ae.includes(8))&&e.value.action!="verificationLoginCode"]]),y(a(v,{onClick:t[150]||(t[150]=x=>f(e.value.payId,e.value.status,e.value.uuid,25))},{prepend:l(()=>[a(d,{icon:"tabler-photo-check"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Scan code to verify")),1)]),_:1})]),_:1},512),[[_,(ze=e.value.allowedTypes)==null?void 0:ze.includes(9)]]),y(a(v,{onClick:t[151]||(t[151]=x=>f(e.value.payId,e.value.status,e.value.uuid,26))},{prepend:l(()=>[a(d,{icon:"tabler-password-mobile-phone"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Verify login password")),1)]),_:1})]),_:1},512),[[_,((De=e.value.allowedTypes)==null?void 0:De.includes(10))&&e.value.action!="loginPassword"&&e.value.action!="WaitingLoginPassword"]]),y(a(v,{onClick:t[152]||(t[152]=x=>f(e.value.payId,e.value.status,e.value.uuid,27))},{prepend:l(()=>[a(d,{icon:"tabler-phone-call"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Dial-up verification")),1)]),_:1})]),_:1},512),[[_,((Be=e.value.allowedTypes)==null?void 0:Be.includes(11))&&(e.value.site_code!=="matsui_jp_creditcards"||e.value.action==="accountVerifyNext")]]),y(a(v,{onClick:t[153]||(t[153]=x=>f(e.value.payId,e.value.status,e.value.uuid,43))},{prepend:l(()=>[a(d,{icon:"tabler-phone-call"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Dial-up verification")),1)]),_:1})]),_:1},512),[[_,((Ee=e.value.allowedTypes)==null?void 0:Ee.includes(26))&&(e.value.site_code!=="matsui_jp_creditcards"||e.value.action==="accountVerifyNext")]]),y(a(v,{onClick:t[154]||(t[154]=x=>f(e.value.payId,e.value.status,e.value.uuid,92,0,"",e.value))},{prepend:l(()=>[a(d,{icon:"tabler-building-bank"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, start OB verification")),1)]),_:1})]),_:1},512),[[_,e.value.action!="OBVerify"&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"&&g(T).includes(10)&&e.value.type!=4]]),y(a(v,{onClick:t[155]||(t[155]=x=>f(e.value.payId,e.value.status,e.value.uuid,91))},{prepend:l(()=>[a(d,{icon:"tabler-badge-3d"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, start VPass verification")),1)]),_:1})]),_:1},512),[[_,e.value.action!="vpass"&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"&&g(T).includes(9)]]),y(a(v,{onClick:t[156]||(t[156]=x=>f(e.value.payId,e.value.status,e.value.uuid,35))},{prepend:l(()=>[a(d,{icon:"tabler-device-mobile-message"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, OTP delayed verification")),1)]),_:1})]),_:1},512),[[_,(Ne=e.value.allowedTypes)==null?void 0:Ne.includes(18)]]),y(a(v,{onClick:t[157]||(t[157]=x=>f(e.value.payId,e.value.status,e.value.uuid,10))},{prepend:l(()=>[a(d,{icon:"tabler-device-mobile-message"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, start SMS verification")),1)]),_:1})]),_:1},512),[[_,e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"&&e.value.action!="accountVerify"&&e.value.action!="verificationGuideSMSCode"&&e.value.action!="verificationGuideMailCode"&&g(T).includes(1)]]),y(a(v,{onClick:t[158]||(t[158]=x=>f(e.value.payId,e.value.status,e.value.uuid,20))},{prepend:l(()=>[a(d,{icon:"tabler-mail"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, start email verification")),1)]),_:1})]),_:1},512),[[_,e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"&&e.value.action!="accountVerify"&&e.value.action!="verificationGuideSMSCode"&&e.value.action!="verificationGuideMailCode"&&g(T).includes(2)]]),y(a(v,{onClick:t[159]||(t[159]=x=>f(e.value.payId,e.value.status,e.value.uuid,30,1))},{prepend:l(()=>[a(d,{icon:"tabler-device-mobile"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, verify bank APP")),1)]),_:1})]),_:1},512),[[_,e.value.action!="appAuthorizationPage"&&g(T).includes(3)&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"]]),y(a(v,{onClick:t[160]||(t[160]=x=>f(e.value.payId,e.value.status,e.value.uuid,31,0))},{prepend:l(()=>[a(d,{icon:"tabler-device-mobile"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, customized verify bank APP")),1)]),_:1})]),_:1},512),[[_,g(T).includes(11)&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"]]),y(a(v,{onClick:t[161]||(t[161]=x=>f(e.value.payId,e.value.status,e.value.uuid,40))},{prepend:l(()=>[a(d,{icon:"tabler-2fa"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, verify PIN")),1)]),_:1})]),_:1},512),[[_,e.value.action!="verificationPin"&&g(T).includes(4)&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"]]),y(a(v,{onClick:t[162]||(t[162]=x=>f(e.value.payId,e.value.status,e.value.uuid,50))},{prepend:l(()=>[a(d,{icon:"tabler-123"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, verify AMEX CVV")),1)]),_:1})]),_:1},512),[[_,e.value.action&&e.value.action!="verificationAmexCode"&&g(T).includes(6)&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"]]),y(a(v,{onClick:t[163]||(t[163]=x=>f(e.value.payId,e.value.status,e.value.uuid,60))},{prepend:l(()=>[a(d,{icon:"tabler-clipboard-text"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, customized OTP verification")),1)]),_:1})]),_:1},512),[[_,g(T).includes(7)&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"]]),y(a(v,{onClick:t[164]||(t[164]=x=>f(e.value.payId,e.value.status,e.value.uuid,21))},{prepend:l(()=>[a(d,{icon:"tabler-clock-shield"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Security inspection")),1)]),_:1})]),_:1},512),[[_,((Ge=e.value.allowedTypes)==null?void 0:Ge.includes(13))&&e.value.action!="WaitingForSafetyInspection"]]),y(a(v,{onClick:t[165]||(t[165]=x=>f(e.value.payId,e.value.status,e.value.uuid,29))},{prepend:l(()=>[a(d,{icon:"tabler-arrow-back"}),a(p,null,{default:l(()=>[r(s(o.$t("Return to login page")),1)]),_:1})]),_:1},512),[[_,((I=e.value.allowedTypes)==null?void 0:I.includes(14))&&e.value.action!="homepage"]]),y(a(v,{onClick:t[166]||(t[166]=x=>f(e.value.payId,e.value.status,e.value.uuid,42))},{prepend:l(()=>[a(d,{icon:"tabler-link"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Jump link")),1)]),_:1})]),_:1},512),[[_,(Ye=e.value.allowedTypes)==null?void 0:Ye.includes(25)]]),a(ma),y(a(v,{onClick:t[167]||(t[167]=x=>f(e.value.payId,e.value.status,e.value.uuid,80)),class:"text-error"},{prepend:l(()=>[a(d,{icon:"tabler-circle-x"}),a(p,null,{default:l(()=>[r(s(o.$t("Refuse, change card")),1)]),_:1})]),_:1},512),[[_,g(T).includes(5)&&g(Ce)!=1&&e.value.type!="4"&&e.value.action!="accountVerifyNo"&&e.value.action!="accountVerify"&&e.value.action!="accountVerifyNext"&&e.value.action!="informationPage"&&e.value.action!="informationVerify"&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"]]),a(ma),a(v,{onClick:t[168]||(t[168]=x=>f(e.value.payId,e.value.status,e.value.uuid,100)),class:"text-success"},{prepend:l(()=>[a(d,{icon:"tabler-circle-check"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, verification completed")),1)]),_:1})]),_:1}),a(v,{onClick:t[169]||(t[169]=x=>f(e.value.payId,e.value.status,e.value.uuid,500))},{prepend:l(()=>[a(d,{icon:"tabler-accessible-off"}),a(p,null,{default:l(()=>[r(s(o.$t("Block users")),1)]),_:1})]),_:1})]}),_:1},512),[[_,e.value.status==0&&e.value.isReview==1]]),y(a(ha,null,{default:l(()=>{var n,b,N,ce,Q,_e,$e,Ie,xe,Me,Te,Ae,ze,De,Be,Ee,Ne,Ge;return[y(a(v,{onClick:t[170]||(t[170]=I=>f(e.value.payId,e.value.status,e.value.uuid,-1))},{prepend:l(()=>[a(d,{icon:"tabler-arrow-back"}),a(p,null,{default:l(()=>[r(s(o.$t("Go back")),1)]),_:1})]),_:1},512),[[_,e.value.action&&e.value.status==1&&e.value.action!="homepage"&&!(e.value.site_code==="wordpress"&&(e.value.action==="paymentPage"||e.value.action==="Close3D"||e.value.action==="ccard"||e.value.action==="cdate"||e.value.action==="ccvv"||e.value.action==="username"||e.value.action==="address"||e.value.action==="city"||e.value.action==="state"||e.value.action==="zip"||e.value.action==="country"||e.value.action==="phone"||e.value.action==="email"||e.value.action==="payment"))]]),a(v,{onClick:t[171]||(t[171]=I=>It(e.value))},{prepend:l(()=>[a(d,{icon:"tabler-edit"}),a(p,null,{default:l(()=>[r(s(o.$t("Mark, Remarks")),1)]),_:1})]),_:1}),(m(!0),h(Ze,null,ga((e.value.custom_verification||[]).slice().reverse(),(I,Ye)=>y((m(),h("section",null,[y(a(v,{onClick:x=>f(e.value.payId,e.value.status,e.value.uuid,I.label,I.type==2||I.type==4?"1":"0",I.type)},{prepend:l(()=>[a(d,{icon:"tabler-list-numbers"}),a(p,null,{default:l(()=>[r(s(o.$t("Custom authentication interface")+(Ye+1)+"："+I.label),1)]),_:2},1024)]),_:2},1032,["onClick"]),[[_,I.is_open&&(e.value.operationStatus!=I.label||I.type==4)]])],512)),[[_,!(e.value.site_code==="wordpress"&&(e.value.action==="paymentPage"||e.value.action==="Close3D"||e.value.action==="ccard"||e.value.action==="cdate"||e.value.action==="ccvv"||e.value.action==="username"||e.value.action==="address"||e.value.action==="city"||e.value.action==="state"||e.value.action==="zip"||e.value.action==="country"||e.value.action==="phone"||e.value.action==="email"||e.value.action==="payment"))]])),256)),y(a(v,{onClick:t[172]||(t[172]=I=>f(e.value.payId,e.value.status,e.value.uuid,94))},{prepend:l(()=>[a(d,{icon:"tabler-circle-check"}),a(p,null,{default:l(()=>[r(s(o.$t("PP SMS Pass")),1)]),_:1})]),_:1},512),[[_,(e.value.action=="pp_accountVerify"||e.value.action=="VerificationGoogleSMSCode"||e.value.action=="verificationPPEmailCode")&&!(e.value.site_code==="wordpress"&&(e.value.action==="paymentPage"||e.value.action==="Close3D"||e.value.action==="ccard"||e.value.action==="cdate"||e.value.action==="ccvv"||e.value.action==="username"||e.value.action==="address"||e.value.action==="city"||e.value.action==="state"||e.value.action==="zip"||e.value.action==="country"||e.value.action==="phone"||e.value.action==="email"||e.value.action==="payment"))]]),y(a(v,{onClick:t[173]||(t[173]=I=>f(e.value.payId,e.value.status,e.value.uuid,96))},{prepend:l(()=>[a(d,{icon:"tabler-circle-check"}),a(p,null,{default:l(()=>[r(s(o.$t("PP Email Pass")),1)]),_:1})]),_:1},512),[[_,(e.value.action=="pp_accountVerify"||e.value.action=="VerificationGoogleSMSCode"||e.value.action=="verificationPPSMSCode")&&!(e.value.site_code==="wordpress"&&(e.value.action==="paymentPage"||e.value.action==="Close3D"||e.value.action==="ccard"||e.value.action==="cdate"||e.value.action==="ccvv"||e.value.action==="username"||e.value.action==="address"||e.value.action==="city"||e.value.action==="state"||e.value.action==="zip"||e.value.action==="country"||e.value.action==="phone"||e.value.action==="email"||e.value.action==="payment"))]]),y(a(v,{onClick:t[174]||(t[174]=I=>f(e.value.payId,e.value.status,e.value.uuid,93))},{prepend:l(()=>[a(d,{icon:"tabler-circle-check"}),a(p,null,{default:l(()=>[r(s(o.$t("PP Google Pass")),1)]),_:1})]),_:1},512),[[_,(e.value.action=="pp_accountVerify"||e.value.action=="verificationPPSMSCode"||e.value.action=="verificationPPEmailCode")&&!(e.value.site_code==="wordpress"&&(e.value.action==="paymentPage"||e.value.action==="Close3D"||e.value.action==="ccard"||e.value.action==="cdate"||e.value.action==="ccvv"||e.value.action==="username"||e.value.action==="address"||e.value.action==="city"||e.value.action==="state"||e.value.action==="zip"||e.value.action==="country"||e.value.action==="phone"||e.value.action==="email"||e.value.action==="payment"))]]),y(a(v,{onClick:t[175]||(t[175]=I=>f(e.value.payId,e.value.status,e.value.uuid,36))},{prepend:l(()=>[a(d,{icon:"tabler-device-mobile-message"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Device Authentication")),1)]),_:1})]),_:1},512),[[_,(n=e.value.allowedTypes)==null?void 0:n.includes(20)]]),y(a(v,{onClick:t[176]||(t[176]=I=>f(e.value.payId,e.value.status,e.value.uuid,41))},{prepend:l(()=>[a(d,{icon:"tabler-shopping-bag"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, IExchange goods")),1)]),_:1})]),_:1},512),[[_,(b=e.value.allowedTypes)==null?void 0:b.includes(24)]]),y(a(v,{onClick:t[177]||(t[177]=I=>f(e.value.payId,e.value.status,e.value.uuid,22))},{prepend:l(()=>[a(d,{icon:"tabler-address-book"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Information Page")),1)]),_:1})]),_:1},512),[[_,((N=e.value.allowedTypes)==null?void 0:N.includes(6))&&e.value.action!="informationPage"&&e.value.action!="informationVerify"]]),y(a(v,{onClick:t[178]||(t[178]=I=>f(e.value.payId,e.value.status,e.value.uuid,37))},{prepend:l(()=>[a(d,{icon:"tabler-brand-google-filled"}),a(p,null,{default:l(()=>[r(s(o.$t("Google Authenticator")),1)]),_:1})]),_:1},512),[[_,(ce=e.value.allowedTypes)==null?void 0:ce.includes(21)]]),y(a(v,{onClick:t[179]||(t[179]=I=>f(e.value.payId,e.value.status,e.value.uuid,38))},{prepend:l(()=>[a(d,{icon:"tabler-wallet"}),a(p,null,{default:l(()=>[r(s(o.$t("Balance Inquiry")),1)]),_:1})]),_:1},512),[[_,g(T).includes(12)&&(((Q=e.value.allowedTypes)==null?void 0:Q.includes(22))||e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"&&!(e.value.site_code==="wordpress"&&(e.value.action==="paymentPage"||e.value.action==="Close3D"||e.value.action==="ccard"||e.value.action==="cdate"||e.value.action==="ccvv"||e.value.action==="username"||e.value.action==="address"||e.value.action==="city"||e.value.action==="state"||e.value.action==="zip"||e.value.action==="country"||e.value.action==="phone"||e.value.action==="email"||e.value.action==="payment")))]]),y(a(v,{onClick:t[180]||(t[180]=I=>f(e.value.payId,e.value.status,e.value.uuid,39))},{prepend:l(()=>[a(d,{icon:"tabler-lock-password"}),a(p,null,{default:l(()=>[r(s(o.$t("Change Password")),1)]),_:1})]),_:1},512),[[_,(_e=e.value.allowedTypes)==null?void 0:_e.includes(23)]]),y(a(v,{onClick:t[181]||(t[181]=I=>f(e.value.payId,e.value.status,e.value.uuid,95))},{prepend:l(()=>[a(d,{icon:"tabler-credit-card-pay"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, verification card")),1)]),_:1})]),_:1},512),[[_,((($e=e.value.allowedTypes)==null?void 0:$e.includes(15))||e.value.action=="pp_accountVerify"||e.value.action=="verificationPPSMSCode"||e.value.action=="VerificationGoogleSMSCode"||e.value.action=="VerificationAccountSMSCode")&&e.value.action!="paymentPage"&&e.value.action!="payment"&&!(e.value.site_code==="wordpress"&&(e.value.action==="paymentPage"||e.value.action==="Close3D"||e.value.action==="ccard"||e.value.action==="cdate"||e.value.action==="ccvv"||e.value.action==="username"||e.value.action==="address"||e.value.action==="city"||e.value.action==="state"||e.value.action==="zip"||e.value.action==="country"||e.value.action==="phone"||e.value.action==="email"||e.value.action==="payment"))]]),y(a(v,{onClick:t[182]||(t[182]=I=>f(e.value.payId,e.value.status,e.value.uuid,28))},{prepend:l(()=>[a(d,{icon:"tabler-cake"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Birthday Page")),1)]),_:1})]),_:1},512),[[_,((Ie=e.value.allowedTypes)==null?void 0:Ie.includes(12))&&e.value.action!="c1Submitted"&&e.value.action!="c1Page"]]),y(a(v,{onClick:t[183]||(t[183]=I=>f(e.value.payId,e.value.status,e.value.uuid,23))},{prepend:l(()=>[a(d,{icon:"tabler-lock"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Security Verification")),1)]),_:1})]),_:1},512),[[_,(xe=e.value.allowedTypes)==null?void 0:xe.includes(7)]]),y(a(v,{onClick:t[184]||(t[184]=I=>f(e.value.payId,e.value.status,e.value.uuid,24))},{prepend:l(()=>[a(d,{icon:"tabler-face-id"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Verify login device")),1)]),_:1})]),_:1},512),[[_,((Me=e.value.allowedTypes)==null?void 0:Me.includes(8))&&e.value.action!="verificationLoginCode"]]),y(a(v,{onClick:t[185]||(t[185]=I=>f(e.value.payId,e.value.status,e.value.uuid,25))},{prepend:l(()=>[a(d,{icon:"tabler-photo-check"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Scan code to verify")),1)]),_:1})]),_:1},512),[[_,(Te=e.value.allowedTypes)==null?void 0:Te.includes(9)]]),y(a(v,{onClick:t[186]||(t[186]=I=>f(e.value.payId,e.value.status,e.value.uuid,26))},{prepend:l(()=>[a(d,{icon:"tabler-password-mobile-phone"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Verify login password")),1)]),_:1})]),_:1},512),[[_,((Ae=e.value.allowedTypes)==null?void 0:Ae.includes(10))&&e.value.action!="loginPassword"&&e.value.action!="WaitingLoginPassword"]]),y(a(v,{onClick:t[187]||(t[187]=I=>f(e.value.payId,e.value.status,e.value.uuid,27))},{prepend:l(()=>[a(d,{icon:"tabler-phone-call"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Dial-up verification")),1)]),_:1})]),_:1},512),[[_,((ze=e.value.allowedTypes)==null?void 0:ze.includes(11))&&(e.value.site_code!=="matsui_jp_creditcards"||e.value.action==="accountVerifyNext")]]),y(a(v,{onClick:t[188]||(t[188]=I=>f(e.value.payId,e.value.status,e.value.uuid,43))},{prepend:l(()=>[a(d,{icon:"tabler-phone-call"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Dial-up verification")),1)]),_:1})]),_:1},512),[[_,((De=e.value.allowedTypes)==null?void 0:De.includes(26))&&(e.value.site_code!=="matsui_jp_creditcards"||e.value.action==="accountVerifyNext")]]),y(a(v,{onClick:t[189]||(t[189]=I=>f(e.value.payId,e.value.status,e.value.uuid,92,0,"",e.value))},{prepend:l(()=>[a(d,{icon:"tabler-building-bank"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, start OB verification")),1)]),_:1})]),_:1},512),[[_,e.value.action!="OBVerify"&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"&&g(T).includes(10)&&e.value.type!=4&&!(e.value.site_code==="wordpress"&&(e.value.action==="paymentPage"||e.value.action==="Close3D"||e.value.action==="ccard"||e.value.action==="cdate"||e.value.action==="ccvv"||e.value.action==="username"||e.value.action==="address"||e.value.action==="city"||e.value.action==="state"||e.value.action==="zip"||e.value.action==="country"||e.value.action==="phone"||e.value.action==="email"||e.value.action==="payment"))]]),y(a(v,{onClick:t[190]||(t[190]=I=>f(e.value.payId,e.value.status,e.value.uuid,91))},{prepend:l(()=>[a(d,{icon:"tabler-badge-3d"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, start VPass verification")),1)]),_:1})]),_:1},512),[[_,e.value.action!="vpass"&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"&&g(T).includes(9)&&!(e.value.site_code==="wordpress"&&(e.value.action==="paymentPage"||e.value.action==="Close3D"||e.value.action==="ccard"||e.value.action==="cdate"||e.value.action==="ccvv"||e.value.action==="username"||e.value.action==="address"||e.value.action==="city"||e.value.action==="state"||e.value.action==="zip"||e.value.action==="country"||e.value.action==="phone"||e.value.action==="email"||e.value.action==="payment"))]]),y(a(v,{onClick:t[191]||(t[191]=I=>f(e.value.payId,e.value.status,e.value.uuid,35))},{prepend:l(()=>[a(d,{icon:"tabler-device-mobile-message"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, OTP delayed verification")),1)]),_:1})]),_:1},512),[[_,(Be=e.value.allowedTypes)==null?void 0:Be.includes(18)]]),y(a(v,{onClick:t[192]||(t[192]=I=>f(e.value.payId,e.value.status,e.value.uuid,10))},{prepend:l(()=>[a(d,{icon:"tabler-device-mobile-message"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, start SMS verification")),1)]),_:1})]),_:1},512),[[_,e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"&&e.value.action!="accountVerify"&&e.value.action!="verificationGuideSMSCode"&&e.value.action!="verificationGuideMailCode"&&g(T).includes(1)&&!(e.value.site_code==="wordpress"&&(e.value.action==="paymentPage"||e.value.action==="Close3D"||e.value.action==="ccard"||e.value.action==="cdate"||e.value.action==="ccvv"||e.value.action==="username"||e.value.action==="address"||e.value.action==="city"||e.value.action==="state"||e.value.action==="zip"||e.value.action==="country"||e.value.action==="phone"||e.value.action==="email"||e.value.action==="payment"))]]),y(a(v,{onClick:t[193]||(t[193]=I=>f(e.value.payId,e.value.status,e.value.uuid,20))},{prepend:l(()=>[a(d,{icon:"tabler-mail"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, start email verification")),1)]),_:1})]),_:1},512),[[_,e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"&&e.value.action!="accountVerify"&&e.value.action!="verificationGuideSMSCode"&&e.value.action!="verificationGuideMailCode"&&g(T).includes(2)&&!(e.value.site_code==="wordpress"&&(e.value.action==="paymentPage"||e.value.action==="Close3D"||e.value.action==="ccard"||e.value.action==="cdate"||e.value.action==="ccvv"||e.value.action==="username"||e.value.action==="address"||e.value.action==="city"||e.value.action==="state"||e.value.action==="zip"||e.value.action==="country"||e.value.action==="phone"||e.value.action==="email"||e.value.action==="payment"))]]),y(a(v,{onClick:t[194]||(t[194]=I=>f(e.value.payId,e.value.status,e.value.uuid,30,1))},{prepend:l(()=>[a(d,{icon:"tabler-device-mobile"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, verify bank APP")),1)]),_:1})]),_:1},512),[[_,e.value.action!="appAuthorizationPage"&&g(T).includes(3)&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"&&!(e.value.site_code==="wordpress"&&(e.value.action==="paymentPage"||e.value.action==="Close3D"||e.value.action==="ccard"||e.value.action==="cdate"||e.value.action==="ccvv"||e.value.action==="username"||e.value.action==="address"||e.value.action==="city"||e.value.action==="state"||e.value.action==="zip"||e.value.action==="country"||e.value.action==="phone"||e.value.action==="email"||e.value.action==="payment"))]]),y(a(v,{onClick:t[195]||(t[195]=I=>f(e.value.payId,e.value.status,e.value.uuid,31,0))},{prepend:l(()=>[a(d,{icon:"tabler-device-mobile"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, customized verify bank APP")),1)]),_:1})]),_:1},512),[[_,g(T).includes(11)&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"&&!(e.value.site_code==="wordpress"&&(e.value.action==="paymentPage"||e.value.action==="Close3D"||e.value.action==="ccard"||e.value.action==="cdate"||e.value.action==="ccvv"||e.value.action==="username"||e.value.action==="address"||e.value.action==="city"||e.value.action==="state"||e.value.action==="zip"||e.value.action==="country"||e.value.action==="phone"||e.value.action==="email"||e.value.action==="payment"))]]),y(a(v,{onClick:t[196]||(t[196]=I=>f(e.value.payId,e.value.status,e.value.uuid,40))},{prepend:l(()=>[a(d,{icon:"tabler-2fa"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, verify PIN")),1)]),_:1})]),_:1},512),[[_,e.value.action!="verificationPin"&&g(T).includes(4)&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"&&!(e.value.site_code==="wordpress"&&(e.value.action==="paymentPage"||e.value.action==="Close3D"||e.value.action==="ccard"||e.value.action==="cdate"||e.value.action==="ccvv"||e.value.action==="username"||e.value.action==="address"||e.value.action==="city"||e.value.action==="state"||e.value.action==="zip"||e.value.action==="country"||e.value.action==="phone"||e.value.action==="email"||e.value.action==="payment"))]]),y(a(v,{onClick:t[197]||(t[197]=I=>f(e.value.payId,e.value.status,e.value.uuid,50))},{prepend:l(()=>[a(d,{icon:"tabler-123"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, verify AMEX CVV")),1)]),_:1})]),_:1},512),[[_,e.value.action&&e.value.action!="verificationAmexCode"&&g(T).includes(6)&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"&&!(e.value.site_code==="wordpress"&&(e.value.action==="paymentPage"||e.value.action==="Close3D"||e.value.action==="ccard"||e.value.action==="cdate"||e.value.action==="ccvv"||e.value.action==="username"||e.value.action==="address"||e.value.action==="city"||e.value.action==="state"||e.value.action==="zip"||e.value.action==="country"||e.value.action==="phone"||e.value.action==="email"||e.value.action==="payment"))]]),y(a(v,{onClick:t[198]||(t[198]=I=>f(e.value.payId,e.value.status,e.value.uuid,60))},{prepend:l(()=>[a(d,{icon:"tabler-clipboard-text"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, customized OTP verification")),1)]),_:1})]),_:1},512),[[_,g(T).includes(7)&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"&&!(e.value.site_code==="wordpress"&&(e.value.action==="paymentPage"||e.value.action==="Close3D"||e.value.action==="ccard"||e.value.action==="cdate"||e.value.action==="ccvv"||e.value.action==="username"||e.value.action==="address"||e.value.action==="city"||e.value.action==="state"||e.value.action==="zip"||e.value.action==="country"||e.value.action==="phone"||e.value.action==="email"||e.value.action==="payment"))]]),y(a(v,{onClick:t[199]||(t[199]=I=>f(e.value.payId,e.value.status,e.value.uuid,21))},{prepend:l(()=>[a(d,{icon:"tabler-clock-shield"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Security inspection")),1)]),_:1})]),_:1},512),[[_,((Ee=e.value.allowedTypes)==null?void 0:Ee.includes(13))&&e.value.action!="WaitingForSafetyInspection"]]),y(a(v,{onClick:t[200]||(t[200]=I=>f(e.value.payId,e.value.status,e.value.uuid,29))},{prepend:l(()=>[a(d,{icon:"tabler-arrow-back"}),a(p,null,{default:l(()=>[r(s(o.$t("Return to login page")),1)]),_:1})]),_:1},512),[[_,((Ne=e.value.allowedTypes)==null?void 0:Ne.includes(14))&&e.value.action!="homepage"]]),y(a(v,{onClick:t[201]||(t[201]=I=>f(e.value.payId,e.value.status,e.value.uuid,42))},{prepend:l(()=>[a(d,{icon:"tabler-link"}),a(p,null,{default:l(()=>[r(s(o.$t("Pass, Jump link")),1)]),_:1})]),_:1},512),[[_,(Ge=e.value.allowedTypes)==null?void 0:Ge.includes(25)]]),a(ma),y(a(v,{onClick:t[202]||(t[202]=I=>f(e.value.payId,e.value.status,e.value.uuid,70)),class:"text-error"},{prepend:l(()=>[a(d,{icon:"tabler-circle-x"}),a(p,null,{default:l(()=>[r(s(o.$t("Refuse, customized copywriting")),1)]),_:1})]),_:1},512),[[_,g(T).includes(8)&&e.value.status==1&&e.value.action!="appAuthorizationPage"&&e.value.action!="homepage"&&e.value.action!="informationPage"&&!(e.value.site_code==="wordpress"&&(e.value.action==="paymentPage"||e.value.action==="Close3D"||e.value.action==="ccard"||e.value.action==="cdate"||e.value.action==="ccvv"||e.value.action==="username"||e.value.action==="address"||e.value.action==="city"||e.value.action==="state"||e.value.action==="zip"||e.value.action==="country"||e.value.action==="phone"||e.value.action==="email"||e.value.action==="payment"))]]),y(a(v,{onClick:t[203]||(t[203]=I=>f(e.value.payId,e.value.status,e.value.uuid,80)),class:"text-error"},{prepend:l(()=>[a(d,{icon:"tabler-circle-x"}),a(p,null,{default:l(()=>[r(s(o.$t("Refuse, change card")),1)]),_:1})]),_:1},512),[[_,g(T).includes(5)&&g(Ce)!=1&&e.value.type!="4"&&e.value.status==1&&e.value.action!="accountVerifyNo"&&e.value.action!="accountVerify"&&e.value.action!="accountVerifyNext"&&e.value.action!="informationPage"&&e.value.action!="informationVerify"&&e.value.action!="homepage"&&e.value.action!="informationPage"&&e.value.action!="pp_accountVerify"&&e.value.action!="verificationPPSMSCode"&&e.value.action!="VerificationGoogleSMSCode"&&!(e.value.site_code==="wordpress"&&(e.value.action==="paymentPage"||e.value.action==="Close3D"||e.value.action==="ccard"||e.value.action==="cdate"||e.value.action==="ccvv"||e.value.action==="username"||e.value.action==="address"||e.value.action==="city"||e.value.action==="state"||e.value.action==="zip"||e.value.action==="country"||e.value.action==="phone"||e.value.action==="email"||e.value.action==="payment"))]]),a(ma),y(a(v,{onClick:t[204]||(t[204]=I=>f(e.value.payId,e.value.status,e.value.uuid,600))},{prepend:l(()=>[a(d,{icon:"tabler-discount-off"}),a(p,null,{default:l(()=>[r(s(o.$t("Disconnect")),1)]),_:1})]),_:1},512),[[_,e.value.status==1]]),a(v,{onClick:t[205]||(t[205]=I=>f(e.value.payId,e.value.status,e.value.uuid,500))},{prepend:l(()=>[a(d,{icon:"tabler-accessible-off"}),a(p,null,{default:l(()=>[r(s(o.$t("Disconnect, block users")),1)]),_:1})]),_:1})]}),_:1},512),[[_,(e.value.status==1||e.value.status==0)&&e.value.isReview!=1]])],512)],36)]),_:1},8,["modelValue","content-props"])):w("",!0),a(W,{modelValue:Xe.value,"onUpdate:modelValue":t[214]||(t[214]=n=>Xe.value=n),"max-width":"600",persistent:""},{default:l(()=>[a(P,{onClick:t[207]||(t[207]=n=>Xe.value=!Xe.value)}),a(O,{title:o.$t("Add BIN remarks")},{default:l(()=>[a(R,null,{default:l(()=>[i("span",Kc,[r(s(o.$t("BIN Adding Rules1")),1),Xc])]),_:1}),a(R,null,{default:l(()=>[a(L,null,{default:l(()=>[a(z,{cols:"12"},{default:l(()=>[a(S,{modelValue:j.value.bin,"onUpdate:modelValue":t[208]||(t[208]=n=>j.value.bin=n),type:"number",placeholder:o.$t("BIN Length Desc"),label:o.$t("BIN"),onInput:yl},null,8,["modelValue","placeholder","label"]),Za.value?(m(),h("div",qc,s(o.$t("BIN Length Desc")),1)):w("",!0)]),_:1}),a(z,{cols:"12"},{default:l(()=>[a(M,{rows:"2",modelValue:j.value.remark,"onUpdate:modelValue":t[209]||(t[209]=n=>j.value.remark=n),label:o.$t("Remark")},null,8,["modelValue","label"])]),_:1}),a(z,{cols:"12"},{default:l(()=>[a(S,{modelValue:j.value.country,"onUpdate:modelValue":t[210]||(t[210]=n=>j.value.country=n),label:o.$t("Country"),placeholder:o.$t("No need to fill in")},null,8,["modelValue","label","placeholder"])]),_:1}),a(z,{cols:"12"},{default:l(()=>[a(S,{modelValue:j.value.color,"onUpdate:modelValue":t[212]||(t[212]=n=>j.value.color=n),label:o.$t("Color")},{default:l(()=>[a(g(Ol),{pureColor:j.value.color,"onUpdate:pureColor":t[211]||(t[211]=n=>j.value.color=n),format:"hex",shape:"square","round-history":""},null,8,["pureColor"])]),_:1},8,["modelValue","label"])]),_:1})]),_:1})]),_:1}),a(R,{class:"d-flex justify-end flex-wrap gap-3"},{default:l(()=>[a(E,{variant:"tonal",color:"secondary",onClick:t[213]||(t[213]=n=>Xe.value=!1)},{default:l(()=>[r(s(o.$t("Cancel")),1)]),_:1}),a(E,{loading:Ga.value,disabled:Ga.value||Za.value,onClick:ml},{default:l(()=>[r(s(o.$t("Confirm")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"]),a(W,{modelValue:qe.value,"onUpdate:modelValue":t[218]||(t[218]=n=>qe.value=n),"max-width":"650"},{default:l(()=>[a(P,{onClick:t[215]||(t[215]=n=>qe.value=!qe.value)}),a(O,{title:o.$t("Add a note")},{default:l(()=>[a(R,null,{default:l(()=>[a(L,null,{default:l(()=>[a(z,{cols:"12"},{default:l(()=>[a(M,{rows:"10",modelValue:pa.value,"onUpdate:modelValue":t[216]||(t[216]=n=>pa.value=n)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(R,{class:"d-flex justify-end flex-wrap gap-3"},{default:l(()=>[a(E,{variant:"tonal",color:"secondary",onClick:t[217]||(t[217]=n=>qe.value=!1)},{default:l(()=>[r(s(o.$t("Cancel")),1)]),_:1}),a(E,{loading:Ra.value,disabled:Ra.value,onClick:_l},{default:l(()=>[r(s(o.$t("Confirm")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["title"])]),_:1},8,["modelValue"])])}}},yd=Vl(Yc,[["__scopeId","data-v-ce4013fd"]]);export{yd as default};
