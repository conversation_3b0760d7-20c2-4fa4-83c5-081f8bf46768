/*! shepherd.js 11.1.1 */var He=function(e){return Ve(e)&&!We(e)};function Ve(t){return!!t&&typeof t=="object"}function We(t){var e=Object.prototype.toString.call(t);return e==="[object RegExp]"||e==="[object Date]"||qe(t)}var Ue=typeof Symbol=="function"&&Symbol.for,Ye=Ue?Symbol.for("react.element"):60103;function qe(t){return t.$$typeof===Ye}function ze(t){return Array.isArray(t)?[]:{}}function wt(t,e){return e.clone!==!1&&e.isMergeableObject(t)?ut(ze(t),t,e):t}function Ke(t,e,n){return t.concat(e).map(function(i){return wt(i,n)})}function Xe(t,e){if(!e.customMerge)return ut;var n=e.customMerge(t);return typeof n=="function"?n:ut}function $e(t){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(t).filter(function(e){return Object.propertyIsEnumerable.call(t,e)}):[]}function Gt(t){return Object.keys(t).concat($e(t))}function we(t,e){try{return e in t}catch{return!1}}function Ze(t,e){return we(t,e)&&!(Object.hasOwnProperty.call(t,e)&&Object.propertyIsEnumerable.call(t,e))}function Ge(t,e,n){var i={};return n.isMergeableObject(t)&&Gt(t).forEach(function(o){i[o]=wt(t[o],n)}),Gt(e).forEach(function(o){Ze(t,o)||(we(t,o)&&n.isMergeableObject(e[o])?i[o]=Xe(o,n)(t[o],e[o],n):i[o]=wt(e[o],n))}),i}function ut(t,e,n){n=n||{},n.arrayMerge=n.arrayMerge||Ke,n.isMergeableObject=n.isMergeableObject||He,n.cloneUnlessOtherwiseSpecified=wt;var i=Array.isArray(e),o=Array.isArray(t),s=i===o;return s?i?n.arrayMerge(t,e,n):Ge(t,e,n):wt(e,n)}ut.all=function(e,n){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce(function(i,o){return ut(i,o,n)},{})};var Je=ut,Vt=Je;function Qe(t){return t instanceof Element}function Wt(t){return t instanceof HTMLElement}function J(t){return typeof t=="function"}function _t(t){return typeof t=="string"}function A(t){return t===void 0}class Ut{on(e,n,i,o){return o===void 0&&(o=!1),A(this.bindings)&&(this.bindings={}),A(this.bindings[e])&&(this.bindings[e]=[]),this.bindings[e].push({handler:n,ctx:i,once:o}),this}once(e,n,i){return this.on(e,n,i,!0)}off(e,n){return A(this.bindings)||A(this.bindings[e])?this:(A(n)?delete this.bindings[e]:this.bindings[e].forEach((i,o)=>{i.handler===n&&this.bindings[e].splice(o,1)}),this)}trigger(e){for(var n=arguments.length,i=new Array(n>1?n-1:0),o=1;o<n;o++)i[o-1]=arguments[o];return!A(this.bindings)&&this.bindings[e]&&this.bindings[e].forEach((s,r)=>{const{ctx:l,handler:c,once:a}=s,h=l||this;c.apply(h,i),a&&this.bindings[e].splice(r,1)}),this}}function _e(t){const e=Object.getOwnPropertyNames(t.constructor.prototype);for(let n=0;n<e.length;n++){const i=e[n],o=t[i];i!=="constructor"&&typeof o=="function"&&(t[i]=o.bind(t))}return t}function tn(t,e){return n=>{if(e.isOpen()){const i=e.el&&n.currentTarget===e.el;(!A(t)&&n.currentTarget.matches(t)||i)&&e.tour.next()}}}function en(t){const{event:e,selector:n}=t.options.advanceOn||{};if(e){const i=tn(n,t);let o;try{o=document.querySelector(n)}catch{}if(!A(n)&&!o)return console.error(`No element was found for the selector supplied to advanceOn: ${n}`);o?(o.addEventListener(e,i),t.on("destroy",()=>o.removeEventListener(e,i))):(document.body.addEventListener(e,i,!0),t.on("destroy",()=>document.body.removeEventListener(e,i,!0)))}else return console.error("advanceOn was defined, but no event name was passed.")}function xe(t){return!_t(t)||t===""?"":t.charAt(t.length-1)!=="-"?`${t}-`:t}function nn(t){const e=t.options.attachTo||{},n=Object.assign({},e);if(J(n.element)&&(n.element=n.element.call(t)),_t(n.element)){try{n.element=document.querySelector(n.element)}catch{}n.element||console.error(`The element for this Shepherd step was not found ${e.element}`)}return n}function ve(t){return t==null?!0:!t.element||!t.on}function Yt(){let t=Date.now();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{const n=(t+Math.random()*16)%16|0;return t=Math.floor(t/16),(e=="x"?n:n&3|8).toString(16)})}function I(){return I=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},I.apply(this,arguments)}function Ee(t,e){if(t==null)return{};var n={},i=Object.keys(t),o,s;for(s=0;s<i.length;s++)o=i[s],!(e.indexOf(o)>=0)&&(n[o]=t[o]);return n}const on=["mainAxis","crossAxis","fallbackPlacements","fallbackStrategy","fallbackAxisSideDirection","flipAlignment"],sn=["mainAxis","crossAxis","limiter"];function kt(t){return t.split("-")[1]}function qt(t){return t==="y"?"height":"width"}function Q(t){return t.split("-")[0]}function Et(t){return["top","bottom"].includes(Q(t))?"x":"y"}function Jt(t,e,n){let{reference:i,floating:o}=t;const s=i.x+i.width/2-o.width/2,r=i.y+i.height/2-o.height/2,l=Et(e),c=qt(l),a=i[c]/2-o[c]/2,h=Q(e),u=l==="x";let f;switch(h){case"top":f={x:s,y:i.y-o.height};break;case"bottom":f={x:s,y:i.y+i.height};break;case"right":f={x:i.x+i.width,y:r};break;case"left":f={x:i.x-o.width,y:r};break;default:f={x:i.x,y:i.y}}switch(kt(e)){case"start":f[l]-=a*(n&&u?-1:1);break;case"end":f[l]+=a*(n&&u?-1:1);break}return f}const rn=async(t,e,n)=>{const{placement:i="bottom",strategy:o="absolute",middleware:s=[],platform:r}=n,l=s.filter(Boolean),c=await(r.isRTL==null?void 0:r.isRTL(e));let a=await r.getElementRects({reference:t,floating:e,strategy:o}),{x:h,y:u}=Jt(a,i,c),f=i,p={},m=0;for(let b=0;b<l.length;b++){const{name:S,fn:y}=l[b],{x:w,y:x,data:g,reset:d}=await y({x:h,y:u,initialPlacement:i,placement:f,strategy:o,middlewareData:p,rects:a,platform:r,elements:{reference:t,floating:e}});if(h=w??h,u=x??u,p=I({},p,{[S]:I({},p[S],g)}),d&&m<=50){m++,typeof d=="object"&&(d.placement&&(f=d.placement),d.rects&&(a=d.rects===!0?await r.getElementRects({reference:t,floating:e,strategy:o}):d.rects),{x:h,y:u}=Jt(a,f,c)),b=-1;continue}}return{x:h,y:u,placement:f,strategy:o,middlewareData:p}};function ln(t){return I({top:0,right:0,bottom:0,left:0},t)}function Se(t){return typeof t!="number"?ln(t):{top:t,right:t,bottom:t,left:t}}function Tt(t){return I({},t,{top:t.y,left:t.x,right:t.x+t.width,bottom:t.y+t.height})}async function Oe(t,e){var n;e===void 0&&(e={});const{x:i,y:o,platform:s,rects:r,elements:l,strategy:c}=t,{boundary:a="clippingAncestors",rootBoundary:h="viewport",elementContext:u="floating",altBoundary:f=!1,padding:p=0}=e,m=Se(p),S=l[f?u==="floating"?"reference":"floating":u],y=Tt(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(S)))==null||n?S:S.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(l.floating)),boundary:a,rootBoundary:h,strategy:c})),w=u==="floating"?I({},r.floating,{x:i,y:o}):r.reference,x=await(s.getOffsetParent==null?void 0:s.getOffsetParent(l.floating)),g=await(s.isElement==null?void 0:s.isElement(x))?await(s.getScale==null?void 0:s.getScale(x))||{x:1,y:1}:{x:1,y:1},d=Tt(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({rect:w,offsetParent:x,strategy:c}):w);return{top:(y.top-d.top+m.top)/g.y,bottom:(d.bottom-y.bottom+m.bottom)/g.y,left:(y.left-d.left+m.left)/g.x,right:(d.right-y.right+m.right)/g.x}}const cn=Math.min,an=Math.max;function Ft(t,e,n){return an(t,cn(e,n))}const fn=t=>({name:"arrow",options:t,async fn(e){const{element:n,padding:i=0}=t||{},{x:o,y:s,placement:r,rects:l,platform:c,elements:a}=e;if(n==null)return{};const h=Se(i),u={x:o,y:s},f=Et(r),p=qt(f),m=await c.getDimensions(n),b=f==="y",S=b?"top":"left",y=b?"bottom":"right",w=b?"clientHeight":"clientWidth",x=l.reference[p]+l.reference[f]-u[f]-l.floating[p],g=u[f]-l.reference[f],d=await(c.getOffsetParent==null?void 0:c.getOffsetParent(n));let _=d?d[w]:0;(!_||!await(c.isElement==null?void 0:c.isElement(d)))&&(_=a.floating[w]||l.floating[p]);const T=x/2-g/2,C=h[S],X=_-m[p]-h[y],R=_/2-m[p]/2+T,U=Ft(C,R,X),nt=kt(r)!=null&&R!=U&&l.reference[p]/2-(R<C?h[S]:h[y])-m[p]/2<0?R<C?C-R:X-R:0;return{[f]:u[f]-nt,data:{[f]:U,centerOffset:R-U}}}}),un={left:"right",right:"left",bottom:"top",top:"bottom"};function Ct(t){return t.replace(/left|right|bottom|top/g,e=>un[e])}function hn(t,e,n){n===void 0&&(n=!1);const i=kt(t),o=Et(t),s=qt(o);let r=o==="x"?i===(n?"end":"start")?"right":"left":i==="start"?"bottom":"top";return e.reference[s]>e.floating[s]&&(r=Ct(r)),{main:r,cross:Ct(r)}}const dn={start:"end",end:"start"};function Dt(t){return t.replace(/start|end/g,e=>dn[e])}function pn(t){const e=Ct(t);return[Dt(t),e,Dt(e)]}function mn(t,e,n){const i=["left","right"],o=["right","left"],s=["top","bottom"],r=["bottom","top"];switch(t){case"top":case"bottom":return n?e?o:i:e?i:o;case"left":case"right":return e?s:r;default:return[]}}function gn(t,e,n,i){const o=kt(t);let s=mn(Q(t),n==="start",i);return o&&(s=s.map(r=>r+"-"+o),e&&(s=s.concat(s.map(Dt)))),s}const bn=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(n){var i;const{placement:o,middlewareData:s,rects:r,initialPlacement:l,platform:c,elements:a}=n,{mainAxis:h=!0,crossAxis:u=!0,fallbackPlacements:f,fallbackStrategy:p="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:b=!0}=e,S=Ee(e,on),y=Q(o),w=Q(l)===l,x=await(c.isRTL==null?void 0:c.isRTL(a.floating)),g=f||(w||!b?[Ct(l)]:pn(l));!f&&m!=="none"&&g.push(...gn(l,b,m,x));const d=[l,...g],_=await Oe(n,S),T=[];let C=((i=s.flip)==null?void 0:i.overflows)||[];if(h&&T.push(_[y]),u){const{main:N,cross:nt}=hn(o,r,x);T.push(_[N],_[nt])}if(C=[...C,{placement:o,overflows:T}],!T.every(N=>N<=0)){var X,R;const N=(((X=s.flip)==null?void 0:X.index)||0)+1,nt=d[N];if(nt)return{data:{index:N,overflows:C},reset:{placement:nt}};let pt=(R=C.filter(it=>it.overflows[0]<=0).sort((it,ot)=>it.overflows[1]-ot.overflows[1])[0])==null?void 0:R.placement;if(!pt)switch(p){case"bestFit":{var U;const it=(U=C.map(ot=>[ot.placement,ot.overflows.filter(mt=>mt>0).reduce((mt,Ne)=>mt+Ne,0)]).sort((ot,mt)=>ot[1]-mt[1])[0])==null?void 0:U[0];it&&(pt=it);break}case"initialPlacement":pt=l;break}if(o!==pt)return{reset:{placement:pt}}}return{}}}};function Ae(t){return t==="x"?"y":"x"}const yn=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(n){const{x:i,y:o,placement:s}=n,{mainAxis:r=!0,crossAxis:l=!1,limiter:c={fn:y=>{let{x:w,y:x}=y;return{x:w,y:x}}}}=e,a=Ee(e,sn),h={x:i,y:o},u=await Oe(n,a),f=Et(Q(s)),p=Ae(f);let m=h[f],b=h[p];if(r){const y=f==="y"?"top":"left",w=f==="y"?"bottom":"right",x=m+u[y],g=m-u[w];m=Ft(x,m,g)}if(l){const y=p==="y"?"top":"left",w=p==="y"?"bottom":"right",x=b+u[y],g=b-u[w];b=Ft(x,b,g)}const S=c.fn(I({},n,{[f]:m,[p]:b}));return I({},S,{data:{x:S.x-i,y:S.y-o}})}}},wn=function(e){return e===void 0&&(e={}),{options:e,fn(n){const{x:i,y:o,placement:s,rects:r,middlewareData:l}=n,{offset:c=0,mainAxis:a=!0,crossAxis:h=!0}=e,u={x:i,y:o},f=Et(s),p=Ae(f);let m=u[f],b=u[p];const S=typeof c=="function"?c(n):c,y=typeof S=="number"?{mainAxis:S,crossAxis:0}:I({mainAxis:0,crossAxis:0},S);if(a){const g=f==="y"?"height":"width",d=r.reference[f]-r.floating[g]+y.mainAxis,_=r.reference[f]+r.reference[g]-y.mainAxis;m<d?m=d:m>_&&(m=_)}if(h){var w,x;const g=f==="y"?"width":"height",d=["top","left"].includes(Q(s)),_=r.reference[p]-r.floating[g]+(d&&((w=l.offset)==null?void 0:w[p])||0)+(d?0:y.crossAxis),T=r.reference[p]+r.reference[g]+(d?0:((x=l.offset)==null?void 0:x[p])||0)-(d?y.crossAxis:0);b<_?b=_:b>T&&(b=T)}return{[f]:m,[p]:b}}}};function L(t){var e;return((e=t.ownerDocument)==null?void 0:e.defaultView)||window}function D(t){return L(t).getComputedStyle(t)}function Te(t){return t instanceof L(t).Node}function Y(t){return Te(t)?(t.nodeName||"").toLowerCase():""}let Ot;function Ce(){if(Ot)return Ot;const t=navigator.userAgentData;return t&&Array.isArray(t.brands)?(Ot=t.brands.map(e=>e.brand+"/"+e.version).join(" "),Ot):navigator.userAgent}function j(t){return t instanceof L(t).HTMLElement}function P(t){return t instanceof L(t).Element}function Qt(t){if(typeof ShadowRoot>"u")return!1;const e=L(t).ShadowRoot;return t instanceof e||t instanceof ShadowRoot}function Rt(t){const{overflow:e,overflowX:n,overflowY:i,display:o}=D(t);return/auto|scroll|overlay|hidden|clip/.test(e+i+n)&&!["inline","contents"].includes(o)}function _n(t){return["table","td","th"].includes(Y(t))}function zt(t){const e=/firefox/i.test(Ce()),n=D(t),i=n.backdropFilter||n.WebkitBackdropFilter;return n.transform!=="none"||n.perspective!=="none"||(i?i!=="none":!1)||e&&n.willChange==="filter"||e&&(n.filter?n.filter!=="none":!1)||["transform","perspective"].some(o=>n.willChange.includes(o))||["paint","layout","strict","content"].some(o=>{const s=n.contain;return s!=null?s.includes(o):!1})}function Kt(){return/^((?!chrome|android).)*safari/i.test(Ce())}function Xt(t){return["html","body","#document"].includes(Y(t))}const te=Math.min,gt=Math.max,It=Math.round;function Ie(t){const e=D(t);let n=parseFloat(e.width),i=parseFloat(e.height);const o=j(t),s=o?t.offsetWidth:n,r=o?t.offsetHeight:i,l=It(n)!==s||It(i)!==r;return l&&(n=s,i=r),{width:n,height:i,fallback:l}}function Le(t){return P(t)?t:t.contextElement}const ke={x:1,y:1};function at(t){const e=Le(t);if(!j(e))return ke;const n=e.getBoundingClientRect(),{width:i,height:o,fallback:s}=Ie(e);let r=(s?It(n.width):n.width)/i,l=(s?It(n.height):n.height)/o;return(!r||!Number.isFinite(r))&&(r=1),(!l||!Number.isFinite(l))&&(l=1),{x:r,y:l}}function tt(t,e,n,i){var o,s;e===void 0&&(e=!1),n===void 0&&(n=!1);const r=t.getBoundingClientRect(),l=Le(t);let c=ke;e&&(i?P(i)&&(c=at(i)):c=at(t));const a=l?L(l):window,h=Kt()&&n;let u=(r.left+(h&&((o=a.visualViewport)==null?void 0:o.offsetLeft)||0))/c.x,f=(r.top+(h&&((s=a.visualViewport)==null?void 0:s.offsetTop)||0))/c.y,p=r.width/c.x,m=r.height/c.y;if(l){const b=L(l),S=i&&P(i)?L(i):i;let y=b.frameElement;for(;y&&i&&S!==b;){const w=at(y),x=y.getBoundingClientRect(),g=getComputedStyle(y);x.x+=(y.clientLeft+parseFloat(g.paddingLeft))*w.x,x.y+=(y.clientTop+parseFloat(g.paddingTop))*w.y,u*=w.x,f*=w.y,p*=w.x,m*=w.y,u+=x.x,f+=x.y,y=L(y).frameElement}}return Tt({width:p,height:m,x:u,y:f})}function q(t){return((Te(t)?t.ownerDocument:t.document)||window.document).documentElement}function Mt(t){return P(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function xn(t){let{rect:e,offsetParent:n,strategy:i}=t;const o=j(n),s=q(n);if(n===s)return e;let r={scrollLeft:0,scrollTop:0},l={x:1,y:1};const c={x:0,y:0};if((o||!o&&i!=="fixed")&&((Y(n)!=="body"||Rt(s))&&(r=Mt(n)),j(n))){const a=tt(n);l=at(n),c.x=a.x+n.clientLeft,c.y=a.y+n.clientTop}return{width:e.width*l.x,height:e.height*l.y,x:e.x*l.x-r.scrollLeft*l.x+c.x,y:e.y*l.y-r.scrollTop*l.y+c.y}}function Re(t){return tt(q(t)).left+Mt(t).scrollLeft}function vn(t){const e=q(t),n=Mt(t),i=t.ownerDocument.body,o=gt(e.scrollWidth,e.clientWidth,i.scrollWidth,i.clientWidth),s=gt(e.scrollHeight,e.clientHeight,i.scrollHeight,i.clientHeight);let r=-n.scrollLeft+Re(t);const l=-n.scrollTop;return D(i).direction==="rtl"&&(r+=gt(e.clientWidth,i.clientWidth)-o),{width:o,height:s,x:r,y:l}}function xt(t){if(Y(t)==="html")return t;const e=t.assignedSlot||t.parentNode||Qt(t)&&t.host||q(t);return Qt(e)?e.host:e}function Me(t){const e=xt(t);return Xt(e)?e.ownerDocument.body:j(e)&&Rt(e)?e:Me(e)}function bt(t,e){var n;e===void 0&&(e=[]);const i=Me(t),o=i===((n=t.ownerDocument)==null?void 0:n.body),s=L(i);return o?e.concat(s,s.visualViewport||[],Rt(i)?i:[]):e.concat(i,bt(i))}function En(t,e){const n=L(t),i=q(t),o=n.visualViewport;let s=i.clientWidth,r=i.clientHeight,l=0,c=0;if(o){s=o.width,r=o.height;const a=Kt();(!a||a&&e==="fixed")&&(l=o.offsetLeft,c=o.offsetTop)}return{width:s,height:r,x:l,y:c}}function Sn(t,e){const n=tt(t,!0,e==="fixed"),i=n.top+t.clientTop,o=n.left+t.clientLeft,s=j(t)?at(t):{x:1,y:1},r=t.clientWidth*s.x,l=t.clientHeight*s.y,c=o*s.x,a=i*s.y;return{width:r,height:l,x:c,y:a}}function ee(t,e,n){let i;if(e==="viewport")i=En(t,n);else if(e==="document")i=vn(q(t));else if(P(e))i=Sn(e,n);else{const r=I({},e);if(Kt()){var o,s;const l=L(t);r.x-=((o=l.visualViewport)==null?void 0:o.offsetLeft)||0,r.y-=((s=l.visualViewport)==null?void 0:s.offsetTop)||0}i=r}return Tt(i)}function On(t,e){const n=e.get(t);if(n)return n;let i=bt(t).filter(l=>P(l)&&Y(l)!=="body"),o=null;const s=D(t).position==="fixed";let r=s?xt(t):t;for(;P(r)&&!Xt(r);){const l=D(r),c=zt(r);l.position==="fixed"?o=null:(s?!c&&!o:!c&&l.position==="static"&&!!o&&["absolute","fixed"].includes(o.position))?i=i.filter(u=>u!==r):o=l,r=xt(r)}return e.set(t,i),i}function An(t){let{element:e,boundary:n,rootBoundary:i,strategy:o}=t;const r=[...n==="clippingAncestors"?On(e,this._c):[].concat(n),i],l=r[0],c=r.reduce((a,h)=>{const u=ee(e,h,o);return a.top=gt(u.top,a.top),a.right=te(u.right,a.right),a.bottom=te(u.bottom,a.bottom),a.left=gt(u.left,a.left),a},ee(e,l,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}}function Tn(t){return Ie(t)}function ne(t,e){return!j(t)||D(t).position==="fixed"?null:e?e(t):t.offsetParent}function Cn(t){let e=xt(t);for(;j(e)&&!Xt(e);){if(zt(e))return e;e=xt(e)}return null}function ie(t,e){const n=L(t);if(!j(t))return n;let i=ne(t,e);for(;i&&_n(i)&&D(i).position==="static";)i=ne(i,e);return i&&(Y(i)==="html"||Y(i)==="body"&&D(i).position==="static"&&!zt(i))?n:i||Cn(t)||n}function In(t,e,n){const i=j(e),o=q(e),s=tt(t,!0,n==="fixed",e);let r={scrollLeft:0,scrollTop:0};const l={x:0,y:0};if(i||!i&&n!=="fixed")if((Y(e)!=="body"||Rt(o))&&(r=Mt(e)),j(e)){const c=tt(e,!0);l.x=c.x+e.clientLeft,l.y=c.y+e.clientTop}else o&&(l.x=Re(o));return{x:s.left+r.scrollLeft-l.x,y:s.top+r.scrollTop-l.y,width:s.width,height:s.height}}const Ln={getClippingRect:An,convertOffsetParentRelativeRectToViewportRelativeRect:xn,isElement:P,getDimensions:Tn,getOffsetParent:ie,getDocumentElement:q,getScale:at,async getElementRects(t){let{reference:e,floating:n,strategy:i}=t;const o=this.getOffsetParent||ie,s=this.getDimensions;return{reference:In(e,await o(n),i),floating:I({x:0,y:0},await s(n))}},getClientRects:t=>Array.from(t.getClientRects()),isRTL:t=>D(t).direction==="rtl"};function kn(t,e,n,i){i===void 0&&(i={});const{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:r=!0,animationFrame:l=!1}=i,c=o&&!l,a=c||s?[...P(t)?bt(t):t.contextElement?bt(t.contextElement):[],...bt(e)]:[];a.forEach(m=>{c&&m.addEventListener("scroll",n,{passive:!0}),s&&m.addEventListener("resize",n)});let h=null;r&&(h=new ResizeObserver(()=>{n()}),P(t)&&!l&&h.observe(t),!P(t)&&t.contextElement&&!l&&h.observe(t.contextElement),h.observe(e));let u,f=l?tt(t):null;l&&p();function p(){const m=tt(t);f&&(m.x!==f.x||m.y!==f.y||m.width!==f.width||m.height!==f.height)&&n(),f=m,u=requestAnimationFrame(p)}return n(),()=>{var m;a.forEach(b=>{c&&b.removeEventListener("scroll",n),s&&b.removeEventListener("resize",n)}),(m=h)==null||m.disconnect(),h=null,l&&cancelAnimationFrame(u)}}const Rn=(t,e,n)=>{const i=new Map,o=I({platform:Ln},n),s=I({},o.platform,{_c:i});return rn(t,e,I({},o,{platform:s}))};function Mn(t){t.cleanup&&t.cleanup();const e=t._getResolvedAttachToOptions();let n=e.element;const i=Nn(e,t),o=ve(e);return o&&(n=document.body,t.shepherdElementComponent.getElement().classList.add("shepherd-centered")),t.cleanup=kn(n,t.el,()=>{if(!t.el){t.cleanup();return}Fn(n,t,i,o)}),t.target=e.element,i}function Pn(t,e){return{floatingUIOptions:Vt(t.floatingUIOptions||{},e.floatingUIOptions||{})}}function jn(t){t.cleanup&&t.cleanup(),t.cleanup=null}function Fn(t,e,n,i){return Rn(t,e.el,n).then(Dn(e,i)).then(o=>new Promise(s=>{setTimeout(()=>s(o),300)})).then(o=>{o&&o.el&&o.el.focus({preventScroll:!0})})}function Dn(t,e){return n=>{let{x:i,y:o,placement:s,middlewareData:r}=n;return t.el&&(e?Object.assign(t.el.style,{position:"fixed",left:"50%",top:"50%",transform:"translate(-50%, -50%)"}):Object.assign(t.el.style,{position:"absolute",left:`${i}px`,top:`${o}px`}),t.el.dataset.popperPlacement=s,Bn(t.el,r)),t}}function Bn(t,e){const n=t.querySelector(".shepherd-arrow");if(n&&e.arrow){const{x:i,y:o}=e.arrow;Object.assign(n.style,{left:i!=null?`${i}px`:"",top:o!=null?`${o}px`:""})}}function Nn(t,e){const n={strategy:"absolute",middleware:[]},i=Hn(e);return ve(t)||(n.middleware.push(bn(),yn({limiter:wn(),crossAxis:!0})),i&&n.middleware.push(fn({element:i})),n.placement=t.on),Vt(e.options.floatingUIOptions||{},n)}function Hn(t){return t.options.arrow&&t.el?t.el.querySelector(".shepherd-arrow"):!1}function k(){}function Vn(t,e){for(const n in e)t[n]=e[n];return t}function Pe(t){return t()}function oe(){return Object.create(null)}function St(t){t.forEach(Pe)}function $t(t){return typeof t=="function"}function H(t,e){return t!=t?e==e:t!==e||t&&typeof t=="object"||typeof t=="function"}function Wn(t){return Object.keys(t).length===0}function ht(t,e){t.appendChild(e)}function F(t,e,n){t.insertBefore(e,n||null)}function M(t){t.parentNode&&t.parentNode.removeChild(t)}function Un(t,e){for(let n=0;n<t.length;n+=1)t[n]&&t[n].d(e)}function B(t){return document.createElement(t)}function se(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function je(t){return document.createTextNode(t)}function Lt(){return je(" ")}function Yn(){return je("")}function Pt(t,e,n,i){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n,i)}function E(t,e,n){n==null?t.removeAttribute(e):t.getAttribute(e)!==n&&t.setAttribute(e,n)}function re(t,e){const n=Object.getOwnPropertyDescriptors(t.__proto__);for(const i in e)e[i]==null?t.removeAttribute(i):i==="style"?t.style.cssText=e[i]:i==="__value"?t.value=t[i]=e[i]:n[i]&&n[i].set?t[i]=e[i]:E(t,i,e[i])}function qn(t){return Array.from(t.childNodes)}function st(t,e,n){t.classList[n?"add":"remove"](e)}let vt;function yt(t){vt=t}function Fe(){if(!vt)throw new Error("Function called outside component initialization");return vt}function zn(t){Fe().$$.on_mount.push(t)}function Zt(t){Fe().$$.after_update.push(t)}const lt=[],dt=[];let ft=[];const le=[],Kn=Promise.resolve();let Bt=!1;function Xn(){Bt||(Bt=!0,Kn.then(De))}function Nt(t){ft.push(t)}const jt=new Set;let rt=0;function De(){if(rt!==0)return;const t=vt;do{try{for(;rt<lt.length;){const e=lt[rt];rt++,yt(e),$n(e.$$)}}catch(e){throw lt.length=0,rt=0,e}for(yt(null),lt.length=0,rt=0;dt.length;)dt.pop()();for(let e=0;e<ft.length;e+=1){const n=ft[e];jt.has(n)||(jt.add(n),n())}ft.length=0}while(lt.length);for(;le.length;)le.pop()();Bt=!1,jt.clear(),yt(t)}function $n(t){if(t.fragment!==null){t.update(),St(t.before_update);const e=t.dirty;t.dirty=[-1],t.fragment&&t.fragment.p(t.ctx,e),t.after_update.forEach(Nt)}}function Zn(t){const e=[],n=[];ft.forEach(i=>t.indexOf(i)===-1?e.push(i):n.push(i)),n.forEach(i=>i()),ft=e}const At=new Set;let $;function Z(){$={r:0,c:[],p:$}}function G(){$.r||St($.c),$=$.p}function v(t,e){t&&t.i&&(At.delete(t),t.i(e))}function O(t,e,n,i){if(t&&t.o){if(At.has(t))return;At.add(t),$.c.push(()=>{At.delete(t),i&&(n&&t.d(1),i())}),t.o(e)}else i&&i()}function Gn(t,e){const n={},i={},o={$$scope:1};let s=t.length;for(;s--;){const r=t[s],l=e[s];if(l){for(const c in r)c in l||(i[c]=1);for(const c in l)o[c]||(n[c]=l[c],o[c]=1);t[s]=l}else for(const c in r)o[c]=1}for(const r in i)r in n||(n[r]=void 0);return n}function et(t){t&&t.c()}function z(t,e,n,i){const{fragment:o,after_update:s}=t.$$;o&&o.m(e,n),i||Nt(()=>{const r=t.$$.on_mount.map(Pe).filter($t);t.$$.on_destroy?t.$$.on_destroy.push(...r):St(r),t.$$.on_mount=[]}),s.forEach(Nt)}function K(t,e){const n=t.$$;n.fragment!==null&&(Zn(n.after_update),St(n.on_destroy),n.fragment&&n.fragment.d(e),n.on_destroy=n.fragment=null,n.ctx=[])}function Jn(t,e){t.$$.dirty[0]===-1&&(lt.push(t),Xn(),t.$$.dirty.fill(0)),t.$$.dirty[e/31|0]|=1<<e%31}function V(t,e,n,i,o,s,r,l){l===void 0&&(l=[-1]);const c=vt;yt(t);const a=t.$$={fragment:null,ctx:[],props:s,update:k,not_equal:o,bound:oe(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(e.context||(c?c.$$.context:[])),callbacks:oe(),dirty:l,skip_bound:!1,root:e.target||c.$$.root};r&&r(a.root);let h=!1;if(a.ctx=n?n(t,e.props||{},function(u,f){const p=!(arguments.length<=2)&&arguments.length-2?arguments.length<=2?void 0:arguments[2]:f;return a.ctx&&o(a.ctx[u],a.ctx[u]=p)&&(!a.skip_bound&&a.bound[u]&&a.bound[u](p),h&&Jn(t,u)),f}):[],a.update(),h=!0,St(a.before_update),a.fragment=i?i(a.ctx):!1,e.target){if(e.hydrate){const u=qn(e.target);a.fragment&&a.fragment.l(u),u.forEach(M)}else a.fragment&&a.fragment.c();e.intro&&v(t.$$.fragment),z(t,e.target,e.anchor,e.customElement),De()}yt(c)}class W{$destroy(){K(this,1),this.$destroy=k}$on(e,n){if(!$t(n))return k;const i=this.$$.callbacks[e]||(this.$$.callbacks[e]=[]);return i.push(n),()=>{const o=i.indexOf(n);o!==-1&&i.splice(o,1)}}$set(e){this.$$set&&!Wn(e)&&(this.$$.skip_bound=!0,this.$$set(e),this.$$.skip_bound=!1)}}function Qn(t){let e,n,i,o,s;return{c(){e=B("button"),E(e,"aria-label",n=t[3]?t[3]:null),E(e,"class",i=`${t[1]||""} shepherd-button ${t[4]?"shepherd-button-secondary":""}`),e.disabled=t[2],E(e,"tabindex","0")},m(r,l){F(r,e,l),e.innerHTML=t[5],o||(s=Pt(e,"click",function(){$t(t[0])&&t[0].apply(this,arguments)}),o=!0)},p(r,l){let[c]=l;t=r,c&32&&(e.innerHTML=t[5]),c&8&&n!==(n=t[3]?t[3]:null)&&E(e,"aria-label",n),c&18&&i!==(i=`${t[1]||""} shepherd-button ${t[4]?"shepherd-button-secondary":""}`)&&E(e,"class",i),c&4&&(e.disabled=t[2])},i:k,o:k,d(r){r&&M(e),o=!1,s()}}}function ti(t,e,n){let{config:i,step:o}=e,s,r,l,c,a,h;function u(f){return J(f)?f=f.call(o):f}return t.$$set=f=>{"config"in f&&n(6,i=f.config),"step"in f&&n(7,o=f.step)},t.$$.update=()=>{t.$$.dirty&192&&(n(0,s=i.action?i.action.bind(o.tour):null),n(1,r=i.classes),n(2,l=i.disabled?u(i.disabled):!1),n(3,c=i.label?u(i.label):null),n(4,a=i.secondary),n(5,h=i.text?u(i.text):null))},[s,r,l,c,a,h,i,o]}class ei extends W{constructor(e){super(),V(this,e,ti,Qn,H,{config:6,step:7})}}function ce(t,e,n){const i=t.slice();return i[2]=e[n],i}function ae(t){let e,n,i=t[1],o=[];for(let r=0;r<i.length;r+=1)o[r]=fe(ce(t,i,r));const s=r=>O(o[r],1,1,()=>{o[r]=null});return{c(){for(let r=0;r<o.length;r+=1)o[r].c();e=Yn()},m(r,l){for(let c=0;c<o.length;c+=1)o[c]&&o[c].m(r,l);F(r,e,l),n=!0},p(r,l){if(l&3){i=r[1];let c;for(c=0;c<i.length;c+=1){const a=ce(r,i,c);o[c]?(o[c].p(a,l),v(o[c],1)):(o[c]=fe(a),o[c].c(),v(o[c],1),o[c].m(e.parentNode,e))}for(Z(),c=i.length;c<o.length;c+=1)s(c);G()}},i(r){if(!n){for(let l=0;l<i.length;l+=1)v(o[l]);n=!0}},o(r){o=o.filter(Boolean);for(let l=0;l<o.length;l+=1)O(o[l]);n=!1},d(r){Un(o,r),r&&M(e)}}}function fe(t){let e,n;return e=new ei({props:{config:t[2],step:t[0]}}),{c(){et(e.$$.fragment)},m(i,o){z(e,i,o),n=!0},p(i,o){const s={};o&2&&(s.config=i[2]),o&1&&(s.step=i[0]),e.$set(s)},i(i){n||(v(e.$$.fragment,i),n=!0)},o(i){O(e.$$.fragment,i),n=!1},d(i){K(e,i)}}}function ni(t){let e,n,i=t[1]&&ae(t);return{c(){e=B("footer"),i&&i.c(),E(e,"class","shepherd-footer")},m(o,s){F(o,e,s),i&&i.m(e,null),n=!0},p(o,s){let[r]=s;o[1]?i?(i.p(o,r),r&2&&v(i,1)):(i=ae(o),i.c(),v(i,1),i.m(e,null)):i&&(Z(),O(i,1,1,()=>{i=null}),G())},i(o){n||(v(i),n=!0)},o(o){O(i),n=!1},d(o){o&&M(e),i&&i.d()}}}function ii(t,e,n){let i,{step:o}=e;return t.$$set=s=>{"step"in s&&n(0,o=s.step)},t.$$.update=()=>{t.$$.dirty&1&&n(1,i=o.options.buttons)},[o,i]}class oi extends W{constructor(e){super(),V(this,e,ii,ni,H,{step:0})}}function si(t){let e,n,i,o,s;return{c(){e=B("button"),n=B("span"),n.textContent="×",E(n,"aria-hidden","true"),E(e,"aria-label",i=t[0].label?t[0].label:"Close Tour"),E(e,"class","shepherd-cancel-icon"),E(e,"type","button")},m(r,l){F(r,e,l),ht(e,n),o||(s=Pt(e,"click",t[1]),o=!0)},p(r,l){let[c]=l;c&1&&i!==(i=r[0].label?r[0].label:"Close Tour")&&E(e,"aria-label",i)},i:k,o:k,d(r){r&&M(e),o=!1,s()}}}function ri(t,e,n){let{cancelIcon:i,step:o}=e;const s=r=>{r.preventDefault(),o.cancel()};return t.$$set=r=>{"cancelIcon"in r&&n(0,i=r.cancelIcon),"step"in r&&n(2,o=r.step)},[i,s,o]}class li extends W{constructor(e){super(),V(this,e,ri,si,H,{cancelIcon:0,step:2})}}function ci(t){let e;return{c(){e=B("h3"),E(e,"id",t[1]),E(e,"class","shepherd-title")},m(n,i){F(n,e,i),t[3](e)},p(n,i){let[o]=i;o&2&&E(e,"id",n[1])},i:k,o:k,d(n){n&&M(e),t[3](null)}}}function ai(t,e,n){let{labelId:i,element:o,title:s}=e;Zt(()=>{J(s)&&n(2,s=s()),n(0,o.innerHTML=s,o)});function r(l){dt[l?"unshift":"push"](()=>{o=l,n(0,o)})}return t.$$set=l=>{"labelId"in l&&n(1,i=l.labelId),"element"in l&&n(0,o=l.element),"title"in l&&n(2,s=l.title)},[o,i,s,r]}class fi extends W{constructor(e){super(),V(this,e,ai,ci,H,{labelId:1,element:0,title:2})}}function ue(t){let e,n;return e=new fi({props:{labelId:t[0],title:t[2]}}),{c(){et(e.$$.fragment)},m(i,o){z(e,i,o),n=!0},p(i,o){const s={};o&1&&(s.labelId=i[0]),o&4&&(s.title=i[2]),e.$set(s)},i(i){n||(v(e.$$.fragment,i),n=!0)},o(i){O(e.$$.fragment,i),n=!1},d(i){K(e,i)}}}function he(t){let e,n;return e=new li({props:{cancelIcon:t[3],step:t[1]}}),{c(){et(e.$$.fragment)},m(i,o){z(e,i,o),n=!0},p(i,o){const s={};o&8&&(s.cancelIcon=i[3]),o&2&&(s.step=i[1]),e.$set(s)},i(i){n||(v(e.$$.fragment,i),n=!0)},o(i){O(e.$$.fragment,i),n=!1},d(i){K(e,i)}}}function ui(t){let e,n,i,o=t[2]&&ue(t),s=t[3]&&t[3].enabled&&he(t);return{c(){e=B("header"),o&&o.c(),n=Lt(),s&&s.c(),E(e,"class","shepherd-header")},m(r,l){F(r,e,l),o&&o.m(e,null),ht(e,n),s&&s.m(e,null),i=!0},p(r,l){let[c]=l;r[2]?o?(o.p(r,c),c&4&&v(o,1)):(o=ue(r),o.c(),v(o,1),o.m(e,n)):o&&(Z(),O(o,1,1,()=>{o=null}),G()),r[3]&&r[3].enabled?s?(s.p(r,c),c&8&&v(s,1)):(s=he(r),s.c(),v(s,1),s.m(e,null)):s&&(Z(),O(s,1,1,()=>{s=null}),G())},i(r){i||(v(o),v(s),i=!0)},o(r){O(o),O(s),i=!1},d(r){r&&M(e),o&&o.d(),s&&s.d()}}}function hi(t,e,n){let{labelId:i,step:o}=e,s,r;return t.$$set=l=>{"labelId"in l&&n(0,i=l.labelId),"step"in l&&n(1,o=l.step)},t.$$.update=()=>{t.$$.dirty&2&&(n(2,s=o.options.title),n(3,r=o.options.cancelIcon))},[i,o,s,r]}class di extends W{constructor(e){super(),V(this,e,hi,ui,H,{labelId:0,step:1})}}function pi(t){let e;return{c(){e=B("div"),E(e,"class","shepherd-text"),E(e,"id",t[1])},m(n,i){F(n,e,i),t[3](e)},p(n,i){let[o]=i;o&2&&E(e,"id",n[1])},i:k,o:k,d(n){n&&M(e),t[3](null)}}}function mi(t,e,n){let{descriptionId:i,element:o,step:s}=e;Zt(()=>{let{text:l}=s.options;J(l)&&(l=l.call(s)),Wt(l)?o.appendChild(l):n(0,o.innerHTML=l,o)});function r(l){dt[l?"unshift":"push"](()=>{o=l,n(0,o)})}return t.$$set=l=>{"descriptionId"in l&&n(1,i=l.descriptionId),"element"in l&&n(0,o=l.element),"step"in l&&n(2,s=l.step)},[o,i,s,r]}class gi extends W{constructor(e){super(),V(this,e,mi,pi,H,{descriptionId:1,element:0,step:2})}}function de(t){let e,n;return e=new di({props:{labelId:t[1],step:t[2]}}),{c(){et(e.$$.fragment)},m(i,o){z(e,i,o),n=!0},p(i,o){const s={};o&2&&(s.labelId=i[1]),o&4&&(s.step=i[2]),e.$set(s)},i(i){n||(v(e.$$.fragment,i),n=!0)},o(i){O(e.$$.fragment,i),n=!1},d(i){K(e,i)}}}function pe(t){let e,n;return e=new gi({props:{descriptionId:t[0],step:t[2]}}),{c(){et(e.$$.fragment)},m(i,o){z(e,i,o),n=!0},p(i,o){const s={};o&1&&(s.descriptionId=i[0]),o&4&&(s.step=i[2]),e.$set(s)},i(i){n||(v(e.$$.fragment,i),n=!0)},o(i){O(e.$$.fragment,i),n=!1},d(i){K(e,i)}}}function me(t){let e,n;return e=new oi({props:{step:t[2]}}),{c(){et(e.$$.fragment)},m(i,o){z(e,i,o),n=!0},p(i,o){const s={};o&4&&(s.step=i[2]),e.$set(s)},i(i){n||(v(e.$$.fragment,i),n=!0)},o(i){O(e.$$.fragment,i),n=!1},d(i){K(e,i)}}}function bi(t){let e,n=!A(t[2].options.title)||t[2].options.cancelIcon&&t[2].options.cancelIcon.enabled,i,o=!A(t[2].options.text),s,r=Array.isArray(t[2].options.buttons)&&t[2].options.buttons.length,l,c=n&&de(t),a=o&&pe(t),h=r&&me(t);return{c(){e=B("div"),c&&c.c(),i=Lt(),a&&a.c(),s=Lt(),h&&h.c(),E(e,"class","shepherd-content")},m(u,f){F(u,e,f),c&&c.m(e,null),ht(e,i),a&&a.m(e,null),ht(e,s),h&&h.m(e,null),l=!0},p(u,f){let[p]=f;p&4&&(n=!A(u[2].options.title)||u[2].options.cancelIcon&&u[2].options.cancelIcon.enabled),n?c?(c.p(u,p),p&4&&v(c,1)):(c=de(u),c.c(),v(c,1),c.m(e,i)):c&&(Z(),O(c,1,1,()=>{c=null}),G()),p&4&&(o=!A(u[2].options.text)),o?a?(a.p(u,p),p&4&&v(a,1)):(a=pe(u),a.c(),v(a,1),a.m(e,s)):a&&(Z(),O(a,1,1,()=>{a=null}),G()),p&4&&(r=Array.isArray(u[2].options.buttons)&&u[2].options.buttons.length),r?h?(h.p(u,p),p&4&&v(h,1)):(h=me(u),h.c(),v(h,1),h.m(e,null)):h&&(Z(),O(h,1,1,()=>{h=null}),G())},i(u){l||(v(c),v(a),v(h),l=!0)},o(u){O(c),O(a),O(h),l=!1},d(u){u&&M(e),c&&c.d(),a&&a.d(),h&&h.d()}}}function yi(t,e,n){let{descriptionId:i,labelId:o,step:s}=e;return t.$$set=r=>{"descriptionId"in r&&n(0,i=r.descriptionId),"labelId"in r&&n(1,o=r.labelId),"step"in r&&n(2,s=r.step)},[i,o,s]}class wi extends W{constructor(e){super(),V(this,e,yi,bi,H,{descriptionId:0,labelId:1,step:2})}}function ge(t){let e;return{c(){e=B("div"),E(e,"class","shepherd-arrow"),E(e,"data-popper-arrow","")},m(n,i){F(n,e,i)},d(n){n&&M(e)}}}function _i(t){let e,n,i,o,s,r,l,c,a=t[4].options.arrow&&t[4].options.attachTo&&t[4].options.attachTo.element&&t[4].options.attachTo.on&&ge();i=new wi({props:{descriptionId:t[2],labelId:t[3],step:t[4]}});let h=[{"aria-describedby":o=A(t[4].options.text)?null:t[2]},{"aria-labelledby":s=t[4].options.title?t[3]:null},t[1],{role:"dialog"},{tabindex:"0"}],u={};for(let f=0;f<h.length;f+=1)u=Vn(u,h[f]);return{c(){e=B("div"),a&&a.c(),n=Lt(),et(i.$$.fragment),re(e,u),st(e,"shepherd-has-cancel-icon",t[5]),st(e,"shepherd-has-title",t[6]),st(e,"shepherd-element",!0)},m(f,p){F(f,e,p),a&&a.m(e,null),ht(e,n),z(i,e,null),t[13](e),r=!0,l||(c=Pt(e,"keydown",t[7]),l=!0)},p(f,p){let[m]=p;f[4].options.arrow&&f[4].options.attachTo&&f[4].options.attachTo.element&&f[4].options.attachTo.on?a||(a=ge(),a.c(),a.m(e,n)):a&&(a.d(1),a=null);const b={};m&4&&(b.descriptionId=f[2]),m&8&&(b.labelId=f[3]),m&16&&(b.step=f[4]),i.$set(b),re(e,u=Gn(h,[(!r||m&20&&o!==(o=A(f[4].options.text)?null:f[2]))&&{"aria-describedby":o},(!r||m&24&&s!==(s=f[4].options.title?f[3]:null))&&{"aria-labelledby":s},m&2&&f[1],{role:"dialog"},{tabindex:"0"}])),st(e,"shepherd-has-cancel-icon",f[5]),st(e,"shepherd-has-title",f[6]),st(e,"shepherd-element",!0)},i(f){r||(v(i.$$.fragment,f),r=!0)},o(f){O(i.$$.fragment,f),r=!1},d(f){f&&M(e),a&&a.d(),K(i),t[13](null),l=!1,c()}}}const xi=9,vi=27,Ei=37,Si=39;function be(t){return t.split(" ").filter(e=>!!e.length)}function Oi(t,e,n){let{classPrefix:i,element:o,descriptionId:s,firstFocusableElement:r,focusableElements:l,labelId:c,lastFocusableElement:a,step:h,dataStepId:u}=e,f,p,m;const b=()=>o;zn(()=>{n(1,u={[`data-${i}shepherd-step-id`]:h.id}),n(9,l=o.querySelectorAll('a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), [tabindex="0"]')),n(8,r=l[0]),n(10,a=l[l.length-1])}),Zt(()=>{m!==h.options.classes&&S()});function S(){y(m),m=h.options.classes,w(m)}function y(d){if(_t(d)){const _=be(d);_.length&&o.classList.remove(..._)}}function w(d){if(_t(d)){const _=be(d);_.length&&o.classList.add(..._)}}const x=d=>{const{tour:_}=h;switch(d.keyCode){case xi:if(l.length===0){d.preventDefault();break}d.shiftKey?(document.activeElement===r||document.activeElement.classList.contains("shepherd-element"))&&(d.preventDefault(),a.focus()):document.activeElement===a&&(d.preventDefault(),r.focus());break;case vi:_.options.exitOnEsc&&h.cancel();break;case Ei:_.options.keyboardNavigation&&_.back();break;case Si:_.options.keyboardNavigation&&_.next();break}};function g(d){dt[d?"unshift":"push"](()=>{o=d,n(0,o)})}return t.$$set=d=>{"classPrefix"in d&&n(11,i=d.classPrefix),"element"in d&&n(0,o=d.element),"descriptionId"in d&&n(2,s=d.descriptionId),"firstFocusableElement"in d&&n(8,r=d.firstFocusableElement),"focusableElements"in d&&n(9,l=d.focusableElements),"labelId"in d&&n(3,c=d.labelId),"lastFocusableElement"in d&&n(10,a=d.lastFocusableElement),"step"in d&&n(4,h=d.step),"dataStepId"in d&&n(1,u=d.dataStepId)},t.$$.update=()=>{t.$$.dirty&16&&(n(5,f=h.options&&h.options.cancelIcon&&h.options.cancelIcon.enabled),n(6,p=h.options&&h.options.title))},[o,u,s,c,h,f,p,x,r,l,a,i,b,g]}class Ai extends W{constructor(e){super(),V(this,e,Oi,_i,H,{classPrefix:11,element:0,descriptionId:2,firstFocusableElement:8,focusableElements:9,labelId:3,lastFocusableElement:10,step:4,dataStepId:1,getElement:12})}get getElement(){return this.$$.ctx[12]}}class Ht extends Ut{constructor(e,n){return n===void 0&&(n={}),super(e,n),this.tour=e,this.classPrefix=this.tour.options?xe(this.tour.options.classPrefix):"",this.styles=e.styles,this._resolvedAttachTo=null,_e(this),this._setOptions(n),this}cancel(){this.tour.cancel(),this.trigger("cancel")}complete(){this.tour.complete(),this.trigger("complete")}destroy(){jn(this),Wt(this.el)&&(this.el.remove(),this.el=null),this._updateStepTargetOnHide(),this.trigger("destroy")}getTour(){return this.tour}hide(){this.tour.modal.hide(),this.trigger("before-hide"),this.el&&(this.el.hidden=!0),this._updateStepTargetOnHide(),this.trigger("hide")}_resolveAttachToOptions(){return this._resolvedAttachTo=nn(this),this._resolvedAttachTo}_getResolvedAttachToOptions(){return this._resolvedAttachTo===null?this._resolveAttachToOptions():this._resolvedAttachTo}isOpen(){return!!(this.el&&!this.el.hidden)}show(){return J(this.options.beforeShowPromise)?Promise.resolve(this.options.beforeShowPromise()).then(()=>this._show()):Promise.resolve(this._show())}updateStepOptions(e){Object.assign(this.options,e),this.shepherdElementComponent&&this.shepherdElementComponent.$set({step:this})}getElement(){return this.el}getTarget(){return this.target}_createTooltipContent(){const e=`${this.id}-description`,n=`${this.id}-label`;return this.shepherdElementComponent=new Ai({target:this.tour.options.stepsContainer||document.body,props:{classPrefix:this.classPrefix,descriptionId:e,labelId:n,step:this,styles:this.styles}}),this.shepherdElementComponent.getElement()}_scrollTo(e){const{element:n}=this._getResolvedAttachToOptions();J(this.options.scrollToHandler)?this.options.scrollToHandler(n):Qe(n)&&typeof n.scrollIntoView=="function"&&n.scrollIntoView(e)}_getClassOptions(e){const n=this.tour&&this.tour.options&&this.tour.options.defaultStepOptions,i=e.classes?e.classes:"",o=n&&n.classes?n.classes:"",s=[...i.split(" "),...o.split(" ")],r=new Set(s);return Array.from(r).join(" ").trim()}_setOptions(e){e===void 0&&(e={});let n=this.tour&&this.tour.options&&this.tour.options.defaultStepOptions;n=Vt({},n||{}),this.options=Object.assign({arrow:!0},n,e,Pn(n,e));const{when:i}=this.options;this.options.classes=this._getClassOptions(e),this.destroy(),this.id=this.options.id||`step-${Yt()}`,i&&Object.keys(i).forEach(o=>{this.on(o,i[o],this)})}_setupElements(){A(this.el)||this.destroy(),this.el=this._createTooltipContent(),this.options.advanceOn&&en(this),Mn(this)}_show(){this.trigger("before-show"),this._resolveAttachToOptions(),this._setupElements(),this.tour.modal||this.tour._setupModal(),this.tour.modal.setupForStep(this),this._styleTargetElementForStep(this),this.el.hidden=!1,this.options.scrollTo&&setTimeout(()=>{this._scrollTo(this.options.scrollTo)}),this.el.hidden=!1;const e=this.shepherdElementComponent.getElement(),n=this.target||document.body;n.classList.add(`${this.classPrefix}shepherd-enabled`),n.classList.add(`${this.classPrefix}shepherd-target`),e.classList.add("shepherd-enabled"),this.trigger("show")}_styleTargetElementForStep(e){const n=e.target;n&&(e.options.highlightClass&&n.classList.add(e.options.highlightClass),n.classList.remove("shepherd-target-click-disabled"),e.options.canClickTarget===!1&&n.classList.add("shepherd-target-click-disabled"))}_updateStepTargetOnHide(){const e=this.target||document.body;this.options.highlightClass&&e.classList.remove(this.options.highlightClass),e.classList.remove("shepherd-target-click-disabled",`${this.classPrefix}shepherd-enabled`,`${this.classPrefix}shepherd-target`)}}function Ti(t){if(t){const{steps:e}=t;e.forEach(n=>{n.options&&n.options.canClickTarget===!1&&n.options.attachTo&&n.target instanceof HTMLElement&&n.target.classList.remove("shepherd-target-click-disabled")})}}function Ci(t){let{width:e,height:n,x:i=0,y:o=0,r:s=0}=t;const{innerWidth:r,innerHeight:l}=window,{topLeft:c=0,topRight:a=0,bottomRight:h=0,bottomLeft:u=0}=typeof s=="number"?{topLeft:s,topRight:s,bottomRight:s,bottomLeft:s}:s;return`M${r},${l}H0V0H${r}V${l}ZM${i+c},${o}a${c},${c},0,0,0-${c},${c}V${n+o-u}a${u},${u},0,0,0,${u},${u}H${e+i-h}a${h},${h},0,0,0,${h}-${h}V${o+a}a${a},${a},0,0,0-${a}-${a}Z`}function Ii(t){let e,n,i,o,s;return{c(){e=se("svg"),n=se("path"),E(n,"d",t[2]),E(e,"class",i=`${t[1]?"shepherd-modal-is-visible":""} shepherd-modal-overlay-container`)},m(r,l){F(r,e,l),ht(e,n),t[11](e),o||(s=Pt(e,"touchmove",t[3]),o=!0)},p(r,l){let[c]=l;c&4&&E(n,"d",r[2]),c&2&&i!==(i=`${r[1]?"shepherd-modal-is-visible":""} shepherd-modal-overlay-container`)&&E(e,"class",i)},i:k,o:k,d(r){r&&M(e),t[11](null),o=!1,s()}}}function Be(t){if(!t)return null;const n=t instanceof HTMLElement&&window.getComputedStyle(t).overflowY;return n!=="hidden"&&n!=="visible"&&t.scrollHeight>=t.clientHeight?t:Be(t.parentElement)}function Li(t,e){const n=t.getBoundingClientRect();let i=n.y||n.top,o=n.bottom||i+n.height;if(e){const r=e.getBoundingClientRect(),l=r.y||r.top,c=r.bottom||l+r.height;i=Math.max(i,l),o=Math.min(o,c)}const s=Math.max(o-i,0);return{y:i,height:s}}function ki(t,e,n){let{element:i,openingProperties:o}=e;Yt();let s=!1,r,l;a();const c=()=>i;function a(){n(4,o={width:0,height:0,x:0,y:0,r:0})}function h(){n(1,s=!1),y()}function u(g,d,_,T){if(g===void 0&&(g=0),d===void 0&&(d=0),T){const{y:C,height:X}=Li(T,_),{x:R,width:U,left:N}=T.getBoundingClientRect();n(4,o={width:U+g*2,height:X+g*2,x:(R||N)-g,y:C-g,r:d})}else a()}function f(g){y(),g.tour.options.useModalOverlay?(w(g),p()):h()}function p(){n(1,s=!0)}const m=g=>{g.preventDefault()},b=g=>{g.stopPropagation()};function S(){window.addEventListener("touchmove",m,{passive:!1})}function y(){r&&(cancelAnimationFrame(r),r=void 0),window.removeEventListener("touchmove",m,{passive:!1})}function w(g){const{modalOverlayOpeningPadding:d,modalOverlayOpeningRadius:_}=g.options,T=Be(g.target),C=()=>{r=void 0,u(d,_,T,g.target),r=requestAnimationFrame(C)};C(),S()}function x(g){dt[g?"unshift":"push"](()=>{i=g,n(0,i)})}return t.$$set=g=>{"element"in g&&n(0,i=g.element),"openingProperties"in g&&n(4,o=g.openingProperties)},t.$$.update=()=>{t.$$.dirty&16&&n(2,l=Ci(o))},[i,s,l,b,o,c,a,h,u,f,p,x]}class Ri extends W{constructor(e){super(),V(this,e,ki,Ii,H,{element:0,openingProperties:4,getElement:5,closeModalOpening:6,hide:7,positionModal:8,setupForStep:9,show:10})}get getElement(){return this.$$.ctx[5]}get closeModalOpening(){return this.$$.ctx[6]}get hide(){return this.$$.ctx[7]}get positionModal(){return this.$$.ctx[8]}get setupForStep(){return this.$$.ctx[9]}get show(){return this.$$.ctx[10]}}const ct=new Ut;class Mi extends Ut{constructor(e){e===void 0&&(e={}),super(e),_e(this);const n={exitOnEsc:!0,keyboardNavigation:!0};return this.options=Object.assign({},n,e),this.classPrefix=xe(this.options.classPrefix),this.steps=[],this.addSteps(this.options.steps),["active","cancel","complete","inactive","show","start"].map(o=>{(s=>{this.on(s,r=>{r=r||{},r.tour=this,ct.trigger(s,r)})})(o)}),this._setTourID(),this}addStep(e,n){let i=e;return i instanceof Ht?i.tour=this:i=new Ht(this,i),A(n)?this.steps.push(i):this.steps.splice(n,0,i),i}addSteps(e){return Array.isArray(e)&&e.forEach(n=>{this.addStep(n)}),this}back(){const e=this.steps.indexOf(this.currentStep);this.show(e-1,!1)}async cancel(){if(this.options.confirmCancel){const e=typeof this.options.confirmCancel=="function",n=this.options.confirmCancelMessage||"Are you sure you want to stop the tour?";(e?await this.options.confirmCancel():window.confirm(n))&&this._done("cancel")}else this._done("cancel")}complete(){this._done("complete")}getById(e){return this.steps.find(n=>n.id===e)}getCurrentStep(){return this.currentStep}hide(){const e=this.getCurrentStep();if(e)return e.hide()}isActive(){return ct.activeTour===this}next(){const e=this.steps.indexOf(this.currentStep);e===this.steps.length-1?this.complete():this.show(e+1,!0)}removeStep(e){const n=this.getCurrentStep();this.steps.some((i,o)=>{if(i.id===e)return i.isOpen()&&i.hide(),i.destroy(),this.steps.splice(o,1),!0}),n&&n.id===e&&(this.currentStep=void 0,this.steps.length?this.show(0):this.cancel())}show(e,n){e===void 0&&(e=0),n===void 0&&(n=!0);const i=_t(e)?this.getById(e):this.steps[e];i&&(this._updateStateBeforeShow(),J(i.options.showOn)&&!i.options.showOn()?this._skipStep(i,n):(this.trigger("show",{step:i,previous:this.currentStep}),this.currentStep=i,i.show()))}start(){this.trigger("start"),this.focusedElBeforeOpen=document.activeElement,this.currentStep=null,this._setupModal(),this._setupActiveTour(),this.next()}_done(e){const n=this.steps.indexOf(this.currentStep);if(Array.isArray(this.steps)&&this.steps.forEach(i=>i.destroy()),Ti(this),this.trigger(e,{index:n}),ct.activeTour=null,this.trigger("inactive",{tour:this}),this.modal&&this.modal.hide(),(e==="cancel"||e==="complete")&&this.modal){const i=document.querySelector(".shepherd-modal-overlay-container");i&&i.remove()}Wt(this.focusedElBeforeOpen)&&this.focusedElBeforeOpen.focus()}_setupActiveTour(){this.trigger("active",{tour:this}),ct.activeTour=this}_setupModal(){this.modal=new Ri({target:this.options.modalContainer||document.body,props:{classPrefix:this.classPrefix,styles:this.styles}})}_skipStep(e,n){const i=this.steps.indexOf(e);if(i===this.steps.length-1)this.complete();else{const o=n?i+1:i-1;this.show(o,n)}}_updateStateBeforeShow(){this.currentStep&&this.currentStep.hide(),this.isActive()||this._setupActiveTour()}_setTourID(){const e=this.options.tourName||"tour";this.id=`${e}--${Yt()}`}}const Pi=typeof window>"u";class ye{constructor(){}}Pi?Object.assign(ct,{Tour:ye,Step:ye}):Object.assign(ct,{Tour:Mi,Step:Ht});export{ct as S};
