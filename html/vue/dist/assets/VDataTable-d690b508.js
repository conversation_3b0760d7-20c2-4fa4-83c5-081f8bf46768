import{bb as T,aQ as J,I as A,a0 as F,B as p,a7 as U,q as d,a8 as R,by as me,cd as ge,aZ as De,aS as Be,bH as Fe,aT as ue,l as L,ce as he,F as Y,ar as _e,az as Oe,aA as ie,bs as $e,aj as ne,bL as Ee,c3 as ye,K as Le,bo as Me,O as Ne,P as Ae,R as He,bp as Ke,C as G,cf as Re,E as le,cb as de,bk as W,bd as Q,W as N,cg as oe,bX as Ge,bY as Ue,U as je,b$ as ze,ag as We,ah as z,ch as Qe,aG as ce,ci as re,cj as Ye,aK as Xe,s as fe,a6 as Ze}from"./index-9a5dc664.js";import{m as Je,V as qe}from"./VSelectionControl-182ca2ca.js";import{g as et,h as tt,e as ve}from"./VTextField-3e2d458d.js";import{V as at}from"./VChip-a30ee730.js";const nt=T({indeterminate:Boolean,indeterminateIcon:{type:J,default:"$checkboxIndeterminate"},...Je({falseIcon:"$checkboxOff",trueIcon:"$checkboxOn"})},"v-checkbox-btn"),q=A()({name:"VCheckboxBtn",props:nt(),emits:{"update:modelValue":e=>!0,"update:indeterminate":e=>!0},setup(e,n){let{slots:t}=n;const a=F(e,"indeterminate"),l=F(e,"modelValue");function s(u){a.value&&(a.value=!1)}const r=p(()=>e.indeterminate?e.indeterminateIcon:e.falseIcon),o=p(()=>e.indeterminate?e.indeterminateIcon:e.trueIcon);return U(()=>d(qe,R(e,{modelValue:l.value,"onUpdate:modelValue":[u=>l.value=u,s],class:["v-checkbox-btn",e.class],style:e.style,type:"checkbox",inline:!0,falseIcon:r.value,trueIcon:o.value,"aria-checked":e.indeterminate?"mixed":void 0}),t)),{}}}),lt=T({chips:Boolean,closableChips:Boolean,eager:Boolean,hideNoData:Boolean,hideSelected:Boolean,menu:Boolean,menuIcon:{type:J,default:"$dropdown"},menuProps:{type:Object},multiple:Boolean,noDataText:{type:String,default:"$vuetify.noDataText"},openOnClear:Boolean,valueComparator:{type:Function,default:me},...ge({itemChildren:!1})},"v-select"),ot=A()({name:"VSelect",props:{...lt(),...De(et({modelValue:null}),["validationValue","dirty","appendInnerIcon"]),...Be({transition:{component:Fe}})},emits:{"update:focused":e=>!0,"update:modelValue":e=>!0,"update:menu":e=>!0},setup(e,n){let{slots:t}=n;const{t:a}=ue(),l=L(),s=L(),r=F(e,"menu"),o=p({get:()=>r.value,set:h=>{var k;r.value&&!h&&((k=s.value)!=null&&k.ΨopenChildren)||(r.value=h)}}),{items:u,transformIn:f,transformOut:i}=he(e),c=F(e,"modelValue",[],h=>f(ye(h)),h=>{const k=i(h);return e.multiple?k:k[0]??null}),v=tt(),y=p(()=>c.value.map(h=>u.value.find(k=>e.valueComparator(k.value,h.value))||h)),x=p(()=>y.value.map(h=>h.props.value)),w=L(!1);let P="",b;const g=p(()=>e.hideSelected?u.value.filter(h=>!y.value.some(k=>k===h)):u.value),m=L();function D(h){e.openOnClear&&(o.value=!0)}function I(){e.hideNoData&&!u.value.length||e.readonly||v!=null&&v.isReadonly.value||(o.value=!o.value)}function j(h){var S,E,K,V;if(e.readonly||v!=null&&v.isReadonly.value)return;["Enter"," ","ArrowDown","ArrowUp","Home","End"].includes(h.key)&&h.preventDefault(),["Enter","ArrowDown"," "].includes(h.key)&&(o.value=!0),["Escape","Tab"].includes(h.key)&&(o.value=!1),h.key==="ArrowDown"?(S=m.value)==null||S.focus("next"):h.key==="ArrowUp"?(E=m.value)==null||E.focus("prev"):h.key==="Home"?(K=m.value)==null||K.focus("first"):h.key==="End"&&((V=m.value)==null||V.focus("last"));const k=1e3;function $(C){const B=C.key.length===1,ae=!C.ctrlKey&&!C.metaKey&&!C.altKey;return B&&ae}if(e.multiple||!$(h))return;const X=performance.now();X-b>k&&(P=""),P+=h.key.toLowerCase(),b=X;const Z=u.value.find(C=>C.title.toLowerCase().startsWith(P));Z!==void 0&&(c.value=[Z])}function _(h){if(e.multiple){const k=x.value.findIndex($=>e.valueComparator($,h.value));if(k===-1)c.value=[...c.value,h];else{const $=[...c.value];$.splice(k,1),c.value=$}}else c.value=[h],o.value=!1}function M(h){var k;(k=m.value)!=null&&k.$el.contains(h.relatedTarget)||(o.value=!1)}function O(h){w.value=!0}function H(h){var k;h.relatedTarget==null&&((k=l.value)==null||k.focus())}return U(()=>{const h=!!(e.chips||t.chip),k=!!(!e.hideNoData||g.value.length||t.prepend||t.append||t["no-data"]),$=c.value.length>0,[X]=ve.filterProps(e),Z=$||!w.value&&e.label&&!e.persistentPlaceholder?void 0:e.placeholder;return d(ve,R({ref:l},X,{modelValue:c.value.map(S=>S.props.value).join(", "),"onUpdate:modelValue":S=>{S==null&&(c.value=[])},focused:w.value,"onUpdate:focused":S=>w.value=S,validationValue:c.externalValue,dirty:$,class:["v-select",{"v-select--active-menu":o.value,"v-select--chips":!!e.chips,[`v-select--${e.multiple?"multiple":"single"}`]:!0,"v-select--selected":c.value.length},e.class],style:e.style,appendInnerIcon:e.menuIcon,readonly:!0,placeholder:Z,"onClick:clear":D,"onMousedown:control":I,onBlur:M,onKeydown:j}),{...t,default:()=>d(Y,null,[d(_e,R({ref:s,modelValue:o.value,"onUpdate:modelValue":S=>o.value=S,activator:"parent",contentClass:"v-select__content",eager:e.eager,maxHeight:310,openOnClick:!1,closeOnContentClick:!1,transition:e.transition},e.menuProps),{default:()=>[k&&d(Oe,{ref:m,selected:x.value,selectStrategy:e.multiple?"independent":"single-independent",onMousedown:S=>S.preventDefault(),onFocusin:O,onFocusout:H},{default:()=>{var S,E,K;return[!g.value.length&&!e.hideNoData&&(((S=t["no-data"])==null?void 0:S.call(t))??d(ie,{title:a(e.noDataText)},null)),(E=t["prepend-item"])==null?void 0:E.call(t),g.value.map((V,C)=>{var B;return t.item?(B=t.item)==null?void 0:B.call(t,{item:V,index:C,props:R(V.props,{onClick:()=>_(V)})}):d(ie,R({key:C},V.props,{onClick:()=>_(V)}),{prepend:ae=>{let{isSelected:Te}=ae;return e.multiple&&!e.hideSelected?d(q,{modelValue:Te,ripple:!1,tabindex:"-1"},null):void 0}})}),(K=t["append-item"])==null?void 0:K.call(t)]}})]}),y.value.map((S,E)=>{var C;function K(B){B.stopPropagation(),B.preventDefault(),_(S)}const V={"onClick:close":K,modelValue:!0,"onUpdate:modelValue":void 0};return d("div",{key:S.value,class:"v-select__selection"},[h?t.chip?d($e,{key:"chip-defaults",defaults:{VChip:{closable:e.closableChips,size:"small",text:S.title}}},{default:()=>{var B;return[(B=t.chip)==null?void 0:B.call(t,{item:S,index:E,props:V})]}}):d(at,R({key:"chip",closable:e.closableChips,size:"small",text:S.title},V),null):((C=t.selection)==null?void 0:C.call(t,{item:S,index:E}))??d("span",{class:"v-select__selection-text"},[S.title,e.multiple&&E<y.value.length-1&&d("span",{class:"v-select__selection-comma"},[ne(",")])])])})])})}),Ee({isFocused:w,menu:o,select:_},l)}});const ut=A()({name:"VTable",props:{fixedHeader:Boolean,fixedFooter:Boolean,height:[Number,String],hover:Boolean,...Le(),...Me(),...Ne(),...Ae()},setup(e,n){let{slots:t}=n;const{themeClasses:a}=He(e),{densityClasses:l}=Ke(e);return U(()=>d(e.tag,{class:["v-table",{"v-table--fixed-height":!!e.height,"v-table--fixed-header":e.fixedHeader,"v-table--fixed-footer":e.fixedFooter,"v-table--has-top":!!t.top,"v-table--has-bottom":!!t.bottom,"v-table--hover":e.hover},a.value,l.value,e.class],style:e.style},{default:()=>{var s,r,o;return[(s=t.top)==null?void 0:s.call(t),t.default?d("div",{class:"v-table__wrapper",style:{height:G(e.height)}},[d("table",null,[t.default()])]):(r=t.wrapper)==null?void 0:r.call(t),(o=t.bottom)==null?void 0:o.call(t)]}})),{}}}),se=Re({align:{type:String,default:"start"},fixed:Boolean,fixedOffset:[Number,String],height:[Number,String],lastFixed:Boolean,noPadding:Boolean,tag:String,width:[Number,String]},(e,n)=>{let{slots:t,attrs:a}=n;const l=e.tag??"td";return d(l,R({class:["v-data-table__td",{"v-data-table-column--fixed":e.fixed,"v-data-table-column--last-fixed":e.lastFixed,"v-data-table-column--no-padding":e.noPadding},`v-data-table-column--align-${e.align}`],style:{height:G(e.height),width:G(e.width),left:G(e.fixedOffset||null)}},a),{default:()=>{var s;return[(s=t.default)==null?void 0:s.call(t)]}})}),rt=T({headers:{type:Array,default:()=>[]}},"v-data-table-header"),be=Symbol.for("vuetify:data-table-headers");function st(e,n){const t=L([]),a=L([]);le(()=>e.headers,()=>{var x,w,P;const s=e.headers.length?Array.isArray(e.headers[0])?e.headers:[e.headers]:[],r=s.flatMap((b,g)=>b.map(m=>({column:m,row:g}))),o=s.length,u={title:"",sortable:!1},f={...u,width:48};if((x=n==null?void 0:n.groupBy)!=null&&x.value.length){const b=r.findIndex(g=>{let{column:m}=g;return m.key==="data-table-group"});b<0?r.unshift({column:{...u,key:"data-table-group",title:"Group",rowspan:o},row:0}):r.splice(b,1,{column:{...u,...r[b].column},row:r[b].row})}if((w=n==null?void 0:n.showSelect)!=null&&w.value){const b=r.findIndex(g=>{let{column:m}=g;return m.key==="data-table-select"});b<0?r.unshift({column:{...f,key:"data-table-select",rowspan:o},row:0}):r.splice(b,1,{column:{...f,...r[b].column},row:r[b].row})}if((P=n==null?void 0:n.showExpand)!=null&&P.value){const b=r.findIndex(g=>{let{column:m}=g;return m.key==="data-table-expand"});b<0?r.push({column:{...f,key:"data-table-expand",rowspan:o},row:0}):r.splice(b,1,{column:{...f,...r[b].column},row:r[b].row})}const i=de(o).map(()=>[]),c=de(o).fill(0);let v=0;r.forEach(b=>{let{column:g,row:m}=b;const D=g.key??`data-table-column-${v++}`;for(let I=m;I<=m+(g.rowspan??1)-1;I++)i[I].push({...g,key:D,fixedOffset:c[I],sortable:g.sortable??!!g.key}),c[I]+=g.width??0}),i.forEach(b=>{for(let g=b.length;g--;g>=0)if(b[g].fixed){b[g].lastFixed=!0;return}});const y=new Set;t.value=i.map(b=>{const g=[];for(const m of b)y.has(m.key)||(y.add(m.key),g.push(m));return g}),a.value=i.at(-1)??[]},{deep:!0,immediate:!0});const l={headers:t,columns:a};return W(be,l),l}function ee(){const e=Q(be);if(!e)throw new Error("Missing headers!");return e}const it=T({showSelect:Boolean,modelValue:{type:Array,default:()=>[]}},"v-data-table-select"),xe=Symbol.for("vuetify:data-table-selection");function dt(e,n){const t=F(e,"modelValue",e.modelValue,c=>new Set(c),c=>[...c.values()]);function a(c){return c.every(v=>t.value.has(v.value))}function l(c){return c.some(v=>t.value.has(v.value))}function s(c,v){const y=new Set(t.value);for(const x of c)v?y.add(x.value):y.delete(x.value);t.value=y}function r(c){s([c],!a([c]))}function o(c){s(n.value,c)}const u=p(()=>t.value.size>0),f=p(()=>a(n.value)),i={toggleSelect:r,select:s,selectAll:o,isSelected:a,isSomeSelected:l,someSelected:u,allSelected:f};return W(xe,i),i}function te(){const e=Q(xe);if(!e)throw new Error("Missing selection!");return e}const ct=T({sortBy:{type:Array,default:()=>[]},multiSort:Boolean,mustSort:Boolean},"v-data-table-sort"),ke=Symbol.for("vuetify:data-table-sort");function ft(e){const n=F(e,"sortBy"),t=N(e,"mustSort"),a=N(e,"multiSort");return{sortBy:n,mustSort:t,multiSort:a}}function vt(e){const{sortBy:n,mustSort:t,multiSort:a,page:l}=e,r={sortBy:n,toggleSort:o=>{let u=n.value.map(i=>({...i}))??[];const f=u.find(i=>i.key===o);f?f.order==="desc"?t.value?f.order="asc":u=u.filter(i=>i.key!==o):f.order="desc":a.value?u=[...u,{key:o,order:"asc"}]:u=[{key:o,order:"asc"}],n.value=u,l&&(l.value=1)}};return W(ke,r),r}function mt(){const e=Q(ke);if(!e)throw new Error("Missing sort!");return e}function gt(e,n,t){const a=p(()=>t.value.reduce((s,r)=>(r.sort&&(s[r.key]=r.sort),s),{}));return{sortedItems:p(()=>n.value.length?ht(e.value,n.value,"en",a.value):e.value)}}function ht(e,n,t,a){const l=new Intl.Collator(t,{sensitivity:"accent",usage:"sort"});return[...e].sort((s,r)=>{for(let o=0;o<n.length;o++){const u=n[o].key,f=n[o].order;if(f===!1)continue;let i=oe(s.raw,u),c=oe(r.raw,u);if(f==="desc"&&([i,c]=[c,i]),a!=null&&a[u]){const v=a[u](i,c);if(!v)continue;return v}if(!(i==null||c==null)){if(i instanceof Date&&c instanceof Date)return i.getTime()-c.getTime();if([i,c]=[i,c].map(v=>(v||"").toString().toLocaleLowerCase()),i!==c)return!isNaN(i)&&!isNaN(c)?Number(i)-Number(c):l.compare(i,c)}}return 0})}const yt=A()({name:"VDataTableHeaders",props:{color:String,sticky:Boolean,multiSort:Boolean,sortAscIcon:{type:J,default:"$sortAsc"},sortDescIcon:{type:J,default:"$sortDesc"},...Ge()},setup(e,n){let{slots:t,emit:a}=n;const{toggleSort:l,sortBy:s}=mt(),{someSelected:r,allSelected:o,selectAll:u}=te(),{columns:f,headers:i}=ee(),{loaderClasses:c}=Ue(e),v=(g,m)=>!e.sticky&&!g.fixed?null:{position:"sticky",zIndex:g.fixed?4:e.sticky?3:void 0,left:g.fixed?G(g.fixedOffset):void 0,top:e.sticky?`calc(var(--v-table-header-height) * ${m})`:void 0};function y(g){const m=s.value.find(D=>D.key===g);return m?m.order==="asc"?e.sortAscIcon:e.sortDescIcon:e.sortAscIcon}const{backgroundColorClasses:x,backgroundColorStyles:w}=je(e,"color"),P=p(()=>({headers:i.value,columns:f.value,toggleSort:l,sortBy:s.value,someSelected:r.value,allSelected:o.value,selectAll:u,getSortIcon:y,getFixedStyles:v})),b=g=>{let{column:m,x:D,y:I}=g;const j=!!s.value.find(M=>M.key===m.key),_=m.key==="data-table-select"||m.key==="data-table-expand";return d(se,{tag:"th",align:m.align,class:["v-data-table__th",{"v-data-table__th--sortable":m.sortable,"v-data-table__th--sorted":j},c.value],style:{width:G(m.width),minWidth:G(m.width),...v(m,I)},colspan:m.colspan,rowspan:m.rowspan,onClick:m.sortable?()=>l(m.key):void 0,lastFixed:m.lastFixed,noPadding:_},{default:()=>{var H;const M=`column.${m.key}`,O={column:m,selectAll:u};return t[M]?t[M](O):m.key==="data-table-select"?((H=t["column.data-table-select"])==null?void 0:H.call(t,O))??d(q,{modelValue:o.value,indeterminate:r.value&&!o.value,"onUpdate:modelValue":u},null):d("div",{class:"v-data-table-header__content"},[d("span",null,[m.title]),m.sortable&&d(We,{key:"icon",class:"v-data-table-header__sort-icon",icon:y(m.key)},null),e.multiSort&&j&&d("div",{key:"badge",class:["v-data-table-header__sort-badge",...x.value],style:w.value},[s.value.findIndex(h=>h.key===m.key)+1])])}})};U(()=>d(Y,null,[t.headers?t.headers(P.value):i.value.map((g,m)=>d("tr",null,[g.map((D,I)=>d(b,{column:D,x:I,y:m},null))])),e.loading&&d("tr",{class:"v-data-table__progress"},[d("th",{colspan:f.value.length},[d(ze,{name:"v-data-table-headers",active:!0,color:typeof e.loading=="boolean"?void 0:e.loading,indeterminate:!0},{default:t.loader})])])]))}}),bt=T({groupBy:{type:Array,default:()=>[]}},"data-table-group"),pe=Symbol.for("vuetify:data-table-group");function xt(e){return{groupBy:F(e,"groupBy")}}function kt(e){const{groupBy:n,sortBy:t}=e,a=L(new Set),l=p(()=>n.value.map(f=>({...f,order:f.order??!1})).concat(t.value));function s(f){return a.value.has(f.id)}function r(f){const i=new Set(a.value);s(f)?i.delete(f.id):i.add(f.id),a.value=i}function o(f){function i(c){const v=[];for(const y of c.items)y.type==="item"?v.push(y):v.push(...i(y));return v}return i({type:"group-header",items:f,id:"dummy",key:"dummy",value:"dummy",depth:0})}const u={sortByWithGroups:l,toggleGroup:r,opened:a,groupBy:n,extractRows:o,isGroupOpen:s};return W(pe,u),u}function we(){const e=Q(pe);if(!e)throw new Error("Missing group!");return e}function pt(e,n){if(!e.length)return[];const t=new Map;for(const a of e){const l=oe(a.raw,n);t.has(l)||t.set(l,[]),t.get(l).push(a)}return t}function Se(e,n){let t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"root";if(!n.length)return[];const l=pt(e,n[0]),s=[],r=n.slice(1);return l.forEach((o,u)=>{const f=n[0],i=`${a}_${f}_${u}`;s.push({depth:t,id:i,key:f,value:u,items:r.length?Se(o,r,t+1,i):o,type:"group-header"})}),s}function Pe(e,n){const t=[];for(const a of e)a.type==="group-header"?(a.value!=null&&t.push(a),(n.has(a.id)||a.value==null)&&t.push(...Pe(a.items,n))):t.push(a);return t}function wt(e,n,t){return{flatItems:p(()=>{if(!n.value.length)return e.value;const l=Se(e.value,n.value.map(s=>s.key));return Pe(l,t.value)})}}const St=A()({name:"VDataTableGroupHeaderRow",props:{item:{type:Object,required:!0}},setup(e,n){let{slots:t}=n;const{isGroupOpen:a,toggleGroup:l,extractRows:s}=we(),{isSelected:r,isSomeSelected:o,select:u}=te(),{columns:f}=ee(),i=p(()=>s([e.item]));return()=>d("tr",{class:"v-data-table-group-header-row",style:{"--v-data-table-group-header-row-depth":e.item.depth}},[f.value.map(c=>{var v,y;if(c.key==="data-table-group"){const x=a(e.item)?"$expand":"$next",w=()=>l(e.item);return((v=t["data-table-group"])==null?void 0:v.call(t,{item:e.item,count:i.value.length,props:{icon:x,onClick:w}}))??d(se,{class:"v-data-table-group-header-row__column"},{default:()=>[d(z,{size:"small",variant:"text",icon:x,onClick:w},null),d("span",null,[e.item.value]),d("span",null,[ne("("),i.value.length,ne(")")])]})}if(c.key==="data-table-select"){const x=r(i.value),w=o(i.value)&&!x,P=b=>u(i.value,b);return((y=t["data-table-select"])==null?void 0:y.call(t,{props:{modelValue:x,indeterminate:w,"onUpdate:modelValue":P}}))??d("td",null,[d(q,{modelValue:x,indeterminate:w,"onUpdate:modelValue":P},null)])}return d("td",null,null)})])}}),Pt=T({expandOnClick:Boolean,showExpand:Boolean,expanded:{type:Array,default:()=>[]}},"v-data-table-expand"),Ie=Symbol.for("vuetify:datatable:expanded");function It(e){const n=N(e,"expandOnClick"),t=F(e,"expanded",e.expanded,o=>new Set(o),o=>[...o.values()]);function a(o,u){const f=new Set(t.value);u?f.add(o.value):f.delete(o.value),t.value=f}function l(o){return t.value.has(o.value)}function s(o){a(o,!l(o))}const r={expand:a,expanded:t,expandOnClick:n,isExpanded:l,toggleExpand:s};return W(Ie,r),r}function Ce(){const e=Q(Ie);if(!e)throw new Error("foo");return e}const Ct=Qe({name:"VDataTableRow",props:{index:Number,item:Object,onClick:Function},setup(e,n){let{slots:t}=n;const{isSelected:a,toggleSelect:l}=te(),{isExpanded:s,toggleExpand:r}=Ce(),{columns:o}=ee();U(()=>d("tr",{class:["v-data-table__tr",{"v-data-table__tr--clickable":!!e.onClick}],onClick:e.onClick},[e.item&&o.value.map((u,f)=>d(se,{align:u.align,fixed:u.fixed,fixedOffset:u.fixedOffset,lastFixed:u.lastFixed,noPadding:u.key==="data-table-select"||u.key==="data-table-expand",width:u.width},{default:()=>{var y,x;const i=e.item,c=`item.${u.key}`,v={index:e.index,item:e.item,columns:o.value,isSelected:a,toggleSelect:l,isExpanded:s,toggleExpand:r};return t[c]?t[c](v):u.key==="data-table-select"?((y=t["item.data-table-select"])==null?void 0:y.call(t,v))??d(q,{modelValue:a([i]),onClick:ce(()=>l(i),["stop"])},null):u.key==="data-table-expand"?((x=t["item.data-table-expand"])==null?void 0:x.call(t,v))??d(z,{icon:s(i)?"$collapse":"$expand",size:"small",variant:"text",onClick:ce(()=>r(i),["stop"])},null):re(i.columns,u.key)}}))]))}}),Vt=A()({name:"VDataTableRows",props:{loading:[Boolean,String],loadingText:{type:String,default:"$vuetify.dataIterator.loadingText"},hideNoData:Boolean,items:{type:Array,default:()=>[]},noDataText:{type:String,default:"$vuetify.noDataText"},rowHeight:Number,"onClick:row":Function},setup(e,n){let{emit:t,slots:a}=n;const{columns:l}=ee(),{expandOnClick:s,toggleExpand:r,isExpanded:o}=Ce(),{isSelected:u,toggleSelect:f}=te(),{toggleGroup:i,isGroupOpen:c}=we(),{t:v}=ue();return U(()=>{var y;return e.loading&&a.loading?d("tr",{class:"v-data-table-rows-loading",key:"loading"},[d("td",{colspan:l.value.length},[a.loading()])]):!e.loading&&!e.items.length&&!e.hideNoData?d("tr",{class:"v-data-table-rows-no-data",key:"no-data"},[d("td",{colspan:l.value.length},[((y=a["no-data"])==null?void 0:y.call(a))??v(e.noDataText)])]):d(Y,null,[e.items.map((x,w)=>{var b;if(x.type==="group-header")return a["group-header"]?a["group-header"]({index:w,item:x,columns:l.value,isExpanded:o,toggleExpand:r,isSelected:u,toggleSelect:f,toggleGroup:i,isGroupOpen:c}):d(St,{key:`group-header_${x.id}`,item:x},a);const P={index:w,item:x,columns:l.value,isExpanded:o,toggleExpand:r,isSelected:u,toggleSelect:f};return d(Y,null,[a.item?a.item(P):d(Ct,{key:`item_${x.value}`,onClick:s.value||e["onClick:row"]?g=>{var m;s.value&&r(x),(m=e["onClick:row"])==null||m.call(e,g,{item:x})}:void 0,index:w,item:x},a),o(x)&&((b=a["expanded-row"])==null?void 0:b.call(a,P))])})])}),{}}});const Tt=T({page:{type:[Number,String],default:1},itemsPerPage:{type:[Number,String],default:10}},"v-data-table-paginate"),Ve=Symbol.for("vuetify:data-table-pagination");function Dt(e){const n=F(e,"page",void 0,a=>+(a??1)),t=F(e,"itemsPerPage",void 0,a=>+(a??10));return{page:n,itemsPerPage:t}}function Bt(e){const{page:n,itemsPerPage:t,itemsLength:a}=e,l=p(()=>t.value===-1?0:t.value*(n.value-1)),s=p(()=>t.value===-1?a.value:Math.min(a.value,l.value+t.value)),r=p(()=>t.value===-1||a.value===0?1:Math.ceil(a.value/t.value));function o(f){t.value=f,n.value=1}const u={page:n,itemsPerPage:t,itemsLength:a,startIndex:l,stopIndex:s,pageCount:r,setItemsPerPage:o};return W(Ve,u),u}function Ft(){const e=Q(Ve);if(!e)throw new Error("Missing pagination!");return e}function _t(e){const{items:n,startIndex:t,stopIndex:a,itemsPerPage:l}=e;return{paginatedItems:p(()=>l.value<=0?n.value:n.value.slice(t.value,a.value))}}const Ot=A()({name:"VDataTableFooter",props:{prevIcon:{type:String,default:"$prev"},nextIcon:{type:String,default:"$next"},firstIcon:{type:String,default:"$first"},lastIcon:{type:String,default:"$last"},itemsPerPageText:{type:String,default:"$vuetify.dataFooter.itemsPerPageText"},pageText:{type:String,default:"$vuetify.dataFooter.pageText"},firstPageLabel:{type:String,default:"$vuetify.dataFooter.firstPage"},prevPageLabel:{type:String,default:"$vuetify.dataFooter.prevPage"},nextPageLabel:{type:String,default:"$vuetify.dataFooter.nextPage"},lastPageLabel:{type:String,default:"$vuetify.dataFooter.lastPage"},itemsPerPageOptions:{type:Array,default:()=>[{value:10,title:"10"},{value:25,title:"25"},{value:50,title:"50"},{value:100,title:"100"},{value:-1,title:"$vuetify.dataFooter.itemsPerPageAll"}]},showCurrentPage:Boolean},setup(e,n){let{slots:t}=n;const{t:a}=ue(),{page:l,pageCount:s,startIndex:r,stopIndex:o,itemsLength:u,itemsPerPage:f,setItemsPerPage:i}=Ft(),c=p(()=>e.itemsPerPageOptions.map(v=>({...v,title:a(v.title)})));return()=>{var v;return d("div",{class:"v-data-table-footer"},[(v=t.prepend)==null?void 0:v.call(t),d("div",{class:"v-data-table-footer__items-per-page"},[d("span",null,[a(e.itemsPerPageText)]),d(ot,{items:c.value,modelValue:f.value,"onUpdate:modelValue":y=>i(Number(y)),density:"compact",variant:"outlined","hide-details":!0},null)]),d("div",{class:"v-data-table-footer__info"},[d("div",null,[a(e.pageText,u.value?r.value+1:0,o.value,u.value)])]),d("div",{class:"v-data-table-footer__pagination"},[d(z,{icon:e.firstIcon,variant:"plain",onClick:()=>l.value=1,disabled:l.value===1,"aria-label":a(e.firstPageLabel)},null),d(z,{icon:e.prevIcon,variant:"plain",onClick:()=>l.value=Math.max(1,l.value-1),disabled:l.value===1,"aria-label":a(e.prevPageLabel)},null),e.showCurrentPage&&d("span",{key:"page",class:"v-data-table-footer__page"},[l.value]),d(z,{icon:e.nextIcon,variant:"plain",onClick:()=>l.value=Math.min(s.value,l.value+1),disabled:l.value===s.value,"aria-label":a(e.nextPageLabel)},null),d(z,{icon:e.lastIcon,variant:"plain",onClick:()=>l.value=s.value,disabled:l.value===s.value,"aria-label":a(e.lastPageLabel)},null)])])}}}),$t=T({...ge({itemValue:"id"})},"v-data-table-item");function Et(e,n,t){const a=n.split(".");for(;a.length>1;){const l=a.shift();e[l]==null&&(e[l]={}),typeof e[l]=="object"&&(e=e[l])}e[a[0]]=t}function Lt(e,n){const{items:t}=he(e);return{items:p(()=>t.value.map(l=>({...l,type:"item",columns:n.value.reduce((s,r)=>(Et(s,r.key,re(l.raw,r.value??r.key)),s),{})})))}}function Mt(e){let{page:n,itemsPerPage:t,sortBy:a,groupBy:l,search:s}=e;const r=Ye("VDataTable"),o=p(()=>({page:n.value,itemsPerPage:t.value,sortBy:a.value,groupBy:l.value,search:s.value}));le(()=>s==null?void 0:s.value,()=>{n.value=1});let u=null;le(o,()=>{me(u,o.value)||(r.emit("update:options",o.value),u=o.value)},{deep:!0,immediate:!0})}const Nt=(e,n,t)=>e==null||n==null?-1:e.toString().toLocaleLowerCase().indexOf(n.toString().toLocaleLowerCase()),At=T({customFilter:Function,customKeyFilter:Object,filterKeys:[Array,String],filterMode:{type:String,default:"intersection"},noFilter:Boolean},"filter");function Ht(e,n,t){var o;const a=[],l=(t==null?void 0:t.default)??Nt,s=t!=null&&t.filterKeys?ye(t.filterKeys):!1,r=Object.keys((t==null?void 0:t.customKeyFilter)??{}).length;if(!(e!=null&&e.length))return a;e:for(let u=0;u<e.length;u++){const f=e[u],i={},c={};let v=-1;if(n&&!(t!=null&&t.noFilter)){if(typeof f=="object"){const w=s||Object.keys(f);for(const P of w){const b=re(f,P,f),g=(o=t==null?void 0:t.customKeyFilter)==null?void 0:o[P];if(v=g?g(b,n,f):l(b,n,f),v!==-1&&v!==!1)g?i[P]=v:c[P]=v;else if((t==null?void 0:t.filterMode)==="every")continue e}}else v=l(f,n,f),v!==-1&&v!==!1&&(c.title=v);const y=Object.keys(c).length,x=Object.keys(i).length;if(!y&&!x||(t==null?void 0:t.filterMode)==="union"&&x!==r&&!y||(t==null?void 0:t.filterMode)==="intersection"&&(x!==r||!y))continue}a.push({index:u,matches:{...c,...i}})}return a}function Kt(e,n,t,a){const l=p(()=>typeof(t==null?void 0:t.value)!="string"&&typeof(t==null?void 0:t.value)!="number"?"":String(t.value)),s=L([]),r=L(new Map);Xe(()=>{s.value=[],r.value=new Map;const u=fe(n);Ht(u,l.value,{customKeyFilter:e.customKeyFilter,default:e.customFilter,filterKeys:fe(a==null?void 0:a.filterKeys)??e.filterKeys,filterMode:e.filterMode,noFilter:e.noFilter}).forEach(i=>{let{index:c,matches:v}=i;const y=u[c];s.value.push(y),r.value.set(y.value,v)})});function o(u){return r.value.get(u.value)}return{filteredItems:s,filteredMatches:r,getMatches:o}}const Rt=T({...$t(),...rt(),hideNoData:Boolean,hover:Boolean,noDataText:{type:String,default:"$vuetify.noDataText"},height:[String,Number],width:[String,Number],fixedHeader:Boolean,fixedFooter:Boolean,"onClick:row":Function,search:String},"v-data-table");A()({name:"VDataTable",props:{...Rt(),...Pt(),...bt(),...it(),...ct(),...Tt(),...At()},emits:{"update:modelValue":e=>!0,"update:page":e=>!0,"update:itemsPerPage":e=>!0,"update:sortBy":e=>!0,"update:options":e=>!0,"update:groupBy":e=>!0,"update:expanded":e=>!0},setup(e,n){let{emit:t,slots:a}=n;const{groupBy:l}=xt(e),{sortBy:s,multiSort:r,mustSort:o}=ft(e),{page:u,itemsPerPage:f}=Dt(e),{columns:i}=st(e,{groupBy:l,showSelect:N(e,"showSelect"),showExpand:N(e,"showExpand")}),{items:c}=Lt(e,i),v=p(()=>i.value.map(O=>"columns."+O.key)),y=N(e,"search"),{filteredItems:x}=Kt(e,c,y,{filterKeys:v});vt({sortBy:s,multiSort:r,mustSort:o,page:u});const{sortByWithGroups:w,opened:P,extractRows:b}=kt({groupBy:l,sortBy:s}),{sortedItems:g}=gt(x,w,i),{flatItems:m}=wt(g,l,P),D=p(()=>m.value.length),{startIndex:I,stopIndex:j}=Bt({page:u,itemsPerPage:f,itemsLength:D}),{paginatedItems:_}=_t({items:m,startIndex:I,stopIndex:j,itemsPerPage:f}),M=p(()=>b(_.value));return dt(e,M),It(e),Mt({page:u,itemsPerPage:f,sortBy:s,groupBy:l,search:y}),Ze({VDataTableRows:{hideNoData:N(e,"hideNoData"),noDataText:N(e,"noDataText")}}),U(()=>d(ut,{class:["v-data-table",{"v-data-table--show-select":e.showSelect}],fixedHeader:e.fixedHeader,fixedFooter:e.fixedFooter,height:e.height,hover:e.hover},{top:a.top,default:a.default??(()=>{var O,H,h,k;return d(Y,null,[(O=a.colgroup)==null?void 0:O.call(a,{columns:i}),d("thead",null,[d(yt,{sticky:e.fixedHeader,multiSort:e.multiSort},a)]),(H=a.thead)==null?void 0:H.call(a),d("tbody",null,[a.body?a.body():d(Vt,{items:_.value,"onClick:row":e["onClick:row"]},a)]),(h=a.tbody)==null?void 0:h.call(a),(k=a.tfoot)==null?void 0:k.call(a)])}),bottom:a.bottom??(()=>d(Ot,null,{prepend:a["footer.prepend"]}))})),{}}});export{gt as A,nt as B,q as C,lt as D,ot as V,Pt as a,rt as b,$t as c,it as d,ct as e,Tt as f,bt as g,xt as h,ft as i,Dt as j,st as k,vt as l,Rt as m,kt as n,Bt as o,It as p,wt as q,dt as r,Mt as s,yt as t,Lt as u,Vt as v,Ot as w,ut as x,At as y,Kt as z};
