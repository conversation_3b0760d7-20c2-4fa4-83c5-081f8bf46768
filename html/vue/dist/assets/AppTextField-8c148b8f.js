import{V as m,e as u}from"./VTextField-3e2d458d.js";import{d as f,B as n,bl as o,o as i,c as b,s as t,b as _,A as x,q as h,b5 as $,a as g,w as k,p as V,b6 as d,b7 as c,t as A}from"./index-9a5dc664.js";const B=f({name:"AppTextField",inheritAttrs:!1}),T=Object.assign(B,{setup(C){const a=n(()=>{const e=o(),s=e.id||e.label;return s?`app-text-field-${s}-${Math.random().toString(36).slice(2,7)}`:void 0}),r=n(()=>o().label);return(e,s)=>(i(),b("div",{class:A(["app-text-field flex-grow-1",e.$attrs.class])},[t(r)?(i(),_(m,{key:0,for:t(a),class:"mb-1 text-body-2 text-high-emphasis",text:t(r)},null,8,["for","text"])):x("",!0),h(u,d(c({...e.$attrs,class:null,label:void 0,variant:"outlined",id:t(a)})),$({_:2},[g(e.$slots,(v,l)=>({name:l,fn:k(p=>[V(e.$slots,l,d(c(p||{})))])}))]),1040)],2))}});export{T as _};
