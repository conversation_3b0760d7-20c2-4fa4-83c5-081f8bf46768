import{I as te,bN as ae,a0 as ne,B as b,l as g,D as le,E as h,G as oe,a7 as se,a$ as re,q as l,a8 as p,F as S,aC as $,be as ue,b4 as ie,bL as ce,a3 as R,bO as de,C as fe,bP as ve,d as me,bl as z,o as T,c as xe,s as C,b as ge,A as he,b5 as be,a as Ve,w as ye,p as we,b6 as H,b7 as D,t as Ce}from"./index-9a5dc664.js";import{m as Fe,b as Pe,u as ke,a as E,f as Ie,c as _e,d as pe,V as Se}from"./VTextField-3e2d458d.js";const Re=te()({name:"VTextarea",directives:{Intersect:ae},inheritAttrs:!1,props:{autoGrow:Boolean,autofocus:Boolean,counter:[<PERSON><PERSON>an,Number,String],counterValue:Function,prefix:String,placeholder:String,persistentPlaceholder:Boolean,persistentCounter:Boolean,noResize:Boolean,rows:{type:[Number,String],default:5,validator:e=>!isNaN(parseFloat(e))},maxRows:{type:[Number,String],validator:e=>!isNaN(parseFloat(e))},suffix:String,modelModifiers:Object,...Fe(),...Pe()},emits:{"click:control":e=>!0,"mousedown:control":e=>!0,"update:focused":e=>!0,"update:modelValue":e=>!0},setup(e,V){let{attrs:d,emit:r,slots:a}=V;const o=ne(e,"modelValue"),{isFocused:i,focus:F,blur:G}=ke(e),O=b(()=>typeof e.counterValue=="function"?e.counterValue(o.value):(o.value||"").toString().length),U=b(()=>{if(d.maxlength)return d.maxlength;if(!(!e.counter||typeof e.counter!="number"&&typeof e.counter!="string"))return e.counter});function L(t,s){var n,u;!e.autofocus||!t||(u=(n=s[0].target)==null?void 0:n.focus)==null||u.call(n)}const B=g(),y=g(),N=g(""),w=g(),j=b(()=>i.value||e.persistentPlaceholder);function P(){var t;w.value!==document.activeElement&&((t=w.value)==null||t.focus()),i.value||F()}function q(t){P(),r("click:control",t)}function J(t){r("mousedown:control",t)}function K(t){t.stopPropagation(),P(),R(()=>{o.value="",de(e["onClick:clear"],t)})}function Q(t){var n;const s=t.target;if(o.value=s.value,(n=e.modelModifiers)!=null&&n.trim){const u=[s.selectionStart,s.selectionEnd];R(()=>{s.selectionStart=u[0],s.selectionEnd=u[1]})}}const f=g();function v(){e.autoGrow&&R(()=>{if(!f.value||!y.value)return;const t=getComputedStyle(f.value),s=getComputedStyle(y.value.$el),n=parseFloat(t.getPropertyValue("--v-field-padding-top"))+parseFloat(t.getPropertyValue("--v-input-padding-top"))+parseFloat(t.getPropertyValue("--v-field-padding-bottom")),u=f.value.scrollHeight,k=parseFloat(t.lineHeight),I=Math.max(parseFloat(e.rows)*k+n,parseFloat(s.getPropertyValue("--v-input-control-height"))),_=parseFloat(e.maxRows)*k+n||1/0;N.value=fe(ve(u??0,I,_))})}le(v),h(o,v),h(()=>e.rows,v),h(()=>e.maxRows,v),h(()=>e.density,v);let c;return h(f,t=>{t?(c=new ResizeObserver(v),c.observe(f.value)):c==null||c.disconnect()}),oe(()=>{c==null||c.disconnect()}),se(()=>{const t=!!(a.counter||e.counter||e.counterValue),s=!!(t||a.details),[n,u]=re(d),[{modelValue:k,...I}]=E.filterProps(e),[_]=Ie(e);return l(E,p({ref:B,modelValue:o.value,"onUpdate:modelValue":m=>o.value=m,class:["v-textarea v-text-field",{"v-textarea--prefixed":e.prefix,"v-textarea--suffixed":e.suffix,"v-text-field--prefixed":e.prefix,"v-text-field--suffixed":e.suffix,"v-textarea--auto-grow":e.autoGrow,"v-textarea--no-resize":e.noResize||e.autoGrow,"v-text-field--flush-details":["plain","underlined"].includes(e.variant)},e.class],style:e.style},n,I,{focused:i.value}),{...a,default:m=>{let{isDisabled:x,isDirty:A,isReadonly:W,isValid:X}=m;return l(_e,p({ref:y,style:{"--v-textarea-control-height":N.value},onClick:q,onMousedown:J,"onClick:clear":K,"onClick:prependInner":e["onClick:prependInner"],"onClick:appendInner":e["onClick:appendInner"],role:"textbox"},_,{active:j.value||A.value,dirty:A.value||e.dirty,disabled:x.value,focused:i.value,error:X.value===!1}),{...a,default:Y=>{let{props:{class:M,...Z}}=Y;return l(S,null,[e.prefix&&l("span",{class:"v-text-field__prefix"},[e.prefix]),$(l("textarea",p({ref:w,class:M,value:o.value,onInput:Q,autofocus:e.autofocus,readonly:W.value,disabled:x.value,placeholder:e.placeholder,rows:e.rows,name:e.name,onFocus:P,onBlur:G},Z,u),null),[[ue("intersect"),{handler:L},null,{once:!0}]]),e.autoGrow&&$(l("textarea",{class:[M,"v-textarea__sizer"],"onUpdate:modelValue":ee=>o.value=ee,ref:f,readonly:!0,"aria-hidden":"true"},null),[[ie,o.value]]),e.suffix&&l("span",{class:"v-text-field__suffix"},[e.suffix])])}})},details:s?m=>{var x;return l(S,null,[(x=a.details)==null?void 0:x.call(a,m),t&&l(S,null,[l("span",null,null),l(pe,{active:e.persistentCounter||i.value,value:O.value,max:U.value},a.counter)])])}:void 0})}),ce({},B,y,w)}}),Be=me({name:"AppTextarea",inheritAttrs:!1}),Me=Object.assign(Be,{setup(e){const V=b(()=>{const r=z(),a=r.id||r.label;return a?`app-textarea-${a}-${Math.random().toString(36).slice(2,7)}`:void 0}),d=b(()=>z().label);return(r,a)=>(T(),xe("div",{class:Ce(["app-textarea flex-grow-1",r.$attrs.class])},[C(d)?(T(),ge(Se,{key:0,for:C(V),class:"mb-1 text-body-2 text-high-emphasis",text:C(d)},null,8,["for","text"])):he("",!0),l(Re,H(D({...r.$attrs,class:null,label:void 0,variant:"outlined",id:C(V)})),be({_:2},[Ve(r.$slots,(o,i)=>({name:i,fn:ye(F=>[we(r.$slots,i,H(D(F||{})))])}))]),1040)],2))}});export{Me as _};
