<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <!-- <link rel="icon" href="/favicon.ico" /> -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="robots" content="noindex,nofollow" />
  <!-- <title></title> -->
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link rel="stylesheet" type="text/css" href="./loader.css" />
  <script type="module" crossorigin src="./assets/index-9a5dc664.js"></script>
  <link rel="stylesheet" href="./assets/index-9a43a361.css">
</head>

<body>
  <div id="app" translate="no">
    <div id="loading-bg">
      <div class="init-loading">
        <div class="init-loading-spinner">
          <div class="init-loading-spinner-container">
            <div class="init-loading-spinner-nib init-loading-spinner-nib-1"></div>
            <div class="init-loading-spinner-nib init-loading-spinner-nib-2"></div>
            <div class="init-loading-spinner-nib init-loading-spinner-nib-3"></div>
            <div class="init-loading-spinner-nib init-loading-spinner-nib-4"></div>
            <div class="init-loading-spinner-nib init-loading-spinner-nib-5"></div>
            <div class="init-loading-spinner-nib init-loading-spinner-nib-6"></div>
            <div class="init-loading-spinner-nib init-loading-spinner-nib-7"></div>
            <div class="init-loading-spinner-nib init-loading-spinner-nib-8"></div>
          </div>
          <span aria-live="assertive" class="init-loading-spinner-label" role="status">
            loading…
          </span>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    const loaderColor = localStorage.getItem('vuexy-initial-loader-bg') || '#FFFFFF'
    const primaryColor = localStorage.getItem('vuexy-initial-loader-color') || '#7367F0'

    if (loaderColor)
      document.documentElement.style.setProperty('--initial-loader-bg', loaderColor)

    if (primaryColor)
      document.documentElement.style.setProperty('--initial-loader-color', primaryColor)
  </script>
</body>

</html>