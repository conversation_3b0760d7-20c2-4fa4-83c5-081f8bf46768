a:3:{i:0;a:5:{s:35:"<PERSON>yper<PERSON>\Cache\CacheListenerCollector";s:6:"a:0:{}";s:35:"Hyperf\Constants\ConstantsCollector";s:88:"a:1:{s:19:"App\Utils\ErrorCode";a:1:{i:500;a:1:{s:7:"message";s:15:"Server Error！";}}}";s:40:"Hyperf\Di\Annotation\AnnotationCollector";s:8718:"a:29:{s:35:"Hyperf\Devtool\VendorPublishCommand";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Command\Annotation\Command";O:33:"Hyperf\Command\Annotation\Command":6:{s:4:"name";s:0:"";s:9:"arguments";a:0:{}s:7:"options";a:0:{}s:11:"description";s:0:"";s:7:"aliases";a:0:{}s:9:"signature";N;}}}s:40:"Hyperf\Devtool\Describe\ListenersCommand";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Command\Annotation\Command";O:33:"Hyperf\Command\Annotation\Command":6:{s:4:"name";s:0:"";s:9:"arguments";a:0:{}s:7:"options";a:0:{}s:11:"description";s:0:"";s:7:"aliases";a:0:{}s:9:"signature";N;}}}s:38:"Hyperf\Devtool\Describe\AspectsCommand";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Command\Annotation\Command";O:33:"Hyperf\Command\Annotation\Command":6:{s:4:"name";s:0:"";s:9:"arguments";a:0:{}s:7:"options";a:0:{}s:11:"description";s:0:"";s:7:"aliases";a:0:{}s:9:"signature";N;}}}s:37:"Hyperf\Devtool\Describe\RoutesCommand";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Command\Annotation\Command";O:33:"Hyperf\Command\Annotation\Command":6:{s:4:"name";s:0:"";s:9:"arguments";a:0:{}s:7:"options";a:0:{}s:11:"description";s:0:"";s:7:"aliases";a:0:{}s:9:"signature";N;}}}s:26:"Hyperf\Devtool\InfoCommand";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Command\Annotation\Command";O:33:"Hyperf\Command\Annotation\Command":6:{s:4:"name";s:0:"";s:9:"arguments";a:0:{}s:7:"options";a:0:{}s:11:"description";s:0:"";s:7:"aliases";a:0:{}s:9:"signature";N;}}}s:45:"Hyperf\Devtool\Generator\KafkaConsumerCommand";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Command\Annotation\Command";O:33:"Hyperf\Command\Annotation\Command":6:{s:4:"name";s:0:"";s:9:"arguments";a:0:{}s:7:"options";a:0:{}s:11:"description";s:0:"";s:7:"aliases";a:0:{}s:9:"signature";N;}}}s:40:"Hyperf\Devtool\Generator\ListenerCommand";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Command\Annotation\Command";O:33:"Hyperf\Command\Annotation\Command":6:{s:4:"name";s:0:"";s:9:"arguments";a:0:{}s:7:"options";a:0:{}s:11:"description";s:0:"";s:7:"aliases";a:0:{}s:9:"signature";N;}}}s:39:"Hyperf\Devtool\Generator\ProcessCommand";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Command\Annotation\Command";O:33:"Hyperf\Command\Annotation\Command":6:{s:4:"name";s:0:"";s:9:"arguments";a:0:{}s:7:"options";a:0:{}s:11:"description";s:0:"";s:7:"aliases";a:0:{}s:9:"signature";N;}}}s:42:"Hyperf\Devtool\Generator\ControllerCommand";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Command\Annotation\Command";O:33:"Hyperf\Command\Annotation\Command":6:{s:4:"name";s:0:"";s:9:"arguments";a:0:{}s:7:"options";a:0:{}s:11:"description";s:0:"";s:7:"aliases";a:0:{}s:9:"signature";N;}}}s:39:"Hyperf\Devtool\Generator\CommandCommand";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Command\Annotation\Command";O:33:"Hyperf\Command\Annotation\Command":6:{s:4:"name";s:0:"";s:9:"arguments";a:0:{}s:7:"options";a:0:{}s:11:"description";s:0:"";s:7:"aliases";a:0:{}s:9:"signature";N;}}}s:44:"Hyperf\Devtool\Generator\NatsConsumerCommand";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Command\Annotation\Command";O:33:"Hyperf\Command\Annotation\Command":6:{s:4:"name";s:0:"";s:9:"arguments";a:0:{}s:7:"options";a:0:{}s:11:"description";s:0:"";s:7:"aliases";a:0:{}s:9:"signature";N;}}}s:43:"Hyperf\Devtool\Generator\NsqConsumerCommand";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Command\Annotation\Command";O:33:"Hyperf\Command\Annotation\Command":6:{s:4:"name";s:0:"";s:9:"arguments";a:0:{}s:7:"options";a:0:{}s:11:"description";s:0:"";s:7:"aliases";a:0:{}s:9:"signature";N;}}}s:42:"Hyperf\Devtool\Generator\MiddlewareCommand";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Command\Annotation\Command";O:33:"Hyperf\Command\Annotation\Command":6:{s:4:"name";s:0:"";s:9:"arguments";a:0:{}s:7:"options";a:0:{}s:11:"description";s:0:"";s:7:"aliases";a:0:{}s:9:"signature";N;}}}s:44:"Hyperf\Devtool\Generator\AmqpProducerCommand";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Command\Annotation\Command";O:33:"Hyperf\Command\Annotation\Command":6:{s:4:"name";s:0:"";s:9:"arguments";a:0:{}s:7:"options";a:0:{}s:11:"description";s:0:"";s:7:"aliases";a:0:{}s:9:"signature";N;}}}s:35:"Hyperf\Devtool\Generator\JobCommand";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Command\Annotation\Command";O:33:"Hyperf\Command\Annotation\Command":6:{s:4:"name";s:0:"";s:9:"arguments";a:0:{}s:7:"options";a:0:{}s:11:"description";s:0:"";s:7:"aliases";a:0:{}s:9:"signature";N;}}}s:40:"Hyperf\Devtool\Generator\ResourceCommand";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Command\Annotation\Command";O:33:"Hyperf\Command\Annotation\Command":6:{s:4:"name";s:0:"";s:9:"arguments";a:0:{}s:7:"options";a:0:{}s:11:"description";s:0:"";s:7:"aliases";a:0:{}s:9:"signature";N;}}}s:39:"Hyperf\Devtool\Generator\RequestCommand";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Command\Annotation\Command";O:33:"Hyperf\Command\Annotation\Command":6:{s:4:"name";s:0:"";s:9:"arguments";a:0:{}s:7:"options";a:0:{}s:11:"description";s:0:"";s:7:"aliases";a:0:{}s:9:"signature";N;}}}s:40:"Hyperf\Devtool\Generator\ConstantCommand";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Command\Annotation\Command";O:33:"Hyperf\Command\Annotation\Command":6:{s:4:"name";s:0:"";s:9:"arguments";a:0:{}s:7:"options";a:0:{}s:11:"description";s:0:"";s:7:"aliases";a:0:{}s:9:"signature";N;}}}s:44:"Hyperf\Devtool\Generator\AmqpConsumerCommand";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Command\Annotation\Command";O:33:"Hyperf\Command\Annotation\Command":6:{s:4:"name";s:0:"";s:9:"arguments";a:0:{}s:7:"options";a:0:{}s:11:"description";s:0:"";s:7:"aliases";a:0:{}s:9:"signature";N;}}}s:38:"Hyperf\Devtool\Generator\AspectCommand";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Command\Annotation\Command";O:33:"Hyperf\Command\Annotation\Command":6:{s:4:"name";s:0:"";s:9:"arguments";a:0:{}s:7:"options";a:0:{}s:11:"description";s:0:"";s:7:"aliases";a:0:{}s:9:"signature";N;}}}s:37:"Hyperf\Devtool\Generator\ClassCommand";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Command\Annotation\Command";O:33:"Hyperf\Command\Annotation\Command":6:{s:4:"name";s:0:"";s:9:"arguments";a:0:{}s:7:"options";a:0:{}s:11:"description";s:0:"";s:7:"aliases";a:0:{}s:9:"signature";N;}}}s:30:"App\Process\AsyncQueueConsumer";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Process\Annotation\Process";O:33:"Hyperf\Process\Annotation\Process":5:{s:4:"nums";N;s:4:"name";N;s:19:"redirectStdinStdout";N;s:8:"pipeType";N;s:15:"enableCoroutine";N;}}}s:29:"App\Controller\ChatController";a:1:{s:2:"_p";a:1:{s:6:"server";a:1:{s:27:"Hyperf\Di\Annotation\Inject";O:27:"Hyperf\Di\Annotation\Inject":3:{s:5:"value";s:29:"Hyperf\WebSocketServer\Server";s:8:"required";b:1;s:4:"lazy";b:0;}}}}s:33:"App\Controller\AbstractController";a:1:{s:2:"_p";a:3:{s:9:"container";a:1:{s:27:"Hyperf\Di\Annotation\Inject";O:27:"Hyperf\Di\Annotation\Inject":3:{s:5:"value";s:32:"Psr\Container\ContainerInterface";s:8:"required";b:1;s:4:"lazy";b:0;}}s:7:"request";a:1:{s:27:"Hyperf\Di\Annotation\Inject";O:27:"Hyperf\Di\Annotation\Inject":3:{s:5:"value";s:43:"Hyperf\HttpServer\Contract\RequestInterface";s:8:"required";b:1;s:4:"lazy";b:0;}}s:8:"response";a:1:{s:27:"Hyperf\Di\Annotation\Inject";O:27:"Hyperf\Di\Annotation\Inject":3:{s:5:"value";s:44:"Hyperf\HttpServer\Contract\ResponseInterface";s:8:"required";b:1;s:4:"lazy";b:0;}}}}s:36:"App\Listener\DbQueryExecutedListener";a:1:{s:2:"_c";a:1:{s:32:"Hyperf\Event\Annotation\Listener";O:32:"Hyperf\Event\Annotation\Listener":1:{s:8:"priority";i:0;}}}s:42:"App\Listener\ResumeExitCoordinatorListener";a:1:{s:2:"_c";a:1:{s:32:"Hyperf\Event\Annotation\Listener";O:32:"Hyperf\Event\Annotation\Listener":1:{s:8:"priority";i:0;}}}s:32:"App\Listener\QueueHandleListener";a:1:{s:2:"_c";a:1:{s:32:"Hyperf\Event\Annotation\Listener";O:32:"Hyperf\Event\Annotation\Listener":1:{s:8:"priority";i:0;}}}s:20:"App\Task\AutoSslTask";a:2:{s:2:"_c";a:1:{s:33:"Hyperf\Crontab\Annotation\Crontab";O:33:"Hyperf\Crontab\Annotation\Crontab":13:{s:4:"rule";s:11:"*/8 * * * *";s:4:"name";s:8:"auto_ssl";s:4:"type";s:8:"callback";s:9:"singleton";N;s:9:"mutexPool";N;s:12:"mutexExpires";N;s:11:"onOneServer";N;s:8:"callback";a:2:{i:0;s:20:"App\Task\AutoSslTask";i:1;s:6:"handle";}s:4:"memo";s:38:"自动为所有站点申请 SSL 证书";s:6:"enable";b:1;s:8:"timezone";N;s:12:"environments";a:0:{}s:7:"options";a:0:{}}}s:2:"_p";a:1:{s:6:"logger";a:1:{s:27:"Hyperf\Di\Annotation\Inject";O:27:"Hyperf\Di\Annotation\Inject":3:{s:5:"value";s:37:"Hyperf\Contract\StdoutLoggerInterface";s:8:"required";b:1;s:4:"lazy";b:0;}}}}s:25:"App\Task\DomainStatusTask";a:1:{s:2:"_c";a:1:{s:33:"Hyperf\Crontab\Annotation\Crontab";O:33:"Hyperf\Crontab\Annotation\Crontab":13:{s:4:"rule";s:12:"*/18 * * * *";s:4:"name";s:19:"domain_status_check";s:4:"type";s:8:"callback";s:9:"singleton";N;s:9:"mutexPool";N;s:12:"mutexExpires";N;s:11:"onOneServer";N;s:8:"callback";a:2:{i:0;s:25:"App\Task\DomainStatusTask";i:1;s:6:"handle";}s:4:"memo";s:63:"每15分钟检测所有域名的 Safe Browsing 状态并更新";s:6:"enable";b:1;s:8:"timezone";N;s:12:"environments";a:0:{}s:7:"options";a:0:{}}}}}";s:36:"Hyperf\Di\Annotation\AspectCollector";s:6663:"a:2:{i:0;a:17:{s:41:"Hyperf\AsyncQueue\Aspect\AsyncQueueAspect";a:3:{s:8:"priority";i:0;s:7:"classes";a:0:{}s:11:"annotations";a:1:{i:0;s:46:"Hyperf\AsyncQueue\Annotation\AsyncQueueMessage";}}s:35:"Hyperf\Cache\Aspect\CacheableAspect";a:3:{s:8:"priority";i:0;s:7:"classes";a:0:{}s:11:"annotations";a:1:{i:0;s:33:"Hyperf\Cache\Annotation\Cacheable";}}s:36:"Hyperf\Cache\Aspect\CacheAheadAspect";a:3:{s:8:"priority";i:0;s:7:"classes";a:0:{}s:11:"annotations";a:1:{i:0;s:34:"Hyperf\Cache\Annotation\CacheAhead";}}s:36:"Hyperf\Cache\Aspect\CacheEvictAspect";a:3:{s:8:"priority";i:0;s:7:"classes";a:0:{}s:11:"annotations";a:1:{i:0;s:34:"Hyperf\Cache\Annotation\CacheEvict";}}s:34:"Hyperf\Cache\Aspect\CachePutAspect";a:3:{s:8:"priority";i:0;s:7:"classes";a:0:{}s:11:"annotations";a:1:{i:0;s:32:"Hyperf\Cache\Annotation\CachePut";}}s:35:"Hyperf\Cache\Aspect\FailCacheAspect";a:3:{s:8:"priority";i:0;s:7:"classes";a:0:{}s:11:"annotations";a:1:{i:0;s:33:"Hyperf\Cache\Annotation\FailCache";}}s:36:"Hyperf\Config\Annotation\ValueAspect";a:3:{s:8:"priority";i:0;s:7:"classes";a:0:{}s:11:"annotations";a:1:{i:0;s:30:"Hyperf\Config\Annotation\Value";}}s:44:"Hyperf\DbConnection\Aspect\TransactionAspect";a:3:{s:8:"priority";i:0;s:7:"classes";a:0:{}s:11:"annotations";a:1:{i:0;s:44:"Hyperf\DbConnection\Annotation\Transactional";}}s:33:"Hyperf\Di\Annotation\InjectAspect";a:3:{s:8:"priority";i:0;s:7:"classes";a:0:{}s:11:"annotations";a:1:{i:0;s:27:"Hyperf\Di\Annotation\Inject";}}s:36:"Hyperf\Tracer\Aspect\CoroutineAspect";a:3:{s:8:"priority";i:0;s:7:"classes";a:1:{i:0;s:34:"Hyperf\Coroutine\Coroutine::create";}s:11:"annotations";a:0:{}}s:45:"Hyperf\Tracer\Aspect\CreateTraceContextAspect";a:3:{s:8:"priority";i:0;s:7:"classes";a:2:{i:0;s:39:"Zipkin\Propagation\TraceContext::create";i:1;s:40:"Zipkin\Propagation\TraceContext::create*";}s:11:"annotations";a:0:{}}s:40:"Hyperf\Tracer\Aspect\ElasticserachAspect";a:3:{s:8:"priority";i:0;s:7:"classes";a:13:{i:0;s:26:"Elasticsearch\Client::bulk";i:1;s:27:"Elasticsearch\Client::count";i:2;s:28:"Elasticsearch\Client::create";i:3;s:25:"Elasticsearch\Client::get";i:4;s:31:"Elasticsearch\Client::getSource";i:5;s:27:"Elasticsearch\Client::index";i:6;s:26:"Elasticsearch\Client::mget";i:7;s:29:"Elasticsearch\Client::msearch";i:8;s:28:"Elasticsearch\Client::scroll";i:9;s:28:"Elasticsearch\Client::search";i:10;s:28:"Elasticsearch\Client::update";i:11;s:35:"Elasticsearch\Client::updateByQuery";i:12;s:28:"Elasticsearch\Client::search";}s:11:"annotations";a:0:{}}s:31:"Hyperf\Tracer\Aspect\GrpcAspect";a:3:{s:8:"priority";i:0;s:7:"classes";a:2:{i:0;s:34:"Hyperf\GrpcClient\GrpcClient::send";i:1;s:34:"Hyperf\GrpcClient\GrpcClient::recv";}s:11:"annotations";a:0:{}}s:37:"Hyperf\Tracer\Aspect\HttpClientAspect";a:3:{s:8:"priority";i:0;s:7:"classes";a:2:{i:0;s:26:"GuzzleHttp\Client::request";i:1;s:31:"GuzzleHttp\Client::requestAsync";}s:11:"annotations";a:0:{}}s:32:"Hyperf\Tracer\Aspect\RedisAspect";a:3:{s:8:"priority";i:0;s:7:"classes";a:1:{i:0;s:26:"Hyperf\Redis\Redis::__call";}s:11:"annotations";a:0:{}}s:30:"Hyperf\Tracer\Aspect\RpcAspect";a:3:{s:8:"priority";i:0;s:7:"classes";a:2:{i:0;s:57:"Hyperf\RpcClient\AbstractServiceClient::__generateRpcPath";i:1;s:29:"Hyperf\RpcClient\Client::send";}s:11:"annotations";a:0:{}}s:42:"Hyperf\Tracer\Aspect\TraceAnnotationAspect";a:3:{s:8:"priority";i:0;s:7:"classes";a:0:{}s:11:"annotations";a:1:{i:0;s:30:"Hyperf\Tracer\Annotation\Trace";}}}i:1;a:2:{s:7:"classes";a:17:{s:41:"Hyperf\AsyncQueue\Aspect\AsyncQueueAspect";a:0:{}s:35:"Hyperf\Cache\Aspect\CacheableAspect";a:0:{}s:36:"Hyperf\Cache\Aspect\CacheAheadAspect";a:0:{}s:36:"Hyperf\Cache\Aspect\CacheEvictAspect";a:0:{}s:34:"Hyperf\Cache\Aspect\CachePutAspect";a:0:{}s:35:"Hyperf\Cache\Aspect\FailCacheAspect";a:0:{}s:36:"Hyperf\Config\Annotation\ValueAspect";a:0:{}s:44:"Hyperf\DbConnection\Aspect\TransactionAspect";a:0:{}s:33:"Hyperf\Di\Annotation\InjectAspect";a:0:{}s:36:"Hyperf\Tracer\Aspect\CoroutineAspect";a:1:{i:0;s:34:"Hyperf\Coroutine\Coroutine::create";}s:45:"Hyperf\Tracer\Aspect\CreateTraceContextAspect";a:2:{i:0;s:39:"Zipkin\Propagation\TraceContext::create";i:1;s:40:"Zipkin\Propagation\TraceContext::create*";}s:40:"Hyperf\Tracer\Aspect\ElasticserachAspect";a:13:{i:0;s:26:"Elasticsearch\Client::bulk";i:1;s:27:"Elasticsearch\Client::count";i:2;s:28:"Elasticsearch\Client::create";i:3;s:25:"Elasticsearch\Client::get";i:4;s:31:"Elasticsearch\Client::getSource";i:5;s:27:"Elasticsearch\Client::index";i:6;s:26:"Elasticsearch\Client::mget";i:7;s:29:"Elasticsearch\Client::msearch";i:8;s:28:"Elasticsearch\Client::scroll";i:9;s:28:"Elasticsearch\Client::search";i:10;s:28:"Elasticsearch\Client::update";i:11;s:35:"Elasticsearch\Client::updateByQuery";i:12;s:28:"Elasticsearch\Client::search";}s:31:"Hyperf\Tracer\Aspect\GrpcAspect";a:2:{i:0;s:34:"Hyperf\GrpcClient\GrpcClient::send";i:1;s:34:"Hyperf\GrpcClient\GrpcClient::recv";}s:37:"Hyperf\Tracer\Aspect\HttpClientAspect";a:2:{i:0;s:26:"GuzzleHttp\Client::request";i:1;s:31:"GuzzleHttp\Client::requestAsync";}s:32:"Hyperf\Tracer\Aspect\RedisAspect";a:1:{i:0;s:26:"Hyperf\Redis\Redis::__call";}s:30:"Hyperf\Tracer\Aspect\RpcAspect";a:2:{i:0;s:57:"Hyperf\RpcClient\AbstractServiceClient::__generateRpcPath";i:1;s:29:"Hyperf\RpcClient\Client::send";}s:42:"Hyperf\Tracer\Aspect\TraceAnnotationAspect";a:0:{}}s:11:"annotations";a:17:{s:41:"Hyperf\AsyncQueue\Aspect\AsyncQueueAspect";a:1:{i:0;s:46:"Hyperf\AsyncQueue\Annotation\AsyncQueueMessage";}s:35:"Hyperf\Cache\Aspect\CacheableAspect";a:1:{i:0;s:33:"Hyperf\Cache\Annotation\Cacheable";}s:36:"Hyperf\Cache\Aspect\CacheAheadAspect";a:1:{i:0;s:34:"Hyperf\Cache\Annotation\CacheAhead";}s:36:"Hyperf\Cache\Aspect\CacheEvictAspect";a:1:{i:0;s:34:"Hyperf\Cache\Annotation\CacheEvict";}s:34:"Hyperf\Cache\Aspect\CachePutAspect";a:1:{i:0;s:32:"Hyperf\Cache\Annotation\CachePut";}s:35:"Hyperf\Cache\Aspect\FailCacheAspect";a:1:{i:0;s:33:"Hyperf\Cache\Annotation\FailCache";}s:36:"Hyperf\Config\Annotation\ValueAspect";a:1:{i:0;s:30:"Hyperf\Config\Annotation\Value";}s:44:"Hyperf\DbConnection\Aspect\TransactionAspect";a:1:{i:0;s:44:"Hyperf\DbConnection\Annotation\Transactional";}s:33:"Hyperf\Di\Annotation\InjectAspect";a:1:{i:0;s:27:"Hyperf\Di\Annotation\Inject";}s:36:"Hyperf\Tracer\Aspect\CoroutineAspect";a:0:{}s:45:"Hyperf\Tracer\Aspect\CreateTraceContextAspect";a:0:{}s:40:"Hyperf\Tracer\Aspect\ElasticserachAspect";a:0:{}s:31:"Hyperf\Tracer\Aspect\GrpcAspect";a:0:{}s:37:"Hyperf\Tracer\Aspect\HttpClientAspect";a:0:{}s:32:"Hyperf\Tracer\Aspect\RedisAspect";a:0:{}s:30:"Hyperf\Tracer\Aspect\RpcAspect";a:0:{}s:42:"Hyperf\Tracer\Aspect\TraceAnnotationAspect";a:1:{i:0;s:30:"Hyperf\Tracer\Annotation\Trace";}}}}";s:48:"Hyperf\ModelListener\Collector\ListenerCollector";s:6:"a:0:{}";}i:1;a:8:{s:26:"Hyperf\Coroutine\Coroutine";s:66:"/work/runtime/container/proxy/Hyperf_Coroutine_Coroutine.proxy.php";s:31:"Zipkin\Propagation\TraceContext";s:71:"/work/runtime/container/proxy/Zipkin_Propagation_TraceContext.proxy.php";s:20:"Elasticsearch\Client";s:60:"/work/runtime/container/proxy/Elasticsearch_Client.proxy.php";s:17:"GuzzleHttp\Client";s:57:"/work/runtime/container/proxy/GuzzleHttp_Client.proxy.php";s:18:"Hyperf\Redis\Redis";s:58:"/work/runtime/container/proxy/Hyperf_Redis_Redis.proxy.php";s:29:"App\Controller\ChatController";s:69:"/work/runtime/container/proxy/App_Controller_ChatController.proxy.php";s:33:"App\Controller\AbstractController";s:73:"/work/runtime/container/proxy/App_Controller_AbstractController.proxy.php";s:20:"App\Task\AutoSslTask";s:60:"/work/runtime/container/proxy/App_Task_AutoSslTask.proxy.php";}i:2;a:0:{}}