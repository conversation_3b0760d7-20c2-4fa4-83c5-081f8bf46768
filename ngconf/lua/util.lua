local _M = {}
local resty_md5 = require "resty.md5"
local str = require "resty.string"

local secret = "my_super_secret_salt_2025"

function _M.sign(token)
    local md5 = resty_md5:new()
    md5:update(token .. secret)
    return str.to_hex(md5:final())
end

function _M.check_signature(token, sig)
    if not token or not sig then
        return false
    end
    return sig == _M.sign(token)
end

return _M
