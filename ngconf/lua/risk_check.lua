local _M = {}

local ipmatcher = require("ipmatcher")
local lrucache = require("resty.lrucache")
local spider_lru = lrucache.new(10)

local cjson = require "cjson.safe"
local redis = require "resty.redis"
local http = require "resty.http"
local site_cache = ngx.shared.site_config_cache
local gate_cache = ngx.shared.gate_cache

local function is_static_or_subreq()
    local uri = ngx.var.uri or ""
    if ngx.req.is_internal() or ngx.req.get_method() ~= "GET" then
        return true
    end
    if uri:match("^.+%.(css|js|png|jpe?g|gif|svg|ico|woff2?|ttf|eot|webp)$") then
        return true
    end
    return false
end

local function return_message(text, status)
    ngx.status = status or 200
    ngx.header["Content-Type"] = "text/plain; charset=utf-8"
    ngx.say(text)
    return ngx.exit(ngx.HTTP_OK)
end

local function report_log(config_key, reason, is_success)
    -- ngx.log(ngx.ERR, "[reason]: ", reason, is_success)
    local httpc = http.new()
    httpc:set_timeout(8000)
    local aheader = ngx.req.get_headers()
    local res, err = httpc:request_uri("http://hyperf:9501/api/client/postLog", {
        method = "POST",
        headers = {
            ["Content-Type"] = "application/json"
        },
        body = cjson.encode({
            site_key = config_key,
            ip = ngx.var.remote_addr,
            ua = ngx.var.http_user_agent or "",
            host = ngx.var.host or "",
            headers = aheader,
            tag = ngx.var.tag or "-",
            reason = reason,
            is_success = is_success,
            timestamp = ngx.now()
        })
    })

    if not res then
        ngx.log(ngx.ERR, "[GateLog] Failed to report: ", err)
        return_message("error", 200)
    end

    local body = res.body
    if body == "false" then
        return_message("Not Found", 200);
    elseif body == "invalid" then
        return_message("authorization failed!!!", 200)
    elseif body == "ok" then
        return true
    else
        return ngx.redirect(body, 302)
    end

end

local function get_redis_conn()
    local red = redis:new()
    red:set_timeout(1000)
    local ok, err = red:connect("hyperf-redis", 6379)
    if not ok then
        ngx.log(ngx.ERR, "[Gate] Redis connect fail: ", err)
        return nil
    end
    return red
end

local function is_spider_ip(ip, red)
    if not red then
        return false
    end

    -- ✅ 全站 matcher 缓存（每个 worker）
    local matcher = spider_lru:get("matcher")
    if matcher then
        if matcher:match(ip) then
            return true, "Spider Block (cidr)"
        end
        return false
    end

    -- 👇 仅首次构建 matcher
    local cached = gate_cache:get("spider_cidrs")
    local cidrs = {}

    if not cached then
        local raw = red:smembers("spider:cidr")
        if type(raw) == "table" then
            cidrs = raw
        end
        gate_cache:set("spider_cidrs", cjson.encode(cidrs), 900)
    else
        cidrs = cjson.decode(cached)
    end

    local matcher, err = ipmatcher.new(cidrs)
    if not matcher then
        ngx.log(ngx.ERR, "[Gate] Failed to build matcher: ", err)
        return false
    end

    -- ✅ 缓存 matcher 到 LRU（900 秒）
    spider_lru:set("matcher", matcher, 900)

    if matcher:match(ip) then
        return true, "Spider Block (cidr)"
    end
    return false
end


local function get_site_config(red, site_key)
    local cached = site_cache:get(site_key)
    if cached then
        return cjson.decode(cached)
    end

    local raw, err = red:get(site_key)
    if not raw or raw == ngx.null then
        ngx.log(ngx.ERR, "[Gate] siteConfig not found: ", site_key)
        return nil
    end
    site_cache:set(site_key, raw, 5)
    return cjson.decode(raw)
end

local function get_real_ip()
    local headers = ngx.req.get_headers()
    local real_ip = headers["CF-Connecting-IP"] or headers["X-Real-IP"] or headers["X-Forwarded-For"] or
                        ngx.var.remote_addr
    if real_ip then

        real_ip = real_ip:match("([^, ]+)")
    end
    return real_ip or "-"
end

function _M.check(site_key)

    local config_key = site_key
    site_key = "siteConfig:" .. site_key
    if ngx.ctx.gate_checked then
        return true
    end
    ngx.ctx.gate_checked = true

    if not site_key then
        return_message("site configuration failed", 200)
        return false
    end

    local ip = get_real_ip()
    local cache_key = "pass:" .. site_key .. ":" .. ip
    local passed = gate_cache:get(cache_key)
    if passed == "1" then
        return true
    end

    local red = get_redis_conn()
    if not red then
        return false
    end

    local is_spider, reason = is_spider_ip(ip, red)
    if is_spider then
        report_log(config_key, "Suspected Crawler IP", 0)

        return_message("Not Found", 200);
        return false
    end

    local cfg = get_site_config(red, site_key)
    if not cfg then

        return_message("site configuration failed", 200)
        return false
    end

    local ua = ngx.var.http_user_agent or ""
    local tag = ngx.var.tag or "-"
    local ip = ngx.var.remote_addr or ""

    local pc = cfg.pc_access or 0
    local block_ios = cfg.block_ios or 0
    local block_android = cfg.block_android or 0

    if ua == "" then
        report_log(config_key, "Unknown Visit", 0)
        return false
    end

    local bot_pattern = [[
        facebookexternalhit|curl/|Google-PageRenderer|zgrab|archive.org_bot|scanner.ducks.party|
        python-requests|paloaltonetworks.com|DingTalkBot|help@moz|Apache-HttpClient|oBot|Little Client|
        fasthttp|xx032_bo9vs83_2a|wp_is_mobile|coccocbot|SeekportBot|ZoominfoBot|Bytespider|HostTracker|
        serpstatbot|alibaba|Fuzz Faster U|DataForSeoBot|CensysInspect|Barkrowler|gobuster|VelenPublicWebCrawler|
        Seekport|Nimbostratus|PetalBot|Qwantify|python|SiteExplorer|SEOkicks-Robot|TwengaBot|spbot|MegaIndex|
        coccoc|Aboundex|Genieo|ShopWiki|sandcrawlerbot|Ezooms|RU_Bot|sqlmap|Linkextractor|Xenu Link Sleuth|
        libcurl/7.64.1|curl/7.64.1|Outbrain|SerendeputyBot|Slackbot-LinkExpanding|Google-Read-Aloud|
        AppEngine-Google|Feedfetcher-Google|WVS|Acunetix|WebInspect|Openvas|Nikto|Nessus|havij|SQLmap|
        Nutch|Zombiebot|WhatsApp|NetcraftSurveyAgent|Screaming Frog SEO Spider|Pandalytics|ApacheBench|
        ZyteSmartProxy|Lynx|HeadlessFirefox|HeadlessChrome|PhantomJS|Site24x7|UptimeRobot|Pingdom|DiscordBot|
        TelegramBot|Slackbot|Pinterest|LinkedInBot|FacebookExternalHit|Twitterbot|Mail.RU_Bot|360Spider|
        Sogou Spider|Baiduspider|DuckDuckBot|YandexBot|Slurp|Bingbot|Googlebot-News|Googlebot|DomainStatsBot|
        MegaIndex.ru|LinkpadBot|Cliqzbot|SeznamBot|Exabot|YisouSpider|BLEXBot|SemrushBot|DotBot|MJ12bot|
        AhrefsBot|WinHttp.WinHttpRequest.5|okhttp|Dalvik|PHP|Jmeter|Pyspider|C# Httpclient|C# Webclient|
        Ruby|perl|phpcrawl|libwww-perl|Wget|Mechanize|axios|node-fetch|Go-http-client|HttpClient|Java|
        Scrapy|Python-urllib|Googlebot|Googlebot-Mobile|Googlebot-Image|Googlebot-Video|AdsBot-Google|AdsBot-Google-Mobile|Feedfetcher-Google|Mediapartners-Google|APIs-Google|bingbot|Slurp|wget|[wW]get|LinkedInBot|Python-urllib|python-requests|aiohttp|httpx|libwww-perl|nutch|Go-http-client|msnbot|FAST-WebCrawler|BIGLOTRON|Teoma|exabot|ia_archiver|GingerCrawler|webmon|HTTrack|grub.org|UsineNouvelleCrawler|antibot|netresearchserver|seekbot|Gigabot|Gigablast|yacybot|AISearchBot|ips-agent|tagoobot|MJ12bot|woriobot|yanga|buzzbot|mlbot|YandexImages|YandexAccessibilityBot|YandexMobileBot|YandexMetrika|YandexTurbo|YandexImageResizer|YandexVideo|YandexAdNet|YandexBlogs|YandexCalendar|YandexDirect|YandexFavicons|YaDirectFetcher|YandexForDomain|YandexMarket|YandexMedia|YandexMobileScreenShotBot|YandexNews|YandexOntoDB|YandexPagechecker|YandexPartner|YandexRCA|YandexSearchShop|YandexSitelinks|YandexSpravBot|YandexTracker|YandexVertis|YandexVerticals|YandexWebmaster|YandexScreenshotBot|purebot|Linguee Bot|CyberPatrol|voilabot|Baiduspider|citeseerxbot|spbot|twengabot|postrank|TurnitinBot|scribdbot|page2rss|sitebot|linkdex|Adidxbot|ezooms|dotbot|Mail.RU_Bot|discobot|heritrix|findthatfile|europarchive.org|NerdByNature.Bot|sistrix crawler|Ahrefs(Bot|SiteAudit)|fuelbot|CrunchBot|IndeedBot|mappydata|woobot|ZoominfoBot|PrivacyAwareBot|Multiviewbot|SWIMGBot|Grobbot|eright|Apercite|semanticbot|Aboundex|domaincrawler|wbsearchbot|summify|CCBot|edisterbot|seznambot|ec2linkfinder|gslfbot|aiHitBot|intelium_bot|facebookexternalhit|Yeti|RetrevoPageAnalyzer|lb-spider|Sogou|lssbot|careerbot|wotbox|wocbot|ichiro|DuckDuckBot|lssrocketcrawler|drupact|webcompanycrawler|acoonbot|openindexspider|gnam gnam spider|web-archive-net.com.bot|backlinkcrawler|coccoc|integromedb|content crawler spider|toplistbot|it2media-domain-crawler|ip-web-crawler.com|siteexplorer.info|elisabot|proximic|changedetection|arabot|WeSEE:Search|niki-bot|CrystalSemanticsBot|rogerbot|360Spider|psbot|InterfaxScanBot|CC Metadata Scaper|g00g1e.net|GrapeshotCrawler|urlappendbot|brainobot|fr-crawler|binlar|SimpleCrawler|Twitterbot|cXensebot|smtbot|bnf.fr_bot|A6-Indexer|ADmantX|Facebot|OrangeBot/|memorybot|AdvBot|MegaIndex|SemanticScholarBot|ltx71|nerdybot|xovibot|BUbiNG|Qwantify|archive.org_bot|Applebot|TweetmemeBot|crawler4j|findxbot|S[eE][mM]rushBot|yoozBot|lipperhey|YJ|Domain Re-Animator Bot|AddThis|Screaming Frog SEO Spider|MetaURI|Scrapy|Livelap[bB]ot|OpenHoseBot|CapsuleChecker|infegy.com|IstellaBot|DeuSu/|betaBot|Cliqzbot/|MojeekBot/|netEstate NE Crawler|SafeSearch microdata crawler|Gluten Free Crawler/|Sonic|Sysomos|Trove|deadlinkchecker|Slack-ImgProxy|Embedly|RankActiveLinkBot|iskanie|SafeDNSBot|SkypeUriPreview|Veoozbot|Slackbot|redditbot|datagnionbot|Google-Adwords-Instant|adbeat_bot|WhatsApp|contxbot|pinterest.com.bot|electricmonk|GarlikCrawler|BingPreview/|vebidoobot|FemtosearchBot|Yahoo Link Preview|MetaJobBot|DomainStatsBot|mindUpBot|Daum/|Jugendschutzprogramm-Crawler|Xenu Link Sleuth|Pcore-HTTP|moatbot|KosmioBot|pingdom|AppInsights|PhantomJS|Gowikibot|PiplBot|Discordbot|TelegramBot|Jetslide|newsharecounts|James BOT|Bark[rR]owler|TinEye|SocialRankIOBot|trendictionbot|Ocarinabot|epicbot|Primalbot|DuckDuckGo-Favicons-Bot|GnowitNewsbot|Leikibot|LinkArchiver|YaK/|PaperLiBot|Digg Deeper|dcrawl|Snacktory|AndersPinkBot|Fyrebot|EveryoneSocialBot|Mediatoolkitbot|Luminator-robots|ExtLinksBot|SurveyBot|NING/|okhttp|Nuzzel|omgili|PocketParser|YisouSpider|um-LN|ToutiaoSpider|MuckRack|Jamie's Spider|AHC/|NetcraftSurveyAgent|Laserlikebot|^Apache-HttpClient|AppEngine-Google|Jetty|Upflow|Thinklab|Traackr.com|Twurly|Mastodon|http_get|DnyzBot|botify|007ac9 Crawler|BehloolBot|BrandVerity|check_http|BDCbot|ZumBot|EZID|ICC-Crawler|ArchiveBot|^LCC|filterdb.iss.net/crawler|BLP_bbot|BomboraBot|Buck/|Companybook-Crawler|Genieo|magpie-crawler|MeltwaterNews|Moreover|newspaper/|ScoutJet|(^| )sentry/|StorygizeBot|UptimeRobot|OutclicksBot|seoscanners|Hatena|Google Web Preview|MauiBot|AlphaBot|SBL-BOT|IAS crawler|adscanner|Netvibes|acapbot|Baidu-YunGuanCe|bitlybot|blogmuraBot|Bot.AraTurka.com|bot-pge.chlooe.com|BoxcarBot|BTWebClient|ContextAd Bot|Digincore bot|Disqus|Feedly|Fetch/|Fever|Flamingo_SearchEngine|FlipboardProxy|g2reader-bot|G2 Web Services|imrbot|K7MLWCBot|Kemvibot|Landau-Media-Spider|linkapediabot|vkShare|Siteimprove.com|BLEXBot/|DareBoost|ZuperlistBot/|Miniflux/|Feedspot|Diffbot/|SEOkicks|tracemyfile|Nimbostratus-Bot|zgrab|PR-CY.RU|AdsTxtCrawler|Datafeedwatch|Zabbix|TangibleeBot|google-xrawler|axios|Amazon CloudFront|Pulsepoint|CloudFlare-AlwaysOnline|Google-Structured-Data-Testing-Tool|WordupInfoSearch|WebDataStats|HttpUrlConnection|Seekport Crawler|ZoomBot|VelenPublicWebCrawler|MoodleBot|jpg-newsbot|outbrain|W3C_Validator|Validator.nu|W3C-checklink|W3C-mobileOK|W3C_I18n-Checker|FeedValidator|W3C_CSS_Validator|W3C_Unicorn|Google-PhysicalWeb|Blackboard|ICBot/|BazQux|Twingly|Rivva|Experibot|awesomecrawler|Dataprovider.com|GroupHigh/|theoldreader.com|AnyEvent|Uptimebot.org|Nmap Scripting Engine|2ip.ru|Clickagy|Caliperbot|MBCrawler|online-webceo-bot|B2B Bot|AddSearchBot|Google Favicon|HubSpot|Chrome-Lighthouse|HeadlessChrome|CheckMarkNetwork/|www.uptime.com|Streamline3Bot/|serpstatbot/|MixnodeCache/|^curl|SimpleScraper|RSSingBot|Jooblebot|fedoraplanet|Friendica|NextCloud|Tiny Tiny RSS|RegionStuttgartBot|Bytespider|Datanyze|Google-Site-Verification|TrendsmapResolver|tweetedtimes|NTENTbot|Gwene|SimplePie|SearchAtlas|Superfeedr|feedbot|UT-Dorkbot|Amazonbot|SerendeputyBot|Eyeotabot|officestorebot|Neticle Crawler|SurdotlyBot|LinkisBot|AwarioSmartBot|AwarioRssBot|RyteBot|FreeWebMonitoring SiteChecker|AspiegelBot|NAVER Blog Rssbot|zenback bot|SentiBot|Domains Project/|Pandalytics|VKRobot|bidswitchbot|tigerbot|NIXStatsbot|Atom Feed Robot|Curebot|PagePeeker/|Vigil/|rssbot/|startmebot/|JobboerseBot|seewithkids|NINJA bot|Cutbot|BublupBot|BrandONbot|RidderBot|Taboolabot|Dubbotbot|FindITAnswersbot|infoobot|Refindbot|BlogTraffic/\d+\.\d+ Feed-Fetcher|SeobilityBot|Cincraw|Dragonbot|VoluumDSP-content-bot|FreshRSS|BitBot|^PHP-Curl-Class|Google-Certificates-Bridge|python|GoogleBot|FeedDemon|CrawlDaddy|Twitterbot/1.0|Java|Feedly|UniversalFeedParser|ApacheBench|Swiftbot|ZmEu|IndyLibrary|oBot|Bot|BOT|jaunty|YandexBot|AhrefsBot|MJ12bot|WinHttp|EasouSpider|HttpClient|Microsoft URL Control|YYSpider|Python-urllib|lightDeckReports Bot|BLEXBot|facebookexternalhit/1.1;line-poker/1.0|360Spider|python-requests/2.25.1|Python-urllib/3.9|webtech/1.2.11|Go-http-client/1.1|Android 2.0.1|Mozilla/5.0 (Linux; Android 9) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/103.0.5060.71 Mobile DuckDuckGo/5 Safari/537.3|Mozilla/5.0 (Linux; U; Android 2.0.1; en-us; Droid Build/ESD56) AppleWebKit/530.17 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36|Mozilla/5.0 (compatible; AhrefsBot/7.0; +http://ahrefs.com/robot/)|Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.75 Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)|Mediapartners-Google|APIs-Google (+https://developers.google.com/webmasters/APIs-Google.html)|Mozilla/5.0 (Linux; Android 5.0; SM-G920A) AppleWebKit (KHTML, like Gecko) Chrome Mobile Safari (compatible; AdsBot-Google-Mobile; +http://www.google.com/mobile/adsbot.html)|Mozilla/5.0 (iPhone; CPU iPhone OS 9_1 like Mac OS X) AppleWebKit/601.1.46 (KHTML,like Gecko) Version/9.0 Mobile/13B143 Safari/601.1 (compatible; AdsBot-Google-Mobile; +http://www.google.com/mobile/adsbot.html)|AdsBot-Google (+http://www.google.com/adsbot.html)|Googlebot-Image/1.0|Googlebot-News|Googlebot-Video/1.0|Googlebot-Video|Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)|Mozilla/5.0 AppleWebKit/537.36 (KHTML, like Gecko; compatible; Googlebot/2.1; +http://www.google.com/bot.html)|Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)|AdsBot-Google-Mobile-Apps|FeedFetcher-Google; (+http://www.google.com/feedfetcher.html)|Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2272.118 Safari/537.36 (compatible; Google-Read-Aloud; +https://developers.google.com/search/docs/advanced/crawling/overview-google-crawlers)|Mozilla/5.0 (Linux; Android 7.0; SM-G930V Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.125 Mobile Safari/537.36 (compatible; Google-Read-Aloud; +https://developers.google.com/search/docs/advanced/crawling/overview-google-crawlers)|google-speakr|Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012; DuplexWeb-Google/1.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.131 Mobile Safari/537.36|Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.75 Safari/537.36 Google Favicon|Mozilla/5.0 (Linux; Android 4.2.1; en-us; Nexus 5 Build/JOP40D) AppleWebKit/535.19 (KHTML, like Gecko) Version/4.0 Mobile Safari/535.19|Mozilla/5.0 (X11; Linux x86_64; Storebot-Google/1.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.88 Safari/537.36|Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012; Storebot-Google/1.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Mobile Safari/537.36|FreshpingBot/1.0 (+https://freshping.io/)|bot|google|softlayer|amazonaws|cyveillance|compatible|facebook|phishtank|dreamhost|netpilot|calyxinstitute|tor-exit|apache-httpclient|lssrocketcrawler|Trident|X11|Macintosh|crawler|urlredirectresolver|jetbrains|spam|windows 95|windows 98|acunetix|netsparker|007ac9|008|192.comagent|200pleasebot|360spider|4seohuntbot|50.nu|a6-indexer|admantx|amznkassocbot|aboundexbot|aboutusbot|abrave spider|accelobot|acoonbot|addthis.com|adsbot-google|ahrefsbot|alexabot|amagit.com|analytics|antbot|apercite|aportworm|arabot|crawl|slurp|spider|seek|accoona|acoon|adressendeutschland|ah-ha.com|ahoy|altavista|ananzi|anthill|appie|arachnophilia|arale|araneo|aranha|architext|aretha|arks|asterias|atlocal|atn|atomz|augurfind|backrub|bannana_bot|baypup|bdfetch|big brother|biglotron|bjaaland|blackwidow|blaiz|blog|blo.|bloodhound|boitho|booch|bradley|butterfly|calif|cassandra|ccubee|cfetch|charlotte|churl|cienciaficcion|cmc|collective|comagent|combine|computingsite|csci|curl|cusco|daumoa|deepindex|delorie|depspid|deweb|die blinde kuh|digger|ditto|dmoz|docomo|download express|dtaagent|dwcp|ebiness|ebingbong|e-collector|ejupiter|emacs-w3 search engine|esther|evliya celebi|ezresult|falcon|felix ide|ferret|fetchrover|fido|findlinks|fireball|fish search|fouineur|funnelweb|gazz|gcreep|genieknows|getterroboplus|geturl|glx|goforit|golem|grabber|grapnel|gralon|griffon|gromit|grub|gulliver|hamahakki|harvest|havindex|helix|heritrix|hku www octopus|homerweb|htdig|html index|html_analyzer|htmlgobble|hubater|hyper-decontextualizer|ia_archiver|ibm_planetwide|ichiro|iconsurf|iltrovatore|image.kapsi.net|imagelock|incywincy|indexer|infobee|informant|ingrid|inktomisearch.com|inspector web|intelliagent|internet shinchakubin|ip3000|iron33|israeli-search|ivia|jack|jakarta|javabee|jetbot|jumpstation|katipo|kdd-explorer|kilroy|knowledge|kototoi|kretrieve|labelgrabber|lachesis|larbin|legs|libwww|linkalarm|link validator|linkscan|lockon|lwp|lycos|magpie|mantraagent|mapoftheinternet|marvin/|mattie|mediafox|mediapartners|mercator|merzscope|microsoft url control|minirank|miva|mj12|mnogosearch|moget|monster|moose|motor|multitext|muncher|muscatferret|mwd.search|myweb|najdi|nameprotect|nationaldirectory|nazilla|ncsa beta|nec-meshexplorer|nederland.zoek|netcarta webmap engine|netmechanic|netresearchserver|netscoop|newscan-online|nhse|nokia6682/|nomad|noyona|nutch|nzexplorer|objectssearch|occam|omni|open text|openfind|openintelligencedata|orb search|osis-project|pack rat|pageboy|pagebull|page_verifier|panscient|parasite|partnersite|patric|pear.|pegasus|peregrinator|pgp key agent|phantom|phpdig|picosearch|piltdownman|pimptrain|pinpoint|pioneer|piranha|plumtreewebaccessor|pogodak|poirot|pompos|poppelsdorf|poppi|popular iconoclast|psycheclone|publisher|python|rambler|raven search|roach|road runner|roadhouse|robbie|robofox|robozilla|rules|salty|sbider|scooter|scoutjet|scrubby|search.|searchprocess|semanticdiscovery|senrigan|sg-scout|shai'hulud|shark|shopwiki|sidewinder|sift|silk|simmany|site searcher|site valet|sitetech-rover|skymob.com|sleek|smartwit|sna-|snappy|snooper|sohu|speedfind|sphere|sphider|spinner|spyder|steeler/|suke|suntek|supersnooper|surfnomore|sven|sygol|szukacz|tach black widow|tarantula|templeton|/teoma|t-h-u-n-d-e-r-s-t-o-n-e|theophrastus|titan|titin|tkwww|toutatis|t-rex|tutorgig|twiceler|twisted|ucsd|udmsearch|url check|updated|vagabondo|valkyrie|verticrawl|victoria|vision-search|volcano|voyager/|voyager-hc|w3c_validator|w3m2|w3mir|walker|wallpaper|wanderer|wauuu|wavefire|web core|web hopper|web wombat|webbandit|webcatcher|webcopy|webfoot|weblayers|weblinker|weblog monitor|webmirror|webmonkey|webquest|webreaper|websitepulse|websnarf|webstolperer|webvac|webwalk|webwatch|webwombat|webzinger|wget|whizbang|whowhere|wild ferret|worldlight|wwwc|wwwster|xenu|xift|xirq|yandex|yanga|yeti|yahoo!
        ]]
    if ua:match(bot_pattern) then
        report_log(config_key, "Suspected Crawler UA", 0)
        return false
    end

    local is_black, err = red:sismember("black_user", ip)
    if is_black == 1 then
        report_log(config_key, "Black User", 0)
        return false
    end

    local black = {}
    for _, v in ipairs(cfg.black_list or {}) do
        black[v] = true
    end
    if black[tag] then
        report_log(config_key, "Country Access denied (in blacklist)" .. tag, 0)
        return false
    end

    local wl = cfg.white_list or {}
    if #wl > 0 then
        local white = {}
        for _, v in ipairs(wl) do
            white[v] = true
        end
        if not white[tag] then
            report_log(config_key, "Country Access denied (not in whitelist)", 0)
            return false
        end
    end

    local is_ios = ua:match("iPhone") or ua:match("iPad")
    local is_android = ua:match("Android")

    if block_ios == 1 and is_ios then
        report_log(config_key, "IOS Access denied", 0)
        return false
    end

    if block_android == 1 and is_android then
        report_log(config_key, "Android Access denied", 0)
        return false
    end

    if pc == 0 then
        local is_mobile = ua:lower():match("mobile") or ua:match("Android") or ua:match("iPhone")
        if not is_mobile then
            report_log(config_key, "PC Access denied", 0)
            return false
        end
    end

    local passed = report_log(config_key, "Success", 1)

    if passed == true then
        gate_cache:set(cache_key, "1", 5)
        return true
    else
        return false
    end

end

return _M
